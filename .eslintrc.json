{"root": true, "ignorePatterns": ["**/*"], "parser": "@typescript-eslint/parser", "plugins": ["@nx", "@typescript-eslint", "eslint-plugin-prettier", "unused-imports"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended", "eslint-config-prettier", "next/core-web-vitals", "prettier"], "overrides": [{"files": ["*.ts", "*.tsx", "*.js", "*.jsx"], "rules": {"@nx/enforce-module-boundaries": ["error", {"enforceBuildableLibDependency": true, "allow": [], "depConstraints": [{"sourceTag": "*", "onlyDependOnLibsWithTags": ["*"]}]}], "react/jsx-no-useless-fragment": "off", "@typescript-eslint/explicit-function-return-type": 0, "@typescript-eslint/explicit-member-accessibility": 0, "@typescript-eslint/indent": 0, "@typescript-eslint/member-delimiter-style": 0, "@typescript-eslint/no-explicit-any": 0, "@typescript-eslint/no-var-requires": 0, "@typescript-eslint/no-use-before-define": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/no-unused-vars": "off", "unused-imports/no-unused-imports": "off", "unused-imports/no-unused-vars": ["warn", {"vars": "all", "varsIgnorePattern": "^_", "args": "after-used", "argsIgnorePattern": "^_"}], "prettier/prettier": ["error", {"endOfLine": "auto"}]}}, {"files": ["*.ts", "*.tsx"], "extends": ["plugin:@nx/typescript"], "rules": {}}, {"files": ["*.js", "*.jsx"], "extends": ["plugin:@nx/javascript"], "rules": {}}]}