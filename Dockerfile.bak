# Use a Node.js base image
FROM node:20-alpine as builder

# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Copy all files in the root
COPY ./*.json ./
COPY ./*.js ./
COPY ./*.ts ./
COPY ./*.md ./
COPY ./*.lock ./

# Copy the whole /libs and /tools folders
COPY ./libs ./libs

# Declare build arguments
ARG APP_NAME

# Copy apps/${APP_NAME} folder
COPY ./apps/${APP_NAME} ./apps/${APP_NAME}

# Copy .env.build.example to root as .env before build
COPY ./apps/${APP_NAME}/.env.build.example ./apps/${APP_NAME}/.env

# Install dependencies
RUN npm install

# Set environment variables (adjust as necessary)
ENV NODE_ENV=production
ENV NX_DAEMON=false

# Reset the nx
RUN npx nx reset

# Build the application
RUN npx nx build ${APP_NAME} --prod

# Start a new stage from node alpine to keep the image small
FROM node:20-alpine

# Install yq
RUN apk add --no-cache yq

WORKDIR /app

# Declare build arguments, expect PORT to be passed in or use 3000 as default
ARG APP_NAME

# Copy the script to replace the basepath
COPY ./apps/${APP_NAME}/*.sh /app

# Copy built assets from the builder stage
COPY --from=builder /app/dist/apps/${APP_NAME} .

# Copy .env.uat.example to root as .env, replace if already exists
COPY ./apps/${APP_NAME}/.env.uat.example /app/.env

# Install production dependencies
RUN npm install --production

# Copy the original .next directory to a temporary location
RUN cp -r /app/.next /app/.next_backup

# Ensure your script is executable
RUN chmod +x /app/replace-basepath.sh

# Expose the port the app runs on
EXPOSE 3000

CMD [ "sh", "-c", "/app/replace-basepath.sh && node_modules/next/dist/bin/next start -p 3000" ]