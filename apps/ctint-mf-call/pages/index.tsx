import { PageRenderer, Toaster } from '@cdss-modules/design-system';
import Detail from '../components/_screen/Detail';
import { basePath, mfName } from '../lib/appConfig';
import loadGlobalConfig from '@cdss-modules/design-system/lib/globalConfig';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { TbarProvider } from '@cdss-modules/design-system/context/TBarContext';
const queryClient = new QueryClient();
export const Page = () => {
    return (
        <div className="relative p-4 w-full h-screen">
            <PageRenderer
                routes={[
                    {
                        path: '/',
                        group: 'ctint-mf-call',
                        component: <QueryClientProvider client={queryClient}>
                            <TbarProvider>
                                <Detail />
                            </TbarProvider>
                        </QueryClientProvider>,
                    }
                ]}
                basePath={basePath}
            />
        </div>
    );
};

export const getServerSideProps = async () => {
    const globalConfig = loadGlobalConfig(mfName);
    const publicEnvVars = Object.keys(process.env).reduce(
        (publicVars: any, key) => {
            if (key.startsWith('CDSS_PUBLIC_')) {
                publicVars[key] = process.env[key];
            }
            return publicVars;
        },
        {}
    ) as any;
    return {
        props: {
            globalConfig,
            publicEnvVars,
        },
    };
};

export default Page;
