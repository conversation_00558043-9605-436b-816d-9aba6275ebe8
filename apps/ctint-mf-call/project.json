{"name": "ctint-mf-call", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/ctint-mf-call", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/next:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"root": "apps/ctint-mf-call", "outputPath": "dist/apps/ctint-mf-call"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "ctint-mf-call:build", "dev": true, "port": 4401, "host": "0.0.0.0"}, "configurations": {"development": {"buildTarget": "ctint-mf-call:build:development", "dev": true}, "production": {"buildTarget": "ctint-mf-call:build:production", "dev": false}}}, "export": {"executor": "@nx/next:export", "options": {"buildTarget": "ctint-mf-call:build:production"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["coverage/apps/ctint-mf-call"], "options": {"jestConfig": "apps/ctint-mf-call/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/ctint-mf-call/**/*.{ts,tsx,js,jsx}"]}}}}