auth:
  headers:
    - name: traceId
    - name: tenant
    - name: sourceId
    - name: previousId
    - name: cdss_authorization
logger:
  headers:
    - name: traceId
    - name: tenant
    - name: sourceId
    - name: previousId
pureengage:
  host: http://*************:8090
  recordsLimit: 200
genesys-cloud:
  environment: mypurecloud.com.au
  apiHost: https://api.mypurecloud.com.au
  authHost: https://login.mypurecloud.com.au
  clientId: ${genesysCloudClientId}
  clientSecret: ${genesysCloudClientSecret}
ctint-dab:
  auth:
    host: http://ctint-dab-auth-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  config:
    host: http://ctint-dab-config-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  conv:
    host: http://ctint-dab-conv-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
ctint-state:
  auth:
    host: http://ctint-state-auth-service.cdss-data-ctint.svc.cluster.local:35000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /v1.0/state/ctint-state-auth
    active: true
  cdssmf:
    host: http://ctint-state-cdssmf-service.cdss-data-ctint.svc.cluster.local:35010
    basepath: /v1.0/state/ctint-state-cdssmf
    active: true
portals:
  - ctint-mf-cpp
  - ctint-mf-tts
  - ctint-mf-wap
microfrontends:
  ctint-mf-cdss:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/mf-cdss
  ctint-mf-cpp:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/mf-cpp
  ctint-mf-user-admin:
    host: http://localhost:4000
    basepath: /ctint/mf-user-admin
    user-tab-names: 
    - labelEn: Name
      labelCh: 姓名
      value: name
      filterType: input #select, date, compare, bool
      readOnly: false
      active: true
      require: true
    - labelEn: Username
      labelCh: 用戶賬號
      value: email
      filterType: input
      readOnly: false
      active: true
      require: true
    - labelEn: State
      labelCh: 狀態
      value: state
      filterType: select
      readOnly: false
      active: true
      require: false
    - labelEn: Description
      labelCh: 描述
      value: description
      filterType: input
      readOnly: false
      active: true
      require: false
    - labelEn: User Groups
      labelCh: 用戶組
      value: groupNames
      filterType: multipleSelect
      readOnly: false
      active: true
      require: false
    - labelEn: Roles
      labelCh: 角色
      value: roleNames
      filterType: multipleSelect
      readOnly: false
      active: true
      require: false
    - labelEn: Division
      labelCh: 部門
      value: divisionName
      filterType: input
      readOnly: true
      active: true
      require: false
    - labelEn: Platform
      labelCh: 平台
      value: platform
      filterType: input
      readOnly: true
      active: true
      require: false
    - labelEn: Tenant
      labelCh: 租戶
      value: tenant
      readOnly: true
      filterType: input
      active: true
      require: false
    - labelEn: Organization
      labelCh: 組織
      value: organization
      filterType: input
      readOnly: true
      active: true
      require: false
    - labelEn: Created By
      labelCh: 創建用戶
      value: createBy
      filterType: input
      readOnly: true
      active: true
      require: false
    - labelEn: Create At
      labelCh: 創建時間
      value: createTime
      filterType: dateRange
      readOnly: true
      active: true
      require: false
    - labelEn: Update By
      labelCh: 更新用戶
      value: updateBy
      filterType: input
      readOnly: true
      active: true
      require: false
    - labelEn: Update At
      labelCh: 更新時間
      value: updateTime
      filterType: dateRange
      readOnly: true
      active: true
      require: false
    audit-tab-names: 
    - labelEn: Event Type
      labelCh: 事件類型
      value: eventType
      filterType: input
      readOnly: false
      active: true
      sort: false
    - labelEn: Event Time
      labelCh: 事件時間
      value: eventTimestamp
      filterType: dateRange
      readOnly: false
      active: true
      sort: true
    - labelEn: IP Address
      labelCh: IP地址
      value: ipAddress
      filterType: input
      readOnly: false
      active: true
      sort: false
    - labelEn: User Agent
      labelCh: 用戶代理
      value: userAgent
      filterType:
      readOnly: true
      active: true
      sort: false
    - labelEn: Browser
      labelCh: 瀏覽器
      value: browser
      filterType: input
      readOnly: false
      active: true
      sort: false
    - labelEn: Operating System
      labelCh: 操作系統
      value: operatingSystem
      readOnly: true
      filterType: input
      active: true
      sort: false
    - labelEn: Device
      labelCh: 設備
      value: device
      filterType: input
      readOnly: true
      active: true
      sort: false
    - labelEn: Failure Reason
      labelCh: 失敗原因
      value: failureReason
      filterType: input
      readOnly: true
      active: true
      sort: false
    - labelEn: Additional Info
      labelCh: 附加信息
      value: additionalInfo
      filterType: input
      readOnly: true
      active: true
      sort: false
  ctint-mf-tts:
    host: http://localhost:4600
    basepath:
  ctint-mf-wap:
    host: http://localhost:4700
    basepath:
  ctint-mf-info:
    host: http://localhost:4800
    basepath:
  ctint-mf-call:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/mf-call
languages:
  supportedLanguages:
    - en
    - zh-HK
  defaultLanguage: en
services:
  ctint-stt:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/stt
    active: false
    provider:
      - astri
    healthcheck: /healthcheck
  ctint-auth:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/auth
    active: true
    provider: # pure-engage / genesys-cloud / ctint-dab-auth / ctint-state-auth
      - pure-engage
      - ctint-dab-auth
      - ctint-state-auth
    healthcheck: /healthcheck
  ctint-conv:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/conv
    active: true
    provider:
      - pureengage
      - ctint-dab-conv
    healthcheck: /healthcheck
  ctint-config:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/config
    active: true
    provider:
      - pureengage
      - ctint-dab-config
    healthcheck: /healthcheck
  ctint-session:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/session
    active: true
    provider:
      - ctint-state-cdssmf
    healthcheck: /healthcheck
  ctint-user:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/user
    active: true
    healthcheck: /healthcheck
  ctint-audit-log:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/audit-log
    active: true
    healthcheck: /healthcheck
  ctint-cdss-ws:
    host: https://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/cdss-ws
    active: true
    healthcheck: /healthcheck
  ctint-call-control:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/call-control
    active: true
    healthcheck: /healthcheck