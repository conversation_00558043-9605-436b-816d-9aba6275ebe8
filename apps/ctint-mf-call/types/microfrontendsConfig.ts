export interface microfrontends {
  'ctint-mf-cdss': CtintMF;
  'ctint-mf-cpp': CtintMF;
  'ctint-mf-user-admin': CtintMFUserAdmin;
  'ctint-mf-tts': CtintMF;
  'ctint-mf-wap': CtintMF;
  'ctint-mf-info': CtintMF;
  'ctint-mf-call': CtintMF;
}

export interface CtintMF {
  auditTabNames: never[];
  host: string;
  basepath: null | string;
}

export interface CtintMFUserAdmin {
  host: string;
  basepath: string;
  'user-tab-names': UserTabName[];
  'audit-tab-names': UserAuditTabName[];
}

export interface UserTabName {
  labelEn: string;
  labelCh: string;
  value: string;
  filterType: string;
  active: boolean;
  readOnly: boolean;
  require: boolean;
}
export interface UserAuditTabName {
  labelEn: string;
  labelCh: string;
  value: string;
  filterType: string;
  active: boolean;
  readOnly: boolean;
  sort: boolean;
}
export interface ShowColumn {
  key: string;
  value: string;
}

export interface TAdminRolesDataResp {
  data: TAdminRole[];
  error: string;
  isSuccess: boolean;
}

export interface TAdminRole {
  name: string;
  code: string;
  state: string;
  description: string;
  platform: string;
  tenant: string;
  createTime: Date;
  updateTime: Date;
  createBy: string;
  updateBy: string;
}

export interface TAdminUserGroupDataResp {
  data: TAdminUserGroup[];
  error: string;
  isSuccess: boolean;
}

export interface TAdminUserGroup {
  id: string;
  name: string;
  divisionId: string;
  divisionName: string;
  state: string;
  tenant: string;
  platform: string;
  createTime: string;
  updateTime: string;
  createBy: string;
  updateBy: string;
}

export interface CombinedRoleNGroupData {
  roleNames: TAdminRole[];
  groupNames: TAdminUserGroup[];
}

export type TAdminUserData = {
  id?: string;
  name?: string;
  email?: string;
  divisionId?: string;
  divisionName?: string;
  state?: string;
  description?: string;
  type?: string;
  platform?: string;
  tenant?: string;
  organization?: string;
  primaryContactInfo?: PrimaryContactInfo[];
  createdBy?: string;
  createdAt?: string;
  updatedBy?: string;
  updatedAt?: string;
  isAllowEdit?: boolean;
  roles?: userRole[];
  roleNames?: string;
  groups?: userGroup[];
  groupNames?: string;
};

export interface userGroup {
  id: string;
  name: string;
  divisionId: string;
  divisionName: string;
  state: string;
  tenant: string;
  platform: string;
  createTime: Date;
  updateTime: string;
  createBy: string;
  updateBy: string;
}

export interface userRole {
  name: string;
  code: string;
  platform: string;
  tenant: string;
  state: string;
  description: string;
  createTime: Date;
  updateTime: string;
  createBy: string;
  updateBy: string;
}

export interface PrimaryContactInfo {
  address: string;
  mediaType: string;
  type: string;
}

export type TAdminUserDataResp = {
  data: TAdminUserData[];
  error: string;
  isSuccess: boolean;
};

export type TformComponent = {
  title: string;
  name: string;
  value: string;
  readOnly: boolean;
  filterType: string;
  require: boolean;
};
