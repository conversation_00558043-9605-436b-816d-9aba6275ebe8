import React, { useState, useEffect } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { WhitePanel } from '@cdss-modules/design-system/components/_ui/WhitePanel';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import SearchInput from '@cdss-modules/design-system/components/_ui/SearchInput';
import FilterComponent, {
  Condition,
} from '@cdss-modules/design-system/components/_ui/FilterComponent';
import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';
import Pagination from '@cdss-modules/design-system/components/_ui/Pagination';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import { optionsMap } from '@cdss-modules/design-system/components/_ui/FilterComponent/config';
import {
  <PERSON>,
  useRole,
  useRoute<PERSON><PERSON><PERSON>,
  toast,
  Loader,
  Toaster,
} from '@cdss-modules/design-system';
import { RefreshCcw } from 'lucide-react';
import dayjs from 'dayjs';
import {
  BroadcastData,
  BroadcastTabName,
  CampaignSaveRequest,
} from '../../../types/microfrontendsConfig';
import { getPage, saveOrUpdate } from '../../../lib/api';
import ActionMenu from './actionMenu';

const queryClient = new QueryClient();

export function BroadcastBody() {
  const { globalConfig } = useRole();
  const { basePath, toPath, searchParams } = useRouteHandler();
  const { t, i18n } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [broadcastList, setBroadcastList] = useState<BroadcastData[]>([]);
  const [rowSelection, setRowSelection] = useState({});
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 50,
    total: 0,
    totalPage: 0,
  });

  // 从配置获取表格列定义
  const broadcastConfig = globalConfig?.microfrontends?.['ctint-mf-campaign'];
  const broadcastTabsCols: BroadcastTabName[] =
    broadcastConfig?.['broadcast-tab-names'] || [];

  // 在这里添加状态选项定义 - broadcastTabsCols 之后，初始化过滤器之前
  const statusOptions = [
    { value: 'Scheduled', labelEn: 'Scheduled', labelCh: '已计划' },
    { value: 'Sending', labelEn: 'Sending', labelCh: '发送中' },
    { value: 'Sent', labelEn: 'Sent', labelCh: '已发送' },
    { value: 'Resent', labelEn: 'Resent', labelCh: '已重发' },
    {
      value: 'ScheduledResend',
      labelEn: 'Scheduled Resend',
      labelCh: '计划重发',
    },
    { value: 'ReSending', labelEn: 'ReSending', labelCh: '重发中' },
  ];
  optionsMap.status = statusOptions;

  // 初始化过滤器值
  const initTableColsData = broadcastTabsCols.reduce(
    (acc, filter) => {
      if (filter.filterType === 'date') {
        acc[filter.value] = {
          ...filter,
          checked: false,
          data: undefined,
        };
      } else if (filter.filterType === 'select') {
        acc[filter.value] = {
          ...filter,
          filterType: 'select',
          checked: false,
          data: undefined,
        };
      } else {
        acc[filter.value] = {
          ...filter,
          checked: false,
          data: undefined,
        };
      }
      return acc;
    },
    {} as Record<string, Condition>
  );

  const [filterValue, setFilterValue] =
    useState<Record<string, Condition>>(initTableColsData);

  // 构建过滤标签
  const conbineFiltersTagName = () => {
    const tags: string[] = [];
    const sortedFilters = [...Object.values(filterValue)];
    sortedFilters.forEach((item) => {
      const key = item.value;
      if (filterValue[key]?.checked && filterValue[key]?.data) {
        let displayValue = filterValue[key].data;

        // 处理日期格式化 - 添加 sendDate 和 resendDate 的处理
        if (
          (key === 'date' || key === 'sendDate' || key === 'resendDate') &&
          typeof displayValue === 'object' &&
          displayValue !== null
        ) {
          // 根据不同日期字段使用不同格式
          if (key === 'date') {
            displayValue = `${dayjs(displayValue.start).format('YYYY-MM-DD')} - ${dayjs(displayValue.end).format('YYYY-MM-DD')}`;
          } else {
            // sendDate和resendDate使用带时间的格式
            displayValue = `${dayjs(displayValue.start).format('YYYY-MM-DD HH:mm')} - ${dayjs(displayValue.end).format('YYYY-MM-DD HH:mm')}`;
          }
        }

        tags.push(
          `${i18n.language === 'en' ? filterValue[key].labelEn : filterValue[key].labelCh}: ${displayValue}`
        );
      }
    });
    return tags;
  };

  // 渲染过滤标签
  const renderTagItems = () => {
    const sortedFilters = Object.values(filterValue);
    return sortedFilters.map((item, i) => {
      const key = item.value;
      if (!filterValue[key]?.checked || !filterValue[key]?.data) return null;

      let displayValue = filterValue[key].data;

      // 处理日期格式化
      if (
        key === 'date' &&
        typeof displayValue === 'object' &&
        displayValue !== null
      ) {
        displayValue = `${dayjs(displayValue.start).format('YYYY-MM-DD')} - ${dayjs(displayValue.end).format('YYYY-MM-DD')}`;
      }

      return (
        <div
          key={i}
          className="p-1 mr-1 mt-1 flex flex-row items-center border border-black rounded-[4px] text-[12px] text-black"
        >
          <span className="truncate">
            {(i18n.language === 'en'
              ? filterValue[key].labelEn
              : filterValue[key].labelCh) +
              ': ' +
              displayValue}
          </span>
          <span
            onClick={() => {
              setFilterValue((prev) => {
                const newFilterValues = { ...prev };
                newFilterValues[key] = {
                  ...newFilterValues[key],
                  checked: false,
                  data: undefined,
                };
                return newFilterValues;
              });
            }}
            className="ml-1 cursor-pointer"
          >
            <Icon name="cross" />
          </span>
        </div>
      );
    });
  };
  const showColumns: string[] = [];
  const showColumnsKey: string[] = [];

  // 正确的遍历方式
  broadcastTabsCols.forEach((item) => {
    if (item.active) {
      showColumnsKey.push(item.value);
      showColumns.push(i18n.language === 'en' ? item.labelEn : item.labelCh);
    }
  });

  // 获取广播列表数据
  const fetchBroadcasts = async (page = 1, pageSize = 50) => {
    setLoading(true);

    // 构建过滤参数
    const filters: Record<string, any> = {};
    Object.entries(filterValue).forEach(([key, condition]) => {
      if (condition.checked && condition.data) {
        if (key === 'date') {
          if (typeof condition.data === 'object' && condition.data !== null) {
            filters.startDate = dayjs(condition.data.start).format(
              'YYYY-MM-DD'
            );
            filters.endDate = dayjs(condition.data.end).format('YYYY-MM-DD');
          } else {
            filters.date = dayjs(condition.data).format('YYYY-MM-DD');
          }
        }
        // 处理 sendDate 和 resendDate 的日期范围
        else if (key === 'sendDate') {
          if (typeof condition.data === 'object' && condition.data !== null) {
            filters.sendDateStart = dayjs(condition.data.start).format(
              'YYYY-MM-DD HH:mm:ss'
            );
            filters.sendDateEnd = dayjs(condition.data.end).format(
              'YYYY-MM-DD HH:mm:ss'
            );
          } else {
            filters.sendDate = dayjs(condition.data).format(
              'YYYY-MM-DD HH:mm:ss'
            );
          }
        } else if (key === 'resendDate') {
          if (typeof condition.data === 'object' && condition.data !== null) {
            filters.resendDateStart = dayjs(condition.data.start).format(
              'YYYY-MM-DD HH:mm:ss'
            );
            filters.resendDateEnd = dayjs(condition.data.end).format(
              'YYYY-MM-DD HH:mm:ss'
            );
          } else {
            filters.resendDate = dayjs(condition.data).format(
              'YYYY-MM-DD HH:mm:ss'
            );
          }
        } else {
          filters[key] = condition.data;
        }
      }
    });

    try {
      const response = await getPage(basePath, page, pageSize, filters);

      // 检查响应是否有效
      if (response && response.data) {
        // 根据API返回的结构获取列表数据
        let listData = [];

        // 判断数据是在 response.data.list 还是 response.data.data.list
        if (response.data.list) {
          listData = response.data.list;
        } else if (response.data.data && response.data.data.list) {
          listData = response.data.data.list;
        }

        // 确保列表不为空
        if (listData && listData.length > 0) {
          // 设置数据到状态
          setBroadcastList(listData);

          // 获取分页信息 -
          const total: number =
            response.data.total ||
            (response.data.data && response.data.data.total
              ? response.data.data.total
              : 0);

          const currentPage: number =
            response.data.page ||
            (response.data.data && response.data.data.page
              ? response.data.data.page
              : page);

          const pageSize: number =
            response.data.pageSize ||
            (response.data.data && response.data.data.pageSize
              ? response.data.data.pageSize
              : pagination.pageSize);

          const totalPages: number =
            response.data.totalPage ||
            (response.data.data && response.data.data.totalPage
              ? response.data.data.totalPage
              : Math.ceil(total / pageSize));

          setPagination({
            current: currentPage,
            pageSize: pageSize,
            total: total,
            totalPage: totalPages,
          });
        } else {
          console.log('未获取到数据');
          setBroadcastList([]);
          setPagination({
            current: page,
            pageSize: pageSize,
            total: 0,
            totalPage: 0,
          });
        }
      } else {
        console.log('响应无效或没有数据');
        setBroadcastList([]);
        setPagination({
          current: page,
          pageSize: pageSize,
          total: 0,
          totalPage: 0,
        });
      }
    } catch (error) {
      console.error('获取广播数据失败:', error);
      setBroadcastList([]);
      toast({
        title: 'Error',
        description: 'Failed to fetch broadcast data',
        variant: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // 更新 filterValue 中的 status 选项
    setFilterValue((prevFilterValue) => ({
      ...prevFilterValue,
      status: {
        ...prevFilterValue.status,
        options: statusOptions,
      },
    }));

    // 获取广播列表数据（原有代码）
    fetchBroadcasts(1, pagination.pageSize);
  }, [basePath, searchParams]);

  // 导航到编辑页面
  const navigateToEdit = (id = 'new') => {
    // toPath(`/campaign/detail?id=${id}&mode=edit`);
    toPath(`/campaign/detail?mode=edit`);
  };

  // 生成表格列定义 - 按照ManualQueueBody的模式
  const generateColumns = (
    columns: string[],
    showColumns: string[],
    columnOrdering: string[]
  ) => {
    return [
      ...columns.map((col, index) => ({
        id: col,
        accessorKey: col,
        header: () => (
          <div className="flex items-center">
            <span className="text-[14px]">{showColumns[index]}</span>
          </div>
        ),
        cell: ({ row }: any) => {
          // 获取对应字段的值
          const value = row.original[col];

          if (value === null || value === undefined) {
            return '-';
          }

          // 状态列的特殊格式化
          if (col === 'status') {
            let color;
            switch (value) {
              case 'Scheduled':
                color = 'text-blue-500';
                break;
              case 'Failed':
                color = 'text-red-500';
                break;
              case 'Sent':
                color = 'text-green-500';
                break;
              default:
                color = '';
            }
            return <span className={color}>{value}</span>;
          }

          // 日期格式化
          if (
            (col === 'date' || col === 'sendDate' || col === 'resendDate') &&
            value
          ) {
            // 将UTC时间转换为UTC+8时区（添加8小时）
            return dayjs(value).add(8, 'hour').format('YYYY-MM-DD HH:mm');
          }

          if (
            col === 'sentCount' ||
            col === 'successCount' ||
            col === 'errorCount' ||
            col === 'readCount' ||
            col === 'repliedCount'
          ) {
            const count = value || '0';

            // 获取对应的百分比字段
            let percentageField = '';
            switch (col) {
              case 'sentCount':
                percentageField = 'sentCountPercent';
                break;
              case 'successCount':
                percentageField = 'successCountPercent';
                break;
              case 'errorCount':
                percentageField = 'errorCountPercent';
                break;
              case 'readCount':
                percentageField = 'readCountPercent';
                break;
              case 'repliedCount':
                percentageField = 'replyCountPercent';
                break;
            }

            const percentage = row.original[percentageField] || '0.00%';

            return (
              <div className="text-center">
                <div className="font-medium text-sm">{count}</div>
                <div className="text-xs text-gray-500">({percentage})</div>
              </div>
            );
          }

          return String(value);
        },
      })),
      // 操作列
      {
        id: 'actions',
        cell: ({ row }: any) => {
          return (
            <ActionMenu
              row={row}
              onViewClick={(row) => {
                toPath(`/campaign/detail?id=${row.original.id}&mode=view`);
              }}
              onResendFailedClick={(row, date) => {
                // Check if resend failed is possible
                if (
                  row.original.status === 'Sent' &&
                  parseInt(row.original.errorCount || '0', 10) > 0
                ) {
                  setLoading(true);
                  // Create request data for resending failed messages
                  const requestData = {
                    id: row.original.id,
                    status: 'ScheduledResend',
                    resendDate: date,
                  } as any;
                  saveOrUpdate(requestData, basePath)
                    .then((response) => {
                      toast({
                        title: 'Success',
                        description:
                          'Resend failed messages initiated successfully',
                        variant: 'success',
                      });

                      // Refresh the list after successful API call
                      fetchBroadcasts(pagination.current, pagination.pageSize);
                    })
                    .catch((error) => {
                      console.error('Error resending failed messages:', error);
                      toast({
                        title: 'Error',
                        description:
                          'Failed to resend failed messages: ' +
                          ((error as any).response?.data?.message ||
                            (error as any).message),
                        variant: 'error',
                      });
                    })
                    .finally(() => {
                      setLoading(false);
                    });
                } else {
                  toast({
                    title: 'Action Unavailable',
                    description:
                      'This action is only available for sent broadcasts with errors',
                    variant: 'error',
                  });
                }
              }}
              onMakeCopyClick={(row) => {
                // Navigate to edit page with copy mode
                toPath(`/campaign/detail?id=${row.original.id}&mode=copy`);
              }}
              onEditClick={(row) => {
                toPath(`/campaign/detail?id=${row.original.id}&mode=edit`);
              }}
              onCancelResendClick={(row) => {
                const requestData = {
                  id: row.original.id,
                  status: 'Sent',
                } as any;
                saveOrUpdate(requestData, basePath)
                  .then((response) => {
                    toast({
                      title: 'Success',
                      description:
                        'Resend failed messages initiated successfully',
                      variant: 'success',
                    });

                    // Refresh the list after successful API call
                    fetchBroadcasts(pagination.current, pagination.pageSize);
                  })
                  .catch((error) => {
                    console.error('Error resending failed messages:', error);
                    toast({
                      title: 'Error',
                      description:
                        'Failed to resend failed messages: ' +
                        ((error as any).response?.data?.message ||
                          (error as any).message),
                      variant: 'error',
                    });
                  })
                  .finally(() => {
                    setLoading(false);
                  });
              }}
            />
          );
        },
      },
    ];
  };

  const handleSearch = () => {
    fetchBroadcasts(1, pagination.pageSize);
  };

  const clearFilter = () => {
    const clearedFilters = { ...initTableColsData };
    Object.keys(clearedFilters).forEach((key) => {
      clearedFilters[key] = {
        ...clearedFilters[key],
        checked: false,
        data: undefined,
      };
    });
    setFilterValue(clearedFilters);
    fetchBroadcasts(1, pagination.pageSize);
  };

  return (
    <>
      {loading && (
        <div
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 999,
          }}
        >
          <Loader size={64} />
        </div>
      )}
      <div
        id="panelContainer"
        className="relative flex flex-col h-full w-full gap-4"
      >
        <WhitePanel className="flex flex-row w-full overflow-visible">
          <div className="flex-1 flex flex-row items-center">
            <SearchInput tags={conbineFiltersTagName()}>
              <section>
                <section className="max-h-[409px] overflow-y-auto">
                  <section className="p-4">
                    {Object.keys(filterValue).filter(
                      (key) => filterValue[key].data !== undefined
                    ).length > 0 && (
                      <div className="flex flex-wrap flex-row">
                        {renderTagItems()}
                      </div>
                    )}
                  </section>
                  <section className="px-4 pb-4">
                    <h2 className="text-[14px] mb-2">
                      {t(
                        'ctint-mf-campaign.filter.available',
                        'Available Filters'
                      )}
                      :
                    </h2>
                    <div className="flex flex-col gap-y-2">
                      <FilterComponent
                        filterValues={filterValue}
                        setFilterValues={setFilterValue}
                      />
                    </div>
                  </section>
                </section>
              </section>
            </SearchInput>
            <Button
              className="self-center ml-2"
              bodyClassName="border border-black py-[0.375rem]"
              onClick={handleSearch}
              size="s"
            >
              {t('ctint-mf-campaign.filter.search', 'Search')}
            </Button>
            <Button
              className="self-center ml-2"
              bodyClassName="border border-black py-[0.375rem]"
              onClick={clearFilter}
              variant="blank"
              size="s"
            >
              {t('ctint-mf-campaign.filter.reset', 'Clear All')}
            </Button>

            {/* 创建新广播按钮 */}
            <button
              onClick={() => navigateToEdit('new')}
              className="ml-2 cursor-pointer"
            >
              <svg
                width="29"
                height="30"
                viewBox="0 0 29 30"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M0 4.5C0 2.29086 1.79086 0.5 4 0.5H25C27.2091 0.5 29 2.29086 29 4.5V25.5C29 27.7091 27.2091 29.5 25 29.5H4C1.79086 29.5 0 27.7091 0 25.5V4.5Z"
                  fill="#00A3FF"
                />
                <path
                  d="M0.5 4.5C0.5 2.567 2.067 1 4 1H25C26.933 1 28.5 2.567 28.5 4.5V25.5C28.5 27.433 26.933 29 25 29H4C2.067 29 0.5 27.433 0.5 25.5V4.5Z"
                  fill="white"
                />
                <path
                  d="M0.5 4.5C0.5 2.567 2.067 1 4 1H25C26.933 1 28.5 2.567 28.5 4.5V25.5C28.5 27.433 26.933 29 25 29H4C2.067 29 0.5 27.433 0.5 25.5V4.5Z"
                  stroke="black"
                />
                <path
                  d="M14.5685 14.4247L14.5684 9.56746C14.5685 9.25145 14.3131 8.99602 13.9971 8.99609C13.6811 8.99609 13.4257 9.25153 13.4257 9.56746L13.4257 14.4248L8.56844 14.4247C8.25241 14.4248 7.99707 14.6801 7.99707 14.9961C7.99699 15.3121 8.25241 15.5675 8.56844 15.5675L13.4257 15.5675L13.4257 20.4247C13.4257 20.7408 13.6811 20.9962 13.9971 20.9961C14.0722 20.9962 14.1465 20.9815 14.2158 20.9528C14.2852 20.9241 14.3482 20.8819 14.4012 20.8288C14.5046 20.7254 14.5685 20.5823 14.5685 20.4247L14.5684 15.5675L19.4257 15.5675C19.5008 15.5676 19.5751 15.5529 19.6444 15.5241C19.7138 15.4954 19.7768 15.4533 19.8298 15.4002C19.9332 15.2968 19.9971 15.1537 19.9971 14.9961C19.9971 14.6801 19.7417 14.4247 19.4257 14.4248L14.5685 14.4247Z"
                  fill="black"
                />
              </svg>
            </button>

            {/* 刷新按钮 */}
            <button
              onClick={handleSearch}
              className="ml-auto p-2 rounded-full hover:bg-primary-50 transition-colors"
              title={t('refresh', 'Refresh')}
            >
              <RefreshCcw size={20} />
            </button>
          </div>
        </WhitePanel>
        <Panel
          headerClassName="bg-white border-b border-grey-200"
          containerClassName="flex flex-col h-full min-h-0"
        >
          <div className="overflow-auto px-4 flex-1 min-h-0">
            <DataTable
              data={broadcastList}
              columns={generateColumns(showColumnsKey, showColumns, [])}
              loading={false}
              emptyMessage={t('ctint-mf-campaign.no_data', 'No data found')}
              resize={true}
              rowSelection={rowSelection}
              setRowSelection={setRowSelection}
              onTableSetUp={(table) => console.log('表格设置完成:', table)}
            />
          </div>
          {pagination.total > 0 && (
            <section className="flex-shrink-0 px-4 py-4 border-t">
              <div>
                <Pagination
                  total={pagination.totalPage}
                  current={pagination.current}
                  initialPage={1}
                  siblings={1}
                  totalCount={pagination.total}
                  perPage={pagination.pageSize}
                  onChange={(page) => {
                    fetchBroadcasts(page, pagination.pageSize);
                  }}
                  handleOnNext={() => {
                    fetchBroadcasts(
                      pagination.current + 1,
                      pagination.pageSize
                    );
                  }}
                  handleOnPrevious={() => {
                    fetchBroadcasts(
                      pagination.current - 1,
                      pagination.pageSize
                    );
                  }}
                  handlePerPageSetter={(newPageSize) => {
                    fetchBroadcasts(1, newPageSize);
                  }}
                />
              </div>
            </section>
          )}
        </Panel>
        <Toaster />
      </div>
    </>
  );
}

export default function BroadcastMain() {
  return (
    <QueryClientProvider client={queryClient}>
      <BroadcastBody />
    </QueryClientProvider>
  );
}
