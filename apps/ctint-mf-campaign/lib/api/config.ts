// eslint-disable-next-line unused-imports/no-unused-imports

import { exportCampaignDetail } from './index';

export const apiConfig = {
  paths: {
    campaign: {
      saveOrUpdate:
        '/api/process-api/ctint-campaign/api/v1/campaign/saveOrUpdate',
      detail: '/api/process-api/ctint-campaign/api/v1/campaign/detail',
      page: '/api/process-api/ctint-campaign/api/v1/campaign/page',
      fileUpload: '/api/process-api/ctint-campaign/api/v1/file/upload',
      exportCampaignDetail:
        '/api/process-api/ctint-campaign/api/v1/campaign/export',

      contactGroups: '/api/process-api/ctint-campaign/api/v1//campaign/groups',
      contactGroupTemplate:
        '/api/process-api/ctint-campaign/api/v1//campaign/contactGroup/template/download',
      upload: '/api/process-api/ctint-campaign/api/v1//campaign/groups/upload',
      messageTemplates:
        '/api/process-api/ctint-campaign/api/v1//campaign/message/templates',
      messageTemplateDetail:
        '/api/process-api/ctint-campaign/api/v1//campaign/message/template',
      jobs: '/api/process-api/ctint-campaign/api/v1//campaign/jobs',
      messagePlatformAccount:
        '/api/process-api/ctint-campaign/api/v1//campaign/message/platformAccount',
    },
    // conv: {
    //   contactGroups: '/api/process-api/ctint-campaign/api/v1//campaign/groups',
    //   contactGroupTemplate:
    //     '/api/process-api/ctint-campaign/api/v1//campaign/contactGroup/template/download',
    //   upload: '/api/process-api/ctint-campaign/api/v1//campaign/groups/upload',
    //   messageTemplates:
    //     '/api/process-api/ctint-campaign/api/v1//campaign/message/templates',
    //   messageTemplateDetail:
    //     '/api/process-api/ctint-campaign/api/v1//campaign/message/template',
    //   jobs: '/api/process-api/ctint-campaign/api/v1//campaign/jobs',
    //   messagePlatformAccount:
    //     '/api/process-api/ctint-campaign/api/v1//campaign/message/platformAccount',
    // },
  },
};
