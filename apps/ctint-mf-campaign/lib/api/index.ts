// eslint-disable-next-line unused-imports/no-unused-imports
import { addLoggingToAxios } from '@cdss-modules/design-system/lib/logging';
import { apiConfig } from './config';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { CampaignSaveRequest } from '../../types/microfrontendsConfig';
import { useQuery } from '@tanstack/react-query';

export const axiosInstance = axios.create({
  timeout: 20000,
  headers: {
    'Content-Type': 'application/json',
    traceId: uuidv4(),
    tenant: 'ctint',
    sourceId: 'ctint-mf-campaign',
    previousId: 'ctint-bff-cdss',
  },
});

export interface FilterParams {
  [key: string]: any;
}
export const getPage = (
  basePath = '',
  pageNumber: number,
  pageSize: number,
  filterParams: FilterParams
) => {
  const queryParams = new URLSearchParams({
    page: pageNumber.toString(),
    page_size: pageSize.toString(),
    ...filterParams,
  });
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.campaign.page}?${queryParams}`
  );
};

export const saveOrUpdate = (
  requestData: CampaignSaveRequest,
  basePath = ''
) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.campaign.saveOrUpdate}`,
    requestData
  );
};

export const getDetail = (id: string, basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.campaign.detail}?id=${id}`
  );
};
// API functions
export const getContactGroupList = (basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.campaign.contactGroups}?spaceType=campaignContactGroup`
  );
};

export const getOptOutList = (basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.campaign.contactGroups}?spaceType=campaignOptOutGroup`
  );
};

export const downloadContactGroupTemplate = (
  basePath = '',
  messageTemplateId = ''
) => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.campaign.contactGroupTemplate}/${messageTemplateId}`,
    { responseType: 'blob' }
  );
};

export const uploadContactGroup = (file: File, basePath = '') => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('spaceType', 'campaignContactGroup');

  return axiosInstance.post(
    `${basePath}${apiConfig.paths.campaign.upload}`,
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
  );
};

export const uploadOptOutGroup = (file: File, basePath = '') => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('spaceType', 'campaignOptOutGroup');

  return axiosInstance.post(
    `${basePath}${apiConfig.paths.campaign.upload}`,
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
  );
};

export const uploadTemplateImg = (basePath = '', file: File) => {
  const formData = new FormData();
  formData.append('file', file);
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.campaign.fileUpload}`,
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
  );
};

export const getMessageTemplateList = (basePath = '', account = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.campaign.messageTemplates}?platformAccount=${account}`
  );
};

export const getMessagePlatformAccount = (basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.campaign.messagePlatformAccount}`
  );
};

export const exportCampaignDetail = (basePath = '', campaignId = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.campaign.exportCampaignDetail}?campaignId=${campaignId}`,
    { responseType: 'blob' }
  );
};

export const getMessageTemplateDetail = (
  id: string | undefined,
  basePath = ''
) => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.campaign.messageTemplateDetail}/${id}`
  );
};

export const getJobResultList = (
  campaignId: string,
  page = 1,
  pageSize = 10,
  orderBy = 'createTime',
  order: 'asc' | 'desc' = 'desc',
  basePath = '',
  destination = ''
) => {
  const params = {
    campaignId,
    page,
    pageSize,
    orderBy,
    order,
    destination,
  };
  return axiosInstance.get(`${basePath}${apiConfig.paths.campaign.jobs}`, {
    params,
  });
};

axiosInstance.interceptors.request.use(
  (config) => {
    // TODO: should call the auth api and set the token when program initialize
    if (
      process.env.NODE_ENV === 'development' &&
      localStorage.getItem('cdss-auth-token') === null
    ) {
      localStorage.setItem('deviceId', uuidv4());
      localStorage.setItem(
        'gc-access-token',
        'ZoICpoNdyYLZpUEM2AqHGWSc3DGPiDmzrm-HaAAIdVjprS9frf76aFPHtKxjewfZ-3PDMQu6pUEDeksTiFLmWA'
      );
      localStorage.setItem(
        'cdss-auth-token',
        'EdOImMlG47o8McBVtqY2QKHtIoT7OPznIInDv7cOXG+0DnbPZAcG6Ra+604VzhdT+fX3I2pbhPKWfQSrGpd+hZ5OuZOcptbvJ01GpJkpzCbiiT6TpuU='
      );
    }
    const cdssAuthToken = localStorage.getItem('cdss-auth-token') || '';
    const gcAccessToken = localStorage.getItem('gc-access-token') || '';
    const deviceId = localStorage.getItem('deviceId') || '';
    config.headers['tenant'] = localStorage.getItem('tenant') || 'ctint';
    if (cdssAuthToken) {
      config.headers['cdss_authorization'] = 'Bearer ' + cdssAuthToken;
    }
    if (gcAccessToken) {
      config.headers['gc_authorization'] = 'Bearer ' + gcAccessToken;
    }
    if (deviceId) {
      config.headers['deviceId'] = deviceId;
    }

    if (config.url?.includes('/interaction/recordings/transcript/')) {
      config.headers['requester'] = 'agent1';
    }

    console.log('axiosInstance.interceptors.request', config);
    return config;
  },
  (error) => {
    Promise.reject(error);
  }
);

addLoggingToAxios(axiosInstance);

export default axiosInstance;
