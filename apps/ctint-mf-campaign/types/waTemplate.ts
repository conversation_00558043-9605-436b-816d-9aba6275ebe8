// User information interface
export interface User {
  user_id: string;
  user_name: string;
}

// Quality score interface
export interface QualityScore {
  reasons: string[] | null;
  score: string;
}

// Button interfaces
export interface BaseButton {
  text: string;
  type: 'FLOW' | 'URL' | 'QUICK_REPLY' | 'PHONE_NUMBER' | 'OTP' | 'COPY_CODE';
}

export interface FlowButton extends BaseButton {
  type: 'FLOW';
  flow_action: string;
  flow_id: number;
  navigate_screen: string;
}

export interface URLButton extends BaseButton {
  type: 'URL';
  url: string;
  example?: string[];
}

export interface PhoneButton extends BaseButton {
  type: 'PHONE_NUMBER';
  phone_number: string;
}

export interface QuickReplyButton extends BaseButton {
  type: 'QUICK_REPLY';
}

export interface OTPButton extends BaseButton {
  type: 'OTP';
  otp_type: string;
}

export interface CopyCodeButton extends BaseButton {
  type: 'COPY_CODE';
}

type Button =
  | FlowButton
  | URLButton
  | PhoneButton
  | QuickReplyButton
  | OTPButton
  | CopyCodeButton;

// Component interfaces
export interface BaseComponent {
  type: 'HEADER' | 'BODY' | 'FOOTER' | 'BUTTONS';
}

export interface HeaderComponent extends BaseComponent {
  type: 'HEADER';
  format?: 'TEXT' | 'IMAGE';
  text?: string;
  example?: {
    header_text?: string[];
    header_handle?: string[];
  };
}

export interface BodyComponent extends BaseComponent {
  type: 'BODY';
  text?: string;
  example?: {
    body_text?: string[][];
  };
  add_security_recommendation?: boolean;
}

export interface FooterComponent extends BaseComponent {
  type: 'FOOTER';
  text?: string;
  code_expiration_minutes?: number;
}

export interface ButtonsComponent extends BaseComponent {
  type: 'BUTTONS';
  buttons: Button[];
}

type Component =
  | HeaderComponent
  | BodyComponent
  | FooterComponent
  | ButtonsComponent;

// Template interface
export interface WABATemplate {
  id: string;
  category: 'MARKETING' | 'AUTHENTICATION' | 'UTILITY';
  components: Component[];
  created_at: string;
  created_by: User;
  external_id: string | null;
  language: string;
  modified_at: string;
  modified_by: User;
  name: string;
  namespace: string;
  partner_id: string;
  quality_score: QualityScore | null;
  rejected_reason: string | null;
  status: 'approved' | 'rejected' | 'created';
  updated_external: boolean;
  waba_account_id: string;
}

// Response interface
export interface WABATemplatesResponse {
  count: number;
  filters: Record<string, unknown>;
  limit: number;
  offset: number;
  sort: string[];
  total: number;
  waba_templates: WABATemplate[];
}
