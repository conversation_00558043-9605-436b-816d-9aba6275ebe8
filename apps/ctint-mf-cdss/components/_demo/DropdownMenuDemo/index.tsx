'use client';

import Link from 'next/link';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@cdss-modules/design-system/components/_ui/DropdownMenu';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { useObserveElementWidth } from '@cdss-modules/design-system/lib/hooks/useObserveElementWidth';

const DropdownMenuDemo = () => {
  const { width, ref } = useObserveElementWidth<HTMLDivElement>();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger>
        <div
          ref={ref}
          className="flex flex-row items-center justify-center gap-2"
        >
          <div className="w-10 h-10 rounded-full bg-green-400"></div>
          <div><PERSON>, <PERSON></div>
          <Icon
            name="dropdown-arrow"
            size={8}
          />
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        // align = "start" | "center" | "end", default center
        style={{ width: width }}
      >
        <DropdownMenuGroup>
          <DropdownMenuItem>
            <button
              className="w-full text-left"
              onClick={() => console.log('option')}
            >
              Option
            </button>
          </DropdownMenuItem>
          <DropdownMenuItem>
            <Link
              href="/"
              className="w-full"
            >
              Home
            </Link>
          </DropdownMenuItem>
          <DropdownMenuSub>
            <DropdownMenuSubTrigger>Apply Template</DropdownMenuSubTrigger>
            <DropdownMenuPortal>
              <DropdownMenuSubContent>
                <DropdownMenuItem>
                  <button
                    className="w-full text-left"
                    onClick={() => console.log('Template 1')}
                  >
                    Template 1
                  </button>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <button
                    className="w-full text-left"
                    onClick={() => console.log('Template 2')}
                  >
                    Template 2
                  </button>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <button
                    className="w-full text-left"
                    onClick={() => console.log('Template 3')}
                  >
                    Template 3
                  </button>
                </DropdownMenuItem>
              </DropdownMenuSubContent>
            </DropdownMenuPortal>
          </DropdownMenuSub>
          <DropdownMenuSeparator />
          <DropdownMenuItem>
            <button
              className="w-full text-left"
              onClick={() => console.log('logout')}
            >
              Logout
            </button>
          </DropdownMenuItem>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default DropdownMenuDemo;
