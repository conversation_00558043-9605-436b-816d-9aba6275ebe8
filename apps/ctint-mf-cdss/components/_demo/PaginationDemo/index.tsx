'use client';

import { useState } from 'react';
import Pagination from '@cdss-modules/design-system/components/_ui/Pagination';

const PaginationDemo = () => {
  const [page, setPage] = useState<number>(2);

  const handlePage = (e: any) => {
    setPage(e);
  };

  return (
    <div className="w-1/2">
      <Pagination
        total={74}
        totalCount={74}
        perPage={10}
        handlePerPageSetter={() => null}
        current={page}
        onChange={handlePage}
      />
    </div>
  );
};

export default PaginationDemo;
