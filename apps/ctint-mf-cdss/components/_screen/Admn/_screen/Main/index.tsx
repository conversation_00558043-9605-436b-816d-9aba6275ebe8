/* eslint-disable @nx/enforce-module-boundaries */

import {
  CDSSAdminProvider,
  Panel,
  Tabs,
  TabsContent,
} from '@cdss-modules/design-system';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import AdminUserList from '../../ui/AdminUserList';
import AdminUserGroupList from '../../ui/AdminUserGroupList';
import AdminQueueGroupList from '../../ui/AdminQueueGroupList';
import AdminUserRoleList from '../../ui/AdminUserRoleList';
import AdminUserPermissionList from '../../ui/AdminUserPermissionList';
import { Filter } from 'lucide-react';
import { Fragment, useState } from 'react';
import { cn } from '@cdss-modules/design-system/lib/utils';
import AdminFilter from '../../ui/AdminFilter';
import { useRole } from '@cdss-modules/design-system/context/RoleContext';
import { Condition } from '@cdss-modules/design-system/components/_ui/FilterComponent';
import { microfrontends } from '../../../../../types/microfrontendsConfig';
import { ColumnFilter } from '@tanstack/react-table';
import { useTranslation } from 'react-i18next';

export const AdminUserBody = () => {
  const [userFilterOpen, setUserFilterOpen] = useState(false);
  const [userGroupFilterOpen, setUserGroupFilterOpen] = useState(false);
  const [queueGroupFilterOpen, setQueueGroupFilterOpen] = useState(false);
  const [tabValue, setTabValue] = useState('users');
  const { globalConfig } = useRole();
  const microfrontendsConfig: microfrontends = globalConfig?.microfrontends;
  const userTabsCols =
    microfrontendsConfig?.['ctint-mf-cdss']?.['user-tab-names'] || [];
  const userGroupTabsCols =
    microfrontendsConfig?.['ctint-mf-cdss']?.['user-group-tab-names'] || [];
  const queueGroupTabsCols =
    microfrontendsConfig?.['ctint-mf-cdss']?.['queue-group-tab-names'] || [];

  const [customFilterValue, setCustomFilterValue] = useState<ColumnFilter[]>(
    []
  );
  const { t } = useTranslation();
  const initTableColsData =
    userTabsCols?.reduce(
      (acc, filter) => {
        acc[filter.value] = {
          ...filter,
          checked: false,
        }; // Initialize all filter values to empty
        return acc;
      },
      {} as Record<string, Condition>
    ) || [];

  const initUserGroupTableColsData =
    userGroupTabsCols?.reduce(
      (acc, filter) => {
        acc[filter.value] = {
          ...filter,
          checked: false,
        };
        return acc;
      },
      {} as Record<string, Condition>
    ) || [];
  const initQueueGroupTableColsData =
    queueGroupTabsCols?.reduce(
      (acc, filter) => {
        acc[filter.value] = {
          ...filter,
          checked: false,
        };
        return acc;
      },
      {} as Record<string, Condition>
    ) || [];

  const [tableCol, setTableCol] = useState(initTableColsData);
  const onChangeTabFunc = (value: string) => {
    setTabValue(value);
    switch (value) {
      case 'users':
        setTableCol(initTableColsData);
        break;
      case 'user-groups':
        setTableCol(initUserGroupTableColsData);
        console.log('user-groups');
        break;
      case 'queue-groups':
        setTableCol(initQueueGroupTableColsData);
        break;
    }
  };
  const filterConfigs = [
    {
      isOpen: userFilterOpen,
      cols: initTableColsData,
      type: 'users',
    },
    // {
    //   isOpen: userGroupFilterOpen,
    //   cols: initUserGroupTableColsData,
    //   type: 'user-groups',
    // },
    // {
    //   isOpen: queueGroupFilterOpen,
    //   cols: initQueueGroupTableColsData,
    //   type: 'queue-groups',
    // },
  ];

  return (
    <div className="flex flex-col h-full gap-y-4 overflow-auto">
      <Panel containerClassName="h-full">
        <Tabs
          onChangeTabFunc={onChangeTabFunc}
          defaultTab={'users'}
          triggers={[
            {
              value: 'users',
              label: t('ctint-mf-user-admin.tab.user'),
            },
            {
              value: 'user-groups',
              label: t('ctint-mf-user-admin.tab.userGroup'),
            },
            {
              value: 'queue-groups',
              label: t('ctint-mf-user-admin.tab.queueGroup'),
            },
            // {
            //   value: 'user-groups',
            //   label: 'User Groups',
            // },
            // {
            //   value: 'roles',
            //   label: 'Roles',
            // },
            // {
            //   value: 'permissions',
            //   label: 'Permissions',
            // },
          ]}
          triggerClassName="py-2 px-2 text-body"
          rightPanel={
            <div className="flex items-center pr-2">
              <button
                className={cn(
                  'hover:text-primary',
                  userFilterOpen && 'text-primary'
                )}
                type="button"
                onClick={() => {
                  switch (tabValue) {
                    case 'users':
                      setUserFilterOpen(!userFilterOpen);
                      break;
                    case 'user-groups':
                      setUserGroupFilterOpen(!userGroupFilterOpen);
                      break;
                    case 'queue-groups':
                      setQueueGroupFilterOpen(!queueGroupFilterOpen);
                      break;
                  }
                }}
              >
                <Filter />
              </button>
            </div>
          }
        >
          {filterConfigs.map((config, index) => (
            <div
              className={cn(
                config.isOpen && config.type == tabValue ? 'block' : 'hidden'
              )}
              key={index}
            >
              <AdminFilter
                key={index}
                cols={config.cols}
                customApplyFilter={(data: Condition) => {
                  const result = Object.entries(data)
                    .filter(([_, item]) => item.checked === true && item.data)
                    .map(([key, item]) => ({
                      id: key,
                      value: item.data,
                    })) as ColumnFilter[];
                  setCustomFilterValue(result);
                }}
                customClearFilter={() => {
                  setCustomFilterValue([]);
                }}
              />
            </div>
          ))}
          <TabsContent
            value={'users'}
            className="p-0 h-0 flex-1 flex flex-col"
          >
            <AdminUserList
              cols={initTableColsData}
              customFilterValue={customFilterValue}
              setFilterOpen={setUserFilterOpen}
            />
          </TabsContent>
          <TabsContent
            value={'user-groups'}
            className="p-0 h-0 flex-1 flex flex-col"
          >
            <AdminUserGroupList
              cols={initUserGroupTableColsData}
              customFilterValue={customFilterValue}
              setFilterOpen={setUserGroupFilterOpen}
            />
          </TabsContent>
          <TabsContent
            value={'queue-groups'}
            className="p-0 h-0 flex-1 flex flex-col"
          >
            <AdminQueueGroupList
              cols={initQueueGroupTableColsData}
              customFilterValue={customFilterValue}
              setFilterOpen={setQueueGroupFilterOpen}
            />
          </TabsContent>
          <TabsContent
            value={'roles'}
            className="p-0 h-0 flex-1 flex flex-col"
          >
            <AdminUserRoleList
              inDetail={undefined}
              isEditing={false}
            />
          </TabsContent>
          <TabsContent
            value={'permissions'}
            className="p-0 h-0 flex-1 flex flex-col"
          >
            <AdminUserPermissionList />
          </TabsContent>
        </Tabs>
      </Panel>
    </div>
  );
};

// Create a client
const queryClient = new QueryClient();

const AdminUser = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <CDSSAdminProvider>
        <AdminUserBody />
      </CDSSAdminProvider>
    </QueryClientProvider>
  );
};

export default AdminUser;
