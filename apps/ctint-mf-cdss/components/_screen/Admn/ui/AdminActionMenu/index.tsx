import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@cdss-modules/design-system/components/_ui/DropdownMenu';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';

export type TAdminActionMenuProps = {
  data?: any;
};

export const AdminActionMenu = ({ data }: TAdminActionMenuProps) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="flex items-center gap-x-2 hover:text-primary-600">
          <Icon
            name="verticalDots"
            size={23}
          />
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <>
          <DropdownMenuGroup>
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                console.log(data);
              }}
              className="text-remark"
            >
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem
              className="text-remark"
              onClick={(e) => {
                e.stopPropagation();
              }}
            >
              Delete
            </DropdownMenuItem>
          </DropdownMenuGroup>
        </>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default AdminActionMenu;
