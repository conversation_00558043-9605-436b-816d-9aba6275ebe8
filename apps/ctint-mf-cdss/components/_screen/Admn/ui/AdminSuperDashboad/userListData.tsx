export const mockMembers = [
  {
    id: '1',
    isActive: true,
    name: '<PERSON>',
    avatar: '/avatars/ken.jpg',
    position: 0,
  },
  {
    id: '2',
    isActive: true,
    name: '<PERSON>',
    avatar: '/avatars/sarah.jpg',
    position: 1,
  },
  {
    id: '3',
    isActive: false,
    name: '<PERSON>',
    avatar: '/avatars/alex.jpg',
    position: 2,
  },
  {
    id: '4',
    isActive: true,
    name: '<PERSON>',
    avatar: '/avatars/emma.jpg',
    position: 3,
  },
  {
    id: '5',
    isActive: true,
    name: '<PERSON>',
    avatar: '/avatars/michael.jpg',
    position: 4,
  },
  {
    id: '6',
    isActive: true,
    name: '<PERSON>',
    avatar: '/avatars/lisa.jpg',
    position: 5,
  },
  {
    id: '7',
    isActive: false,
    name: '<PERSON>',
    avatar: '/avatars/james.jpg',
    position: 6,
  },
  {
    id: '8',
    isActive: true,
    name: '<PERSON>',
    avatar: '/avatars/nina.jpg',
    position: 7,
  },
  {
    id: '9',
    isActive: true,
    name: '<PERSON>',
    avatar: '/avatars/david.jpg',
    position: 8,
  },
  {
    id: '10',
    isActive: true,
    name: 'Sophie <PERSON>',
    avatar: '/avatars/sophie.jpg',
    position: 9,
  },
  {
    id: '11',
    isActive: false,
    name: '<PERSON>',
    avatar: '/avatars/ryan.jpg',
    position: 10,
  },
  {
    id: '12',
    isActive: true,
    name: 'Maria Garcia',
    avatar: '/avatars/maria.jpg',
    position: 11,
  },
  {
    id: '13',
    isActive: true,
    name: 'Thomas Lee',
    avatar: '/avatars/thomas.jpg',
    position: 12,
  },
  {
    id: '14',
    isActive: true,
    name: 'Anna Kowalski',
    avatar: '/avatars/anna.jpg',
    position: 13,
  },
  {
    id: '15',
    isActive: false,
    name: 'Chris Murphy',
    avatar: '/avatars/chris.jpg',
    position: 14,
  },
  {
    id: '16',
    isActive: true,
    name: 'Laura Schmidt',
    avatar: '/avatars/laura.jpg',
    position: 15,
  },
  {
    id: '17',
    isActive: true,
    name: 'Kevin Brown',
    avatar: '/avatars/kevin.jpg',
    position: 16,
  },
  {
    id: '18',
    isActive: true,
    name: 'Yuki Tanaka',
    avatar: '/avatars/yuki.jpg',
    position: 17,
  },
  {
    id: '19',
    isActive: false,
    name: 'Oliver Smith',
    avatar: '/avatars/oliver.jpg',
    position: 18,
  },
  {
    id: '20',
    isActive: true,
    name: 'Isabella Silva',
    avatar: '/avatars/isabella.jpg',
    position: 19,
  },
  {
    id: '21',
    isActive: true,
    name: 'Daniel Wong',
    avatar: '/avatars/daniel.jpg',
    position: 20,
  },
  {
    id: '22',
    isActive: true,
    name: 'Elena Popov',
    avatar: '/avatars/elena.jpg',
    position: 21,
  },
  {
    id: '23',
    isActive: false,
    name: 'Marco Rossi',
    avatar: '/avatars/marco.jpg',
    position: 22,
  },
  {
    id: '24',
    isActive: true,
    name: 'Aisha Patel',
    avatar: '/avatars/aisha.jpg',
    position: 23,
  },
  {
    id: '25',
    isActive: true,
    name: 'Lucas Santos',
    avatar: '/avatars/lucas.jpg',
    position: 24,
  },
  {
    id: '26',
    isActive: true,
    name: 'Sophia Chen',
    avatar: '/avatars/sophia.jpg',
    position: 25,
  },
  {
    id: '27',
    isActive: false,
    name: 'William Park',
    avatar: '/avatars/william.jpg',
    position: 26,
  },
  {
    id: '28',
    isActive: true,
    name: 'Mia Johnson',
    avatar: '/avatars/mia.jpg',
    position: 27,
  },
  {
    id: '29',
    isActive: true,
    name: 'Hassan Ahmed',
    avatar: '/avatars/hassan.jpg',
    position: 28,
  },
  {
    id: '30',
    isActive: true,
    name: 'Natalie Wu',
    avatar: '/avatars/natalie.jpg',
    position: 29,
  },
];
