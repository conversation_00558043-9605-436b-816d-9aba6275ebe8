/* eslint-disable react/no-unescaped-entities */
'use client';
import React from 'react';
import {
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle,
} from '@cdss-modules/design-system/components/_ui/Resizable';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import Panel from '@cdss-modules/design-system/components/_ui/Panel';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { toast, useRole, useRouteHandler } from '@cdss-modules/design-system';
import Breadcrumb from '@cdss-modules/design-system/components/_ui/Breadcrumb';
import {
  QueryClient,
  QueryClientProvider,
  useMutation,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import dayjs from 'dayjs';
import { GLOBAL_DATETIME_FORMAT } from '@cdss-modules/design-system/lib/constants';
import { secondsToTimeDisplay } from '@cdss-modules/design-system/lib/utils';
import {
  fireGetSingleCompletedCallPureconnect,
  updateCompletedCallPureconnect,
} from '../../../../../../lib/api';

import {
  Tabs,
  TabsContent,
} from '@cdss-modules/design-system/components/_ui/Tabs';
import IconStartTime from '@cdss-modules/design-system/components/_ui/Icon/IconStartTime';
import IconEndTime from '@cdss-modules/design-system/components/_ui/Icon/IconEndTime';
import IconInteractionId from '@cdss-modules/design-system/components/_ui/Icon/IconInteractionId';
import IconDirection from '@cdss-modules/design-system/components/_ui/Icon/IconDirection';
import IconDuration from '@cdss-modules/design-system/components/_ui/Icon/IconDuration';
import IconQueue from '@cdss-modules/design-system/components/_ui/Icon/IconQueue';
import IconUsers from '@cdss-modules/design-system/components/_ui/Icon/IconUsers';
import { QmVisitPermission } from '@cdss-modules/design-system/@types/QmVisitPermission';
import { usePermission } from '@cdss-modules/design-system/context/PremissionContext';
import IconPhone from '@cdss-modules/design-system/components/_ui/Icon/IconPhone';
import CallDetailEditForm from '../DetailEditForm';

export const TableDemoDetailBody = () => {
  const { toPath, searchParams, basePath } = useRouteHandler();
  const { t } = useTranslation();
  const { globalConfig } = useRole();
  const queryClient = useQueryClient();
  // const isUAT = apiConfig.isUAT;
  const id = searchParams?.get('id') || '';
  const tab = searchParams?.get('tab') || 'info';

  const { permissions } = usePermission();

  // call the conversation detail info API, method: GET
  const { data, isLoading, error } = useQuery({
    queryKey: ['pc-calls-detail', id],
    queryFn: async () =>
      await fireGetSingleCompletedCallPureconnect(id, basePath).then(
        (res) => res.data
      ),
    enabled: !!id,
  });

  const details = data?.data?.interaction;

  const qmVisitPermission = new QmVisitPermission(globalConfig, permissions);

  const callDirection = details?.callDirection?.toLowerCase() ?? 'inbound';

  // call the patch API
  const updateCall = useMutation({
    mutationFn: async (data: any) =>
      await updateCompletedCallPureconnect(id, data, basePath),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['pc-calls-detail', id] });
      toast({
        variant: 'success',
        title: 'Success',
        description: `Call detail updated successfully for ${id}`,
      });
    },
    onError: (error) => {
      console.error('updateCall error', error);
      toast({
        variant: 'error',
        title: 'Error',
        description: `Failed to update the call detail: ${error}`,
      });
    },
  });

  const processSubmit = (data: any) => {
    console.log('patchCompletedCall data', data);
    updateCall.mutate(data);
  };

  return (
    <div className="relative flex flex-col h-full">
      <div className="flex items-center gap-2 mb-4">
        <Button
          asSquare
          variant="back"
          onClick={() => toPath(`/call-patch`)}
          beforeIcon={<Icon name="back" />}
        />
        <Breadcrumb
          items={[
            {
              label: t('ctint-mf-interaction.filter.search'),
              link: '/',
            },
            {
              label: error || !id ? 'Error' : `${id}`,
              link: '',
            },
          ]}
        />
      </div>
      {error || !id ? (
        <Panel containerClassName="h-full">
          <div className="p-4">
            <h2 className="mb-2 font-bold text-t6">{`Error loading data: ${
              id ? error?.message : 'No valid ID.'
            }`}</h2>
          </div>
        </Panel>
      ) : (
        <>
          {isLoading ? (
            <Panel
              loading={isLoading}
              containerClassName="h-full"
            />
          ) : (
            <>
              <ResizablePanelGroup
                autoSaveId="playback-detail-panel"
                direction="horizontal"
                className="flex flex-1 w-full h-0 overflow-auto gap-x-3"
              >
                <ResizablePanel minSize={40}>
                  <Panel
                    loading={isLoading}
                    containerClassName="h-full"
                  >
                    <Tabs
                      defaultTab={tab}
                      triggers={[
                        {
                          value: 'info',
                          label: 'Info',
                        },
                      ]}
                      triggerClassName="py-1 px-2 text-body"
                    >
                      <TabsContent
                        value={'info'}
                        className="p-0 overflow-y-auto"
                      >
                        {/* Interaction Metrics */}
                        <section>
                          <div className="grid grid-cols-2 gap-6 p-4">
                            {/* Conversation ID */}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconInteractionId
                                  alt="Interaction Id"
                                  size="24"
                                  color="black"
                                />
                              </div>
                              <section className="grid grid-flow-col grid-rows-2 gap-2">
                                <div className="col-span-11 row-span-1">
                                  <strong>
                                    {t('ctint-mf-interaction.columns.id')}:
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>{details?.callId || 'N/A'}</p>
                                </div>
                              </section>
                            </section>

                            {/* Recording ID */}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconInteractionId
                                  alt="Recording Id"
                                  size="24"
                                  color="black"
                                />
                              </div>
                              <section className="grid grid-flow-col grid-rows-2 gap-2">
                                <div className="col-span-11 row-span-1">
                                  <strong>
                                    {t('ctint-mf-cdss.callPatch.recordingId')}:
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>{details?.recordingId || 'N/A'}</p>
                                </div>
                              </section>
                            </section>

                            {/* Start Time */}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconStartTime
                                  alt="Start Time"
                                  size="24"
                                  color="black"
                                />
                              </div>
                              <section className="grid grid-flow-col grid-rows-2 gap-2">
                                <div className="col-span-11 row-span-1">
                                  <strong>
                                    {t(
                                      'ctint-mf-interaction.columns.conversationStart'
                                    )}
                                    :
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>
                                    {details?.connectedDateTimeGMT
                                      ? dayjs(
                                          details?.connectedDateTimeGMT
                                        ).format(GLOBAL_DATETIME_FORMAT)
                                      : 'N/A'}{' '}
                                  </p>
                                </div>
                              </section>
                            </section>

                            {/* End Time */}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconEndTime
                                  alt="End Time"
                                  size="24"
                                  color="black"
                                />
                              </div>
                              <section className="grid grid-flow-col grid-rows-2 gap-2">
                                <div className="col-span-11 row-span-1">
                                  <strong>
                                    {t(
                                      'ctint-mf-interaction.columns.conversationEnd'
                                    )}
                                    :
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>
                                    {details?.terminatedDateTimeGMT
                                      ? dayjs(
                                          details?.terminatedDateTimeGMT
                                        ).format(GLOBAL_DATETIME_FORMAT)
                                      : 'N/A'}{' '}
                                  </p>
                                </div>
                              </section>
                            </section>

                            {/* Remote number */}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconPhone
                                  alt="RemoteNumber"
                                  size="24"
                                />
                              </div>
                              <section className="grid grid-flow-col grid-rows-2 gap-2">
                                <div className="col-span-11 row-span-1">
                                  <strong>
                                    {t('ctint-mf-cdss.callPatch.remoteNumber')}:
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>{details?.remoteNumber || 'N/A'}</p>
                                </div>
                              </section>
                            </section>

                            {/* Local number */}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconPhone
                                  alt="LocalNumber"
                                  size="24"
                                />
                              </div>
                              <section className="grid grid-flow-col grid-rows-2 gap-2">
                                <div className="col-span-11 row-span-1">
                                  <strong>
                                    {t('ctint-mf-cdss.callPatch.localNumber')}:
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>{details?.localNumber || 'N/A'}</p>
                                </div>
                              </section>
                            </section>

                            {/* Initiator */}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconDirection
                                  alt="Direction"
                                  size="24"
                                  color="black"
                                />
                              </div>
                              <section className="grid grid-flow-col grid-rows-2 gap-2">
                                <div className="col-span-11 row-span-1">
                                  <strong>
                                    {t(
                                      'ctint-mf-interaction.columns.direction'
                                    )}
                                    :
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>{details?.callDirection || 'N/A'}</p>
                                </div>
                              </section>
                            </section>

                            {/* Duration */}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconDuration
                                  alt="Duration"
                                  size="24"
                                  color="black"
                                />
                              </div>
                              <section className="grid grid-flow-col grid-rows-2 gap-2">
                                <div className="col-span-11 row-span-1">
                                  <strong>
                                    {t(
                                      'ctint-mf-interaction.columns.conversationDuration'
                                    )}
                                    :
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>
                                    {secondsToTimeDisplay(details?.duration, [
                                      ' hours',
                                      ' minutes',
                                      ' seconds',
                                    ]) || 'N/A'}
                                  </p>
                                </div>
                              </section>
                            </section>

                            {/* Branch */}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconQueue
                                  alt="Queue"
                                  size="24"
                                  color="black"
                                />
                              </div>
                              <section className="grid grid-flow-col grid-rows-2 gap-2">
                                <div className="col-span-11 row-span-1">
                                  <strong>
                                    {t('ctint-mf-cdss.callPatch.branchCode')}:
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>{details?.branchCode || 'N/A'}</p>
                                </div>
                              </section>
                            </section>

                            {/* User name*/}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconUsers
                                  alt="Users"
                                  size="24"
                                  color="black"
                                />
                              </div>
                              <section className="grid grid-flow-col grid-rows-2 gap-2">
                                <div className="col-span-11 row-span-1">
                                  <strong>
                                    {t('ctint-mf-cdss.callPatch.userName')}:
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>{details?.localName || 'N/A'}</p>
                                </div>
                              </section>
                            </section>

                            {/* User id*/}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconUsers
                                  alt="Users"
                                  size="24"
                                  color="black"
                                />
                              </div>
                              <section className="grid grid-flow-col grid-rows-2 gap-2">
                                <div className="col-span-11 row-span-1">
                                  <strong>
                                    {t('ctint-mf-cdss.callPatch.userId')}:
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>{details?.localUserId || 'N/A'}</p>
                                </div>
                              </section>
                            </section>
                          </div>
                        </section>
                      </TabsContent>
                    </Tabs>
                  </Panel>
                </ResizablePanel>
                <ResizableHandle />
                <ResizablePanel
                  minSize={30}
                  className="h-full"
                >
                  <Panel containerClassName="h-full">
                    <Tabs
                      defaultTab={tab}
                      triggers={[
                        {
                          value: 'info',
                          label: 'Edit',
                        },
                      ]}
                      triggerClassName="py-1 px-2 text-body"
                    >
                      <TabsContent
                        value={'info'}
                        className="p-0 overflow-y-auto"
                      >
                        {/* Info Edit Panel */}
                        <CallDetailEditForm
                          details={details}
                          processSubmit={processSubmit}
                          isLoading={updateCall.isPending}
                        />
                      </TabsContent>
                    </Tabs>
                  </Panel>
                </ResizablePanel>
              </ResizablePanelGroup>
            </>
          )}
        </>
      )}
    </div>
  );
};
// Create a client
const queryClient = new QueryClient();

export const CallDetailComponent = () => (
  <QueryClientProvider client={queryClient}>
    <TableDemoDetailBody />
  </QueryClientProvider>
);

export default CallDetailComponent;
