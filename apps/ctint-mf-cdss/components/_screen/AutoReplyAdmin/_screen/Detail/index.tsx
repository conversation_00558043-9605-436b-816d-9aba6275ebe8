import React, { useEffect, useState } from 'react';
import { useAutoReplyNavigationStore } from '../../_store/autoReplyNavigationStore';
import {
  useAutoReplyFormStore,
  TAutoReplyFormData,
  TChannelType,
  IWhatsAppConfig,
  TChannelConfig,
} from '../../_store/autoReplyFormStore';
import { Button, LoadingBlock, useRole } from '@cdss-modules/design-system';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

import BasicInfo from '../../_ui/BasicInfo';
import { toast } from '@cdss-modules/design-system';

// Added imports for RoutingRuleEditor
import RoutingRuleEditor, {
  IRoutingRule,
  TSubjectType,
} from '../../../../_ui/RoutingRuleEditor';
import {
  ITemplateResponse,
  TQueueOption,
} from '../../../EmailAdmin/_screen/MailboxRoutingRules/Detail';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import {
  fireGetCannedMessage,
  fireGetAutoReplyRuleDetail,
  fireCreateAutoReplyRule,
  fireUpdateAutoReplyRule,
} from '@cdss/lib/api';
import { basePath } from '@cdss/lib/appConfig';

// Define the type of auto reply data returned from the API
type AutoReplyApiData = {
  id: string;
  name: string;
  description: string;
  isActive: 0 | 1; // 0 for Inactive, 1 for Active
  channelType: string; // e.g., "whatsapp"
  address: string; // Phone number or email address
  rules: IRoutingRule[]; // Assuming IRoutingRule is correctly defined and imported
  createTime: string;
  updateTime: string;
  createBy: string;
  updateBy: string;
} | null;

// API function to fetch item by ID using real API
const fetchItemById = async (id: string | null): Promise<AutoReplyApiData> => {
  if (!id) return null;

  try {
    console.log(`Fetching auto reply rule with ID: ${id}`);
    const response = await fireGetAutoReplyRuleDetail(id, basePath);

    if (response.data?.isSuccess && response.data?.data) {
      return response.data.data;
    } else {
      console.error('Failed to fetch auto reply rule:', response.data?.error);
      return null;
    }
  } catch (error) {
    console.error('Error fetching auto reply rule:', error);
    throw error;
  }
};

const DetailComponent = () => {
  const { selectedItemId, backToList, mode, editDetail, viewDetail, isCreate } =
    useAutoReplyNavigationStore();

  const {
    formData,
    validationErrors,
    isDirty,
    isNew,
    loadExistingData,
    updateField,
    resetForm,
    initNewForm,
    updateRules,
    validateForm,
  } = useAutoReplyFormStore();

  const { t } = useTranslation();
  const { globalConfig } = useRole();
  const queryClient = useQueryClient();

  // Define the allowed channel types and statuses
  const allowedChannels: TChannelType[] = ['WhatsApp'];
  const allowedStatuses: TAutoReplyFormData['status'][] = [1, 0]; // Updated to 0 | 1

  // Use React Query for data fetching
  const { isLoading, error, data } = useQuery<AutoReplyApiData, Error>({
    queryKey: ['autoReply', selectedItemId],
    queryFn: () => fetchItemById(selectedItemId),
    enabled: !!selectedItemId, // Only run query if we have an ID
  });

  // Save mutation
  const saveMutation = useMutation({
    mutationFn: async (formData: TAutoReplyFormData) => {
      // Prepare the API payload
      const apiPayload = {
        channelType: formData.channelType.toLowerCase(), // Convert to lowercase for API
        description: formData.description,
        address: formData.address,
        name: formData.ruleName,
        priority: formData.priority, // Add priority field
        rules: formData.rules.map((rule) => ({
          conditions: rule.conditions.map((condition) => ({
            field: condition.field,
            operator: condition.operator,
            value:
              condition.field === 'time' && typeof condition.value === 'string'
                ? (() => {
                    console.log(
                      'Converting time values for API:',
                      condition.value
                    );
                    const parsedTimes = JSON.parse(condition.value);
                    const convertedTimes = parsedTimes.map(
                      (timeStr: string) => {
                        // Convert from HH:mm format back to API format (ISO-like)
                        if (timeStr.match(/^\d{2}:\d{2}$/)) {
                          // Convert HH:mm to HH:mm:ss.ssssssZ format expected by API
                          return `${timeStr}:47.430708Z`;
                        }
                        return timeStr;
                      }
                    );
                    console.log(
                      'Converted time values for API:',
                      convertedTimes
                    );
                    return convertedTimes;
                  })()
                : condition.value,
          })),
          logic: rule.logic,
          priority: rule.priority,
          return: {
            // Template IDs are now stored as comma-separated string in autoReplyId for both single and multi-language templates
            autoReplyId: rule.return.autoReplyId,
            ...(rule.return.language && { language: rule.return.language }),
          },
        })),
        isActive: formData.status,
        // Include ruleId for updates
        ...(!isNew && formData.ruleId && { ruleId: formData.ruleId }),
      };

      console.log('Saving auto reply rule with payload:', apiPayload);

      // Use the same API endpoint for both create and update
      const response = isNew
        ? await fireCreateAutoReplyRule(apiPayload, basePath)
        : await fireUpdateAutoReplyRule(apiPayload, basePath);

      // Check if the API response indicates failure
      if (!response.data?.isSuccess) {
        // Throw an error with the API error message for onError to handle
        throw new Error(
          response.data?.error || 'Failed to save auto reply rule'
        );
      }

      return response.data;
    },
    onSuccess: (data) => {
      console.log('Auto reply rule saved successfully');

      // Show success toast
      toast({
        variant: 'success',
        title: 'Success',
        description: `Auto reply rule ${isNew ? 'created' : 'updated'} successfully.`,
      });

      // Invalidate and refetch the auto reply list
      queryClient.invalidateQueries({
        queryKey: ['autoReplies'],
      });

      // If it was a new item, also invalidate the specific item query
      if (data.data?.id) {
        queryClient.invalidateQueries({
          queryKey: ['autoReply', data.data.id],
        });
      }

      // If updating existing item, invalidate its query
      if (selectedItemId) {
        queryClient.invalidateQueries({
          queryKey: ['autoReply', selectedItemId],
        });
      }

      // Navigate back to list
      backToList();
    },
    onError: (error: any) => {
      console.error('Failed to save auto reply rule:', error);

      // Extract error message from different possible sources
      let errorMessage = 'Failed to save auto reply rule. Please try again.';

      if (error?.response?.data?.error) {
        // API returned structured error in response.data.error (axios error)
        errorMessage = error.response.data.error;
      } else if (error?.response?.data?.message) {
        // API returned error in response.data.message (axios error)
        errorMessage = error.response.data.message;
      } else if (error?.message) {
        // Error thrown with message (our custom error or other errors)
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        // Error is a string
        errorMessage = error;
      }

      // Show error toast with the specific error message
      toast({
        variant: 'error',
        title: 'Error',
        description: errorMessage,
      });
    },
  });

  // Initialize the form
  useEffect(() => {
    if (data) {
      // Transform AutoReplyApiData to TAutoReplyFormData before loading
      // This transformation is now primarily handled within loadExistingData in the store
      console.log('Loading auto reply data:', data);
      loadExistingData(data); // Store will handle the API data structure mapping
    }
  }, [selectedItemId, data, loadExistingData]);

  // Mock data for RoutingRuleEditor
  const mockQueueOptions: TQueueOption[] = []; // Empty as per requirement

  // Fetch canned messages using the real API
  const { data: cannedMessagesData, isLoading: isLoadingCannedMessages } =
    useQuery<ITemplateResponse, Error>({
      queryKey: [
        'cannedMessages',
        (globalConfig as any)?.cannedMessage?.spaceId,
      ],
      queryFn: async () => {
        try {
          const response = await fireGetCannedMessage(
            'full',
            'whatsapp',
            basePath,
            globalConfig
          );
          return response.data;
        } catch (error) {
          console.error('Error fetching canned messages:', error);
          throw error;
        }
      },
      enabled: !!globalConfig, // Only run when globalConfig is available
    });

  // Use real canned messages data or fallback to empty structure
  const templateOptions: ITemplateResponse[] = cannedMessagesData
    ? [cannedMessagesData]
    : [
        {
          data: [],
          error: '',
          isSuccess: true,
        },
      ];

  // Define which subject fields are enabled for this instance of the editor
  const enabledFieldsForAutoReplyEditor: TSubjectType[] = [
    'time' as TSubjectType, // Useful for time-based auto-replies
    // Other fields like 'subject', 'sender' are less common for WhatsApp auto-reply but can be enabled if needed
  ];

  const handleSave = async () => {
    const isValid = validateForm();

    if (!isValid) {
      return;
    }

    // Use the mutation instead of direct saveForm call
    saveMutation.mutate(formData);
  };

  const handleCancel = () => {
    resetForm();
    backToList();
  };

  // Handle cancel from edit mode - adjusted to match EmailAdmin
  const handleCancelEdit = () => {
    resetForm();
    if (selectedItemId) {
      viewDetail(selectedItemId);
    } else {
      backToList();
    }
  };

  // Show loading state
  if (isLoading || isLoadingCannedMessages) {
    return (
      <div className="bg-white rounded-lg shadow p-10 flex justify-center items-center h-full">
        <LoadingBlock />
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="p-4">
        <div className="flex items-center mb-4 gap-x-2">
          <h1 className="text-2xl font-bold">Error</h1>
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <div className="text-red-500 mb-4">
            Failed to load auto reply data. Please try again.
          </div>
          <Button onClick={() => backToList()}>Return to List</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg">
      <form
        onSubmit={(e) => {
          e.preventDefault();
          handleSave();
        }}
      >
        <div className="flex items-center mb-4 justify-between px-4 pt-4 sticky top-0 z-10 bg-white py-2 ">
          <h1 className="text-base font-bold">
            {isNew ? 'Create New Auto Reply Rule' : 'Edit Auto Reply Rule'}
          </h1>
          <div className="flex justify-end gap-2">
            {/* Secondary button - Return or Cancel */}
            <Button
              size="s"
              variant="secondary"
              onClick={
                mode === 'edit' && selectedItemId
                  ? handleCancelEdit
                  : handleCancel
              }
              disabled={saveMutation.isPending}
            >
              {mode === 'edit' && selectedItemId ? 'Cancel' : 'Return'}
            </Button>

            {/* Primary button - Save or Edit */}
            <Button
              size="s"
              variant="primary"
              onClick={
                mode === 'view' && selectedItemId
                  ? () => editDetail(selectedItemId)
                  : handleSave
              }
              disabled={(mode !== 'view' && !isDirty) || saveMutation.isPending}
              // loading={saveMutation.isPending}
            >
              {mode === 'view' && selectedItemId ? 'Edit Rule' : 'Save Rule'}
            </Button>
          </div>
        </div>
        <BasicInfo
          formData={formData}
          updateField={updateField}
          mode={mode}
          validationErrors={validationErrors}
          allowedChannels={allowedChannels}
          allowedStatuses={allowedStatuses}
        />

        {/* RoutingRuleEditor Integration */}
        <div className="my-6">
          <h2 className="font-bold text-sm mb-3 text-primary-500  px-4">
            Rule Editor
          </h2>
          <RoutingRuleEditor
            rules={formData.rules || []} // Use rules from formData
            onUpdateRules={updateRules} // Pass the action from the store
            queueOptions={mockQueueOptions} // Pass mock queue options
            templateOptions={templateOptions} // Pass canned message template options
            isEditMode={mode === 'edit'} // Control edit mode
            validationErrors={validationErrors} // Pass validation errors (consider structure)
            enabledSubjectFields={enabledFieldsForAutoReplyEditor} // Pass enabled fields
            showQueueInput={false}
            useMultiLanguageTemplates={true} // Enable multi-language template support for auto-reply
            allowedLogicTypes={['OR']} // Restrict to only OR logic for auto reply
            isCreate={isCreate}
          />
        </div>

        {/* <div className="mb-6 flex flex-col gap-4 w-full">
          <h2 className="font-bold text-sm text-primary-500">Canned Message</h2>

          <CannedMessagePicker mode={mode} />
        </div> */}
      </form>
    </div>
  );
};

export default DetailComponent;
