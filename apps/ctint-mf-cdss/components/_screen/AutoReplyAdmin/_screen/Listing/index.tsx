import React, { use<PERSON>allback, useEffect, useState, useMemo } from 'react';
import { useAutoReplyNavigationStore } from '../../_store/autoReplyNavigationStore';
import { Button } from '@cdss-modules/design-system';
import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';
import { SortingButton } from '@cdss-modules/design-system';
import { ColumnDef, Table as TableType } from '@tanstack/react-table';
import Pagination from '@cdss-modules/design-system/components/_ui/Pagination';
import { toast } from '@cdss-modules/design-system';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { EllipsisVertical } from 'lucide-react';
import PopoverMenu from '@cdss-modules/design-system/components/_ui/PopoverMenu';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import StatusBadge from '@cdss-modules/design-system/components/_ui/StatusBadge';
import {
  fireGetAutoReplyRuleList,
  fireDeleteAutoReplyRule,
  fireGetPlatformAccounts,
} from '@cdss/lib/api';
import { basePath } from '@cdss/lib/appConfig';

// Types
type TAutoReply = {
  ruleId: string;
  address: string;
  updateTime: string;
  isActive: number;
  channelType: string;
  name: string;
  direction: string;
  description: string;
  priority: number;
};

// API function to fetch auto reply rules
const fetchAutoReplies = async (
  page: number,
  pageSize: number,
  dataType = 'whatsapp',
  filters?: {
    ruleName?: string;
    isActive?: boolean;
    address?: string;
  },
  sorting?: {
    order?: 'ASC' | 'DESC';
    orderBy?: 'address' | 'name' | 'updateTime' | 'isActive' | 'priority';
  }
) => {
  try {
    // Build URLSearchParams with all parameters
    const params = new URLSearchParams();
    params.append('page', page.toString());
    params.append('pageSize', pageSize.toString());
    params.append('channelType', dataType);

    // Add filters if provided
    if (filters) {
      if (filters.ruleName) {
        params.append('name', filters.ruleName);
      }
      if (filters.isActive !== undefined) {
        params.append('isActive', filters.isActive ? '1' : '0');
      }
      if (filters.address) {
        params.append('address', filters.address);
      }
    }

    // Add sorting parameters if provided
    if (sorting) {
      if (sorting.order) {
        params.append('order', sorting.order);
      }
      if (sorting.orderBy) {
        params.append('orderBy', sorting.orderBy);
      }
    }

    const response = await fireGetAutoReplyRuleList(basePath, params);

    // Use the API response data directly
    const apiData = response.data?.data;

    return {
      data: {
        autoReplies: apiData?.list || [],
        total: apiData?.total || 0,
      },
    };
  } catch (error) {
    console.error('Error fetching auto reply rules:', error);
    throw error;
  }
};

const ListingComponent = () => {
  const { viewDetail, editDetail, createNew } = useAutoReplyNavigationStore();
  const [rowSelection, setRowSelection] = useState({});
  const [table, setTable] = useState<TableType<TAutoReply>>();
  const [sortOrder, setSortOrder] = useState<any>();
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [channelFilter, setChannelFilter] = useState<string>('all');
  const [addressFilter, setAddressFilter] = useState<string>('all');
  const [searchValue, setSearchValue] = useState<string>('');

  // Pagination
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [perPage, setPerPage] = useState<number>(50);

  // Debounced search value
  const [debouncedSearchValue, setDebouncedSearchValue] = useState<string>('');

  const queryClient = useQueryClient();

  // Fetch platform accounts for address filter
  const { data: platformAccountsData, isLoading: isLoadingPlatformAccounts } =
    useQuery({
      queryKey: ['platformAccounts', '852'], // Using 852 as default country code
      queryFn: async () => {
        try {
          const response = await fireGetPlatformAccounts('852', basePath);
          return response.data;
        } catch (error) {
          console.error('Error fetching platform accounts:', error);
          throw error;
        }
      },
      enabled: true,
    });

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchValue(searchValue);
      setCurrentPage(1); // Reset to first page when search changes
    }, 500);

    return () => clearTimeout(timer);
  }, [searchValue]);

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [statusFilter, channelFilter, addressFilter]);

  // Reset page when sorting changes
  useEffect(() => {
    setCurrentPage(1);
  }, [sortOrder]);

  // Transform platform accounts data for Select component
  const addressOptions = useMemo(() => {
    const baseOptions = [{ id: 'all', label: 'All Addresses', value: 'all' }];

    if (platformAccountsData?.isSuccess && platformAccountsData.data) {
      const accountOptions = platformAccountsData.data.map((account: any) => ({
        id: account.value,
        value: account.value,
        label: account.label,
      }));
      return [...baseOptions, ...accountOptions];
    }

    return baseOptions;
  }, [platformAccountsData]);

  // Prepare filters for API
  const apiFilters = useMemo(
    () => ({
      ...(statusFilter !== 'all' && {
        isActive: statusFilter === 'Active',
      }),
      ...(addressFilter !== 'all' && { address: addressFilter }),
      ...(debouncedSearchValue && { ruleName: debouncedSearchValue }),
    }),

    [statusFilter, addressFilter, debouncedSearchValue]
  );

  // Prepare sorting parameters for API
  const apiSorting = useMemo(() => {
    if (!sortOrder) return undefined;

    // Find the first sorting field that has a value
    const sortField = Object.keys(sortOrder).find((key) => sortOrder[key]);

    if (!sortField) return undefined;

    return {
      order: sortOrder[sortField] as 'ASC' | 'DESC',
      orderBy: sortField as
        | 'address'
        | 'name'
        | 'updateTime'
        | 'isActive'
        | 'priority',
    };
  }, [sortOrder]);

  // React Query
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['autoReplies', currentPage, perPage, apiFilters, apiSorting],
    queryFn: () =>
      fetchAutoReplies(
        currentPage,
        perPage,
        channelFilter === 'all' ? 'whatsapp' : channelFilter,
        apiFilters,
        apiSorting
      ),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: async (ruleId: string) => {
      console.log(`Deleting auto reply rule with ID: ${ruleId}`);
      const response = await fireDeleteAutoReplyRule(ruleId, basePath);

      if (!response.data?.isSuccess) {
        throw new Error(
          response.data?.error || 'Failed to delete auto reply rule'
        );
      }

      return response.data;
    },
    onSuccess: (data, ruleId) => {
      console.log('Auto reply rule deleted successfully');

      // Show success toast
      toast({
        variant: 'success',
        title: 'Success',
        description: 'Auto reply rule deleted successfully.',
      });

      // Invalidate and refetch the auto reply list
      queryClient.invalidateQueries({
        queryKey: ['autoReplies'],
      });

      // Also invalidate the specific item query if it exists
      queryClient.invalidateQueries({
        queryKey: ['autoReply', ruleId],
      });
    },
    onError: (error, ruleId) => {
      console.error('Error deleting auto reply rule:', error);

      // Show error toast
      toast({
        variant: 'error',
        title: 'Error',
        description:
          error instanceof Error
            ? error.message
            : 'Failed to delete auto reply rule. Please try again.',
      });
    },
  });

  console.log('data::::', data);

  const autoReplies = data?.data?.autoReplies || [];
  const totalCount = data?.data?.total || 0;
  const totalPages = Math.ceil(totalCount / perPage);

  const handleNext = () => {
    let tar = currentPage + 1;
    if (tar > totalPages) tar = totalPages;
    setCurrentPage(tar);
  };

  const handlePrevious = () => {
    let tar = currentPage - 1;
    if (tar < 1) tar = 1;
    setCurrentPage(tar);
  };

  const handleDelete = async (ruleId: string) => {
    // Use the mutation instead of direct API call
    deleteMutation.mutate(ruleId);
  };

  // Define columns
  const columns = [
    {
      id: 'name',
      accessorKey: 'name',
      header: () => (
        <SortingButton
          sorting={
            sortOrder?.name
              ? sortOrder?.name === 'ASC'
                ? 'asc'
                : 'desc'
              : false
          }
          onClick={() => {
            const targetSortOrder = sortOrder?.name === 'ASC' ? 'DESC' : 'ASC';
            setSortOrder({
              name: targetSortOrder,
            });
          }}
        >
          Rule Name
        </SortingButton>
      ),
      cell: ({ row }) => <div>{row.getValue('name')}</div>,
    },
    {
      id: 'isActive',
      accessorKey: 'isActive',
      header: () => (
        <SortingButton
          sorting={
            sortOrder?.isActive
              ? sortOrder?.isActive === 'ASC'
                ? 'asc'
                : 'desc'
              : false
          }
          onClick={() => {
            const targetSortOrder =
              sortOrder?.isActive === 'ASC' ? 'DESC' : 'ASC';
            setSortOrder({
              isActive: targetSortOrder,
            });
          }}
        >
          Status
        </SortingButton>
      ),
      cell: ({ row }) => {
        const isActive = row.getValue('isActive') as number;
        const status = isActive === 1 ? 'active' : 'inactive';
        return <StatusBadge status={status as 'active' | 'inactive'} />;
      },
    },
    {
      id: 'channelType',
      accessorKey: 'channelType',
      header: () => (
        <SortingButton
          sorting={
            sortOrder?.channelType
              ? sortOrder?.channelType === 'ASC'
                ? 'asc'
                : 'desc'
              : false
          }
          onClick={() => {
            const targetSortOrder =
              sortOrder?.channelType === 'ASC' ? 'DESC' : 'ASC';
            setSortOrder({
              channelType: targetSortOrder,
            });
          }}
        >
          Channel
        </SortingButton>
      ),
      cell: ({ row }) => <div>{row.getValue('channelType')}</div>,
    },
    {
      id: 'address',
      accessorKey: 'address',
      header: () => (
        <SortingButton
          sorting={
            sortOrder?.address
              ? sortOrder?.address === 'ASC'
                ? 'asc'
                : 'desc'
              : false
          }
          onClick={() => {
            const targetSortOrder =
              sortOrder?.address === 'ASC' ? 'DESC' : 'ASC';
            setSortOrder({
              address: targetSortOrder,
            });
          }}
        >
          Address
        </SortingButton>
      ),
      cell: ({ row }) => <div>{row.getValue('address')}</div>,
    },
    {
      id: 'priority',
      accessorKey: 'priority',
      header: () => (
        <SortingButton
          sorting={
            sortOrder?.priority
              ? sortOrder?.priority === 'ASC'
                ? 'asc'
                : 'desc'
              : false
          }
          onClick={() => {
            const targetSortOrder =
              sortOrder?.priority === 'ASC' ? 'DESC' : 'ASC';
            setSortOrder({
              priority: targetSortOrder,
            });
          }}
        >
          Priority
        </SortingButton>
      ),
      cell: ({ row }) => <div>{row.getValue('priority')}</div>,
    },
    // {
    //   id: 'direction',
    //   accessorKey: 'direction',
    //   header: () => (
    //     <SortingButton
    //       sorting={
    //         sortOrder?.direction
    //           ? sortOrder?.direction === 'ASC'
    //             ? 'asc'
    //             : 'desc'
    //           : false
    //       }
    //       onClick={() => {
    //         const targetSortOrder =
    //           sortOrder?.direction === 'ASC' ? 'DESC' : 'ASC';
    //         setSortOrder({
    //           direction: targetSortOrder,
    //         });
    //       }}
    //     >
    //       Direction
    //     </SortingButton>
    //   ),
    //   cell: ({ row }) => <div>{row.getValue('direction')}</div>,
    // },
    {
      id: 'updateTime',
      accessorKey: 'updateTime',
      header: () => (
        <SortingButton
          sorting={
            sortOrder?.updateTime
              ? sortOrder?.updateTime === 'ASC'
                ? 'asc'
                : 'desc'
              : false
          }
          onClick={() => {
            const targetSortOrder =
              sortOrder?.updateTime === 'ASC' ? 'DESC' : 'ASC';
            setSortOrder({
              updateTime: targetSortOrder,
            });
          }}
        >
          Last Updated
        </SortingButton>
      ),
      cell: ({ row }) => {
        const updateTime = row.getValue('updateTime') as string;
        return (
          <div>{updateTime ? new Date(updateTime).toLocaleString() : ''}</div>
        );
      },
    },
    {
      id: 'action',
      header: () => <div>Action</div>,
      cell: ({ row }) => {
        const isDeleting =
          deleteMutation.isPending &&
          deleteMutation.variables === row.original.ruleId;

        const menuItems = [
          {
            label: 'View',
            onClick: () => viewDetail(row.original.ruleId),
            className: 'w-full text-left px-4 py-2 hover:bg-primary-100',
            disabled: isDeleting,
          },
          {
            label: 'Edit',
            onClick: () => editDetail(row.original.ruleId),
            className: 'w-full text-left px-4 py-2 hover:bg-primary-100',
            disabled: isDeleting,
          },
          {
            label: isDeleting ? 'Deleting...' : 'Delete',
            onClick: () => handleDelete(row.original.ruleId),
            className: `w-full text-left px-4 py-2 hover:bg-primary-100 ${
              isDeleting ? 'text-gray-400 cursor-not-allowed' : 'text-red-600'
            }`,
            disabled: isDeleting,
          },
        ];

        return (
          <div onClick={(e) => e.stopPropagation()}>
            <PopoverMenu
              icon={
                <EllipsisVertical className="w-5 h-5 cursor-pointer hover:text-primary-500" />
              }
              side="left"
            >
              <div className="bg-white rounded shadow-md py-1 min-w-32">
                {menuItems.map((item, index) => (
                  <button
                    key={index}
                    className={item.className}
                    onClick={item.onClick}
                    disabled={item.disabled}
                  >
                    {item.label}
                  </button>
                ))}
              </div>
            </PopoverMenu>
          </div>
        );
      },
    },
  ] as ColumnDef<TAutoReply>[];

  // Status options for filter dropdown
  const statusOptions = [
    { id: 'all', label: 'All Statuses', value: 'all' },
    { id: 'active', label: 'Active', value: 'Active' },
    { id: 'inactive', label: 'Inactive', value: 'Inactive' },
  ];

  // Channel options for filter dropdown
  const channelOptions = [
    { id: 'all', label: 'All Channels', value: 'all' },
    { id: 'whatsapp', label: 'WhatsApp', value: 'whatsapp' },
    // { id: 'sms', label: 'SMS', value: 'sms' },
    // { id: 'email', label: 'Email', value: 'email' },
  ];

  return (
    <div className="px-4 pt-1 pb-6 flex flex-col h-full gap-y-4">
      <div className="flex justify-between items-center overflow-auto gap-2 pt-2">
        <div className="flex items-center gap-2">
          <Select
            options={statusOptions}
            value={statusFilter}
            onChange={setStatusFilter}
            placeholder="All Statuses"
            isPagination={false}
            labelClassName="h-full"
            triggerClassName="disabled:opacity-100 disabled:bg-grey-100 h-8 disabled:text-black text-sm"
          />
          <Select
            options={channelOptions}
            value={channelFilter}
            onChange={setChannelFilter}
            placeholder="All Channels"
            isPagination={false}
            labelClassName="h-full"
            triggerClassName="disabled:opacity-100 disabled:bg-grey-100 h-8 disabled:text-black text-sm"
          />
          <Select
            options={addressOptions}
            value={addressFilter}
            onChange={setAddressFilter}
            placeholder={
              isLoadingPlatformAccounts
                ? 'Loading addresses...'
                : 'All Addresses'
            }
            isPagination={false}
            labelClassName="h-full"
            triggerClassName="disabled:opacity-100 disabled:bg-grey-100 h-8 disabled:text-black text-sm"
            disabled={isLoadingPlatformAccounts}
          />
          <div className="flex-1">
            <Input
              className=""
              // isSearch
              beforeIcon={<Icon name="search" />}
              placeholder="Search by rule name"
              value={searchValue}
              onChange={(value) => setSearchValue(String(value))}
              allowClear
              size="s"
            />
          </div>
        </div>
        <Button
          size="s"
          onClick={() => createNew()}
        >
          Create
        </Button>
      </div>

      <div className="flex-1 h-0">
        <DataTable<TAutoReply>
          data={autoReplies}
          columns={columns}
          loading={isLoading}
          emptyMessage="No auto replies found"
          error={error instanceof Error ? error.message : undefined}
          rowSelection={rowSelection}
          setRowSelection={setRowSelection}
          onClickRow={(row) => {
            viewDetail(row.original.ruleId);
          }}
          onTableSetUp={(tableInstance) => setTable(tableInstance)}
          resize={true}
        />
      </div>
      {totalPages > 0 && (
        <section className="flex-row">
          <div>
            <Pagination
              current={currentPage}
              perPage={perPage}
              total={totalPages}
              totalCount={totalCount}
              onChange={(v) => setCurrentPage(v)}
              handleOnPrevious={() => handlePrevious()}
              handleOnNext={() => handleNext()}
              handlePerPageSetter={(p: number) => {
                const pageSize = Number(p);
                if (!isNaN(pageSize)) {
                  setPerPage(pageSize);
                }
                setCurrentPage(1);
              }}
            />
          </div>
        </section>
      )}
    </div>
  );
};

export default ListingComponent;
