import React, { ChangeEvent, useState } from 'react';
import { But<PERSON> } from 'libs/design-system/src/components/_ui/Button';
import { Select } from 'libs/design-system/src/components/_ui/Select';

import {
  IBooleanCondition,
  TBooleanConditionType,
  TCondition,
  TConditionType,
  ITimeCondition,
} from '../../_store/autoReplyFormStore';
import Checkbox from '@cdss-modules/design-system/components/_ui/Checkbox';
import { Trash } from 'lucide-react';
import { cn } from '@cdss-modules/design-system/lib/utils';

interface ConditionProps {
  condition: TCondition;
  isGlobal: boolean;
  groupIndex?: number;
  conditionIndex: number;
  onUpdate: (newCondition: TCondition) => void;
  onRemove: () => void;
  allowedConditionTypes: TConditionType[];
  allowedBooleanTypes?: TBooleanConditionType[];
  disabled?: boolean;
}

// Custom style for time input to ensure the clock icon is positioned correctly
const timeInputStyles = `
  input[type="time"]::-webkit-calendar-picker-indicator {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
  }

  input[type="time"] {
    position: relative;
    padding-right: 20px;
  }
`;

// Reusable styled input component
const StyledInput: React.FC<{
  type: string;
  value: string;
  onChange: (e: ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}> = ({ type, value, onChange, placeholder, className, disabled }) => {
  const [focus, setFocus] = useState(false);
  const isTimeInput = type === 'time';

  return (
    <>
      {isTimeInput && <style>{timeInputStyles}</style>}
      <div className="relative w-full">
        <input
          disabled={disabled}
          type={type}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          onFocus={() => setFocus(true)}
          onBlur={() => setFocus(false)}
          className={cn(
            'w-full flex rounded bg-white border-[1px] border-grey-200 outline-none p-1 text-sm h-8',
            value ? '!border-black border' : '',
            !focus && !value ? 'hover:border hover:border-primary-500' : '',
            focus
              ? 'border-primary-900 !border bg-common-white shadow-field'
              : '',
            'focus:outline-none',
            'disabled:bg-common-disable disabled:border-none disabled:cursor-not-allowed',
            className
          )}
        />
      </div>
    </>
  );
};

// Component for Time condition
const TimeCondition: React.FC<{
  condition: ITimeCondition;
  onUpdate: (newCondition: ITimeCondition) => void;
  disabled?: boolean;
}> = ({ condition, onUpdate, disabled }) => {
  const handleOperatorChange = (newOperator: string) => {
    const operator = newOperator as 'within' | 'outside' | 'before' | 'after';
    const newTimes =
      operator === 'within' || operator === 'outside' ? ['', ''] : [''];
    const newCondition = {
      ...condition,
      operator,
      times: newTimes,
    };
    onUpdate(newCondition);
  };

  const handleTimeChange = (timeIndex: number, value: string) => {
    const newTimes = [...condition.times];
    newTimes[timeIndex] = value;
    const newCondition = { ...condition, times: newTimes };
    onUpdate(newCondition);
  };

  const timeOperatorOptions = [
    { id: 'within', label: 'Within', value: 'within' },
    { id: 'outside', label: 'Outside', value: 'outside' },
    { id: 'before', label: 'Before', value: 'before' },
    { id: 'after', label: 'After', value: 'after' },
  ];

  return (
    <>
      <Select
        disabled={disabled}
        isPagination={false}
        options={timeOperatorOptions}
        value={condition.operator}
        onChange={handleOperatorChange}
        labelClassName="h-full"
        triggerClassName="disabled:opacity-100 disabled:bg-grey-100 h-8 disabled:text-black text-sm"
      />

      {condition.operator === 'within' || condition.operator === 'outside' ? (
        <>
          <StyledInput
            type="time"
            value={condition.times[0] || ''}
            onChange={(e) => handleTimeChange(0, e.target.value)}
            disabled={disabled}
          />
          <div className="text-gray-500 text-sm">to</div>
          <StyledInput
            type="time"
            value={condition.times[1] || ''}
            onChange={(e) => handleTimeChange(1, e.target.value)}
            disabled={disabled}
          />
        </>
      ) : (
        <StyledInput
          type="time"
          value={condition.times[0] || ''}
          onChange={(e) => handleTimeChange(0, e.target.value)}
          disabled={disabled}
        />
      )}
    </>
  );
};

// Component for Boolean condition
const BooleanCondition: React.FC<{
  condition: IBooleanCondition;
  onUpdate: (newCondition: IBooleanCondition) => void;
  disabled?: boolean;
}> = ({ condition, onUpdate, disabled }) => {
  const handleTypeChange = (newType: string) => {
    const newCondition = {
      ...condition,
      conditionType: newType as TBooleanConditionType,
    };
    onUpdate(newCondition);
  };

  const handleValueChange = (e: ChangeEvent<HTMLInputElement>) => {
    const newCondition = {
      ...condition,
      value: e.target.checked,
    };
    onUpdate(newCondition);
  };

  const toggleValue = () => {
    const newCondition = {
      ...condition,
      value: !condition.value,
    };
    onUpdate(newCondition);
  };

  // Helper function to get human-readable description of each condition type
  const getConditionDescription = (type: TBooleanConditionType): string => {
    switch (type) {
      case 'isHoliday':
        return 'Is a holiday';
      case 'isWeekend':
        return 'Is a weekend day';
      case 'isBusinessHour':
        return 'Is during business hours';
      default:
        return type;
    }
  };

  // const booleanTypeOptions = [
  //   { id: 'isHoliday', label: 'Holiday', value: 'isHoliday' },
  //   { id: 'isWeekend', label: 'Weekend', value: 'isWeekend' },
  //   { id: 'isBusinessHour', label: 'Business Hours', value: 'isBusinessHour' },
  // ];

  return (
    <>
      <div
        className="flex gap-2 items-center justify-start cursor-pointer"
        onClick={toggleValue}
      >
        <Checkbox
          disabled={disabled}
          checked={condition.value}
          onChange={handleValueChange}
          id={`checkbox-${condition.conditionType}`}
        />
        <span className="text-sm text-gray-700 whitespace-nowrap">
          {condition.value ? 'Yes' : 'No'} -{' '}
          {getConditionDescription(condition.conditionType)}
        </span>
      </div>
    </>
  );
};

// Component for Text condition
const TextCondition: React.FC<{
  condition: any;
  onUpdate: (newCondition: any) => void;
  disabled?: boolean;
}> = ({ condition, onUpdate, disabled }) => {
  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const newCondition = {
      ...condition,
      text: e.target.value,
    };
    onUpdate(newCondition);
  };

  return (
    <StyledInput
      type="text"
      value={condition.text || ''}
      onChange={handleChange}
      placeholder="Enter text condition"
      disabled={disabled}
    />
  );
};

const Condition: React.FC<ConditionProps> = ({
  disabled,
  condition,
  isGlobal,
  groupIndex,
  conditionIndex,
  onUpdate,
  onRemove,
  allowedConditionTypes,
  allowedBooleanTypes = ['isHoliday', 'isWeekend', 'isBusinessHour'],
}) => {
  const handleTypeChange = (newValue: string) => {
    // Check if this is a boolean subtype (format: 'boolean:subtypeValue')
    if (newValue.startsWith('boolean:')) {
      const conditionType = newValue.split(':')[1] as TBooleanConditionType;
      const newCondition: TCondition = {
        type: 'boolean',
        conditionType,
        value: true,
      };
      onUpdate(newCondition);
    } else {
      const type = newValue as TConditionType;
      let newCondition: TCondition;

      switch (type) {
        case 'time':
          newCondition = { type: 'time', operator: 'within', times: ['', ''] };
          break;
        case 'text':
          newCondition = { type: 'text', text: '' };
          break;
        default:
          newCondition = condition;
      }

      onUpdate(newCondition);
    }
  };

  // Render the appropriate condition component based on type
  const renderConditionContent = () => {
    switch (condition.type) {
      case 'time':
        return (
          <TimeCondition
            disabled={disabled}
            condition={condition as ITimeCondition}
            onUpdate={onUpdate}
          />
        );
      case 'boolean':
        return (
          <BooleanCondition
            disabled={disabled}
            condition={condition as IBooleanCondition}
            onUpdate={onUpdate}
          />
        );
      case 'text':
        return (
          <TextCondition
            disabled={disabled}
            condition={condition}
            onUpdate={onUpdate}
          />
        );
      default:
        return null;
    }
  };

  const conditionTypeOptions = allowedConditionTypes.flatMap((type) => {
    if (type === 'boolean') {
      // Instead of showing "boolean" as an option, directly show the specific boolean conditions
      // Only include the boolean conditions that are in allowedBooleanTypes
      return allowedBooleanTypes.map((booleanType) => {
        let label = '';
        switch (booleanType) {
          case 'isHoliday':
            label = 'Holiday';
            break;
          case 'isWeekend':
            label = 'Weekend';
            break;
          case 'isBusinessHour':
            label = 'Business Hours';
            break;
          default:
            label = booleanType;
        }

        return {
          id: `boolean-${booleanType}`,
          label,
          value: `boolean:${booleanType}`,
        };
      });
    }

    // For non-boolean types, just return the standard option
    let label = '';
    let typeAsString = '';

    switch (type) {
      case 'time':
        label = 'Time';
        break;
      case 'text':
        label = 'Text';
        break;
      default:
        // Fallback to capitalized type name if new types are added in the future
        typeAsString = String(type);
        label = typeAsString.charAt(0).toUpperCase() + typeAsString.slice(1);
    }

    return [
      {
        id: type,
        label,
        value: type,
      },
    ];
  });

  // Get location identifier (global or group number)
  // const locationLabel = isGlobal ? 'Global' : `Group ${(groupIndex || 0) + 1}`;

  return (
    <div
      className={cn(
        'flex flex-col rounded-md justify-between w-full',
        isGlobal ? 'p-2 bg-gray-100 ' : '',
        disabled && 'bg-white p-0'
      )}
    >
      {isGlobal && (
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm text-gray-700 font-semibold">Condition</h3>
          {!disabled && (
            <Button
              disabled={disabled}
              type="button"
              variant="secondary"
              size="s"
              onClick={onRemove}
              className="disabled:text-black"
              beforeIcon={<Trash className="h-3 w-3 mr-1" />}
            >
              Remove Condition
            </Button>
          )}
        </div>
      )}

      <div className="flex items-center gap-2">
        <div className="flex items-center gap-2 flex-1">
          <Select
            disabled={disabled}
            isPagination={false}
            options={conditionTypeOptions}
            value={
              condition.type === 'boolean'
                ? `boolean:${(condition as IBooleanCondition).conditionType}`
                : condition.type
            }
            labelClassName="h-full"
            triggerClassName="disabled:opacity-100 disabled:bg-grey-100 h-8 disabled:text-black text-sm"
            onChange={handleTypeChange}
          />

          {renderConditionContent()}
          {!isGlobal && !disabled && (
            <button
              type="button"
              onClick={onRemove}
            >
              <Trash className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default Condition;
