// import React from 'react';
// import { Button } from 'libs/design-system/src/components/_ui/Button';
// import { Select } from 'libs/design-system/src/components/_ui/Select';

// import {
//   IGroup,
//   TConditionType,
//   TBooleanConditionType,
//   useAutoReplyFormStore,
// } from '../../_store/autoReplyFormStore';
// import Condition from './Condition';

// interface GroupProps {
//   group: IGroup;
//   groupIndex: number;
//   allowedConditionTypes: TConditionType[];
//   allowedBooleanTypes?: TBooleanConditionType[];
// }

// const Group: React.FC<GroupProps> = ({
//   group,
//   groupIndex,
//   allowedConditionTypes,
//   allowedBooleanTypes = ['isHoliday', 'isWeekend', 'isBusinessHour'],
// }) => {
//   const {
//     updateGroupLogic,
//     addConditionToGroup,
//     removeGroup,
//     updateConditionInGroup,
//     removeConditionFromGroup,
//   } = useAutoReplyFormStore();

//   // Get a user-friendly name for displaying button text for adding conditions
//   const getDefaultConditionTypeName = (): string => {
//     const defaultType = allowedConditionTypes[0];
//     let typeAsString = '';

//     switch (defaultType) {
//       case 'boolean':
//         // Use the first specific boolean condition type if available
//         if (allowedBooleanTypes.length > 0) {
//           switch (allowedBooleanTypes[0]) {
//             case 'isHoliday':
//               return 'Holiday';
//             case 'isWeekend':
//               return 'Weekend';
//             case 'isBusinessHour':
//               return 'Business Hours';
//             default:
//               return String(allowedBooleanTypes[0]);
//           }
//         }
//         return 'Holiday';
//       case 'time':
//         return 'Time';
//       case 'text':
//         return 'Text';
//       default:
//         typeAsString = String(defaultType);
//         return typeAsString.charAt(0).toUpperCase() + typeAsString.slice(1);
//     }
//   };

//   const handleAddCondition = () => {
//     // Create a condition of the first allowed type
//     const defaultType = allowedConditionTypes[0];
//     let newCondition;

//     if (defaultType === 'time') {
//       newCondition = {
//         type: 'time' as const,
//         operator: 'within' as const,
//         times: ['', ''],
//       };
//     } else if (defaultType === 'boolean') {
//       // When adding a boolean condition, default to first allowed boolean type
//       const defaultBooleanType =
//         allowedBooleanTypes.length > 0 ? allowedBooleanTypes[0] : 'isHoliday';
//       newCondition = {
//         type: 'boolean' as const,
//         conditionType: defaultBooleanType,
//         value: true,
//       };
//     } else if (defaultType === 'text') {
//       newCondition = {
//         type: 'text' as const,
//         text: '',
//       };
//     }

//     if (newCondition) {
//       addConditionToGroup(groupIndex, newCondition);
//     }
//   };

//   const logicOptions = [
//     { id: 'and', label: 'And', value: 'and' },
//     { id: 'or', label: 'Or', value: 'or' },
//   ];

//   return (
//     <div className="mb-4 p-4 border rounded">
//       <div className="flex items-center mb-2 justify-between">
//         <div className="flex items-center">
//           <span className="mr-2 font-semibold">Group {groupIndex + 1}:</span>
//           <Select
//             isPagination={false}
//             options={logicOptions}
//             value={group.logic}
//             onChange={(value) =>
//               updateGroupLogic(groupIndex, value as 'and' | 'or')
//             }
//             triggerClassName="mr-2"
//           />
//         </div>
//         <Button
//           variant="blank"
//           onClick={() => removeGroup(groupIndex)}
//         >
//           Remove Group
//         </Button>
//       </div>
//       <div>
//         {group.conditions.length === 0 && (
//           <p className="text-gray-500">No conditions in this group.</p>
//         )}
//         {group.conditions.map((condition, conditionIndex) => (
//           <Condition
//             key={conditionIndex}
//             condition={condition.condition}
//             isGlobal={false}
//             groupIndex={groupIndex}
//             conditionIndex={conditionIndex}
//             onUpdate={(newCondition) =>
//               updateConditionInGroup(groupIndex, conditionIndex, newCondition)
//             }
//             onRemove={() =>
//               removeConditionFromGroup(groupIndex, conditionIndex)
//             }
//             allowedConditionTypes={allowedConditionTypes}
//             allowedBooleanTypes={allowedBooleanTypes}
//           />
//         ))}
//       </div>
//       <Button
//         onClick={handleAddCondition}
//         className="mt-2"
//       >
//         Add {getDefaultConditionTypeName()} Condition
//       </Button>
//     </div>
//   );
// };

// export default Group;
