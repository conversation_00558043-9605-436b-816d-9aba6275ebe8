// import React, { useState } from 'react';
// import { Button } from 'libs/design-system/src/components/_ui/Button';
// import { Select } from 'libs/design-system/src/components/_ui/Select';
// import { Tooltip } from 'libs/design-system/src/components/_ui/Tooltip';

// import {
//   TCondition,
//   TConditionType,
//   TBooleanConditionType,
//   TLogicType,
//   useAutoReplyFormStore,
//   TUnifiedCondition,
// } from '../../_store/autoReplyFormStore';
// import ConditionComponent from './Condition';
// import { HelpCircle, Plus, Layers, Trash } from 'lucide-react';
// import PopoverMenu from '@cdss-modules/design-system/components/_ui/PopoverMenu';
// import { cn } from '@cdss-modules/design-system/lib/utils';

// export const ConditionButton = ({
//   children,
//   mode,
//   onClick,
// }: {
//   children: React.ReactNode;
//   mode: string;
//   onClick: () => void;
// }) => (
//   <button
//     disabled={mode === 'view'}
//     className="flex items-center justify-center bg-white rounded-md px-2 py-1 border border-gray-200"
//     onClick={onClick}
//   >
//     {children}
//   </button>
// );

// // Guide content for the tooltip
// const GuideContent = () => (
//   <div className="w-fit space-y- bg-white rounded-md p-4 shadow-md border border-gray-200 ml-1 text-sm text-gray-600">
//     <p className="">Time-Based Condition Examples</p>
//     <p className="">Time Conditions</p>
//     <ul className="pl-5 space-y-1 list-none">
//       <li className="relative before:absolute before:content-['•'] before:-left-4 ">
//         Time within &quot;09:00-17:00&quot; - Matches messages received during
//         business hours
//       </li>
//       <li className="relative before:absolute before:content-['•'] before:-left-4 ">
//         Time outside &quot;09:00-17:00&quot; - Matches messages received outside
//         business hours
//       </li>
//       <li className="relative before:absolute before:content-['•'] before:-left-4 ">
//         Time before &quot;08:00&quot; - Matches early morning messages
//       </li>
//       <li className="relative before:absolute before:content-['•'] before:-left-4 ">
//         Time after &quot;17:00&quot; - Matches evening messages
//       </li>
//     </ul>
//     <div className="border-t border-gray-200 my-2"></div>
//     <p className="">
//       <span className="">Tip:</span> You can define different responses for
//       different times of day.
//     </p>
//     <p className="">
//       <span className="">Example:</span> Create a rule for business hours
//       (9AM-5PM) and a separate rule for after-hours.
//     </p>
//   </div>
// );

// interface ConditionEditorProps {
//   mode: string;
//   allowedConditionTypes?: TConditionType[];
//   allowedBooleanTypes?: TBooleanConditionType[];
// }

// const ConditionEditor: React.FC<ConditionEditorProps> = ({
//   mode,
//   allowedConditionTypes = ['time'], // Default to only allow time conditions
//   allowedBooleanTypes = [], // Default to all boolean types
// }) => {
//   const {
//     formData,
//     addCondition,
//     removeCondition,
//     updateCondition,
//     updateConditionLogic,
//     addGroup,
//     addConditionToGroup,
//     updateConditionInGroup,
//     removeConditionFromGroup,
//     updateConditionLogicInGroup,
//     resetConditions,
//   } = useAutoReplyFormStore();

//   // Extract conditions from form data
//   const { conditions } = formData;

//   const disabled = mode === 'view';

//   const handleAddCondition = () => {
//     // Create a condition of the first allowed type
//     const defaultType = allowedConditionTypes[0];
//     let newCondition: TCondition | null = null;

//     if (defaultType === 'time') {
//       newCondition = {
//         type: 'time' as const,
//         operator: 'within' as const,
//         times: ['', ''],
//       };
//     } else if (defaultType === 'boolean') {
//       // When adding a boolean condition, default to first allowed boolean type
//       const defaultBooleanType =
//         allowedBooleanTypes.length > 0 ? allowedBooleanTypes[0] : 'isHoliday';
//       newCondition = {
//         type: 'boolean' as const,
//         conditionType: defaultBooleanType,
//         value: true,
//       };
//     } else if (defaultType === 'text') {
//       newCondition = {
//         type: 'text' as const,
//         text: '',
//       };
//     }

//     if (newCondition) {
//       addCondition(newCondition);
//     }
//   };

//   const logicOptions = [
//     { id: 'and', label: 'Match ALL conditions (And)', value: 'and' },
//     { id: 'or', label: 'Match ANY condition (Or)', value: 'or' },
//   ];

//   // Handle adding a condition to a group
//   const handleAddConditionToGroup = (groupIndex: number) => {
//     // Create a condition of the first allowed type
//     const defaultType = allowedConditionTypes[0];
//     let newCondition: TCondition | null = null;

//     if (defaultType === 'time') {
//       newCondition = {
//         type: 'time' as const,
//         operator: 'within' as const,
//         times: ['', ''],
//       };
//     } else if (defaultType === 'boolean') {
//       // When adding a boolean condition, default to first allowed boolean type
//       const defaultBooleanType =
//         allowedBooleanTypes.length > 0 ? allowedBooleanTypes[0] : 'isHoliday';
//       newCondition = {
//         type: 'boolean' as const,
//         conditionType: defaultBooleanType,
//         value: true,
//       };
//     } else if (defaultType === 'text') {
//       newCondition = {
//         type: 'text' as const,
//         text: '',
//       };
//     }

//     if (newCondition) {
//       addConditionToGroup(groupIndex, newCondition);
//     }
//   };

//   // Logic selector component between conditions
//   const LogicSelector = ({
//     logic = 'and',
//     onChange,
//   }: {
//     logic?: TLogicType;
//     onChange: (logic: TLogicType) => void;
//   }) => (
//     <div className="flex justify-start">
//       <Select
//         disabled={disabled}
//         isPagination={false}
//         options={logicOptions}
//         value={logic}
//         onChange={(value) => onChange(value as TLogicType)}
//         labelClassName="h-full"
//         triggerClassName="disabled:opacity-100 disabled:bg-grey-100 h-8 disabled:text-black text-sm min-w-[100px]"
//       />
//     </div>
//   );

//   // Render a condition item based on its type (simple or group)
//   const renderConditionItem = (item: TUnifiedCondition, index: number) => {
//     if (item.type === 'simple') {
//       return (
//         <div key={`simple-${index}`}>
//           <div className="">
//             <ConditionComponent
//               disabled={disabled}
//               condition={item.condition}
//               isGlobal={true}
//               conditionIndex={index}
//               onUpdate={(newCondition) => updateCondition(index, newCondition)}
//               onRemove={() => removeCondition(index)}
//               allowedConditionTypes={allowedConditionTypes}
//               allowedBooleanTypes={allowedBooleanTypes}
//             />
//           </div>

//           {/* Add logic selector if this is not the last condition */}
//           {index < conditions.length - 1 && (
//             <div className="my-3">
//               <LogicSelector
//                 logic={item.logic}
//                 onChange={(logic) => updateConditionLogic(index, logic)}
//               />
//             </div>
//           )}
//         </div>
//       );
//     } else if (item.type === 'group') {
//       return (
//         <div key={`group-${index}`}>
//           <div
//             className={cn(
//               'bg-gray-100 p-3 rounded-lg mb-3 space-y-3',
//               disabled && 'bg-white p-0'
//             )}
//           >
//             {/* Group Header */}
//             <div className="flex items-center justify-between">
//               <div className="flex items-center">
//                 <span className="mr-2 text-sm font-semibold text-gray-700">
//                   Group
//                 </span>
//                 <span className="text-sm text-gray-500">
//                   {item.group.conditions.length} conditions
//                 </span>
//               </div>

//               {!disabled && (
//                 <Button
//                   type="button"
//                   variant="secondary"
//                   size="s"
//                   onClick={() => removeCondition(index)}
//                   beforeIcon={<Trash className="h-3 w-3 mr-1" />}
//                   disabled={disabled}
//                 >
//                   Remove Group
//                 </Button>
//               )}
//             </div>

//             {/* Group Conditions */}
//             <div className="">
//               {item.group.conditions.length === 0 && (
//                 <p className="text-gray-500 mb-2">
//                   No conditions in this group.
//                 </p>
//               )}
//               <div className="">
//                 {item.group.conditions.map(
//                   (conditionWithLogic, conditionIndex) => (
//                     <div key={conditionIndex}>
//                       <ConditionComponent
//                         disabled={disabled}
//                         condition={conditionWithLogic.condition}
//                         isGlobal={false}
//                         groupIndex={index}
//                         conditionIndex={conditionIndex}
//                         onUpdate={(newCondition) =>
//                           updateConditionInGroup(
//                             index,
//                             conditionIndex,
//                             newCondition
//                           )
//                         }
//                         onRemove={() =>
//                           removeConditionFromGroup(index, conditionIndex)
//                         }
//                         allowedConditionTypes={allowedConditionTypes}
//                         allowedBooleanTypes={allowedBooleanTypes}
//                       />

//                       {/* Add logic selector if this is not the last condition */}
//                       {conditionIndex < item.group.conditions.length - 1 && (
//                         <div className="my-3">
//                           <LogicSelector
//                             logic={conditionWithLogic.logic}
//                             onChange={(logic) =>
//                               updateConditionLogicInGroup(
//                                 index,
//                                 conditionIndex,
//                                 logic
//                               )
//                             }
//                           />
//                         </div>
//                       )}
//                     </div>
//                   )
//                 )}
//               </div>
//             </div>

//             {/* Add Condition to Group Button */}
//             {!disabled && (
//               <Button
//                 variant="secondary"
//                 size="s"
//                 onClick={() => handleAddConditionToGroup(index)}
//                 beforeIcon={<Plus className="h-3 w-3 mr-1" />}
//                 disabled={disabled}
//               >
//                 Add Condition to Group
//               </Button>
//             )}
//           </div>

//           {/* Add logic selector if this is not the last item */}
//           {index < conditions.length - 1 && (
//             <div className="my-3">
//               <LogicSelector
//                 logic={item.logic}
//                 onChange={(logic) => updateConditionLogic(index, logic)}
//               />
//             </div>
//           )}
//         </div>
//       );
//     }

//     return null;
//   };

//   return (
//     <div className="mb-6">
//       <div className="mb-3 flex items-center justify-start gap-2">
//         <h2 className="text-sm font-semibold text-primary-500">Conditions</h2>
//         {/* <Tooltip
//           delayDuration={100}
//           trigger={
//             <HelpCircle
//               className="h-5 w-5 text-primary-500 cursor-pointer"
//               onClick={() => setShowGuide(!showGuide)}
//             />
//           }
//           content={<GuideContent />}
//           side="top"
//           open={showGuide}
//           onOpenChange={() => setShowGuide(!showGuide)}
//         /> */}
//         <PopoverMenu
//           icon={<HelpCircle className="h-4 w-4 text-primary-500" />}
//           side="right"
//         >
//           <GuideContent />
//         </PopoverMenu>
//       </div>

//       {/* Unified Conditions Area */}
//       <div className="mb-4">
//         {/* No conditions message */}
//         {conditions.length === 0 && (
//           <p className="text-gray-500 mb-2">No conditions defined yet.</p>
//         )}

//         {/* Render all conditions in order */}
//         {conditions.map((item, index) => renderConditionItem(item, index))}

//         {/* Action Buttons */}
//         {!disabled && (
//           <div className="flex gap-2 mt-3">
//             <Button
//               size="s"
//               type="button"
//               variant="secondary"
//               onClick={handleAddCondition}
//               disabled={disabled}
//             >
//               <Plus className="h-4 w-4 mr-1" />
//               Add Condition
//             </Button>
//             <Button
//               size="s"
//               type="button"
//               variant="secondary"
//               onClick={addGroup}
//               disabled={disabled}
//             >
//               <Layers className="h-4 w-4 mr-1" />
//               Add Condition Group
//             </Button>
//           </div>
//         )}
//       </div>
//     </div>
//   );
// };

// export default ConditionEditor;
