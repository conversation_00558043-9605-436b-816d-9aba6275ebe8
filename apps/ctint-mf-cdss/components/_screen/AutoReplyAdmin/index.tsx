import ListingComponent from './_screen/Listing';
import DetailComponent from './_screen/Detail';
import { useAutoReplyNavigationStore } from './_store/autoReplyNavigationStore';
import { Tabs, TabsContent, TabsProvider } from '@cdss-modules/design-system';

export const AutoReplyAdminPage = () => {
  const autoReplyView = useAutoReplyNavigationStore((state) => state.view);

  // const backToList = useAutoReplyNavigationStore((state) => state.backToList);

  return (
    <TabsProvider>
      <Tabs
        defaultTab="auto-reply"
        triggers={[
          {
            value: 'auto-reply',
            label: 'Auto Reply',
          },
        ]}
        // onChangeTabFunc={backToList}
      >
        <TabsContent
          value="auto-reply"
          className="p-0 h-0 flex-1"
        >
          {autoReplyView === 'list' ? (
            <ListingComponent />
          ) : (
            <DetailComponent />
          )}
        </TabsContent>
      </Tabs>
    </TabsProvider>
  );
};

export default AutoReplyAdminPage;
