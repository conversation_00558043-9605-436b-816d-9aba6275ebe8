/* eslint-disable @nx/enforce-module-boundaries */

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import AdminAudit from '../../ui/AdminAudit';
import { CDSSAdminProvider } from '@cdss-modules/design-system';

export const AdminAuditBody = () => {
  return <AdminAudit />;
};

// Create a client
const queryClient = new QueryClient();

const AdminAuditScreen = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <CDSSAdminProvider>
        <AdminAuditBody />
      </CDSSAdminProvider>
    </QueryClientProvider>
  );
};

export default AdminAuditScreen;
