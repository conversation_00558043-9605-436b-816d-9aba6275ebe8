/* eslint-disable @nx/enforce-module-boundaries */

import {
  Panel,
  QMProvider,
  Tabs,
  TabsContent,
} from '@cdss-modules/design-system';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import Dictionary from './ui/Dictionary';
import MetaData from './ui/MetaData';
import SOP from './ui/SOP';
import TestRules from './ui/TestRules';
import Rules from './ui/Rules';
import Topics from './ui/Topics';

export const DemoQMAdminBody = () => {
  return (
    <div className="flex flex-col h-full gap-y-4 overflow-auto">
      <Panel containerClassName="h-full">
        <Tabs
          defaultTab={'sop'}
          triggers={[
            {
              value: 'sop',
              label: 'SOP',
            },
            {
              value: 'rules',
              label: 'Rules',
            },
            {
              value: 'topics',
              label: 'Topics',
            },
            {
              value: 'dictionary',
              label: 'Dictionary',
            },
            {
              value: 'metadata',
              label: 'Meta Data',
            },
            {
              value: 'test',
              label: 'Test a Step',
            },
          ]}
          triggerClassName="py-2 px-2 text-body"
        >
          <TabsContent
            value={'dictionary'}
            className="p-0 h-0 flex-1 flex flex-col"
          >
            <Dictionary />
          </TabsContent>
          <TabsContent
            value={'metadata'}
            className="p-0 h-0 flex-1 flex flex-col"
          >
            <MetaData />
          </TabsContent>
          <TabsContent
            value={'rules'}
            className="p-0 h-0 flex-1 flex flex-col"
          >
            <Rules />
          </TabsContent>
          <TabsContent
            value={'topics'}
            className="p-0 h-0 flex-1 flex flex-col"
          >
            <Topics />
          </TabsContent>
          <TabsContent
            value={'sop'}
            className="p-0 h-0 flex-1 flex flex-col"
          >
            <SOP />
          </TabsContent>
          <TabsContent
            value={'test'}
            className="p-0 h-0 flex-1 flex flex-col"
          >
            <TestRules />
          </TabsContent>
        </Tabs>
      </Panel>
    </div>
  );
};

// Create a client
const queryClient = new QueryClient();

const DemoQMAdmin = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <QMProvider defaultFormAns={''}>
        <DemoQMAdminBody />
      </QMProvider>
    </QueryClientProvider>
  );
};

export default DemoQMAdmin;
