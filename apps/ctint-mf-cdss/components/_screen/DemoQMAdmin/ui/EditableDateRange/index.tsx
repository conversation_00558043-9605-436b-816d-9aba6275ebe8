import Icon from '@cdss-modules/design-system/components/_ui/Icon';

import { Check, Edit, X } from 'lucide-react';
import React, { useState } from 'react';
import dayjs from 'dayjs';
import { GLOBAL_DATE_FORMAT } from '@cdss-modules/design-system/lib/constants';
import { Button, DatePicker } from '@cdss-modules/design-system';

export const EditableDateRange = () => {
  const [isEditable, setIsEditable] = useState(false);
  const [startDate, setStartDate] = useState<Date | null>(null);
  return (
    <div>
      {isEditable ? (
        <div className="flex items-center gap-x-2">
          <DatePicker
            date={startDate}
            onChange={(date) => setStartDate(date)}
          />
          <span>-</span>
          <DatePicker
            date={startDate}
            onChange={(date) => setStartDate(date)}
          />
          <Button
            type="button"
            variant="primary"
            asSquare
            className=""
            onClick={() => setIsEditable(false)}
          >
            <Check />
          </Button>
          <Button
            type="button"
            variant="secondary"
            asSquare
            className=""
            onClick={() => setIsEditable(false)}
          >
            <X />
          </Button>
        </div>
      ) : (
        <button
          type="button"
          onClick={() => setIsEditable(true)}
          className="flex items-center gap-x-2 group/editable"
        >
          <Icon
            name="calendar"
            size={19}
          />
          <span>{dayjs().add(-5, 'months').format(GLOBAL_DATE_FORMAT)}</span>-
          <span>{dayjs().format(GLOBAL_DATE_FORMAT)}</span>
          <Edit
            className=" group-hover/editable:text-primary"
            size={20}
          />
        </button>
      )}
    </div>
  );
};

export default EditableDateRange;
