import React, { memo } from 'react';
import { Button } from '@cdss-modules/design-system';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { Check, Edit, Plus, Trash2, X } from 'lucide-react';
import { cn } from '@cdss-modules/design-system/lib/utils';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { Controller, useFieldArray, useFormContext } from 'react-hook-form';

export const EditableParticipants = ({ control, ...props }: any) => {
  const [editing, setEditing] = React.useState(false);
  const { watch } = useFormContext();
  const {
    fields: participantFields,
    append: appendParticipants,
    remove: removeParticipants,
  } = useFieldArray({
    control, // control props comes from useForm (optional: if you are using FormProvider)
    name: 'participants', // unique name for your Field Array
  });
  const participants = watch('participants');
  if (editing) {
    return (
      <div className="relative flex items-center gap-x-2 justify-betwee max-w-[1000px] pointer-events-auto">
        <Icon
          size={22}
          name="conference"
        />
        <div className="flex items-center gap-x-2 overflow-auto">
          {participantFields?.map((_: any, index: number) => (
            <div
              className="relative inline-flex gap-x-1 w-[100px] flex-none"
              key={`participant-${index}`}
            >
              <Controller
                name={`participants.${index}.name`}
                control={control}
                rules={{ required: 'Participant Name is required' }}
                render={({ field }) => (
                  <Input
                    {...field}
                    size="s"
                    className="w-full pr-6"
                    placeholder="Participant"
                  />
                )}
              />
              <Trash2
                className="absolute right-2 top-1/2 -translate-y-1/2 cursor-pointer"
                size={20}
                onClick={() => removeParticipants(index)}
              />
            </div>
          ))}
          <button
            type="button"
            className="bg-black hover:bg-primary text-white rounded-full size-5 flex-none flex justify-center items-center"
            onClick={() => {
              appendParticipants({
                id: `participants-${Math.random()}`,
                name: `participant-${participantFields.length}`,
              });
            }}
          >
            <Plus size={16} />
          </button>
        </div>
        <div
          className={cn(
            'inline-flex gap-x-2 px-2'
            // 'absolute right-0 top-1/2 -translate-y-1/2'
          )}
        >
          <Button
            type="button"
            variant="primary"
            asSquare
            className=""
            onClick={() => setEditing(false)}
          >
            <Check />
          </Button>
          <Button
            type="button"
            variant="secondary"
            asSquare
            className=""
            onClick={() => setEditing(false)}
          >
            <X />
          </Button>
        </div>
      </div>
    );
  }
  return (
    <button
      type="button"
      className="group/editable-input inline-flex items-center gap-x-2  pointer-events-auto"
      onClick={() => setEditing(true)}
    >
      <div className="flex items-center gap-x-2">
        <Icon
          size={22}
          name="conference"
        />
        <span>
          {participants?.map((field: any) => field?.name)?.join(', ')}
        </span>
      </div>
      <Edit
        className=" group-hover/editable-input:text-primary"
        size={20}
      />
    </button>
  );
};

export default memo(EditableParticipants);
