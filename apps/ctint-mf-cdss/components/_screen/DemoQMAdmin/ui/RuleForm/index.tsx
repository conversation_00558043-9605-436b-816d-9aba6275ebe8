import {
  Popup,
  PopupContent,
} from '@cdss-modules/design-system/components/_ui/Popup';
import { Controller, FormProvider, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import SOPVerificationRule from '../SOPVerificationRule';
import { Button } from '@cdss-modules/design-system';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import EditableInput from '../EditableInput';

export type TRuleFormProps = {
  data: any;
  onOpenChange: (open: boolean) => void;
};
export type TSOPRule = {
  id?: string;
  key?: string;
  name?: string;
  dataType?: 'text' | 'metadata' | 'dict' | 'rule-set';
  dataKey?: string;
  dataValue?: string;
  relation?: 'exists' | 'equals' | 'all' | 'numberof';
  relationFactor?: number;
  ruleSet?: string[];
};

const ruleSchema = yup
  .object({
    data: yup.object({
      id: yup.string(),
      key: yup.string(),
      name: yup.string(),
      dataType: yup.string(),
      dataKey: yup.string(),
      dataValue: yup.string(),
      relation: yup.string(),
      relationFactor: yup.number(),
      ruleSet: yup.array().of(yup.string()),
    }),
  })
  .required();
export const RuleForm = ({ data, onOpenChange }: TRuleFormProps) => {
  const methods = useForm({
    resolver: yupResolver(ruleSchema),
    defaultValues: { data: { ...data } },
  });
  const {
    handleSubmit,
    control,
    formState: { errors },
  } = methods;

  const onSubmit = async (data: any) => {
    window.alert(`Submitted data: ${JSON.stringify(data)}`);
  };
  return (
    <Popup
      open={!!data}
      onOpenChange={onOpenChange}
    >
      <PopupContent
        className="w-4/5 max-w-[700px] shadow-md"
        title={data?.id ? 'Edit Rule' : 'New Rule'}
      >
        <FormProvider {...methods}>
          <form
            onSubmit={handleSubmit(onSubmit)}
            className="flex flex-col w-full px-4 pt-4 pb-6"
          >
            <div className="flex gap-4 items-center mb-4">
              <Field
                title="Name"
                className="w-full"
              >
                <Controller
                  name="data.name"
                  control={control}
                  rules={{ required: 'Name is required' }}
                  render={({ field }) => (
                    <EditableInput
                      {...field}
                      placeholder="Enter Rule Name"
                      value={field.value ?? 'New Rule'}
                    />
                  )}
                />
              </Field>
              <Field
                title="Key"
                className="w-full"
              >
                <Controller
                  name="data.key"
                  control={control}
                  rules={{ required: 'Key is required' }}
                  render={({ field }) => (
                    <EditableInput
                      {...field}
                      placeholder="Enter Rule Key"
                      value={field.value ?? 'GR-5'}
                    />
                  )}
                />
              </Field>
            </div>
            <SOPVerificationRule
              control={control}
              path="data"
              index=""
              fields
              isRuleForm
            />
            <div className="flex mt-4">
              <Button type="submit">Submit</Button>
            </div>
          </form>
        </FormProvider>
      </PopupContent>
    </Popup>
  );
};

export default RuleForm;
