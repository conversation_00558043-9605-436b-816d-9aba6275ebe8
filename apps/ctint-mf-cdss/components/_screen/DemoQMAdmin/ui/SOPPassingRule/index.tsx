import React, { useEffect } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';

const typeOptions = [
  { label: 'All above rules passed', value: 'all' },
  { label: 'A number of above rules passed', value: 'numberof' },
];

const SOPPassingRule = ({ control, path, label, fields }: any) => {
  const { watch, setValue } = useFormContext();
  const type = watch(`${path}.type`);

  const showNumberInput = type === 'numberof';

  useEffect(() => {
    if (!showNumberInput) {
      setValue(`${path}.type`, 'all');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className="flex gap-2 -mt-4 items-center">
      <div className="font-bold text-body italic mr-2 text-primary-900">{`${label}`}</div>
      <div className="flex items-center gap-2">
        <Field
          title=""
          className="w-full"
        >
          <Controller
            name={`${path}.type`}
            control={control}
            rules={{ required: 'Type is required' }}
            render={({ field }) => (
              <Select
                {...field}
                labelClassName="h-full text-remark"
                labelContainerClassName="h-8"
                options={typeOptions?.map((option) => ({
                  id: option.value,
                  label: option.label,
                  value: option.value,
                }))}
                placeholder="Select Type"
              />
            )}
          />
        </Field>

        {showNumberInput && (
          <div className="flex items-center gap-2">
            <div>:</div>
            <Field
              title=""
              className="w-[80px]"
            >
              <Controller
                name={`${path}.numberOf`}
                control={control}
                rules={{ required: 'Number of rules is required', min: 1 }}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="number"
                    placeholder="Num."
                    size="ms"
                    min="1"
                  />
                )}
              />
            </Field>
            <div>of</div>
            <div className="w-[200px]">
              <Controller
                name={`${path}.rules`}
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    placeholder="Select rules"
                    mode="multiple"
                    showSearch={true}
                    labelClassName="h-full text-remark"
                    labelContainerClassName="h-8"
                    options={
                      new Array(fields?.length || 0)?.fill(0)?.map((_, i) => ({
                        id: `Rule V${i + 1}`,
                        label: `Rule V${i + 1}`,
                        value: `Rule V${i + 1}`,
                      })) || []
                    }
                    onChange={(e) => {
                      const isSelected = e?.target.checked;
                      const value = e?.target.value;
                      const prevValues = field?.value || [];

                      if (isSelected) {
                        field?.onChange([...prevValues, value]);
                      } else {
                        field?.onChange(
                          prevValues?.filter((prev: string) => prev !== value)
                        );
                      }
                    }}
                    value={field?.value || []}
                  />
                )}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SOPPassingRule;
