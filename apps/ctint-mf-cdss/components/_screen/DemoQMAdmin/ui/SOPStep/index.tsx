import React from 'react';
import { Controller } from 'react-hook-form';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import { ArrowRight, Trash2 } from 'lucide-react';
import { cn } from '@cdss-modules/design-system/lib/utils';
import EditableInput from '../EditableInput';
import { useQM } from '@cdss-modules/design-system';

const SOPStep = ({ control, errors, path, stepIndex, removeStep }: any) => {
  const { updateOpenedAdminSOPStep, openedAdminSOPStep } = useQM();
  const isActive = openedAdminSOPStep?.path === path;

  const onStepClick = () => {
    updateOpenedAdminSOPStep(null);
    if (!isActive) {
      setTimeout(() => {
        updateOpenedAdminSOPStep({
          control,
          errors,
          path,
          stepIndex,
          removeStep,
        });
      }, 50);
    }
  };

  return (
    <div
      className={cn(
        'relative p-3 rounded flex flex-col gap-y-4 hover:shadow-md',
        isActive ? 'bg-primary-200 shadow-md' : 'bg-gray-100'
      )}
    >
      <div className="relative flex justify-between items-center gap-x-4 z-10 pointer-events-none">
        <Field
          title=""
          status={errors?.[path]?.name ? 'danger' : undefined}
          message={errors?.[path]?.name?.message}
          className="w-full"
        >
          <Controller
            name={`${path}.name`}
            control={control}
            rules={{ required: 'Step Name is required' }}
            render={({ field }) => (
              <EditableInput
                {...field}
                placeholder="Enter Step Name"
              />
            )}
          />
        </Field>
        <button
          type="button"
          className="pointer-events-auto"
          onClick={() => removeStep(stepIndex)}
        >
          <Trash2 size={20} />
        </button>
        {!isActive && (
          <button
            type="button"
            onClick={() => onStepClick()}
            className={cn('pointer-events-auto')}
          >
            <ArrowRight />
          </button>
        )}
      </div>

      <button
        type="button"
        onClick={() => onStepClick()}
        className={cn('absolute left-0 top-0 w-full h-full z-0 cursor-pointer')}
      ></button>
      <div
        className={cn(
          'absolute left-full top-1/2 -translate-y-1/2',
          !isActive && 'hidden'
        )}
      >
        <div className="border-solid border-r-primary-200 border-r-[60px] border-y-transparent border-y-[36px] border-l-0"></div>
      </div>
    </div>
  );
};

export default SOPStep;
