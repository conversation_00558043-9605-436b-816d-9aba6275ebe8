import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';
import { useState } from 'react';
import { ColumnDef, Table as TableType } from '@tanstack/react-table';
import { SortingButton, toast, Tooltip } from '@cdss-modules/design-system';
import { GLOBAL_DATETIME_FORMAT } from '@cdss-modules/design-system/lib/constants';
import dayjs from 'dayjs';
import Checkbox from '@cdss-modules/design-system/components/_ui/Checkbox';
import { Files, Plus } from 'lucide-react';
import DictionaryActionMenu from '../DictionaryActionMenu';
import { DUMMY_TOPICS } from '../../dummy';
import TopicForm, { TSOPTopic } from '../TopicForm';

type TSOPRuleColumn = keyof TSOPTopic;

const DICTIONARY_COLUMN_LABEL: any = {
  id: 'ID',
  key: 'Key',
  name: 'Name',
  createdBy: 'Created By',
  createdAt: 'Created At',
  updatedBy: 'Updated By',
  updatedAt: 'Updated At',
};

const generateColumns = (
  columns: TSOPRuleColumn[],
  columnOrdering: string[],
  sortOrder: any,
  setSortOrder: (input: any) => void,
  openEntity: (id?: string) => void
) => {
  const selectionCol = {
    id: 'select',
    header: ({ table }: any) => (
      <div
        className="bg-white z-30 inline-flex items-center my-1"
        onClick={(e) => e.stopPropagation()}
      >
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onChange={(e: any) => {
            const isSelected = e?.target.checked;
            table.toggleAllPageRowsSelected(isSelected);
          }}
        />
      </div>
    ),
    cell: ({ row }: any) => (
      <div
        className="z-0 inline-flex items-center my-1"
        onClick={(e) => e.stopPropagation()}
      >
        <Checkbox
          checked={row.getIsSelected()}
          onChange={(e: any) => {
            const isSelected = e?.target.checked;
            row.toggleSelected(isSelected);
          }}
        />
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  };

  const actionCol = {
    id: 'action',
    header: () => (
      <div>
        <Tooltip
          trigger={
            <button
              type="button"
              className="bg-primary text-white rounded-full size-6 flex-none flex justify-center items-center animate-pulse hover:animate-none"
              onClick={() => {
                openEntity();
              }}
            >
              <Plus size={18} />
            </button>
          }
          content="Add New Entity"
        />
      </div>
    ),
    cell: (
      <div className="flex gap-x-2 z-0">
        <DictionaryActionMenu data={{}} />
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  };

  const orderedColumns = columnOrdering
    ? columns.sort((firstEl, secondEl) => {
        const firstIndex = columnOrdering?.findIndex(
          (item: any) => item.name === firstEl
        );
        const secondIndex = columnOrdering?.findIndex(
          (item: any) => item.name === secondEl
        );
        return firstIndex - secondIndex;
      })
    : columns;

  const formattedColumns = orderedColumns.map((column: string) => {
    const isDate = column === 'createdAt' || column === 'updatedAt';

    return {
      id: column,
      accessorKey: column,
      header: () => {
        return (
          <SortingButton
            sorting={
              sortOrder?.[column]
                ? sortOrder?.[column] === 'ASC'
                  ? 'asc'
                  : 'desc'
                : false
            }
            onClick={async () => {
              const targetSortOrder =
                sortOrder?.[column] === 'ASC' ? 'DESC' : 'ASC';
              setSortOrder({
                [column]: targetSortOrder,
              });
            }}
          >
            {DICTIONARY_COLUMN_LABEL?.[column] || column}
          </SortingButton>
        );
      },
      cell: ({ row }) => {
        let val = row.getValue(column) as any;
        if (isDate) val = dayjs(val).format(GLOBAL_DATETIME_FORMAT);
        if (column === 'values') val = val?.map((v: any) => v.value).join(', ');
        if (column === 'key') {
          return (
            <button
              onClick={(e) => {
                e.stopPropagation();
                toast({
                  variant: 'success',
                  title: 'Text Copied',
                  description: `"${val}" copied to clipboard`,
                });
              }}
              className="text-primary-900 font-bold flex items-center gap-x-2 group/table-copy"
            >
              {val}
              <Files
                size={16}
                className="opacity-0 group-hover/table-copy:opacity-100"
              />
            </button>
          );
        }
        if (column === 'name')
          return (
            <div className="text-primary-900 font-bold flex items-center gap-x-2">
              {val}
            </div>
          );
        return <div>{val}</div>;
      },
    } as ColumnDef<TSOPTopic>;
  });

  return [selectionCol, ...formattedColumns, actionCol];
};

export const Topics = () => {
  const dummyTopics = DUMMY_TOPICS as TSOPTopic[];
  const [rowSelection, setRowSelection] = useState({});
  const [table, setTable] = useState<TableType<TSOPTopic>>();
  const [sortOrder, setSortOrder] = useState<any>();
  const [openedEntity, setOpenedEntity] = useState<TSOPTopic | null>(null);
  const shownColumns = [
    'name',
    'key',
    'createdBy',
    'createdAt',
    'updatedAt',
    'updatedBy',
  ] as TSOPRuleColumn[];

  return (
    <div className="px-6 pt-1 pb-6 flex flex-col h-full gap-y-4 overflow-auto">
      {openedEntity ? (
        <TopicForm
          data={openedEntity}
          onOpenChange={(open: boolean) => {
            if (!open) setOpenedEntity(null);
          }}
        />
      ) : (
        <div className="flex-1 h-0">
          <DataTable<TSOPTopic>
            data={dummyTopics}
            columns={
              generateColumns(
                shownColumns,
                [],
                sortOrder,
                (input) => {
                  setSortOrder(input);
                },
                (id?: string) => {
                  setOpenedEntity(
                    dummyTopics?.find((d) => d?.id === `${id}`) || {
                      data: [
                        {
                          type: 'text',
                          value: '',
                        },
                      ],
                    }
                  );
                }
              ) as any
            }
            loading={false}
            // error={error?.message}
            rowSelection={rowSelection}
            setRowSelection={setRowSelection}
            onClickRow={(row) => {
              setOpenedEntity(dummyTopics?.[row?.index || 0] || {});
            }}
            onTableSetUp={(table) => setTable(table)}
          />
        </div>
      )}
    </div>
  );
};

export default Topics;
