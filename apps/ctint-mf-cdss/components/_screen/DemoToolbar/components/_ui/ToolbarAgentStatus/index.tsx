/* eslint-disable @nx/enforce-module-boundaries */
import { useMemo, useState } from 'react';
import { useTbarContext } from '@cdss-modules/design-system/context/TBarContext';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Pill from '@cdss-modules/design-system/components/_ui/Pill';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { AGENT_STATUS_COLOR_MAP } from '@cdss-modules/design-system/lib/constants/status';
import _ from 'lodash';

type TAgentStatusProps = {
  id: string;
  type: string;
  languageLabelEnUs: string;
  systemPresence: string;
  divisionId: string;
  deactivated: boolean;
  selfUri: string;
};

export const ToolbarDirectory = () => {
  const [searchValue, setSearchValue] = useState('');
  const {
    closeToolbarModal,
    statusContext: {
      agentStatus,
      agentStatusList,
      onChangeAgentStatus,
      onChangeAgentColor,
    },
  } = useTbarContext();

  const selectGroup = (statusId: string, status: string, color: string) => {
    onChangeAgentStatus(statusId, status);
    onChangeAgentColor(color);
    closeToolbarModal();
  };

  const handleInputChange = (e: any) => {
    const value = e?.toLowerCase();
    setSearchValue(value);
  };

  const filteredList = useMemo(() => {
    return (
      agentStatusList?.filter((item: TAgentStatusProps) =>
        item?.languageLabelEnUs.toLowerCase().includes(searchValue)
      ) || []
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchValue, agentStatusList]);

  const formattedList = useMemo(() => {
    const groupOrder = [
      'Available',
      'Away',
      'Break',
      'Meal',
      'Training',
      'Busy',
      'Meeting',
    ];
    const orderMap = groupOrder.reduce((acc: any, item, index) => {
      acc[item] = index;
      return acc;
    }, {});

    const orderedList = _.orderBy(
      agentStatusList,
      [
        (item) => orderMap[item.systemPresence],
        (item) => (groupOrder.includes(item?.languageLabelEnUs) ? 0 : 1),
        'languageLabelEnUs',
      ],
      'asc'
    );

    const groupedList = _.groupBy(orderedList, 'systemPresence');

    const groupWithMultipleItems = _.pickBy(
      groupedList,
      (value) => value.length > 1
    );
    const groupWithSingleItem = _.pickBy(
      groupedList,
      (value) => value.length === 1
    );

    return {
      ...groupWithMultipleItems,
      Others: Object.values(groupWithSingleItem).flat(),
    };
  }, [agentStatusList]);

  return (
    <div className="flex flex-col gap-2 size-full">
      <div className="w-full pr-4">
        <Input
          type="text"
          size="s"
          beforeIcon={<Icon name="search" />}
          placeholder="Search Agent Status"
          value={searchValue}
          onChange={handleInputChange}
        />
      </div>
      <div className="flex flex-1 h-0 gap-x-4 overflow-y-auto">
        <div className="flex flex-col size-full gap-y-2">
          {Object.entries(formattedList).map(([key, list]) => {
            const showList = list?.filter((item: TAgentStatusProps) =>
              filteredList.some(
                (fItem: TAgentStatusProps) => fItem.id === item.id
              )
            );
            if (showList.length === 0) return null;
            return (
              <div
                className="flex flex-col size-full gap-y-2"
                key={`agent-category-${key}`}
              >
                <div className="w-full flex gap-x-2 text-footnote text-grey-400 items-center">
                  {`${key}`}
                  <div className="w-full h-px bg-grey-200" />
                </div>
                <div className="flex flex-wrap gap-2 justify-start">
                  {showList?.map((item: TAgentStatusProps) => {
                    return (
                      <Pill
                        variant="person"
                        key={item.id}
                        onClick={() =>
                          selectGroup(
                            item.id,
                            item.languageLabelEnUs,
                            AGENT_STATUS_COLOR_MAP[
                              item.systemPresence?.toLowerCase()
                            ]
                          )
                        }
                        active={agentStatus === item.languageLabelEnUs}
                      >
                        <div className="relative flex flex-col items-start leading-tight">
                          <div className="flex gap-1 items-center">
                            <div
                              style={{
                                backgroundColor:
                                  AGENT_STATUS_COLOR_MAP[
                                    item.systemPresence?.toLowerCase()
                                  ],
                              }}
                              className={`size-[10px] rounded-full`}
                            />
                            <div className="text-remark">
                              {item.languageLabelEnUs}
                            </div>
                          </div>
                        </div>
                      </Pill>
                    );
                  })}
                </div>
              </div>
            );
          })}
          {filteredList.length === 0 && (
            <div className="text-body">No specific result(s).</div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ToolbarDirectory;
