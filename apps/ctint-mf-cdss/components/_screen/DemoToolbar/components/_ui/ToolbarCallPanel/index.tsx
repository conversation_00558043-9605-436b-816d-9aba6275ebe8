import { useTbarContext } from '@cdss-modules/design-system/context/TBarContext';
import { TCallControlType } from '@cdss-modules/design-system/@types';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import CallControlButton from '@cdss-modules/design-system/components/_ui/CallControlButton';
import { cn } from '@cdss-modules/design-system/lib/utils';
import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useHandleInteractionData } from '@cdss-modules/design-system/lib/hooks/useHandleInteractionData';
import { useCallControl } from '@cdss-modules/design-system/lib/hooks/useCallControl';
export const ToolbarCallPanel = () => {
  const { callConverSationControl, conference } = useCallControl();
  const {
    activeModal,
    openToolbarModal,
    closeToolbarModal,
    selectedInteraction,
    refreshInteractions,
  } = useTbarContext();

  const {
    conversationId,
    isConference,
    fullConference,
    agentData,
    customerData,
    isHistory,
    consultInitiator,
    tBarStatus,
  } = useHandleInteractionData(selectedInteraction);

  const isDirectoryOpen = activeModal === 'outbound-panel';
  const queryClient = useQueryClient();

  const invalidateDirectory = () => {
    queryClient.invalidateQueries({ queryKey: ['call-control-directory-all'] });
  };

  const handleCallControl = (type: string) => {
    let contorlType: TCallControlType = 'mute';
    if (type == 'muteAndUnMute') {
      contorlType = agentData?.muted ? 'unmute' : 'mute';
    }
    if (type == 'holdAndUnHold') {
      contorlType = agentData?.held ? 'unhold' : 'hold';
    }
    callConverSationControl({
      type: contorlType,
      conversationId,
      agentParticipantId: agentData.id,
    });
  };
  return (
    <div className="flex items-center gap-x-4 cdssctr-controlbar__call-btns">
      <CallControlButton
        tooltip={agentData?.muted ? 'Unmute' : 'Mute'}
        icon={
          <Icon
            name="mute"
            size={25}
          />
        }
        className={cn('size-10')}
        active={agentData?.muted}
        handleOnChange={() => handleCallControl('muteAndUnMute')}
        disabled={
          !selectedInteraction || isHistory || tBarStatus == 'disconnected'
        }
      />
      <CallControlButton
        id="toolbar-hold-btn"
        tooltip={agentData?.held ? 'Unhold' : 'Hold'}
        icon={
          <Icon
            name="hold"
            size={22}
          />
        }
        className={cn('size-10')}
        active={agentData?.held}
        handleOnChange={() => handleCallControl('holdAndUnHold')}
        disabled={
          !selectedInteraction || isHistory || tBarStatus == 'disconnected'
        }
      />
      <CallControlButton
        tooltip={'Transfer'}
        icon={
          <Icon
            name="transfer"
            size={25}
          />
        }
        className={cn('size-10')}
        active={activeModal === 'transfer-panel'}
        handleOnChange={() =>
          activeModal === 'transfer-panel'
            ? closeToolbarModal()
            : openToolbarModal('transfer-panel')
        }
        disabled={
          !selectedInteraction ||
          isConference ||
          isHistory ||
          tBarStatus == 'disconnected'
        }
      />
      <CallControlButton
        tooltip={'Transfer to IVR'}
        icon={
          <Icon
            name="ivr"
            size={25}
          />
        }
        className={cn('size-10')}
        active={activeModal === 'toolbar-ivr-panel'}
        handleOnChange={() => openToolbarModal('toolbar-ivr-panel')}
        disabled={
          !selectedInteraction ||
          isConference ||
          isHistory ||
          tBarStatus == 'disconnected'
        }
      />
      <CallControlButton
        tooltip={'Conference'}
        icon={
          <Icon
            name="conference"
            size={25}
          />
        }
        className={cn('size-10')}
        active={fullConference}
        disabled={
          !isConference ||
          isHistory ||
          consultInitiator === 'consult' ||
          tBarStatus == 'disconnected'
        }
        onClick={() =>
          conference('BOTH', conversationId, customerData?.id || '')
        }
      />
      <CallControlButton
        tooltip={'Call'}
        icon={
          <Icon
            name="secondCall"
            size={25}
          />
        }
        className={cn('size-10')}
        active={isDirectoryOpen}
        onClick={() => {
          invalidateDirectory();
          isDirectoryOpen
            ? closeToolbarModal()
            : openToolbarModal('outbound-panel');
        }}
      />
      <CallControlButton
        tooltip={'Dialpad'}
        onClick={() => {
          activeModal === 'dialpad-panel'
            ? closeToolbarModal()
            : openToolbarModal('dialpad-panel');
        }}
        icon={
          <Icon
            name="pad"
            size={25}
          />
        }
        className={cn('size-10')}
        active={activeModal === 'dialpad-panel'}
        handleOnChange={() => null}
      />
    </div>
  );
};

export default ToolbarCallPanel;
