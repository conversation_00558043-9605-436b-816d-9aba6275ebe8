'use client';

import { useState } from 'react';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { cn } from '@cdss-modules/design-system/lib/utils';

type TToolbarDialPadProps = {
  value?: string[];
  buttonClassName?: string;
};

const keys = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '*', '0', '#'];

const ToolbarDialPad: React.FC<TToolbarDialPadProps> = ({
  value,
  buttonClassName,
}) => {
  const [inputValue, setInputValue] = useState<any>(value);

  const handleInputValue = (e: any) => {
    // setInputValue([...inputValue, e]);
  };

  const formattedValue = inputValue?.length > 0 && inputValue?.join('');

  return (
    <div className="flex flex-col justify-center items-center gap-4 text-t6">
      <Input
        value={inputValue?.length > 0 ? formattedValue : ''}
        onFocus={() => {
          return null;
        }}
        readOnly
        containerClassName="bg-grey-100 border-none rounded-lg hidden"
        className="focus:shadow-none cursor-not-allowed"
      />
      <div className="flex flex-col gap-y-4 max-w-[465px]">
        <div className="grid grid-cols-3 gap-x-6 gap-y-2">
          {keys.map((key) => (
            <button
              key={`key-${key}`}
              className={cn(
                'w-8 h-8 flex justify-center items-center rounded-full bg-grey-200 hover:bg-grey-300 active:bg-grey-400 font-bold',
                key === '*' && 'pt-3',
                buttonClassName
              )}
              onClick={() => handleInputValue(key)}
            >
              {key}
            </button>
          ))}
          {/* <button
            className={cn(
              'w-8 h-8 flex justify-center items-center rounded-full hover:border-black hover:border active:bg-primary-500 active:shadow-button-primary active:border-none'
            )}
            onClick={removeValue}
          >
            <Icon
              name="backspace"
              size={32}
            />
          </button>
          <button
            className={cn(
              'w-8 h-8 flex justify-center items-center rounded-full ',
              pickup ? 'bg-status-danger' : 'bg-status-success'
            )}
            onClick={() => setPickup(!pickup)}
          >
            {pickup ? (
              <Icon
                name="pickup"
                size={16}
              />
            ) : (
              <Icon
                name="phone"
                size={16}
              />
            )}
          </button>
          <div
            className={cn(
              'w-8 h-8'
            )}
          /> */}
        </div>
      </div>
    </div>
  );
};

export default ToolbarDialPad;
