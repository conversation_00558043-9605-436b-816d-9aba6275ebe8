/* eslint-disable @nx/enforce-module-boundaries */
import { useState, useRef } from 'react';
import {
  ResizableHandle,
  ResizablePanel,
  Tooltip,
} from '@cdss-modules/design-system';
import { cn } from '@cdss-modules/design-system/lib/utils';
import { ImperativePanelHandle } from 'react-resizable-panels';

export type TToolbarFlexiblePanelProps = {
  icon?: React.ReactNode;
  tooltip?: string;
  children?: React.ReactNode;
  minSize?: number;
  maxSize?: number;
};

export const ToolbarFlexiblePanel = ({
  icon,
  children,
  tooltip,
  minSize = 15,
  maxSize,
}: TToolbarFlexiblePanelProps) => {
  const [toolbarPanelCollapsed, setToolbarPanelCollapsed] = useState(false);
  const toolbarPanelRef = useRef<ImperativePanelHandle>(null);

  const onClickExpand = () => {
    toolbarPanelRef.current?.expand();
  };

  return (
    <>
      <ResizableHandle className="w-0" />
      <ResizablePanel
        collapsible={true}
        collapsedSize={2}
        minSize={minSize}
        maxSize={maxSize}
        defaultSize={30}
        ref={toolbarPanelRef}
        onCollapse={() => {
          setToolbarPanelCollapsed(true);
        }}
        onExpand={() => {
          setToolbarPanelCollapsed(false);
        }}
      >
        <div
          className={cn(
            'h-full flex items-center',
            !toolbarPanelCollapsed && 'hidden'
          )}
        >
          <Tooltip
            trigger={
              <button
                className="w-full h-full flex items-center justify-center whitespace-nowrap bg-primary-200"
                onClick={onClickExpand}
              >
                {icon || (
                  <div
                    className={cn(
                      'w-0 h-0 border-t-[8px] border-t-transparent border-r-[8px] border-r-black border-b-[8px] border-b-transparent'
                    )}
                  />
                )}
              </button>
            }
            content={tooltip || 'Drag to expand'}
            side="left"
          />
        </div>
        <div className={cn('w-full h-full', toolbarPanelCollapsed && 'hidden')}>
          {children}
        </div>
      </ResizablePanel>
    </>
  );
};

export default ToolbarFlexiblePanel;
