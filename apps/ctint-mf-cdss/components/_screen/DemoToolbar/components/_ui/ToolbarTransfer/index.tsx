/* eslint-disable @nx/enforce-module-boundaries */
import ToolbarDirectory from '../ToolbarDirectory';
import { useState } from 'react';

export const ToolbarTransfer = () => {
  const [selected, setSelected] = useState<any>(null);
  return (
    <div className="size-full">
      <ToolbarDirectory
        callBy="id"
        selected={selected}
        onChangeInput={() => {
          if (selected) {
            setSelected(null);
          }
        }}
        onSelect={(item: any) => setSelected(item)}
      />
    </div>
  );
};

export default ToolbarTransfer;
