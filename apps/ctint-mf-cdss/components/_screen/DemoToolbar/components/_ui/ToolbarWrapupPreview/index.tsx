import Button from '@cdss-modules/design-system/components/_ui/Button';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { TSubmitWrapup, TWrapupItem } from '@cdss-modules/design-system/@types';
import { fireSubmitWrapupCode, fireUpdateWrapupCode } from '@cdss/lib/api';
import { useTbarContext } from '@cdss-modules/design-system/context/TBarContext';
import { basePath } from '../../../../../../lib/appConfig/index';
import { useMemo, useState } from 'react';
import { cn } from '@cdss-modules/design-system/lib/utils';
import { useHandleInteractionData } from '@cdss-modules/design-system/lib/hooks/useHandleInteractionData';
import { toast } from '@cdss-modules/design-system';
type TWrapupPreviewProps = {
  onChangeWrapupPreview: () => void;
  updateSelectedCategory: (value: string | null) => void;
  updateSelectedGroup: (value: string | null) => void;
  updateShowTextarea: (data: any) => void;
};

type TTreeWrapupContainerProps = {
  item: TSubmitWrapup;
};

const ToolbarWrapupPreview = ({
  onChangeWrapupPreview,
  updateSelectedCategory,
  updateSelectedGroup,
  updateShowTextarea,
}: TWrapupPreviewProps) => {
  const {
    wrapupContext: {
      selectLastActiveAgentId,
      selectLastActiveConvId,
      setShowWrapup,
      selectedWrapupList,
      wrapupCategoryFullListHandle,
      updateSelectedWrapupId,
      updateSelectedWrapupList,
    },
    updateInteractionFilter,
    selectedInteraction,
    closeToolbarModal,
    removeInteraction,
    conversationHistoryListHandle,
  } = useTbarContext();
  const { agentData, consultData, isConference, conversationId } =
    useHandleInteractionData(selectedInteraction);
  const initialNotes =
    agentData?.wrapUps?.wrapUpList?.find((v: any) => v?.wrapUpCode === '')
      ?.remark || '';
  const [notes, setNotes] = useState<string>(initialNotes);
  const findAllLevelWrapup = (
    data: TWrapupItem[],
    selectedValue: TSubmitWrapup
  ): TSubmitWrapup | null => {
    for (const category of data) {
      // this is for category has no options
      if (
        category?.type === 'CATEGORY' &&
        category?.code === selectedValue?.wrapUpCode
      ) {
        return {
          categoryName: category.name,
          wrapUpName: selectedValue?.wrapUpName,
          wrapUpCode: selectedValue?.wrapUpCode,
          remark: selectedValue?.remark,
        };
      }

      if (category?.type === 'CATEGORY') {
        for (const group of category.items!) {
          // this is for group has no options
          if (!group.items || group.items?.length === 0) {
            return {
              categoryName: category.name,
              groupName: group.name,
              wrapUpName: selectedValue?.wrapUpName,
              wrapUpCode: selectedValue?.wrapUpCode,
              remark: selectedValue?.remark,
            };
          }

          if (group.type === 'GROUP' && group.items) {
            for (const code of group.items) {
              if (code.code === selectedValue.wrapUpCode) {
                return {
                  categoryName: category.name,
                  groupName: group.name,
                  codeName: code.name,
                  wrapUpName: selectedValue?.wrapUpName,
                  wrapUpCode: selectedValue?.wrapUpCode,
                  remark: selectedValue?.remark,
                };
              }
            }
          }
        }
      }
    }
    return null; // Return null if not found
  };

  const wrapupCategory = useMemo(() => {
    if (agentData?.queue?.id) {
      return (
        wrapupCategoryFullListHandle?.filter((item: any) => {
          return item?.queueId === agentData?.queue?.id;
        })?.[0]?.items || []
      );
    }
    return wrapupCategoryFullListHandle?.flatMap(
      (queueItem: any) => queueItem?.items || []
    );
  }, [wrapupCategoryFullListHandle, agentData]);

  const treeWrapup = selectedWrapupList
    ?.map((wrapup: TSubmitWrapup) => findAllLevelWrapup(wrapupCategory, wrapup))
    .filter((item: any) => item !== null);

  const TreeWrapupContainer = ({ item }: TTreeWrapupContainerProps) => {
    return (
      <div className="mix-w-[165px] ">
        <div className="p-2 font-bold bg-primary-300">{item?.categoryName}</div>
        {(item.groupName || item.remark) && (
          <div className="flex flex-col min-h-12 p-2 bg-primary-100 overflow-y-auto">
            {item.groupName && (
              <div className="flex items-center gap-2">
                <Icon
                  name="dropdown-arrow"
                  size={6}
                  className="rotate-180"
                />
                <div className="whitespace-nowrap">{item?.groupName}</div>
              </div>
            )}

            {item.codeName && (
              <div className="flex items-center gap-2 ml-4">
                <Icon
                  name="dropdown-arrow"
                  size={6}
                  className="rotate-180"
                />
                <div className="whitespace-nowrap">{item?.codeName}</div>
              </div>
            )}
            <div
              className={cn(
                'flex whitespace-nowrap ml-10',
                item.groupName && item.codeName ? 'ml-10' : 'ml-0'
              )}
            >
              {item?.remark}
            </div>
          </div>
        )}
      </div>
    );
  };

  const existedWrapup = selectedWrapupList.some((wrapup) => wrapup.id);
  const handleWrapUp = () => {
    const apiFunc = existedWrapup ? fireUpdateWrapupCode : fireSubmitWrapupCode;
    apiFunc(
      basePath,
      selectedWrapupList,
      agentData?.id || selectLastActiveAgentId,
      conversationId || selectLastActiveConvId,
      notes
    )
      .then((res) => {
        if (res.data.isSuccess) {
          conversationHistoryListHandle.refetch();
          setShowWrapup(false);
          updateSelectedWrapupList([]);
          updateInteractionFilter('current');
          updateSelectedWrapupId(null);
          onChangeWrapupPreview();
          updateSelectedCategory(null);
          updateSelectedGroup(null);
          updateShowTextarea({
            type: null,
            isOpen: false,
          });
          setNotes('');
          removeInteraction(conversationId);
          isConference && removeInteraction(consultData?.id);
          closeToolbarModal();
        }
      })
      .catch((error) => {
        return toast({
          variant: 'error',
          title: 'Error',
          description: `${error.response.data.error}`,
        });
      });
  };
  return (
    <div className="flex flex-col w-full h-full">
      <div className="flex items-center justify-between p-2 border-b border-primary-300">
        <div className="font-bold">Selected wrap-up codes</div>
        <div className="flex gap-2">
          <Button
            onClick={onChangeWrapupPreview}
            variant={'primary'}
            size={'mini'}
          >
            <Icon
              name="undo"
              size={12}
              className="text-white"
            />
          </Button>
          <Button
            onClick={handleWrapUp}
            variant={'primary'}
            size={'mini'}
          >
            <Icon
              name="check"
              size={12}
              className="text-white"
            />
          </Button>
        </div>
      </div>
      <div className="flex w-full h-full overflow-hidden">
        <div className="flex p-2 min-w-[66%] gap-2 overflow-y-auto">
          {treeWrapup.map((wrapup: any) => {
            return (
              <TreeWrapupContainer
                key={wrapup.wrapUpCode}
                item={wrapup}
              />
            );
          })}
        </div>
        <div className="w-1/3 h-full border-l border-primary-300">
          <textarea
            value={notes}
            onChange={(e: any) => setNotes(e.target.value)}
            className="w-full h-[95%] p-2 outline-none"
            placeholder="remarks..."
          />
        </div>
      </div>
    </div>
  );
};

export default ToolbarWrapupPreview;
