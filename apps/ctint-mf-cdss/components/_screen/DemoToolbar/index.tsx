/* eslint-disable @nx/enforce-module-boundaries */
import Toolbar from './components/Toolbar';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
export const DemoToolBarBody = () => {
  return (
    <div className="flex flex-col h-full gap-y-4 overflow-auto">
      <div className="flex flex-col h-full gap-y-4 overflow-auto">
        <Toolbar />
      </div>
    </div>
  );
};

// Create a client
const queryClient = new QueryClient();

const DemoToolBar = () => {
  return (
    <QueryClientProvider client={queryClient}>
        <DemoToolBarBody />
    </QueryClientProvider>
  );
};

export default DemoToolBar;
