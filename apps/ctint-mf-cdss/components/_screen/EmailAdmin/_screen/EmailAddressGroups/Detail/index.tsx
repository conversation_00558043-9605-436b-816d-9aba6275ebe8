import React, { useState, useEffect, ChangeEvent, MouseEvent } from 'react';
import {
  Button,
  useToast,
  // Assuming Input, Label, Badge are available or will be replaced by standard elements
  // Input,
  // Label,
  // Badge,
} from '@cdss-modules/design-system';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { Trash, X } from 'lucide-react'; // Using X from lucide-react
import {
  useEmailAddressGroupsFormStore,
  TEmailAddressGroupsFormData,
} from '../../../_store/emailAddressGroupsFormStore'; // Corrected path again
import { useEmailNavigationStore } from '../../../_store/emailNavigationStore'; // Corrected path again
import { useQueryClient } from '@tanstack/react-query';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';

// Placeholder for design system components if not found
const Label = (props: React.LabelHTMLAttributes<HTMLLabelElement>) => (
  <label {...props} />
);

interface EmailAddressGroupsDetailProps {
  isPickerMode?: boolean;
  onGroupSelected?: (groupId: string, groupName: string) => void;
  onPickerActionCompleted?: () => void;
  onPickerCancel?: () => void;
}

const EmailAddressGroupsDetail: React.FC<EmailAddressGroupsDetailProps> = ({
  isPickerMode = false,
  onGroupSelected,
  onPickerActionCompleted,
  onPickerCancel,
}) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const {
    formData,
    isNew,
    validationErrors,
    updateField,
    addEmail,
    removeEmail,
    saveForm,
    resetForm,
    loadExistingData,
    isEditingEmails,
    emailsInEdit,
    startEditEmails,
    updateEmailInEdit,
    saveEditedEmails,
    cancelEditEmails,
    removeEmailFromEdit,
    // CSV Import related store items
    stagedForImportGroup,
    stagedForImportValidation,
    setStagedEmailsForImport,
    updateStagedEmailForImport,
    removeStagedEmailForImport,
    confirmAndAddImportedEmails,
    cancelImportProcess,
  } = useEmailAddressGroupsFormStore();

  const { backToList } = useEmailNavigationStore();
  const queryClient = useQueryClient();

  const [emailInput, setEmailInput] = useState('');
  const [importedFile, setImportedFile] = useState<File | null>(null); // To store the selected file

  const handleSave = async () => {
    const result = await saveForm();
    if (result.success) {
      // Show success message
      toast({
        title: t(
          'ctint-mf-cdss.emailAdmin.emailAddressGroups.common.saveSuccess'
        ),
        description: t(
          'ctint-mf-cdss.emailAdmin.emailAddressGroups.common.saveSuccessDescription'
        ),
        variant: 'success',
      });

      // Invalidate cache for email address groups to ensure fresh data
      queryClient.invalidateQueries({ queryKey: ['emailAddressGroups'] });

      // Also invalidate the specific group cache if we have an ID
      if (result.id) {
        queryClient.invalidateQueries({
          queryKey: ['emailAddressGroup', result.id],
        });
      }

      if (isPickerMode && onGroupSelected && result.id && result.name) {
        onGroupSelected(result.id, result.name);

        if (onPickerActionCompleted) {
          onPickerActionCompleted();
        }
      } else if (!isPickerMode) {
        console.log(
          '[Detail] handleSave: Normal mode - navigating back to list.'
        );
        backToList('email-address-groups');
      } else {
        console.log(
          '[Detail] handleSave: Save was successful, but picker mode conditions not met.'
        );

        // Fallback: if we're in picker mode but missing some data, try to call with available data
        if (isPickerMode && onGroupSelected && (result.id || result.name)) {
          onGroupSelected(result.id || '', result.name || '');
          if (onPickerActionCompleted) {
            onPickerActionCompleted();
          }
        }
      }
    } else {
      // Check if we have a specific error message from the API
      if (result.error) {
        // Show the specific error message from the API response
        toast({
          title: t(
            'ctint-mf-cdss.emailAdmin.emailAddressGroups.common.saveError'
          ),
          description: result.error,
          variant: 'error',
        });
      } else {
        // Show error message for validation failures
        const errors = Object.keys(validationErrors);
        if (errors.length > 0) {
          // Validation errors
          toast({
            title: t(
              'ctint-mf-cdss.emailAdmin.emailAddressGroups.common.validationError'
            ),
            description: t(
              'ctint-mf-cdss.emailAdmin.emailAddressGroups.common.validationErrorDescription'
            ),
            variant: 'error',
          });
        } else {
          // General save error fallback
          toast({
            title: t(
              'ctint-mf-cdss.emailAdmin.emailAddressGroups.common.saveError'
            ),
            description: t(
              'ctint-mf-cdss.emailAdmin.emailAddressGroups.common.saveErrorDescription'
            ),
            variant: 'error',
          });
        }
      }
    }
  };

  const handleCancel = () => {
    resetForm();
    if (isPickerMode && onPickerCancel) {
      onPickerCancel(); // e.g., close popover
    } else {
      backToList('email-address-groups');
    }
  };

  const addSingleEmail = () => {
    if (emailInput.trim() !== '') {
      addEmail(emailInput.trim());
      setEmailInput('');
    }
  };

  const onAddEmailClick = (event: MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
    addSingleEmail();
  };

  const onEmailInputKeyPress = (
    event: React.KeyboardEvent<HTMLInputElement>
  ) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      addSingleEmail();
    }
  };

  const handleSaveEmailChanges = () => {
    saveEditedEmails(); // This will set isEditingEmails to false on success
  };

  const handleCancelEmailChanges = () => {
    cancelEditEmails();
  };

  const handleExportCSV = () => {
    // Placeholder for CSV export logic
    console.log('Export CSV clicked');
    const csvContent = 'Email\<EMAIL>\<EMAIL>';
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', 'email_template.csv');
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const onImportFileSelected = (event: ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setImportedFile(event.target.files[0]);
      // Placeholder: Immediately try to parse or wait for a button click
      // For now, let's assume we parse on button click
      console.log('File selected:', event.target.files[0].name);
    }
  };

  const parseAndStageImportFile = async () => {
    if (!importedFile) {
      alert('Please select a file first.');
      return;
    }
    // Placeholder for file parsing logic (e.g., using papaparse or xlsx)
    console.log('Processing imported file:', importedFile.name);
    // Simulating parsing
    // In a real scenario, you'd read the file content and parse it.
    // For CSV: use a library like Papaparse.
    // For Excel: use a library like XLSX (sheetjs).
    // This example assumes a simple CSV with one email per line in the first column.
    const reader = new FileReader();
    reader.onload = async (e) => {
      const text = e.target?.result as string;
      // Basic CSV parsing: split by lines, assuming first line is header
      const lines = text
        .split('\n')
        .map((line) => line.trim())
        .filter((line) => line);
      if (lines.length > 0) {
        // Process all lines if file has content
        const emailsFromFile = lines
          .slice(lines[0].toLowerCase().includes('email') ? 1 : 0) // Skip header if 'email' is present
          .map((email) => email.split(',')[0].trim()) // Take first column, trim
          .filter((email) => email); // Filter out empty strings after processing

        // setStagedEmails(emailsFromFile); // REMOVED
        setStagedEmailsForImport(emailsFromFile); // USE STORE ACTION
        console.log('Staged emails for import via store:', emailsFromFile);
        if (emailsFromFile.length === 0 && lines.length > 0) {
          alert(
            'No valid email data found after parsing. Please check file content and format.'
          );
        }
      } else {
        alert('File is empty or does not contain processable data.');
        // setStagedEmails([]); // REMOVED
        setStagedEmailsForImport([]); // USE STORE ACTION (to clear/validate)
      }
    };
    reader.onerror = () => {
      alert('Failed to read file.');
      // setStagedEmails([]); // REMOVED
      setStagedEmailsForImport([]); // USE STORE ACTION
    };
    reader.readAsText(importedFile); // Reading as text for CSV
  };

  const handleConfirmImportedEmails = () => {
    const success = confirmAndAddImportedEmails(); // USE STORE ACTION
    if (success) {
      // setStagedEmails([]); // REMOVED
      setImportedFile(null);
      const fileInput = document.getElementById(
        'importEmailFile'
      ) as HTMLInputElement;
      if (fileInput) fileInput.value = '';
      console.log('Confirmed and added imported emails to group');
    } else {
      // Errors should be displayed via the component reacting to stagedForImportValidation state
      console.log(
        'Failed to confirm imported emails due to validation issues.'
      );
      // Optionally, alert the user if the UI isn't clear enough
      // alert("Please review the highlighted errors in the staged email list.");
    }
  };

  const handleCancelImport = () => {
    // setStagedEmails([]); // REMOVED
    cancelImportProcess(); // USE STORE ACTION
    setImportedFile(null);
    const fileInput = document.getElementById(
      'importEmailFile'
    ) as HTMLInputElement;
    if (fileInput) fileInput.value = '';
    console.log('Cancelled import');
  };

  return (
    <div className="p-4 flex flex-col gap-6 h-full bg-white rounded-xl overflow-y-auto">
      <div className="flex justify-between gap-2">
        <h1 className="text-base font-bold">
          {isNew
            ? t(
                'ctint-mf-cdss.emailAdmin.emailAddressGroups.detail.createTitle'
              )
            : t('ctint-mf-cdss.emailAdmin.emailAddressGroups.detail.editTitle')}
        </h1>
        <div className="flex gap-2">
          <Button
            onClick={handleCancel}
            variant="secondary"
            size="s"
          >
            {t('ctint-mf-cdss.emailAdmin.emailAddressGroups.common.cancel')}
          </Button>
          <Button
            size="s"
            onClick={handleSave}
            disabled={
              !useEmailAddressGroupsFormStore.getState().isDirty && !isNew
            }
          >
            {t('ctint-mf-cdss.emailAdmin.emailAddressGroups.detail.saveGroup')}
          </Button>
        </div>
      </div>
      <div className="flex flex-col gap-2">
        <Label
          htmlFor="groupName"
          className="text-sm font-medium text-gray-700"
        >
          {t('ctint-mf-cdss.emailAdmin.emailAddressGroups.common.groupName')}
        </Label>
        <Input
          id="groupName"
          value={formData.name}
          onChange={(value: string | number) =>
            updateField('name', String(value))
          }
          placeholder={t(
            'ctint-mf-cdss.emailAdmin.emailAddressGroups.detail.groupNamePlaceholder'
          )}
          status={validationErrors.name ? 'danger' : undefined}
          message={validationErrors.name}
          size="s"
        />
        {validationErrors.name && (
          <p className="text-red-500 text-sm">{validationErrors.name}</p>
        )}
      </div>

      <div
        className={`flex flex-col gap-2 ${isEditingEmails ? 'opacity-50 pointer-events-none' : ''}`}
      >
        <Label
          htmlFor="emailInput"
          className="text-sm font-medium text-gray-700"
        >
          {t(
            'ctint-mf-cdss.emailAdmin.emailAddressGroups.detail.addEmailAddressLabel'
          )}
        </Label>
        <div className="flex gap-2">
          <Input
            id="emailInput"
            type="email"
            value={emailInput}
            onChange={(value: string | number) => setEmailInput(String(value))}
            onKeyDown={onEmailInputKeyPress}
            placeholder={t(
              'ctint-mf-cdss.emailAdmin.emailAddressGroups.detail.emailAddressPlaceholder'
            )}
            status={validationErrors.email ? 'danger' : undefined}
            message={validationErrors.email}
            className="flex-grow"
            size="s"
          />
          <Button
            className="h-fit"
            onClick={onAddEmailClick}
            variant="secondary"
            size="s"
          >
            {t('ctint-mf-cdss.emailAdmin.emailAddressGroups.common.add')}
          </Button>
        </div>
        {validationErrors.email && (
          <p className="text-red-500 text-sm">{validationErrors.email}</p>
        )}
        {validationErrors.emails && !validationErrors.email && (
          <p className="text-red-500 text-sm">{validationErrors.emails}</p>
        )}
      </div>

      {/* Email Import/Export Section */}
      <div className="flex flex-col gap-4 py-4 border-t border-b border-gray-200">
        <div className="flex justify-between items-center">
          <Label className="text-sm font-medium text-gray-700">
            {t(
              'ctint-mf-cdss.emailAdmin.emailAddressGroups.detail.importExportSection'
            )}
          </Label>
          <Button
            onClick={handleExportCSV}
            variant="secondary"
            size="s"
          >
            {t(
              'ctint-mf-cdss.emailAdmin.emailAddressGroups.detail.downloadCsvTemplate'
            )}
          </Button>
        </div>
        <div className="flex flex-col gap-2">
          <Label
            htmlFor="importEmailFile"
            className="text-sm text-gray-600"
          >
            {t(
              'ctint-mf-cdss.emailAdmin.emailAddressGroups.detail.importFromCsvExcel'
            )}
          </Label>
          <div className="flex gap-2 items-center">
            <input
              id="importEmailFile"
              type="file"
              accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
              onChange={onImportFileSelected}
              className="cursor-pointer flex-grow border border-gray-300 rounded text-sm file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-gray-100 file:text-gray-700 hover:file:bg-gray-200 file:cursor-pointer"
            />
            <Button
              onClick={parseAndStageImportFile}
              disabled={!importedFile}
              variant="secondary"
              size="s"
            >
              {t(
                'ctint-mf-cdss.emailAdmin.emailAddressGroups.detail.loadEmailsFromFile'
              )}
            </Button>
          </div>
        </div>

        {stagedForImportGroup.length > 0 && (
          <div className="flex flex-col gap-3 mt-2 p-3 border border-gray-300 rounded-md bg-gray-50">
            <h4 className="text-sm font-medium text-gray-700">
              {t(
                'ctint-mf-cdss.emailAdmin.emailAddressGroups.detail.emailsStagedForImport',
                {
                  count: stagedForImportGroup.length,
                }
              )}
            </h4>
            {stagedForImportValidation &&
              stagedForImportValidation.errors.length > 0 && (
                <div className="p-2 mb-2 text-sm text-red-700 bg-red-100 border border-red-400 rounded">
                  <p className="font-semibold">
                    {t(
                      'ctint-mf-cdss.emailAdmin.emailAddressGroups.detail.correctIssuesBelow'
                    )}
                  </p>
                  <ul className="list-disc list-inside">
                    {/* Show a summary of unique error messages or types if needed */}
                    {/* For now, rely on per-email error display */}
                  </ul>
                </div>
              )}
            <div className="max-h-48 overflow-y-auto flex flex-col gap-2 pr-2">
              {stagedForImportGroup.map((email, index) => {
                const emailErrors = stagedForImportValidation?.errors.filter(
                  (err) => err.index === index
                );
                const hasError = emailErrors && emailErrors.length > 0;
                return (
                  <div
                    key={`staged-${index}`}
                    className="flex flex-col gap-1"
                  >
                    <div className="flex items-center gap-2">
                      <Input
                        type="email"
                        value={email}
                        onChange={(value: string | number) => {
                          // const updatedStagedEmails = [...stagedEmails];
                          // updatedStagedEmails[index] = String(value);
                          // setStagedEmails(updatedStagedEmails);
                          updateStagedEmailForImport(index, String(value)); // USE STORE ACTION
                        }}
                        className={`flex-grow ${hasError ? 'border-red-500' : ''}`}
                        size="s"
                        status={hasError ? 'danger' : undefined}
                      />
                      <Button
                        variant="blank"
                        size="xs"
                        onClick={() => {
                          // const updatedStagedEmails = stagedEmails.filter(
                          //   (_, i) => i !== index
                          // );
                          // setStagedEmails(updatedStagedEmails);
                          removeStagedEmailForImport(index); // USE STORE ACTION
                        }}
                        aria-label="Remove staged email"
                      >
                        <X
                          size={16}
                          className="text-red-500"
                        />
                      </Button>
                    </div>
                    {hasError &&
                      emailErrors.map((err) => (
                        <p
                          key={`${err.message}-${err.value}`}
                          className="text-xs text-red-600 pl-1"
                        >
                          {err.message}{' '}
                          {err.value !== email && `(Value: "${err.value}")`}
                        </p>
                      ))}
                  </div>
                );
              })}
            </div>
            <div className="flex justify-end gap-2 mt-2">
              <Button
                onClick={handleCancelImport}
                variant="secondary"
                size="s"
              >
                {t(
                  'ctint-mf-cdss.emailAdmin.emailAddressGroups.detail.cancelImport'
                )}
              </Button>
              <Button
                onClick={handleConfirmImportedEmails}
                size="s"
                disabled={
                  !stagedForImportValidation?.isValid ||
                  stagedForImportGroup.length === 0
                }
              >
                {t(
                  'ctint-mf-cdss.emailAdmin.emailAddressGroups.detail.confirmAddToGroup'
                )}
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Unified Email List Section */}
      <div className="flex flex-col gap-3">
        {/* Header: Title and Action Buttons */}
        <div className="flex justify-between items-center">
          <h3 className="text-sm font-medium text-gray-700">
            {isEditingEmails
              ? t(
                  'ctint-mf-cdss.emailAdmin.emailAddressGroups.detail.editingEmails',
                  {
                    count: emailsInEdit.length,
                  }
                )
              : t(
                  'ctint-mf-cdss.emailAdmin.emailAddressGroups.detail.addedEmails',
                  {
                    count: formData.emails.length,
                  }
                )}
            :
          </h3>
          {isEditingEmails && validationErrors.emailsInEdit && (
            <p
              className="text-red-500 text-sm flex-grow mx-2 text-right" // Adjusted for better layout
              dangerouslySetInnerHTML={{
                __html: validationErrors.emailsInEdit.replace(/; /g, '<br />'),
              }}
            />
          )}
          <div className="flex justify-end gap-2">
            {isEditingEmails ? (
              <>
                <Button
                  onClick={handleCancelEmailChanges}
                  variant="secondary"
                  size="s"
                >
                  {t(
                    'ctint-mf-cdss.emailAdmin.emailAddressGroups.detail.cancelEdit'
                  )}
                </Button>
                <Button
                  onClick={handleSaveEmailChanges}
                  size="s"
                >
                  {t(
                    'ctint-mf-cdss.emailAdmin.emailAddressGroups.detail.saveEmailChanges'
                  )}
                </Button>
              </>
            ) : (
              formData.emails.length > 0 && ( // Only show if there are emails to edit
                <Button
                  onClick={startEditEmails}
                  variant="secondary"
                  size="s"
                >
                  {t(
                    'ctint-mf-cdss.emailAdmin.emailAddressGroups.detail.editEmails'
                  )}
                </Button>
              )
            )}
          </div>
        </div>

        {/* Conditional rendering for the list or "no emails" message */}
        {(isEditingEmails && emailsInEdit.length > 0) ||
        (!isEditingEmails && formData.emails.length > 0) ? (
          (isEditingEmails ? emailsInEdit : formData.emails).map(
            (email, index) => (
              <div
                key={`email-item-${index}`}
                className="flex items-center gap-2"
              >
                <Input
                  type="email"
                  value={email}
                  onChange={(value: string | number) => {
                    if (isEditingEmails) {
                      updateEmailInEdit(index, String(value));
                    }
                  }}
                  placeholder={
                    isEditingEmails ? `Email ${index + 1}` : undefined
                  }
                  status={
                    isEditingEmails &&
                    validationErrors.emailsInEdit?.includes(
                      `Email #${index + 1}`
                    )
                      ? 'danger'
                      : undefined
                  }
                  className="flex-grow disabled:text-black"
                  size="s"
                  disabled={!isEditingEmails} // Key change: input disabled based on mode
                />
                {isEditingEmails && ( // Show remove button only in edit mode
                  <Trash
                    className="text-red-500 hover:text-red-700 cursor-pointer"
                    onClick={() => removeEmailFromEdit(index)}
                    size={16}
                  />
                )}
              </div>
            )
          )
        ) : (
          <p className="text-sm text-gray-500 pl-1 pt-1">
            {isEditingEmails
              ? t(
                  'ctint-mf-cdss.emailAdmin.emailAddressGroups.detail.noEmailsToEdit'
                )
              : t(
                  'ctint-mf-cdss.emailAdmin.emailAddressGroups.detail.noEmailsAdded'
                )}
          </p>
        )}
      </div>
    </div>
  );
};

export default EmailAddressGroupsDetail;
