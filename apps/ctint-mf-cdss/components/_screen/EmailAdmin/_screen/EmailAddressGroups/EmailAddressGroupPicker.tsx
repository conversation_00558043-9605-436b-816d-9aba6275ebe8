import React, { useState, useEffect, useMemo } from 'react';
// import { Button } from '@cdss-modules/design-system'; // No longer directly needed by picker
// import { ChevronDown, ExternalLink } from 'lucide-react'; // Removed unused
import EmailAddressGroupsListing from './Listing';
import EmailAddressGroupsDetail from './Detail';
import { useEmailAddressGroupsFormStore } from '../../_store/emailAddressGroupsFormStore';
import { useEmailNavigationStore } from '../../_store/emailNavigationStore'; // For createNew/editDetail logic
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { cn } from '@cdss-modules/design-system/lib/utils';
import {
  Popup,
  PopupTrigger,
  PopupContent,
} from '@cdss-modules/design-system/components/_ui/Popup'; // Added Popup imports
import { useTranslation } from '@cdss-modules/design-system/i18n/client';

interface EmailAddressGroupPickerProps {
  // selectedGroupId: string | null; // Removed unused prop
  selectedGroupName: string | null;
  onGroupSelected: (groupId: string, groupName: string) => void;
  disabled?: boolean;
}

type PickerView = 'list' | 'detail';

const EmailAddressGroupPicker: React.FC<EmailAddressGroupPickerProps> = ({
  // selectedGroupId, // Removed unused prop
  selectedGroupName,
  onGroupSelected,
  disabled = false,
}) => {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [pickerView, setPickerView] = useState<PickerView>('list');
  const [pickerSelectedItemId, setPickerSelectedItemId] = useState<
    string | null
  >(null);

  // Get store actions for navigation within the picker
  const storeBackToList = useEmailNavigationStore((state) => state.backToList);

  // Memoize the display text to avoid unnecessary re-renders
  const displayText = useMemo(() => {
    return (
      selectedGroupName ||
      t(
        'ctint-mf-cdss.emailAdmin.emailAddressGroups.picker.selectGroupPlaceholder'
      )
    );
  }, [selectedGroupName, t]);

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
  };

  const handleSelectInListing = (groupId: string, groupName: string) => {
    onGroupSelected(groupId, groupName);
    setIsOpen(false); // Close popover on selection
  };

  const handleEditInListing = (groupId: string) => {
    setPickerSelectedItemId(groupId); // Keep track for the picker's detail view
    setPickerView('detail');
  };

  const handleCreateNewInListing = () => {
    useEmailAddressGroupsFormStore.getState().initNewForm();
    setPickerSelectedItemId(null);
    setPickerView('detail');
  };

  // This function is called by EmailAddressGroupsDetail when a group is selected/saved.
  // Its primary role is to inform the parent of the EmailAddressGroupPicker.
  const onDetailSelectGroup = (groupId: string, groupName: string) => {
    onGroupSelected(groupId, groupName);
  };

  // This function is called by EmailAddressGroupsDetail after it has successfully handled saving and selection.
  const closePickerAfterDetailAction = () => {
    setIsOpen(false);
    setPickerView('list');
    setPickerSelectedItemId(null);
    useEmailNavigationStore.getState().backToList('email-address-groups');
  };

  const handleCancelInDetail = () => {
    setPickerView('list');
    setPickerSelectedItemId(null);
    // Ensure the main navigation store also goes back to list for this tab
    // and the form is reset
    const resetForm = useEmailAddressGroupsFormStore.getState().resetForm;
    resetForm();
    useEmailNavigationStore.getState().backToList('email-address-groups');
  };

  const triggerButton = (
    <button
      onClick={() => setIsOpen(!isOpen)}
      disabled={disabled}
      className={cn(
        'flex w-full items-center justify-between rounded border border-grey-200 bg-white px-2 py-1 text-sm data-[state=open]:border data-[state=open]:border-primary-900 hover:border hover:border-primary-500 focus:outline-none focus:border focus:border-primary-900 focus:shadow-field disabled:cursor-not-allowed disabled:border-0 [&>span]:line-clamp-1',
        // Maintaining original height, can be adjusted if needed
        'h-8',
        selectedGroupName && 'border-black',
        disabled && 'bg-grey-100'
      )}
    >
      <span className={cn(disabled && 'text-black')}>{displayText}</span>
      <Icon
        name="dropdown-arrow"
        size={8}
        className={cn(
          'transition-all mt-[2px]',
          isOpen ? `rotate-180` : `rotate-0`,
          disabled && `text-grey-600`
        )}
      />
    </button>
  );

  return (
    <Popup
      open={isOpen}
      onOpenChange={setIsOpen}
    >
      <PopupTrigger asChild>{triggerButton}</PopupTrigger>
      <PopupContent
        title={t(
          'ctint-mf-cdss.emailAdmin.emailAddressGroups.picker.popupTitle'
        )}
        className="p-0 w-[580px] min-h-[400px] max-h-[600px] flex flex-col"
      >
        {pickerView === 'list' ? (
          <EmailAddressGroupsListing
            isPickerMode={true}
            onGroupSelect={handleSelectInListing}
            onCreateNew={handleCreateNewInListing}
            onEditItem={handleEditInListing}
          />
        ) : (
          <EmailAddressGroupsDetail
            isPickerMode={true}
            onGroupSelected={onDetailSelectGroup}
            onPickerActionCompleted={closePickerAfterDetailAction}
            onPickerCancel={handleCancelInDetail}
          />
        )}
      </PopupContent>
    </Popup>
  );
};

export default EmailAddressGroupPicker;
