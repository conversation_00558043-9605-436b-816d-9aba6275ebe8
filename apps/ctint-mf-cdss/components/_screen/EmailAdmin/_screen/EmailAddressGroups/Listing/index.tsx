import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@cdss-modules/design-system';
import { ChevronRight, Edit, Trash } from 'lucide-react';
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useEmailNavigationStore } from '../../../_store/emailNavigationStore';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import {
  Popup,
  PopupTrigger,
  PopupContent,
  PopupFooter,
  PopupClose,
} from '@cdss-modules/design-system/components/_ui/Popup';
import { useRole } from '@cdss-modules/design-system';
import { usePermission } from '@cdss-modules/design-system/context/PremissionContext';
import { CommonPermission } from '@cdss-modules/design-system/@types/CommonPermission';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import { fireGetFullAddressList } from 'apps/ctint-mf-cdss/lib/api';
import { useQuery } from '@tanstack/react-query';
import { basePath } from '@cdss/lib/appConfig';
import {
  useEmailAddressGroupsFormStore,
  TEmailAddressGroupsFormData,
} from '../../../_store/emailAddressGroupsFormStore';
import Pagination from '@cdss-modules/design-system/components/_ui/Pagination';
import { debounce } from 'lodash';

interface EmailAddressGroup {
  id: string;
  groupName: string;
  fullAddressList: string[];
  updateTime?: string;
}

interface ApiListResponseData {
  list: EmailAddressGroup[];
  total: number;
  totalPages?: number;
}

interface ApiFullResponse {
  success: boolean;
  code: number;
  data: ApiListResponseData;
  message?: string;
}

interface EmailAddressGroupsListingProps {
  isPickerMode?: boolean;
  onGroupSelect?: (groupId: string, groupName: string) => void;
  onCreateNew?: () => void;
  onEditItem?: (groupId: string) => void;
}

const EmailAddressGroupsListing: React.FC<EmailAddressGroupsListingProps> = ({
  isPickerMode = false,
  onGroupSelect,
  onCreateNew,
  onEditItem,
}) => {
  const { t } = useTranslation();
  const { createNew: storeCreateNew, editDetail: storeEditDetail } =
    useEmailNavigationStore();
  const { loadExistingData } = useEmailAddressGroupsFormStore();
  // queryClient can be used for cache invalidation if needed later for other operations
  // const queryClient = useQueryClient();

  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>(
    {}
  );
  const [searchType, setSearchType] = useState<string>('group');
  const [searchValue, setSearchValue] = useState<string>('');
  const [debouncedSearchValue, setDebouncedSearchValue] = useState<string>('');
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [groupToDelete, setGroupToDelete] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(50);

  const emailAdminPermission = new CommonPermission(
    useRole().globalConfig,
    usePermission().permissions
  );
  const canCreate = emailAdminPermission.isPermissionEnabled(
    'ctint-mf-admin',
    'email',
    'create'
  );
  const canEdit = emailAdminPermission.isPermissionEnabled(
    'ctint-mf-admin',
    'email',
    'edit'
  );
  const canDelete = emailAdminPermission.isPermissionEnabled(
    'ctint-mf-admin',
    'email',
    'delete'
  );

  // Debounced search handler
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setDebouncedSearchValue(value);
    }, 300),
    []
  );

  // Update debounced value when search input changes
  useEffect(() => {
    debouncedSearch(searchValue);
  }, [searchValue, debouncedSearch]);

  const queryKey = [
    'emailAddressGroups',
    currentPage,
    pageSize,
    searchType,
    debouncedSearchValue,
  ];

  const fetchEmailAddressGroups = async ({
    queryKey,
  }: {
    queryKey: readonly (string | number)[];
  }): Promise<{ data: ApiListResponseData }> => {
    const [_key, page, size, type, term] = queryKey as [
      string,
      number,
      number,
      string,
      string,
    ];
    const params: Record<string, any> = { page, pageSize: size };
    if (term) {
      if (type === 'group') {
        params.groupName = term;
      } else {
        params.fullAddress = term;
      }
    }
    const response = await fireGetFullAddressList(params, basePath);

    if (
      response &&
      response.data &&
      response.data.isSuccess &&
      response.data.data
    ) {
      return { data: response.data.data };
    }
    throw new Error(
      response?.data?.message ||
        'Failed to fetch email address groups or unexpected data structure'
    );
  };

  const { data, isLoading, isError, error, isFetching } = useQuery({
    queryKey,
    queryFn: fetchEmailAddressGroups,
  });

  const groupsListResponseData = data?.data;
  const groups: EmailAddressGroup[] = groupsListResponseData?.list || [];
  const totalItems: number = groupsListResponseData?.total || 0;
  const totalPages: number = Math.ceil(totalItems / pageSize);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchType, debouncedSearchValue]);

  // Auto-expand logic for email search
  useEffect(() => {
    const term = debouncedSearchValue.trim();
    if (term && searchType === 'email') {
      const newExpandedGroups: Record<string, boolean> = {};
      groups.forEach((group) => {
        newExpandedGroups[group.id] = true;
      });
      setExpandedGroups(newExpandedGroups);
    } else if (!term) {
      setExpandedGroups({});
    }
  }, [debouncedSearchValue, searchType, groups]);

  const toggleGroupExpansion = (groupId: string) => {
    setExpandedGroups((prev) => ({ ...prev, [groupId]: !prev[groupId] }));
  };

  const handleCreateNew = () => {
    if (onCreateNew) onCreateNew();
    else storeCreateNew('email-address-groups');
  };

  const handleEdit = (groupId: string) => {
    const groupToEdit = groups.find((g) => g.id === groupId);
    if (groupToEdit) {
      const formDataToLoad: Partial<TEmailAddressGroupsFormData> = {
        id: groupToEdit.id,
        name: groupToEdit.groupName,
        emails: groupToEdit.fullAddressList
          ? [...groupToEdit.fullAddressList]
          : [],
        lastUpdated: groupToEdit.updateTime || new Date().toISOString(),
      };
      loadExistingData(formDataToLoad);
    }

    if (onEditItem) onEditItem(groupId);
    else storeEditDetail(groupId, 'email-address-groups');
  };

  const handleDelete = (groupId: string) => {
    setGroupToDelete(groupId);
    setShowDeleteConfirmation(true);
  };

  const confirmDelete = () => {
    if (groupToDelete) {
      console.log(
        'Attempting to delete group (placeholder action):',
        groupToDelete
      );
      alert(
        `Placeholder: Delete group: ${groupToDelete}. Actual API call not implemented here.`
      );
      setGroupToDelete(null);
      setShowDeleteConfirmation(false);
    }
  };

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const handlePreviousPage = () => {
    setCurrentPage((prev) => Math.max(1, prev - 1));
  };

  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(totalPages, prev + 1));
  };

  const handlePerPageChange = (newPageSize: number) => {
    const newSize = Number(newPageSize);
    if (!isNaN(newSize)) {
      setPageSize(newSize);
      setCurrentPage(1);
    }
  };

  return (
    <div className="px-4 pt-1 pb-6 flex flex-col h-full gap-y-4  overflow-y-auto">
      <div className="flex justify-between items-center gap-2 mt-2">
        <div className="flex items-center gap-2">
          <Select
            options={[
              {
                id: 'group',
                label: t(
                  'ctint-mf-cdss.emailAdmin.emailAddressGroups.common.groupName'
                ),
                value: 'group',
              },
              {
                id: 'email',
                label: t(
                  'ctint-mf-cdss.emailAdmin.emailAddressGroups.common.emailAddress'
                ),
                value: 'email',
              },
            ]}
            value={searchType}
            onChange={(value) => {
              setSearchType(value as string);
              setCurrentPage(1);
            }}
            isPagination={false}
            triggerClassName="text-sm w-[150px]"
            labelClassName="h-full"
          />
          <div className="flex-1">
            <Input
              className=""
              beforeIcon={<Icon name="search" />}
              placeholder={
                searchType === 'group'
                  ? t(
                      'ctint-mf-cdss.emailAdmin.emailAddressGroups.listing.searchByGroupName'
                    )
                  : t(
                      'ctint-mf-cdss.emailAdmin.emailAddressGroups.listing.searchByEmailAddress'
                    )
              }
              value={searchValue}
              onChange={(value) => setSearchValue(String(value))}
              allowClear
              size="s"
            />
          </div>
        </div>
        {canCreate && (
          <Button
            size="s"
            onClick={handleCreateNew}
            disabled={!canCreate}
          >
            {t('ctint-mf-cdss.emailAdmin.emailAddressGroups.common.createNew')}
          </Button>
        )}
      </div>

      {isLoading && <LoadingBlock />}
      {isError && (
        <p className="text-red-500">
          Error loading groups: {error?.message || 'Unknown error'}
        </p>
      )}
      {!isLoading && !isError && groups.length === 0 && (
        <p>
          {t(
            'ctint-mf-cdss.emailAdmin.emailAddressGroups.listing.emptyMessage'
          )}
        </p>
      )}

      {!isLoading && !isError && groups.length > 0 && (
        <div className="flex-1 h-0 overflow-y-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-white sticky top-0 z-20">
              <tr>
                <th
                  scope="col"
                  className="px-3 py-3 text-left text-sm font-semibold text-black tracking-wider w-12"
                ></th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-sm font-semibold text-black tracking-wider"
                >
                  {t(
                    'ctint-mf-cdss.emailAdmin.emailAddressGroups.common.groupName'
                  )}
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-sm font-semibold text-black tracking-wider"
                >
                  {t(
                    'ctint-mf-cdss.emailAdmin.emailAddressGroups.listing.emailCount'
                  )}
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-sm font-semibold text-black tracking-wider"
                >
                  {t(
                    'ctint-mf-cdss.emailAdmin.emailAddressGroups.common.action'
                  )}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white">
              {groups.map((group) => (
                <React.Fragment key={group.id}>
                  <tr
                    className="cursor-pointer hover:bg-primary-100 group border-b border-gray-200 hover:border-primary-500 transition-colors duration-150"
                    onClick={() => toggleGroupExpansion(group.id)}
                  >
                    <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-700">
                      <ChevronRight
                        size={16}
                        className={`transition-transform duration-150 ease-in-out ${
                          expandedGroups[group.id] ? 'rotate-90' : ''
                        }`}
                      />
                    </td>
                    <td className="px-6 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                      {group.groupName}
                    </td>
                    <td className="px-6 py-2 whitespace-nowrap text-sm text-gray-500">
                      {group.fullAddressList.length}
                    </td>
                    <td className="px-6 py-2 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center gap-3">
                        {isPickerMode && onGroupSelect && (
                          <Button
                            onClick={(e: React.MouseEvent) => {
                              e.stopPropagation();
                              onGroupSelect(group.id, group.groupName);
                            }}
                            aria-label={`Select ${group.groupName}`}
                            size="s"
                            className="z-0"
                          >
                            {t(
                              'ctint-mf-cdss.emailAdmin.emailAddressGroups.common.select'
                            )}
                          </Button>
                        )}
                        {canEdit && (
                          <Edit
                            size={16}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEdit(group.id);
                            }}
                            className={`cursor-pointer hover:text-primary-500 ${
                              !canEdit ? 'opacity-50 cursor-not-allowed' : ''
                            }`}
                          />
                        )}
                      </div>
                    </td>
                  </tr>
                  {expandedGroups[group.id] && (
                    <tr
                      className="group cursor-pointer"
                      onClick={() => toggleGroupExpansion(group.id)}
                    >
                      <td
                        colSpan={4}
                        className="px-0 py-0"
                      >
                        <div className="pl-12 bg-gray-50 group-hover:bg-primary-100 py-2">
                          <div className="overflow-y-auto max-h-[450px]">
                            {group.fullAddressList.length > 0 ? (
                              <ul className="list-disc list-inside pl-1 text-gray-700 space-y-1 group-hover:text-black mt-2">
                                {group.fullAddressList.map((email) => (
                                  <li
                                    key={email}
                                    className="text-sm break-all"
                                  >
                                    {email}
                                  </li>
                                ))}
                              </ul>
                            ) : (
                              <p className="text-sm text-gray-500">
                                {t(
                                  'ctint-mf-cdss.emailAdmin.emailAddressGroups.listing.noEmailsInGroup'
                                )}
                              </p>
                            )}
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {totalPages > 0 && (
        <section className="flex-row">
          <div>
            <Pagination
              current={currentPage}
              perPage={pageSize}
              total={totalPages}
              totalCount={totalItems}
              onChange={handlePageChange}
              handleOnPrevious={handlePreviousPage}
              handleOnNext={handleNextPage}
              handlePerPageSetter={(p: number | string) =>
                handlePerPageChange(Number(p))
              }
            />
          </div>
        </section>
      )}

      {/* Delete Confirmation Modal */}
      <Popup
        open={showDeleteConfirmation}
        onOpenChange={setShowDeleteConfirmation}
      >
        <PopupContent
          title="Confirm Deletion"
          className="sm:max-w-md"
        >
          <div className="p-4">
            <p>{`Are you sure you want to delete this group? This action cannot be undone.`}</p>
          </div>
          <PopupFooter className="px-4 pb-4 pt-2">
            <PopupClose asChild>
              <Button
                variant="secondary"
                onClick={() => setShowDeleteConfirmation(false)}
              >
                Cancel
              </Button>
            </PopupClose>
            <Button
              variant="primary"
              onClick={confirmDelete}
              disabled={!canDelete}
            >
              Delete
            </Button>
          </PopupFooter>
        </PopupContent>
      </Popup>
    </div>
  );
};

export default EmailAddressGroupsListing;
