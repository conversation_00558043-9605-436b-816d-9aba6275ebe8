import {
  <PERSON>,
  Tabs,
  TabsContent,
  TabsProvider,
  useRole,
} from '@cdss-modules/design-system';
import {
  TabName,
  useEmailNavigationStore,
} from '../_store/emailNavigationStore';
import MailboxRoutingRulesListing from './MailboxRoutingRules/Listing';
import MailboxRoutingRulesDetail from './MailboxRoutingRules/Detail';
import AutoReplyExclusionsListing from './AutoReplyExclusions/Listing';
import AutoReplyExclusionsDetail from './AutoReplyExclusions/Detail';
import EmailAddressGroupsListing from './EmailAddressGroups/Listing';
import EmailAddressGroupsDetail from './EmailAddressGroups/Detail';

import { usePermission } from '@cdss-modules/design-system/context/PremissionContext';
import { CommonPermission } from '@cdss-modules/design-system/@types/CommonPermission';

export const EmailAdminPage = () => {
  const { globalConfig } = useRole();
  const { permissions } = usePermission();

  const emailAdminPermission = new CommonPermission(globalConfig, permissions);

  const isViewPermission = emailAdminPermission.isPermissionEnabled(
    'ctint-mf-admin',
    'email',
    'view'
  );

  const currentTab = useEmailNavigationStore((state) => state.currentTab);
  const setCurrentTab = useEmailNavigationStore((state) => state.setCurrentTab);
  const mailboxRoutingRulesView = useEmailNavigationStore(
    (state) => state.mailboxRoutingRules.view
  );
  const autoReplyExclusionsView = useEmailNavigationStore(
    (state) => state.autoReplyExclusions.view
  );
  const emailAddressGroupsView = useEmailNavigationStore(
    (state) => state.emailAddressGroups.view
  );

  const backToList = useEmailNavigationStore((state) => state.backToList);

  const handleTabChange = (tab: string) => {
    const tabValue = tab as TabName;

    // If clicking the same tab and in detail view, go back to list view
    // if (tabValue === currentTab) {
    // if (
    //   (currentTab === 'mailbox-routing-rules' &&
    //     mailboxRoutingRulesView === 'detail') ||
    //   (currentTab === 'auto-reply-exclusions' &&
    //     autoReplyExclusionsView === 'detail') ||
    //   (currentTab === 'email-address-groups' &&
    //     emailAddressGroupsView === 'detail')
    // ) {
    //   backToList(tabValue);
    // }
    // } else {
    // Different tab, just update the current tab
    // setCurrentTab(tabValue);
    // }
    setCurrentTab(tabValue);
  };

  if (!isViewPermission) {
    return <div>You do not have permission to access this page.</div>;
  }

  return (
    <TabsProvider key={currentTab}>
      <Tabs
        //   className="h-full"
        defaultTab={currentTab}
        triggers={[
          {
            value: 'mailbox-routing-rules',
            label: 'Mailbox Routing Rules',
          },
          {
            value: 'auto-reply-exclusions',
            label: 'Auto Reply Exclusions',
          },
          {
            value: 'email-address-groups',
            label: 'Email Address Groups',
          },
        ]}
        onChangeTabFunc={handleTabChange}
      >
        <TabsContent
          value="mailbox-routing-rules"
          className="p-0 h-0 flex-1"
        >
          {mailboxRoutingRulesView === 'list' ? (
            <MailboxRoutingRulesListing />
          ) : (
            <MailboxRoutingRulesDetail />
          )}
        </TabsContent>
        <TabsContent
          value="auto-reply-exclusions"
          className="p-0 h-0 flex-1"
        >
          {autoReplyExclusionsView === 'list' ? (
            <AutoReplyExclusionsListing />
          ) : (
            <AutoReplyExclusionsDetail />
          )}
        </TabsContent>
        <TabsContent
          value="email-address-groups"
          className="p-0 h-0 flex-1"
        >
          {emailAddressGroupsView === 'list' ? (
            <EmailAddressGroupsListing
              isPickerMode={false}
              // onGroupSelect will be properly handled by the picker component
            />
          ) : (
            <EmailAddressGroupsDetail
              isPickerMode={false}
              // onGroupSelected and onPickerActionCompleted will be handled by the picker
            />
          )}
        </TabsContent>
      </Tabs>
    </TabsProvider>
  );
};

export default EmailAdminPage;
