import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import {
  fireCreateAutoReplyFilterRule,
  fireUpdateAutoReplyFilterRule,
} from '@cdss/lib/api';
import { basePath } from '@cdss/lib/appConfig';

// Define the form data structure
export type TAutoReplyExclusionFormData = {
  id: string;
  ruleType: 'subject' | 'domain' | 'email';
  pattern: string;
  status: 'active' | 'inactive';
  reason: string;
  expirationDate: string | null;
  lastUpdated: string;
};

// Initial state for empty form
const initialFormState: TAutoReplyExclusionFormData = {
  id: '',
  ruleType: 'subject',
  pattern: '',
  status: 'active',
  reason: '',
  expirationDate: null,
  lastUpdated: new Date().toISOString(),
};

// Helper to create a deep copy of the form data
const deepCopyFormData = (
  data: TAutoReplyExclusionFormData
): TAutoReplyExclusionFormData => {
  return {
    ...data,
  };
};

interface IAutoReplyExclusionFormState {
  // Form data
  formData: TAutoReplyExclusionFormData;
  originalFormData: TAutoReplyExclusionFormData | null; // Store the original data for reverting
  isNew: boolean;
  isDirty: boolean;
  validationErrors: Record<string, string>;

  // Form Actions
  initNewForm: () => void;
  loadExistingData: (data: Partial<TAutoReplyExclusionFormData>) => void;
  updateField: <K extends keyof TAutoReplyExclusionFormData>(
    field: K,
    value: TAutoReplyExclusionFormData[K]
  ) => void;
  validateForm: () => boolean;
  resetForm: () => void;
  saveForm: () => Promise<{ success: boolean; error?: string }>;
}

export const useAutoReplyExclusionFormStore =
  create<IAutoReplyExclusionFormState>()(
    immer((set, get) => ({
      // State
      formData: { ...initialFormState },
      originalFormData: null,
      isNew: true,
      isDirty: false,
      validationErrors: {},

      // Form Actions
      initNewForm: () =>
        set((state) => {
          // Create a fresh copy of initialFormState to avoid reference issues
          const freshFormData: TAutoReplyExclusionFormData = {
            id: '',
            ruleType: 'subject' as 'subject' | 'domain' | 'email',
            pattern: '',
            status: 'active' as 'active' | 'inactive',
            reason: '',
            expirationDate: null,
            lastUpdated: new Date().toISOString(),
          };

          state.formData = freshFormData;
          state.originalFormData = null;
          state.isNew = true;
          state.isDirty = false;
          state.validationErrors = {};
        }),

      loadExistingData: (data) =>
        set((state) => {
          const formData = {
            ...initialFormState,
            ...data,
          };

          console.log('formData', formData);

          // Store a deep copy of the original data for reverting
          const originalData = deepCopyFormData(formData);

          console.log('originalData', originalData);

          state.formData = formData;
          state.originalFormData = originalData;
          state.isNew = false;
          state.isDirty = false;
          state.validationErrors = {};
        }),

      updateField: (field, value) =>
        set((state) => {
          state.formData[field] = value;
          state.isDirty = true;
          // Clear validation error for this field if exists
          if (state.validationErrors[field]) {
            delete state.validationErrors[field];
          }
        }),

      validateForm: () => {
        const { formData } = get();
        const errors: Record<string, string> = {};

        // Basic validation rules
        if (!formData.pattern.trim()) {
          errors.pattern = 'Pattern is required';
        }

        // Additional validation based on rule type
        if (formData.ruleType === 'email' && !formData.pattern.includes('@')) {
          errors.pattern = 'Valid email address is required';
        }

        if (formData.ruleType === 'domain' && !formData.pattern.includes('.')) {
          errors.pattern = 'Valid domain is required';
        }

        set((state) => {
          state.validationErrors = errors;
        });

        return Object.keys(errors).length === 0;
      },

      resetForm: () =>
        set((state) => {
          if (state.originalFormData) {
            // Restore original data if available
            state.formData = deepCopyFormData(state.originalFormData);

            console.log('state.formData', state.formData);
          } else if (state.isNew) {
            // Create a fresh copy for new forms
            const freshFormData: TAutoReplyExclusionFormData = {
              id: '',
              ruleType: 'subject' as 'subject' | 'domain' | 'email',
              pattern: '',
              status: 'active' as 'active' | 'inactive',
              reason: '',
              expirationDate: null,
              lastUpdated: new Date().toISOString(),
            };

            state.formData = freshFormData;
          }

          state.isDirty = false;
          state.validationErrors = {};
        }),

      saveForm: async () => {
        const isValid = get().validateForm();

        if (!isValid) {
          return { success: false };
        }

        const { formData, isNew } = get();

        try {
          // Prepare API payload
          const apiPayload = {
            id: formData.id || undefined, // Only include id if it exists
            type: formData.ruleType,
            value: formData.pattern,
            status: formData.status === 'active' ? 1 : 0,
            reason: formData.reason || undefined,
            expirationDate: formData.expirationDate || undefined,
          };

          // Make the API call
          let response;
          if (isNew) {
            response = await fireCreateAutoReplyFilterRule(
              apiPayload,
              basePath
            );
          } else {
            response = await fireUpdateAutoReplyFilterRule(
              apiPayload,
              basePath
            );
          }

          // Check response
          if (response.data && response.data.isSuccess) {
            set((state) => {
              state.isDirty = false;

              // If it was a new item, now it's not
              if (state.isNew) {
                state.isNew = false;

                // Update with the ID from the API if provided
                if (response.data.data && response.data.data.id) {
                  state.formData.id = response.data.data.id;
                }
              }

              // Update the lastUpdated timestamp
              if (response.data.data && response.data.data.updateTime) {
                state.formData.lastUpdated = response.data.data.updateTime;
              } else {
                state.formData.lastUpdated = new Date().toISOString();
              }

              // Update the original data to match the saved state
              state.originalFormData = deepCopyFormData(state.formData);
            });

            return { success: true };
          } else {
            // API returned an error
            const errorMessage =
              response.data?.error || 'An unknown error occurred';
            set((state) => {
              state.validationErrors = {
                ...state.validationErrors,
                general: errorMessage,
              };
            });
            return { success: false, error: errorMessage };
          }
        } catch (error) {
          console.error('Failed to save form:', error);

          // Extract error message from axios error response
          let errorMessage = 'Unknown error occurred';

          if (error && typeof error === 'object' && 'response' in error) {
            // This is an axios error with response
            const axiosError = error as any;
            console.log(
              'Axios error response data:',
              axiosError.response?.data
            );

            if (axiosError.response?.data?.error) {
              // Use the specific error message from the API
              errorMessage = axiosError.response.data.error;
              console.log('Using API error message:', errorMessage);
            } else if (axiosError.response?.data?.message) {
              // Some APIs use 'message' instead of 'error'
              errorMessage = axiosError.response.data.message;
              console.log('Using API message:', errorMessage);
            } else if (axiosError.message) {
              // Fallback to axios error message
              errorMessage = axiosError.message;
              console.log('Using axios error message:', errorMessage);
            }
          } else if (error instanceof Error) {
            errorMessage = error.message;
            console.log('Using generic error message:', errorMessage);
          }

          set((state) => {
            state.validationErrors = {
              ...state.validationErrors,
              general: errorMessage,
            };
          });
          return { success: false, error: errorMessage };
        }
      },
    }))
  );
