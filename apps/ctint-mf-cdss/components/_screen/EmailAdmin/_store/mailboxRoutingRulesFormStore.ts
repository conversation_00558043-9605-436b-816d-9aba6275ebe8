import { create } from 'zustand';
import { IRoutingRule } from '../../../_ui/RoutingRuleEditor';
import { fireUpdateEmailRule, fireCreateEmailRule } from '@cdss/lib/api';
import { basePath } from '@cdss/lib/appConfig';

// Updated form data type to match required API format
export type TMailboxRoutingRuleFormData = {
  emailRuleId: string | null;
  mailBoxAddress: string;
  isActive: number; // 0 for inactive, 1 for active
  rules: {
    defaultReturn: {
      queueId: string;
      autoReplyId: string | null;
    };
    rules: IRoutingRule[];
  };
  lastUpdated?: string; // For UI display only, not part of the API payload
};

type TValidationErrors = Record<string, string>;

interface MailboxRoutingRuleFormStore {
  formData: TMailboxRoutingRuleFormData;
  originalFormData: TMailboxRoutingRuleFormData | null; // Store original data for reset operations
  isDirty: boolean;
  validationErrors: TValidationErrors;

  updateField: <K extends keyof TMailboxRoutingRuleFormData>(
    field: K,
    value: TMailboxRoutingRuleFormData[K]
  ) => void;

  updateDefaultReturn: (
    field: 'queueId' | 'autoReplyId',
    value: string | null
  ) => void;

  loadExistingData: (data: any) => void;
  initNewForm: () => void;
  resetForm: () => void;
  saveForm: () => Promise<{ success: boolean; error?: string }>;
  updateRoutingRules: (rules: IRoutingRule[]) => void;
  validateForm: (skipRealtimeFields?: boolean) => TValidationErrors;
}

// Default/initial form state
const initialFormState: TMailboxRoutingRuleFormData = {
  emailRuleId: null,
  mailBoxAddress: '',
  isActive: 1, // Active by default
  rules: {
    defaultReturn: {
      queueId: '',
      autoReplyId: null,
    },
    rules: [],
  },
  lastUpdated: new Date().toISOString(),
};

// Helper to create a deep copy of the form data
const deepCopyFormData = (
  data: TMailboxRoutingRuleFormData
): TMailboxRoutingRuleFormData => {
  return {
    ...data,
    rules: {
      defaultReturn: { ...data.rules.defaultReturn },
      rules: data.rules.rules.map((rule) => ({
        ...rule,
        conditions: [...rule.conditions],
        return: { ...rule.return },
      })),
    },
  };
};

export const useMailboxRoutingRuleFormStore =
  create<MailboxRoutingRuleFormStore>((set, get) => ({
    formData: { ...initialFormState },
    originalFormData: null,
    isDirty: false,
    validationErrors: {},

    updateField: (field, value) => {
      set((state) => ({
        formData: { ...state.formData, [field]: value },
        isDirty: true,
        // Clear validation errors for this field when it's updated
        validationErrors: {
          ...state.validationErrors,
          [field]: '',
        },
      }));

      // Validate the form after updating a field, but skip mailbox address and default queue
      setTimeout(() => get().validateForm(true), 0);
    },

    updateDefaultReturn: (field, value) => {
      set((state) => ({
        formData: {
          ...state.formData,
          rules: {
            ...state.formData.rules,
            defaultReturn: {
              ...state.formData.rules.defaultReturn,
              [field]: value,
            },
          },
        },
        isDirty: true,
        validationErrors: {
          ...state.validationErrors,
          [`defaultReturn.${field}`]: '',
        },
      }));

      // Validate the form after updating default return, but skip mailbox address and default queue
      setTimeout(() => get().validateForm(true), 0);
    },

    loadExistingData: (data) => {
      // Transform incoming data to match our form structure if needed
      const formattedData = { ...initialFormState };

      if (data.id || data.emailRuleId) {
        formattedData.emailRuleId = data.emailRuleId || data.id;
      }

      if (data.mailboxAddress || data.mailBoxAddress) {
        formattedData.mailBoxAddress =
          data.mailBoxAddress || data.mailboxAddress;
      }

      if (data.status !== undefined) {
        formattedData.isActive = data.status === 'active' ? 1 : 0;
      } else if (data.isActive !== undefined) {
        formattedData.isActive =
          typeof data.isActive === 'number'
            ? data.isActive
            : data.isActive
              ? 1
              : 0;
      }

      // Handle default return configuration
      if (data.rules?.defaultReturn) {
        formattedData.rules.defaultReturn = data.rules.defaultReturn;
      }
      //  else {
      //   formattedData.rules.defaultReturn = {
      //     queueId: data.defaultQueue || '',
      //     autoReplyId: data.template?.templateId || null,
      //   };
      // }

      // Handle routing rules
      if (data.rules?.rules && Array.isArray(data.rules.rules)) {
        // Sort rules by priority when loading from database
        formattedData.rules.rules = [...data.rules.rules].sort(
          (a: IRoutingRule, b: IRoutingRule) => a.priority - b.priority
        );
      } else if (data.routingRules && Array.isArray(data.routingRules)) {
        // Transform old format rules to new format
        const transformedRules = data.routingRules.map((rule: any) => ({
          id: rule.id,
          logic: rule.logicType?.toUpperCase() || 'AND',
          priority: rule.priority,
          conditions: rule.conditions.map((condition: any) => ({
            field: condition.subject,
            operator: condition.operator,
            value: condition.value,
          })),
          return: {
            queueId: rule.queue,
            autoReplyId: rule.template?.templateId || null,
          },
        }));
        // Sort transformed rules by priority
        formattedData.rules.rules = transformedRules.sort(
          (a: IRoutingRule, b: IRoutingRule) => a.priority - b.priority
        );
      }

      if (data.lastUpdated) {
        formattedData.lastUpdated = data.lastUpdated;
      }

      // Store a deep copy of the original data
      const originalData = deepCopyFormData(formattedData);

      set({
        formData: formattedData,
        originalFormData: originalData,
        isDirty: false,
        validationErrors: {},
      });
    },

    initNewForm: () => {
      // Create a deep copy of initialFormState
      const freshFormData = {
        emailRuleId: null,
        mailBoxAddress: '',
        isActive: 1,
        rules: {
          defaultReturn: {
            queueId: '',
            autoReplyId: null,
          },
          rules: [],
        },
        lastUpdated: new Date().toISOString(),
      };

      set({
        formData: freshFormData,
        originalFormData: null,
        isDirty: false,
        validationErrors: {},
      });
    },

    resetForm: () => {
      const { originalFormData } = get();

      if (originalFormData) {
        // Restore to original data if available (for existing items)
        set({
          formData: deepCopyFormData(originalFormData),
          isDirty: false,
          validationErrors: {},
        });
      } else {
        // Reset to initial state for new items
        const freshFormData = {
          emailRuleId: null,
          mailBoxAddress: '',
          isActive: 1,
          rules: {
            defaultReturn: {
              queueId: '',
              autoReplyId: null,
            },
            rules: [],
          },
          lastUpdated: new Date().toISOString(),
        };

        set({
          formData: freshFormData,
          isDirty: false,
          validationErrors: {},
        });
      }
    },

    validateForm: (skipRealtimeFields = false) => {
      const { formData } = get();
      const errors: TValidationErrors = {};

      // Skip validation for mailbox address and default queue during real-time updates
      if (!skipRealtimeFields) {
        // Validate mailbox address
        if (!formData.mailBoxAddress) {
          errors.mailBoxAddress = 'Mailbox address is required';
        } else if (
          !/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(
            formData.mailBoxAddress
          )
        ) {
          errors.mailBoxAddress = 'Invalid email format';
        }

        // Default queue is required
        if (!formData.rules.defaultReturn.queueId) {
          errors['defaultReturn.queueId'] = 'Default queue is required';
        }
      }

      // Validate routing rules
      if (formData.rules.rules && formData.rules.rules.length > 0) {
        // Check for duplicate priorities
        const priorities = formData.rules.rules.map((rule) => rule.priority);
        const duplicatePriorities = priorities.filter(
          (priority, index) => priorities.indexOf(priority) !== index
        );

        // Add specific errors for duplicate priorities
        if (duplicatePriorities.length > 0) {
          const uniqueDuplicates = [...new Set(duplicatePriorities)];
          uniqueDuplicates.forEach((duplicatePriority) => {
            const rulesWithSamePriority = formData.rules.rules
              .map((rule, index) => ({ rule, index }))
              .filter(({ rule }) => rule.priority === duplicatePriority);

            rulesWithSamePriority.forEach(({ index }) => {
              errors[`rule${index}.priority`] =
                `Priority ${duplicatePriority} is used by multiple rules`;
            });
          });
        }

        formData.rules.rules.forEach((rule, index) => {
          // Validate that each rule has at least one condition
          if (rule.conditions.length === 0) {
            errors[`rule${index}.conditions`] =
              `Rule #${index + 1} must have at least one condition`;
          }

          // Validate that each condition has a value
          rule.conditions.forEach((condition, condIndex) => {
            if (!condition.value.trim()) {
              errors[`rule${index}.condition${condIndex}.value`] =
                `Value is required for condition ${condIndex + 1} in rule #${index + 1}`;
            }
          });

          // We don't validate queueId here as it's optional for routing rules
          // We also don't validate template selection as it's optional for mailbox routing rules
        });
      }

      // Update validation errors in the store
      set({ validationErrors: errors });

      return errors;
    },

    saveForm: async () => {
      // Use the validateForm function to validate before saving (include all fields)
      const errors = get().validateForm(false);

      // Return false if there are errors
      if (Object.keys(errors).length > 0) {
        return { success: false };
      }

      try {
        // Prepare the data for API submission
        const { formData } = get();
        const apiPayload = {
          emailRuleId: formData.emailRuleId,
          mailBoxAddress: formData.mailBoxAddress,
          isActive: formData.isActive,
          rules: formData.rules,
        };

        console.log('Saving form data:', apiPayload);

        let response;
        // Determine if this is a create or update operation
        if (formData.emailRuleId) {
          // Update existing rule
          console.log('Updating existing email rule');
          response = await fireUpdateEmailRule(apiPayload, basePath);
        } else {
          // Create new rule
          console.log('Creating new email rule');
          response = await fireCreateEmailRule(apiPayload, basePath);
        }

        if (response.data && response.data.isSuccess) {
          // Update the original data to the newly saved data
          set({
            isDirty: false,
            originalFormData: deepCopyFormData(formData),
          });
          console.log('Email rule saved successfully:', response.data);
          return { success: true };
        } else {
          const errorMessage =
            response.data?.error || 'Failed to save email rule';
          console.error('Failed to save email rule:', errorMessage);
          // Set the error message in validationErrors if needed
          set({
            validationErrors: {
              ...errors,
              general: errorMessage,
            },
          });
          return { success: false, error: errorMessage };
        }
      } catch (error) {
        console.error('Error saving email rule:', error);

        // Extract error message from axios error response
        let errorMessage = 'An error occurred while saving';

        if (error && typeof error === 'object' && 'response' in error) {
          // This is an axios error with response
          const axiosError = error as any;
          console.log('Axios error response data:', axiosError.response?.data);

          if (axiosError.response?.data?.error) {
            // Use the specific error message from the API
            errorMessage = axiosError.response.data.error;
            console.log('Using API error message:', errorMessage);
          } else if (axiosError.response?.data?.message) {
            // Some APIs use 'message' instead of 'error'
            errorMessage = axiosError.response.data.message;
            console.log('Using API message:', errorMessage);
          } else if (axiosError.message) {
            // Fallback to axios error message
            errorMessage = axiosError.message;
            console.log('Using axios error message:', errorMessage);
          }
        } else if (error instanceof Error) {
          errorMessage = error.message;
          console.log('Using generic error message:', errorMessage);
        }

        set({
          validationErrors: {
            ...get().validationErrors,
            general: errorMessage,
          },
        });
        return { success: false, error: errorMessage };
      }
    },

    updateRoutingRules: (rules) => {
      set((state) => ({
        formData: {
          ...state.formData,
          rules: {
            ...state.formData.rules,
            rules: rules,
          },
        },
        isDirty: true,
      }));

      // Validate the form after updating rules, but skip mailbox address and default queue
      get().validateForm(true);
    },
  }));
