import Loader from '@cdss-modules/design-system/components/_ui/Loader';
import { Suspense } from 'react';
import DemoGroup from '../../_ui/DemoGroup';
import { GLOBAL_TAILWIND_CONFIG } from '../../../../../libs/design-system/tailwind.config';
import DemoTypography from '../../_ui/DemoTypography';
import DemoColor from '../../_ui/DemoColor';
import Button, {
  TButtonVariants,
} from '@cdss-modules/design-system/components/_ui/Button';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import Checkbox from '@cdss-modules/design-system/components/_ui/Checkbox';

import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';

import DemoRadio from '../../_demo/RadioDemo';
import DemoRadioGroup from '../../_demo/RadioGroupDemo';
import CheckboxDemo from '../../_demo/CheckboxDemo';
import SwitchDemo from '../../_demo/SwitchDemo';
import Link from 'next/link';
import Avatar from '@cdss-modules/design-system/components/_ui/Avatar';
import Badge from '@cdss-modules/design-system/components/_ui/Badge';
import DropdownMenuDemo from '../../_demo/DropdownMenuDemo';
import SelectDemo from '../../_demo/SelectDemo';
import SheetDemo from '../../_demo/SheetDemo';
import PaginationDemo from '../../_demo/PaginationDemo';
import DraggableDemo from '../../_demo/DraggableDemo';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import PopupDemo from '../../_demo/PopupDemo';
import BreadcrumbDemo from '../../_demo/BreadcrumbDemo';
import CallControlButtonDemo from '../../_demo/CallControlButtonDemo';
import DialPadDemo from '../../_demo/DiadPadDemo';
import DatePickerDemo from '../../_demo/DatePickerDemo';
import TooltipDemo from '@cdss/components/_demo/TooltipDemo';

export const StyleGuide = () => {
  const fontSizes = GLOBAL_TAILWIND_CONFIG?.theme?.extend?.fontSize ?? {};
  const colors = GLOBAL_TAILWIND_CONFIG?.theme?.extend?.colors ?? {};

  return (
    <Suspense fallback={<Loader />}>
      <div className="pb-6 h-full overflow-auto">
        <DemoGroup title="Style Library">
          <p className="text-body">
            For the design of style library, please refer to{' '}
            <a
              rel="noreferrer"
              className="underline text-primary hover:text-primary-contrast"
              target="_blank"
              href="https://www.figma.com/file/9E1xKZVjR1RW3lNSfIBUCR/CDSS-Style-Library-3.0?type=design&node-id=3%3A2&mode=dev"
            >
              Figma
            </a>
          </p>
        </DemoGroup>
        <DemoGroup title="Typography">
          {Object.entries(fontSizes).map(([key, value]) => {
            return (
              <div key={`typo-${key}`}>
                <DemoTypography
                  typoKey={key}
                  typoValue={value as string}
                />
              </div>
            );
          })}
        </DemoGroup>
        <DemoGroup title="Colors">
          {Object.entries(colors).map(([key, color]) => {
            if (typeof color === 'string') {
              return (
                <div
                  key={`typo-${key}`}
                  className={`p-6 pb-2 mb-6 border-[1px] border-primary`}
                >
                  <DemoColor
                    colorKey={key}
                    colorCode={color}
                  />
                </div>
              );
            }
            return (
              <div
                key={`typo-${key}`}
                className={`p-6 pb-2 mb-6 border-[1px] border-primary`}
              >
                <DemoColor
                  colorKey={key}
                  colorCode={(color as any).DEFAULT}
                />
                <div className="flex flex-wrap gap-x-8">
                  {Object.entries(color as object).map(([subkey, subColor]) => {
                    if (subkey === 'DEFAULT') return null;
                    return (
                      <div key={`typo-${subkey}`}>
                        <DemoColor
                          colorKey={`${key}-${subkey}`}
                          colorCode={subColor}
                        />
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </DemoGroup>
        <DemoGroup title="Buttons">
          {['primary', 'secondary', 'blank', 'back'].map((variant) => {
            return (
              <div
                key={`button-${variant}`}
                className="mb-8 flex flex-wrap gap-x-8 gap-y-4"
              >
                {['l', 'm', 's'].map((size) => {
                  return (
                    <div key={`button-${variant}-${size}`}>
                      <Button
                        variant={variant as TButtonVariants['variant']}
                        size={size as TButtonVariants['size']}
                        beforeIcon={
                          variant === 'back' ? <Icon name="back" /> : undefined
                        }
                      >
                        {`${variant} - ${size}`}
                      </Button>
                    </div>
                  );
                })}
              </div>
            );
          })}
        </DemoGroup>
        <DemoGroup title="Input">
          <div className="flex flex-col gap-4">
            <div className="w-[300px]">
              <Input
                placeholder="Placeholder"
                isSearch={false}
              />
            </div>

            <div className="w-[300px]">
              <Input
                isSearch
                placeholder="Placeholder"
                beforeIcon={<Icon name="search" />}
              />
            </div>
          </div>
        </DemoGroup>
        <DemoGroup title="Field">
          <div className="flex flex-col gap-2">
            <Field
              title="Custom Error Field"
              icon={<Icon name="error" />}
              status="danger"
              message="error"
            >
              <Input
                value={'Inputed text'}
                status="danger"
                message="error"
              />
            </Field>
          </div>
        </DemoGroup>

        <DemoGroup title="Table">
          <DataTable />
        </DemoGroup>

        <DemoGroup title="Radio">
          <DemoRadio />
        </DemoGroup>
        <DemoGroup title="RadioGroup">
          <DemoRadioGroup />
        </DemoGroup>
        <DemoGroup title="Checkbox">
          <Checkbox label="Hello" />
        </DemoGroup>
        <DemoGroup title="Checkbox Group">
          <CheckboxDemo />
        </DemoGroup>
        <DemoGroup title="Switch">
          <SwitchDemo />
        </DemoGroup>
        <DemoGroup title="App Layout">
          <Link
            href="/demo/appLayout"
            className="underline"
          >
            App Layout
          </Link>
        </DemoGroup>

        <DemoGroup title="Avatar">
          <div className="flex gap-4">
            <label>Normal</label>
            <Avatar>AA</Avatar>

            <label>Avatar with icon</label>
            <Avatar icon={<Icon name="back" />} />

            <label>Avatar with image</label>
            <Avatar src="/images/img-avatar-placeholder.jpg" />
          </div>
        </DemoGroup>
        <DemoGroup title="Badge">
          <div className="flex gap-4">
            <label>Normal</label>
            <Badge>
              <Avatar>AA</Avatar>
            </Badge>

            <label>With numbers (optional)</label>
            <Badge count={999}>
              <Avatar>AA</Avatar>
            </Badge>
          </div>
        </DemoGroup>
        <DemoGroup title="DropdownMenu">
          <DropdownMenuDemo />
        </DemoGroup>
        <DemoGroup title="Select">
          <SelectDemo />
        </DemoGroup>
        <DemoGroup title="SheetDemo">
          <SheetDemo />
        </DemoGroup>
        <DemoGroup title="Pagination">
          <PaginationDemo />
        </DemoGroup>
        <DemoGroup title="Draggable">
          <DraggableDemo />
        </DemoGroup>
        <DemoGroup title="Popup">
          <PopupDemo />
        </DemoGroup>
        <DemoGroup title="Breadcrumb">
          <BreadcrumbDemo />
        </DemoGroup>

        <DemoGroup title="CallControlButton">
          <CallControlButtonDemo />
        </DemoGroup>
        <DemoGroup title="DiadPad">
          <DialPadDemo />
        </DemoGroup>
        <DemoGroup title="DatePicker">
          <DatePickerDemo />
        </DemoGroup>
        <DemoGroup title="Tooltip">
          <TooltipDemo />
        </DemoGroup>
        <div className="gap-x-3 p-[2px] pt-4" />
      </div>
    </Suspense>
  );
};

export default StyleGuide;
