/* eslint-disable react/no-children-prop */
import { Editor, EditorConfig } from 'grapesjs';
import GjsEditor, { Canvas } from '@grapesjs/react';
import Basics from 'grapesjs-blocks-basic';

import BaseReactComponent from './base-react-component';
import ReactComponents from './react-components';
import { useEffect, useState } from 'react';
import Topbar from './components/Topbar';
import RightSidebar from './components/RightSidebar';

export const UIBuilder = () => {
  const [styles, setStyles] = useState<string[]>([]);

  useEffect(() => {
    const styleTags = Array.from(document.head.getElementsByTagName('style'));
    setStyles(styleTags?.map((tag) => tag.outerHTML));
  }, []);

  const onEditor = (editor: Editor) => {
    editor.addComponents(`${styles.join('')}`);
    editor.addComponents(`
      <style>
        [data-gjs-type="wrapper"]{display:flex;flex-direction:column;gap: 16px; padding: 16px;}
      </style>
    `);
  };

  const gjsOptions: EditorConfig = {
    height: '100vh',
    storageManager: false,
    blockManager: { appendTo: '.gjs-blocks', blocks: [] },
    // undoManager: { trackSelection: false },
    // selectorManager: { componentFirst: true },
    plugins: [
      (editor) =>
        Basics(editor, {
          blocks: ['column1', 'column2', 'column3', 'text', 'link'],
        }),
      BaseReactComponent,
      ReactComponents,
    ],
  };

  return (
    <div className="flex flex-col h-full gap-y-4 overflow-auto border border-black bg-white">
      <div className="flex flex-col h-0 flex-1 bg-white rounded-lg">
        <GjsEditor
          className="gjs-custom-editor text-white bg-slate-900"
          grapesjs="https://unpkg.com/grapesjs"
          grapesjsCss="https://unpkg.com/grapesjs/dist/css/grapes.min.css"
          options={gjsOptions}
          onEditor={onEditor}
        >
          <div className={`flex h-full`}>
            <div className="gjs-column-m flex flex-col flex-grow">
              <Topbar className="min-h-[48px] border-b border-black" />
              <Canvas className="flex-grow gjs-custom-editor-canvas bg-white" />
            </div>
            <RightSidebar
              className={`gjs-column-r w-[300px] border-l border-black`}
            />
          </div>
          {/* <ModalProvider>
          {({ open, title, content, close }) => (
            <CustomModal
              open={open}
              title={title}
              children={content}
              close={close}
            />
          )}
        </ModalProvider> */}
          {/* <AssetsProvider>
          {({ assets, select, close, Container }) => (
            <Container>
              <CustomAssetManager
                assets={assets}
                select={select}
                close={close}
              />
            </Container>
          )}
        </AssetsProvider> */}
        </GjsEditor>
      </div>
    </div>
  );
};

export default UIBuilder;
