import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { useMemo } from 'react';
import { flattenGjsComponents } from '@cdss-modules/design-system/lib/uiBuilder';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { cn } from '@cdss-modules/design-system/lib/utils';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';

export const UIBuilderPreview = () => {
  const gjsComponentData = localStorage.getItem('gjs-components') || '{}';
  const gjsComponentDataObj = JSON.parse(gjsComponentData);

  const schemaFromGjsComponentData = useMemo(() => {
    const gjsFulllist = flattenGjsComponents(gjsComponentDataObj);
    const schemaObj: any = {};
    gjsFulllist.forEach((component) => {
      if (
        (component.type === 'Input' || component.type === 'Select') &&
        component?.attributes?.validation
      ) {
        const fieldName = component?.attributes?.fieldName || '';
        schemaObj[fieldName] = yup
          .string()
          .required(`${fieldName} is required`);
      }
    });
    return schemaObj;
  }, [gjsComponentDataObj]);

  const {
    handleSubmit,
    control,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(yup.object(schemaFromGjsComponentData).required()),
  });

  const onSubmit = async (data: any) => {
    window.alert(`Submitted data: ${JSON.stringify(data)}`);
  };

  const renderGjsComponent = (component: any) => {
    const fieldName = component?.attributes?.fieldName || '';
    const errorMsg = errors?.[fieldName]?.message as string | undefined;
    const props = component?.attributes || {};
    if (component.type === 'Input') {
      return (
        <Field
          title={props?.label || fieldName}
          icon={<Icon name="error" />}
          status={errorMsg ? 'danger' : undefined}
          message={errorMsg}
          className="w-full"
        >
          <Controller
            name={fieldName}
            control={control}
            rules={{ required: true }}
            render={({ field }) => (
              <Input
                {...props}
                {...field}
              />
            )}
          />
        </Field>
      );
    } else if (component.type === 'Select') {
      return (
        <Field
          title={props?.label || fieldName}
          icon={<Icon name="error" />}
          status={errorMsg ? 'danger' : undefined}
          message={errorMsg}
          className="w-full"
        >
          <Controller
            name={fieldName}
            control={control}
            rules={{ required: true }}
            render={({ field }) => (
              <Select
                mode="single"
                options={[
                  {
                    id: 'option1',
                    label: 'Option 1',
                    value: 'option1',
                  },
                  {
                    id: 'option2',
                    label: 'Option 2',
                    value: 'option2',
                  },
                  {
                    id: 'option3',
                    label: 'Option 3',
                    value: 'option3',
                  },
                ]}
                showSearch={true}
                status={errors?.fromNo?.message ? 'danger' : undefined}
                {...field}
              />
            )}
          />
        </Field>
      );
    } else if (component.type === 'Button') {
      return (
        <Button
          type="submit"
          {...props}
        >
          {props?.text}
        </Button>
      );
    } else {
      return (
        <div
          className={cn(
            'w-full flex gap-4',
            component.name === 'Cell' && 'p-4'
          )}
        >
          {component?.components &&
            component?.components?.map((sCompo: any) =>
              renderGjsComponent(sCompo)
            )}
        </div>
      );
    }
  };

  return (
    <div className="flex flex-col h-full gap-y-4 overflow-auto rounded-lg ">
      <div className="flex flex-col h-0 flex-1 p-4 bg-white rounded-lg">
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="flex flex-col gap-4"
        >
          {gjsComponentDataObj?.map((sCompo: any) =>
            renderGjsComponent(sCompo)
          )}
          <div className="border border-dashed border-black p-4">
            <div className="font-bold mb-4">Development only:</div>
            {gjsComponentData}
            ##{JSON.stringify(schemaFromGjsComponentData)}##
          </div>
        </form>
      </div>
    </div>
  );
};

export default UIBuilderPreview;
