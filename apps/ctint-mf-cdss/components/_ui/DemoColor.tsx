'use client';
import { toast } from '@cdss-modules/design-system/components/_ui/Toast/use-toast'; // TODO: we should move away from radix and shadcn
import clsx from 'clsx';
import { useCopyToClipboard } from 'usehooks-ts';

interface DemoColorProps {
  colorKey: string;
  colorCode: string;
}

const DemoColor = ({ colorKey, colorCode }: DemoColorProps) => {
  const [_value, copy] = useCopyToClipboard();
  return (
    <button
      // text={colorKey}
      onClick={() => {
        copy(colorKey);
        toast({
          title: 'Success',
          description: `Copied color key "${colorKey}" to clipboard`,
        });
      }}
    >
      <div className="mb-4 flex items-center gap-x-4 cursor-pointer group">
        <div
          className={clsx(
            'w-8 h-8 drop-shadow-md group-hover:shadow-lg transition-all',
            `bg-${colorKey}`
          )}
        />
        <div className="group-hover:-translate-x-1 transition-all">
          <strong>[{colorKey}]</strong> - {`${colorCode}`}
        </div>
      </div>
    </button>
  );
};

export default DemoColor;
