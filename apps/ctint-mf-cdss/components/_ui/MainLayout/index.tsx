'use client';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from '@cdss-modules/design-system';
import dynamic from 'next/dynamic';
import { Auth<PERSON>he<PERSON> } from '@cdss-modules/design-system/components/_ui/AuthChecker';
import AppLayout from '@cdss-modules/design-system/components/_ui/AppLayout';
import { Toaster } from '@cdss-modules/design-system/components/_ui/Toast/toaster';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import { cn } from '@cdss-modules/design-system/lib/utils';
import {
  AlignJustify,
  ClipboardList,
  Users,
  Rows4,
  FileSliders,
  SquareActivity,
  Headset,
  ScrollText,
  Mail,
  MessageSquareReply,
  MessageSquarePlus,
  ListPlus,
  FileCog,
  Gauge,
} from 'lucide-react';
import { CommonPermission } from '@cdss-modules/design-system/@types/CommonPermission';

type HeaderEntry = {
  name: string;
  icon: string;
  path: string;
  permission: string;
};

const iconComponents: Record<string, React.ComponentType<any>> = {
  Rows4,
  Users,
  ClipboardList,
  FileSliders,
  SquareActivity,
  Headset,
  ScrollText,
  AlignJustify,
  ListPlus,
  Mail,
  MessageSquareReply,
  MessageSquarePlus,
  FileCog,
  Gauge,
};
import { useRole } from '@cdss-modules/design-system';
import { usePermission } from '@cdss-modules/design-system/context/PremissionContext';
import { useTabsContext } from '@cdss-modules/design-system/context/TabsContext';
import InteractionSidebar from '../Sidebar';
import AgentActivityActionBox from '../AgentActivityActionBox';
import { useOpenBlockingContext } from '@cdss-modules/design-system/context/BlockingContext';
import FixedRightMenu from '@cdss-modules/design-system/components/_ui/FixedRightMenu';
import React, { useState, useEffect } from 'react';
import { moduleMap } from './projectMenuConfig';
import * as Popover from '@radix-ui/react-popover';
type TMainLayoutProps = {
  children: React.ReactNode;
};

export const MainLayout = ({ children }: TMainLayoutProps) => {
  const { toGroup, activePath, toPath, basePath } = useRouteHandler();
  const { isBlocked, open, setBlockingOpen, blockingList } =
    useOpenBlockingContext();
  const { t, i18n } = useTranslation();
  const targetLng = i18n.language === 'en' ? 'zh-HK' : 'en';
  const pathToCheck = activePath || '';

  const { globalConfig } = useRole();
  const { permissions } = usePermission();
  const { onChangeTab } = useTabsContext();
  const [panelContent, setPanelContent] = useState<React.ReactNode>(<></>);
  const [remoteName, setRemoteName] = useState<string>('');

  // 添加状态来跟踪当前选中的项目
  const [selectedProject, setSelectedProject] = useState<number | null>(null);

  // 处理动态组件加载
  useEffect(() => {
    if (remoteName && moduleMap[remoteName]) {
      console.log('remoteName', remoteName);
      const DynamicComponent = dynamic(moduleMap[remoteName].entry, {
        ssr: false,
        loading: () => <LoadingBlock />,
      });
      setPanelContent(<DynamicComponent />);
    }
  }, [remoteName]);

  return (
    <AppLayout
      headerPanel={
        <Popover.Root>
          <Popover.Trigger className="cursor-pointer hover:text-primary-500 data-[state=open]:text-primary-500">
            <AlignJustify />
          </Popover.Trigger>
          <Popover.Portal>
            <Popover.Content
              onOpenAutoFocus={(e) => e.preventDefault()}
              align="center"
              className="mt-1 bg-white shadow-[0px_2px_10px_0px_rgba(0,0,0,0.18)] z-50"
            >
              <div className="flex flex-col gap-4 bg-white p-4 items-start">
                <AuthChecker emptyWhenUnauthorized>
                  {globalConfig?.microfrontends?.headerEntries?.map(
                    (item: HeaderEntry, index: number) => {
                      const [module, func, type] = item.permission.split('.');
                      return new CommonPermission(
                        globalConfig,
                        permissions
                      ).isPermissionEnabled(module, func, type) ? (
                        <button
                          className={cn(
                            'flex flex-row gap-2 items-center justify-center aspect-square h-8 text-grey-600 hover:text-primary-500 rounded-sm',
                            pathToCheck &&
                              ((pathToCheck == basePath && item.path == '/') ||
                                pathToCheck == basePath + item.path) &&
                              'text-primary-500',
                            'w-full'
                          )}
                          type="button"
                          onClick={() => {
                            if (item.path === '/') {
                              onChangeTab(['info']);
                            } else if (item.path === '/admin/user') {
                              onChangeTab(['users']);
                            } else if (item.path === '/admin/qm') {
                              onChangeTab(['sop']);
                            } else if (item.path === '/admin/dnc') {
                              onChangeTab(['dnc']);
                            }
                            toPath(item.path);
                          }}
                          key={item?.path + '_' + index}
                        >
                          {iconComponents[item.icon] &&
                            React.createElement(iconComponents[item.icon], {
                              size: 24,
                            })}
                          <span className="flex-1 text-sm whitespace-nowrap text-start">
                            {t(`ctint-mf-cdss.menu.${item.name}`)}
                          </span>
                        </button>
                      ) : null;
                    }
                  )}
                </AuthChecker>
                <AuthChecker
                  requiredPemissions={{
                    global: {
                      portals: ['ctint-mf-cpp'],
                    },
                    user: {
                      permissions: ['ctint-mf-cpp.application.visit'],
                    },
                  }}
                  emptyWhenUnauthorized
                >
                  <Button
                    size="s"
                    onClick={() => toGroup('ctint-mf-cpp')}
                  >
                    Playback Portal
                  </Button>
                </AuthChecker>
                <AuthChecker
                  requiredPemissions={{
                    global: {
                      portals: ['ctint-mf-wap'],
                    },
                    user: {
                      permissions: ['ctint-mf-wap.application.visit'],
                    },
                  }}
                  emptyWhenUnauthorized
                >
                  <Button
                    size="s"
                    onClick={() => toGroup('ctint-mf-wap')}
                  >
                    Single Message
                  </Button>
                </AuthChecker>
                <AuthChecker
                  requiredPemissions={{
                    global: {
                      portals: ['ctint-mf-dev'],
                    },
                    user: {
                      permissions: ['ctint-mf-dev'],
                    },
                  }}
                  emptyWhenUnauthorized
                >
                  <Button
                    size="s"
                    onClick={() => toGroup('style-guide')}
                  >
                    Style Guide
                  </Button>
                </AuthChecker>
                {/*<AuthChecker*/}
                {/*  // requiredPemissions={{*/}
                {/*  //   global: {*/}
                {/*  //     portals: ['ctint-mf-template'],*/}
                {/*  //   },*/}
                {/*  //   user: {*/}
                {/*  //     permissions: ['ctint-mf-template.application.visit'],*/}
                {/*  //   },*/}
                {/*  // }}*/}
                {/*  emptyWhenUnauthorized*/}
                {/*>*/}
                {/*  <Button*/}
                {/*    size="s"*/}
                {/*    onClick={() => toGroup('ctint-mf-template')}*/}
                {/*  >*/}
                {/*    {t('ctint-mf-cdss.template')}*/}
                {/*  </Button>*/}
                {/*</AuthChecker>*/}
                <AuthChecker
                  requiredPemissions={{
                    global: {
                      portals: ['ctint-mf-dev'],
                    },
                    user: {
                      permissions: ['ctint-mf-dev'],
                    },
                  }}
                  emptyWhenUnauthorized
                >
                  <Button
                    size="s"
                    onClick={() => {
                      i18n.changeLanguage(targetLng);
                    }}
                  >{`${t(
                    'ctint-mf-template.langDemo.changeTo'
                  )} ${targetLng}`}</Button>
                </AuthChecker>
                <AuthChecker
                  requiredPemissions={{
                    global: {
                      portals: ['ctint-mf-info'],
                    },
                    user: {
                      permissions: ['ctint-mf-info.application.visit'],
                    },
                  }}
                  emptyWhenUnauthorized
                >
                  <Button
                    size="s"
                    onClick={() => toGroup('ctint-mf-info')}
                  >
                    Info
                  </Button>
                </AuthChecker>
              </div>
            </Popover.Content>
          </Popover.Portal>
        </Popover.Root>
      }
      hasSideNav={globalConfig?.hasSidebar}
      sideNav={<InteractionSidebar />}
    >
      {/* ##
      {JSON.stringify(globalConfig)}
      **
      {JSON.stringify(userConfig)}
      ## */}
      {children}
      <Toaster />
      {localStorage.getItem('loginPlatform') !== 'pure-engage' && (
        <AgentActivityActionBox />
      )}
      <PopupModel
        onChang={(v) => {
          isBlocked.current = false;
          setBlockingOpen(v);
        }}
        openPopup={open}
        blockingList={blockingList}
      />
      {/* project button */}
      {/* <FixedRightMenu panelContent={panelContent}>
        <div className="flex flex-col gap-2 bg-primary-300 rounded-tl-lg rounded-bl-lg">
          {Object.keys(moduleMap).map((key) => (
            <div
              key={key}
              className={
                'py-2 px-4 text-primary-500 hover:text-primary-700 flex items-center justify-center'
              }
              onClick={() => setRemoteName(key)}
              title={t(`ctint-mf-cdss.${moduleMap[key].entryName}`)}
            >
              {iconComponents[moduleMap[key].icon] &&
                React.createElement(iconComponents[moduleMap[key].icon], {
                  size: 20,
                })}
            </div>
          ))}
        </div>
      </FixedRightMenu> */}
    </AppLayout>
  );
};

export default MainLayout;
