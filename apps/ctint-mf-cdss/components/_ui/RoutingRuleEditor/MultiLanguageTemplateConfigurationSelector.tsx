import React, { useState, useEffect, useMemo, FC } from 'react';
import { Button } from '@cdss-modules/design-system';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { Trash, ChevronDown } from 'lucide-react';
import { cn } from '@cdss-modules/design-system/lib/utils';
import { ITemplateResponse } from '../../_screen/EmailAdmin/_screen/MailboxRoutingRules/Detail';

import IconEmptyRecords from '@cdss-modules/design-system/components/_ui/Icon/IconEmptyRecords';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import Fuse from 'fuse.js';
import { LoadingBlock } from '@cdss-modules/design-system';
import {
  ITemplateDetail,
  languageOptions,
} from './TemplateConfigurationSelector';

// Multi-language template info interface
export interface MultiLanguageTemplateInfo {
  [language: string]: string | null; // language -> templateId mapping
}

export interface MultiLanguageTemplateConfigurationSelectorProps {
  templateValue: MultiLanguageTemplateInfo | null;
  queueValue: string;
  queueOptions: any[];
  templateOptions: ITemplateResponse[];
  isEditMode: boolean;
  onUpdateTemplate: (template: MultiLanguageTemplateInfo | null) => void;
  onUpdateQueue: (queue: string) => void;
  title?: string;
  queueLabel?: string;
  templateLabel?: string;
  isLoadingQueues?: boolean;
  isLoadingTemplates?: boolean;
  queueValidationError?: string;
  alwaysShowTemplateSelector?: boolean;
  isQueueRequired?: boolean;
  showQueueInput?: boolean;
  isTemplateRequired?: boolean;
  templateDetailData?: { [language: string]: ITemplateDetail | null };
  isLoadingTemplateDetailData?: boolean;
  isWhatsAppTemplate?: boolean; // Flag to indicate WhatsApp templates
  languageValidationErrors: string;
  templateValidationErrors: string;
}

interface ITemplateItem {
  id: string;
  title: string;
  language: string;
  contentType: string;
  content?: string; // WhatsApp templates have content directly
}

interface IDirectoryItem {
  id: string;
  title: string;
  items: ITemplateItem[];
  contentType: string;
}

interface ISpaceItem {
  id: string;
  name: string;
  spaceType: string;
  description: string;
  items: IDirectoryItem[];
}

type TNormalizedTemplateOptions = ISpaceItem[];

const MultiLanguageTemplateConfigurationSelector: FC<
  MultiLanguageTemplateConfigurationSelectorProps
> = ({
  templateValue,
  queueValue,
  queueOptions,
  templateOptions,
  isEditMode,
  onUpdateTemplate,
  onUpdateQueue,
  title: propTitle,
  queueLabel: propQueueLabel,
  templateLabel: propTemplateLabel,
  isLoadingQueues = false,
  isLoadingTemplates = false,
  queueValidationError,
  alwaysShowTemplateSelector = false,
  isQueueRequired = false,
  showQueueInput = true,
  isTemplateRequired = false,
  templateDetailData = {},
  isLoadingTemplateDetailData = false,
  isWhatsAppTemplate = false,
  languageValidationErrors,
  templateValidationErrors,
}) => {
  const { t } = useTranslation();

  console.log('templateValue', templateValue);

  const resolvedTitle =
    propTitle ||
    t('ctint-mf-cdss.common.templateConfigurationSelector.defaultTitle');
  const resolvedQueueLabel =
    propQueueLabel ||
    t('ctint-mf-cdss.common.templateConfigurationSelector.defaultQueueLabel');
  const resolvedTemplateLabel =
    propTemplateLabel ||
    t(
      'ctint-mf-cdss.common.templateConfigurationSelector.defaultTemplateLabel'
    );

  const [selectedLanguages, setSelectedLanguages] = useState<string[]>([]);
  const [expandedSpaces, setExpandedSpaces] = useState<{
    [language: string]: string | null;
  }>({});
  const [expandedDirectories, setExpandedDirectories] = useState<{
    [language: string]: string | null;
  }>({});
  const [searchTerms, setSearchTerms] = useState<{
    [language: string]: string;
  }>({});

  const NO_QUEUE_VALUE = '__NO_QUEUE_SELECTED__';

  // Initialize selected languages from templateValue
  useEffect(() => {
    console.log(
      'MultiLanguageTemplateConfigurationSelector: templateValue changed',
      templateValue
    );

    if (templateValue) {
      // Get all languages from templateValue keys, regardless of whether they have templates assigned
      const languages = Object.keys(templateValue);
      console.log('Setting selected languages:', languages);
      setSelectedLanguages(languages);
    } else {
      // Reset when templateValue is null
      console.log('Resetting languages - templateValue is null');
      setSelectedLanguages([]);
    }
  }, [templateValue]);

  // Extract the actual template data from the response
  const getNormalizedTemplateOptions = (): TNormalizedTemplateOptions => {
    if (!templateOptions || templateOptions.length === 0) return [];

    const templateResponse = templateOptions[0];

    if (
      templateResponse &&
      templateResponse.data &&
      Array.isArray(templateResponse.data)
    ) {
      return templateResponse.data as ISpaceItem[];
    }

    return [];
  };

  const normalizedTemplateOptions = getNormalizedTemplateOptions();

  // Auto-expand hierarchy to show selected templates
  useEffect(() => {
    if (templateValue && normalizedTemplateOptions.length > 0) {
      const newExpandedSpaces: { [language: string]: string | null } = {};
      const newExpandedDirectories: { [language: string]: string | null } = {};

      Object.entries(templateValue).forEach(([language, templateId]) => {
        if (templateId) {
          // Find which space and directory contains this template
          for (const space of normalizedTemplateOptions) {
            for (const directory of space.items) {
              for (const template of directory.items) {
                if (template.id === templateId) {
                  newExpandedSpaces[language] = space.id;
                  newExpandedDirectories[language] = directory.id;
                  console.log(
                    `Auto-expanding hierarchy for ${language}: space=${space.id}, directory=${directory.id}, template=${templateId}`
                  );
                  return; // Break out of all loops for this language
                }
              }
            }
          }
        }
      });

      // Update expansion states to show selected templates
      setExpandedSpaces(newExpandedSpaces);
      setExpandedDirectories(newExpandedDirectories);
    } else if (!templateValue) {
      // Reset expanded states when no templates are selected
      setExpandedSpaces({});
      setExpandedDirectories({});
    }
  }, [templateValue, normalizedTemplateOptions]);

  // Get WhatsApp template data for preview
  const getWhatsAppTemplateData = (): {
    [language: string]: ITemplateItem | null;
  } => {
    if (!isWhatsAppTemplate || !templateValue) {
      console.log('getWhatsAppTemplateData: Early return', {
        isWhatsAppTemplate,
        templateValue,
      });
      return {};
    }

    const whatsAppTemplates: { [language: string]: ITemplateItem | null } = {};

    Object.entries(templateValue).forEach(([language, templateId]) => {
      console.log(
        `Looking for template ${templateId} for language ${language}`
      );

      if (templateId) {
        // Find the template in the normalized options
        let foundTemplate: ITemplateItem | null = null;

        for (const space of normalizedTemplateOptions) {
          for (const directory of space.items) {
            for (const template of directory.items) {
              if (template.id === templateId) {
                console.log(`Found template:`, template);
                foundTemplate = template;
                break;
              }
            }
            if (foundTemplate) break;
          }
          if (foundTemplate) break;
        }

        if (!foundTemplate) {
          console.log(
            `Template ${templateId} not found in normalizedTemplateOptions`
          );
        }

        whatsAppTemplates[language] = foundTemplate;
      } else {
        whatsAppTemplates[language] = null;
      }
    });

    console.log('getWhatsAppTemplateData result:', whatsAppTemplates);
    return whatsAppTemplates;
  };

  const whatsAppTemplateData = getWhatsAppTemplateData();

  const allTemplatesForFuse = useMemo(() => {
    if (!normalizedTemplateOptions || normalizedTemplateOptions.length === 0) {
      return [];
    }
    const flatTemplates: ITemplateItem[] = [];
    normalizedTemplateOptions.forEach((space) => {
      space.items.forEach((directory) => {
        directory.items.forEach((template) => {
          flatTemplates.push(template);
        });
      });
    });
    return flatTemplates;
  }, [normalizedTemplateOptions]);

  const fuse = useMemo(() => {
    return new Fuse(allTemplatesForFuse, {
      keys: ['title'],
      threshold: 0.4,
    });
  }, [allTemplatesForFuse]);

  const finalQueueOptions = useMemo(() => {
    const options = [...queueOptions];
    if (!isQueueRequired) {
      options.unshift({
        id: NO_QUEUE_VALUE,
        label: t(
          'ctint-mf-cdss.common.templateConfigurationSelector.noQueueOption'
        ),
        value: NO_QUEUE_VALUE,
      });
    }
    return options;
  }, [queueOptions, isQueueRequired, t]);

  // Filter templates based on language and search term
  const getFilteredTemplatesForLanguage = (language: string) => {
    if (!normalizedTemplateOptions || normalizedTemplateOptions.length === 0) {
      return [];
    }

    const searchTerm = searchTerms[language] || '';

    const langMatches = (template: ITemplateItem) => {
      const templateLang = template.language?.toLowerCase() || '';
      const selectedLang = language.toLowerCase();

      if (selectedLang === 'en') {
        return templateLang === 'en' || templateLang === 'english';
      } else if (selectedLang === 'zh_cn') {
        return (
          templateLang.includes('zh') &&
          (templateLang.includes('cn') || templateLang.includes('china'))
        );
      } else if (selectedLang === 'zh_hk') {
        return templateLang.includes('zh') && templateLang.includes('hk');
      }

      return (
        templateLang.includes(selectedLang) ||
        selectedLang.includes(templateLang)
      );
    };

    let filtered = normalizedTemplateOptions;

    if (searchTerm) {
      const searchResults = fuse.search(searchTerm);
      const matchingTemplates = searchResults.map((result) => result.item);

      filtered = normalizedTemplateOptions
        .map((space) => ({
          ...space,
          items: space.items
            .map((directory) => ({
              ...directory,
              items: directory.items.filter(
                (template) =>
                  matchingTemplates.some((match) => match.id === template.id) &&
                  langMatches(template)
              ),
            }))
            .filter((directory) => directory.items.length > 0),
        }))
        .filter((space) => space.items.length > 0);
    } else {
      filtered = normalizedTemplateOptions
        .map((space) => ({
          ...space,
          items: space.items
            .map((directory) => ({
              ...directory,
              items: directory.items.filter(langMatches),
            }))
            .filter((directory) => directory.items.length > 0),
        }))
        .filter((space) => space.items.length > 0);
    }

    return filtered;
  };

  // Handle language selection change
  const handleLanguageChange = (e: any) => {
    const isSelected = e?.target?.checked;
    const value = e?.target?.value;

    console.log('handleLanguageChange:', {
      isSelected,
      value,
      currentSelectedLanguages: selectedLanguages,
    });

    if (!value) return;

    let newSelectedLanguages: string[];

    if (isSelected) {
      newSelectedLanguages = [...selectedLanguages, value];
    } else {
      newSelectedLanguages = selectedLanguages.filter((lang) => lang !== value);
    }

    console.log('New selected languages:', newSelectedLanguages);
    setSelectedLanguages(newSelectedLanguages);

    // Update template value to include/exclude languages
    const newTemplateValue: MultiLanguageTemplateInfo = {};
    newSelectedLanguages.forEach((lang) => {
      newTemplateValue[lang] = templateValue?.[lang] || null;
    });

    const finalTemplateValue =
      Object.keys(newTemplateValue).length > 0 ? newTemplateValue : null;
    console.log('Calling onUpdateTemplate with:', finalTemplateValue);
    onUpdateTemplate(finalTemplateValue);
  };

  // Handle removing all language selections
  const handleRemoveAllLanguageSelections = () => {
    setSelectedLanguages([]);
    onUpdateTemplate(null);
  };

  // Handle template selection for specific language
  const handleSelectTemplate = (template: ITemplateItem, language: string) => {
    console.log('handleSelectTemplate called:', { template, language });
    console.log('Current templateValue:', templateValue);

    const newTemplateValue: MultiLanguageTemplateInfo = {
      ...templateValue,
      [language]: template.id,
    };

    console.log('New templateValue:', newTemplateValue);
    onUpdateTemplate(newTemplateValue);
  };

  // Handle removing template for specific language
  const handleRemoveTemplate = (language: string) => {
    const newSelectedLanguages = selectedLanguages.filter(
      (lang) => lang !== language
    );
    setSelectedLanguages(newSelectedLanguages);

    const newTemplateValue: MultiLanguageTemplateInfo = { ...templateValue };
    delete newTemplateValue[language];

    onUpdateTemplate(
      Object.keys(newTemplateValue).length > 0 ? newTemplateValue : null
    );
  };

  // Handle removing individual language selection (for the X button in Select)
  const handleRemoveLanguageSelection = (language: string) => {
    console.log('Removing language selection:', language);
    handleRemoveTemplate(language);
  };

  // Handle space expansion toggle for specific language
  const handleToggleSpace = (spaceId: string, language: string) => {
    setExpandedSpaces((prev) => ({
      ...prev,
      [language]: prev[language] === spaceId ? null : spaceId,
    }));
    setExpandedDirectories((prev) => ({
      ...prev,
      [language]: null,
    }));
  };

  // Handle directory expansion toggle for specific language
  const handleToggleDirectory = (directoryId: string, language: string) => {
    setExpandedDirectories((prev) => ({
      ...prev,
      [language]: prev[language] === directoryId ? null : directoryId,
    }));
  };

  // Handle search term change for specific language
  const handleSearchTermChange = (searchTerm: string, language: string) => {
    setSearchTerms((prev) => ({
      ...prev,
      [language]: searchTerm,
    }));
  };

  // Render queue selection
  const renderQueueSelect = () => {
    if (isLoadingQueues) {
      return (
        <div className="animate-pulse mt-1 h-9 bg-gray-200 rounded w-full"></div>
      );
    }

    return (
      <Select
        showSearch
        disabled={!isEditMode}
        value={queueValue}
        onChange={(selectedValue) => {
          if (selectedValue === NO_QUEUE_VALUE) {
            onUpdateQueue('');
          } else {
            onUpdateQueue(String(selectedValue));
          }
        }}
        options={finalQueueOptions}
        isPagination={false}
        placeholder={t(
          'ctint-mf-cdss.common.templateConfigurationSelector.placeholder'
        )}
        status={queueValidationError ? 'danger' : undefined}
        labelClassName="h-full"
        triggerClassName="disabled:opacity-100 disabled:bg-grey-100 h-8 disabled:text-black text-sm"
      />
    );
  };

  // Render template selection UI for specific language
  const renderTemplateSelectionUIForLanguage = (language: string) => {
    if (isLoadingTemplates) {
      return (
        <div className="text-center py-4">
          {t(
            'ctint-mf-cdss.common.templateConfigurationSelector.loadingTemplates'
          )}
        </div>
      );
    }

    const filteredTemplates = getFilteredTemplatesForLanguage(language);
    const searchTerm = searchTerms[language] || '';
    const expandedSpaceId = expandedSpaces[language];
    const expandedDirectoryId = expandedDirectories[language];

    if (filteredTemplates.length === 0) {
      return (
        <div className="text-center py-4 text-gray-500">
          {t(
            'ctint-mf-cdss.common.templateConfigurationSelector.noTemplatesForLanguage'
          )}
        </div>
      );
    }

    return filteredTemplates.map((space) => (
      <div
        key={space.id}
        className="mb-2"
      >
        <button
          type="button"
          className={cn(
            'w-full flex items-center justify-between p-2 text-left text-sm font-semibold',
            expandedSpaceId === space.id && 'bg-primary-100'
          )}
          onClick={() => !searchTerm && handleToggleSpace(space.id, language)}
          disabled={!!searchTerm}
        >
          <span>{space.name}</span>
          <ChevronDown
            className={cn(
              'h-4 w-4 transition-transform',
              expandedSpaceId === space.id && 'rotate-180'
            )}
          />
        </button>

        {(expandedSpaceId === space.id || searchTerm) && (
          <div className="">
            {space.items.map((directory) => (
              <div
                key={directory.id}
                className="mb-1"
              >
                <button
                  type="button"
                  className={cn(
                    'w-full flex items-center justify-between p-2 pl-4 text-left text-sm',
                    expandedDirectoryId === directory.id && 'bg-primary-100'
                  )}
                  onClick={() =>
                    !searchTerm && handleToggleDirectory(directory.id, language)
                  }
                  disabled={!!searchTerm}
                >
                  <span>{directory.title}</span>
                  <ChevronDown
                    className={cn(
                      'h-4 w-4 transition-transform',
                      expandedDirectoryId === directory.id && 'rotate-180'
                    )}
                  />
                </button>

                {(expandedDirectoryId === directory.id || searchTerm) && (
                  <div className="">
                    {directory.items.map((template) => {
                      const isSelected =
                        templateValue?.[language] === template.id;
                      return (
                        <button
                          key={template.id}
                          type="button"
                          className={cn(
                            'w-full text-left p-2 pl-6 text-sm truncate',
                            isSelected
                              ? 'bg-primary-100 text-primary-600'
                              : 'hover:bg-gray-100'
                          )}
                          onClick={() =>
                            handleSelectTemplate(template, language)
                          }
                        >
                          {template.title}
                        </button>
                      );
                    })}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    ));
  };

  // Render individual language template section
  const renderLanguageTemplateSection = (language: string) => {
    const languageLabel =
      languageOptions(t).find((opt) => opt.value === language)?.label ||
      language;

    const template = isWhatsAppTemplate
      ? whatsAppTemplateData?.[language]
      : templateDetailData[language];

    return (
      <div
        key={language}
        className={
          isEditMode ? 'mb-4 border-b border-gray-200 pb-4 last:border-b-0' : ''
        }
      >
        {isEditMode && (
          <div className="flex justify-between items-center mb-1">
            <h4 className="font-semibold text-sm">{languageLabel}</h4>
          </div>
        )}

        {isEditMode ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Template Selection */}
            <div className="flex flex-col gap-2">
              {/* <label className="block text-sm font-medium">
                {isEditMode ? 'Select Template' : 'Selected Templates'}
              </label> */}
              <Input
                disabled={!isEditMode}
                size="s"
                placeholder={t(
                  'ctint-mf-cdss.common.templateConfigurationSelector.searchTemplatesPlaceholder'
                )}
                value={searchTerms[language] || ''}
                onChange={(value) =>
                  handleSearchTermChange(String(value), language)
                }
                allowClear
                className="disabled:text-black"
              />
              <div className="border border-gray-200 rounded-md h-full overflow-y-auto">
                {renderTemplateSelectionUIForLanguage(language)}
              </div>
            </div>

            {/* Template Preview */}
            <div className="flex flex-col gap-2 border border-gray-200 rounded-md p-2">
              <div className="flex justify-between items-center">
                <label className="block text-sm font-medium">
                  {t(
                    'ctint-mf-cdss.common.templateConfigurationSelector.previewLabel'
                  )}
                </label>
                {isEditMode && (
                  <Button
                    variant="secondary"
                    size="xs"
                    onClick={() => handleRemoveTemplate(language)}
                  >
                    <Trash className="h-3 w-3" />
                  </Button>
                )}
              </div>
              <div className="h-[290px] overflow-y-auto">
                {isLoadingTemplateDetailData && !isWhatsAppTemplate ? (
                  <div className="text-center flex items-center justify-center h-full">
                    <LoadingBlock />
                  </div>
                ) : template ? (
                  <div className="h-full flex flex-col">
                    <div className="font-medium bg-primary-200 p-2 flex justify-between items-center">
                      <span>{template.title}</span>
                      <div className="bg-primary-100 text-primary-600 px-2 py-0.5 text-xs rounded">
                        {template.language === 'en'
                          ? t(
                              'ctint-mf-cdss.common.templateConfigurationSelector.languageEN'
                            )
                          : template.language === 'zh_cn'
                            ? t(
                                'ctint-mf-cdss.common.templateConfigurationSelector.languageZHCN'
                              )
                            : template.language === 'zh_hk'
                              ? t(
                                  'ctint-mf-cdss.common.templateConfigurationSelector.languageZHTW'
                                )
                              : template.language ||
                                t(
                                  'ctint-mf-cdss.common.templateConfigurationSelector.languageEN'
                                )}
                      </div>
                    </div>
                    <div className="text-sm whitespace-pre-wrap flex-1 bg-primary-100 p-2">
                      {template.content}
                    </div>
                  </div>
                ) : (
                  <div className="text-center text-gray-500 flex items-center justify-center h-full">
                    <div className="flex flex-col items-center gap-2">
                      <IconEmptyRecords size="48" />
                      <span className="text-sm">
                        {t(
                          'ctint-mf-cdss.common.templateConfigurationSelector.noTemplateSelected'
                        )}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>
            {templateValidationErrors && (
              <p className="text-red-500 text-xs italic mt-1">
                {templateValidationErrors}
              </p>
            )}
          </div>
        ) : (
          /* Read-only preview */
          <div className="flex flex-col gap-2 border border-gray-200 rounded-md p-2">
            <div className="flex justify-between items-center">
              <label className="block text-sm font-medium">
                {languageLabel} -{' '}
                {t(
                  'ctint-mf-cdss.common.templateConfigurationSelector.previewLabel'
                )}
              </label>
            </div>
            <div className="h-[290px] overflow-y-auto">
              {template ? (
                <div className="h-full flex flex-col">
                  <div className="font-medium bg-primary-200 p-2 flex justify-between items-center">
                    <span>{template.title}</span>
                    <div className="bg-primary-100 text-primary-600 px-2 py-0.5 text-xs rounded">
                      {template.language === 'en'
                        ? t(
                            'ctint-mf-cdss.common.templateConfigurationSelector.languageEN'
                          )
                        : template.language === 'zh_cn'
                          ? t(
                              'ctint-mf-cdss.common.templateConfigurationSelector.languageZHCN'
                            )
                          : template.language === 'zh_hk'
                            ? t(
                                'ctint-mf-cdss.common.templateConfigurationSelector.languageZHTW'
                              )
                            : template.language ||
                              t(
                                'ctint-mf-cdss.common.templateConfigurationSelector.languageEN'
                              )}
                    </div>
                  </div>
                  <div className="text-sm whitespace-pre-wrap flex-1 bg-primary-100 p-2">
                    {template.content}
                  </div>
                </div>
              ) : (
                <div className="text-center text-gray-500 flex items-center justify-center h-full">
                  <div className="flex flex-col items-center gap-2">
                    <IconEmptyRecords size="48" />
                    <span className="text-sm">
                      {t(
                        'ctint-mf-cdss.common.templateConfigurationSelector.noTemplateSelected'
                      )}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="mb-4 mt-4">
      <h3 className="font-semibold mb-2">{resolvedTitle}</h3>

      {/* Queue Selection */}
      {showQueueInput && (
        <div className="mb-4">
          <label className="block text-sm font-medium mb-1">
            {resolvedQueueLabel}
          </label>
          {renderQueueSelect()}
          {queueValidationError && (
            <p className="text-xs text-red-500 mt-1">{queueValidationError}</p>
          )}
        </div>
      )}

      {/* Language Selection */}
      <div className="mb-4">
        <label className="block text-sm font-medium mb-1">
          {t(
            'ctint-mf-cdss.common.templateConfigurationSelector.languageLabel'
          )}
        </label>
        <Select
          disabled={!isEditMode}
          mode="multiple"
          value={selectedLanguages}
          onChange={handleLanguageChange}
          options={languageOptions(t)}
          isPagination={false}
          placeholder="Select languages..."
          labelClassName="h-full"
          triggerClassName="disabled:opacity-100 disabled:bg-grey-100 h-12 md:h-8 disabled:text-black text-sm"
          displaySelectedItems={true}
          removeAllSelection={handleRemoveAllLanguageSelections}
          removeSelection={handleRemoveLanguageSelection}
          status={languageValidationErrors ? 'danger' : undefined}
          message={languageValidationErrors}
        />
      </div>

      {/* Individual Language Template Sections */}
      {(alwaysShowTemplateSelector || selectedLanguages.length > 0) && (
        <div className="mb-4">
          {/* <label className="block text-sm font-medium">
            {resolvedTemplateLabel}
          </label> */}

          {selectedLanguages.length === 0 ? (
            <div className="text-center py-8 text-gray-500 border border-gray-200 rounded-lg">
              <div className="flex flex-col items-center gap-2">
                <IconEmptyRecords size="64" />
                <span>Please select languages first</span>
              </div>
            </div>
          ) : isEditMode ? (
            <div className="space-y-4">
              {selectedLanguages.map((language) =>
                renderLanguageTemplateSection(language)
              )}
            </div>
          ) : (
            /* Read-only mode with grid layout */
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {selectedLanguages.map((language) =>
                renderLanguageTemplateSection(language)
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MultiLanguageTemplateConfigurationSelector;
