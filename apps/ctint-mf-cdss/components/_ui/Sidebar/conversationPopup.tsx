import IconBadge from '@cdss-modules/design-system/components/_ui/ConversationItem/IconBadge';
import { QueueName } from '@cdss-modules/design-system/components/_ui/ConversationItem/queueName';
import { Phone } from 'lucide-react';
import React from 'react';
import { string } from 'yup';

interface TConversationPopup {
  left?: string;
  top?: string;
  declineAction: () => void;
  pickUpAction: () => void;
  type: string;
  name: string;
  queueId: string;
  conversation: any;
  mediaType: string;
}
export default function ConversationPopup({
  left = '0.1rem',
  top = '0.1rem',
  declineAction,
  pickUpAction,
  type,
  name,
  queueId,
  conversation,
  mediaType = '',
}: TConversationPopup) {
  console.log('popup conversation', conversation);
  console.log('popup mediaType', mediaType);
  console.log('popup queueId', queueId);
  let iconType = type;
  if (type === 'callback' && mediaType === 'callback.voicemail') {
    iconType = 'voicemail';
  }

  // 为 email 类型处理邮箱名称显示
  const displayName = React.useMemo(() => {
    if (type === 'email' && name.includes('@')) {
      // 如果是多个邮箱地址，只显示第一个
      const firstEmail = name.split(',')[0].trim();
      // 如果邮箱地址很长，截断显示
      if (firstEmail.length > 25) {
        return `${firstEmail.substring(0, 22)}...`;
      }
      return firstEmail;
    }
    return name;
  }, [name, type]);

  console.log(iconType);
  return (
    <div
      className="z-50 min-w-96 bg-white rounded-xl shadow-lg p-5 space-y-4 absolute conversationPopup heartbeat"
      style={{ left: left ? `${left}` : '0', top: top ? `${top}` : '0' }}
    >
      <div className="flex w-full items-center space-x-2">
        <IconBadge
          type={iconType}
          containerClassName="w-12 h-12"
          iconClassName="w-12 h-12"
          icon={conversation?.icon}
        />
        <span className="text-lg">{displayName}</span>
      </div>

      <div className="space-y-2 w-full">
        <div className="text-xl font-bold">Queue:</div>
        <div className="text-2xl">
          <QueueName queueId={queueId} />
        </div>
      </div>

      <div className="flex gap-4 pt-2">
        <button
          onClick={declineAction}
          className="flex-1 px-2 py-2 border-2 border-black rounded-lg text-lg font-medium hover:bg-gray-100 transition-colors"
        >
          Decline
        </button>
        <button
          onClick={pickUpAction}
          className="flex-1 px-2 py-2 bg-black text-white rounded-lg text-lg font-medium hover:bg-gray-800 transition-colors"
        >
          Answer
        </button>
      </div>
    </div>
  );
}
