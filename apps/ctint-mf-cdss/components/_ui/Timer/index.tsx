import { cn } from '@cdss-modules/design-system/lib/utils';
import { useState, useEffect } from 'react';

interface TimerProps {
  initialTime?: number | string; // Accepts both timestamp and ISO string
  className?: string;
  show?: boolean;
  showAsProgressRing?: boolean;
  countDuration?: number;
}

export const Timer = ({
  initialTime = Date.now(),
  className,
  show = true,
  showAsProgressRing,
  countDuration = 300000,
}: TimerProps) => {
  const [time, setTime] = useState<string>('00:00');
  const [count, setCount] = useState<number>(0);

  useEffect(() => {
    if (!initialTime) return;
    // Convert initialTime to a Date object
    const initialDate = new Date(initialTime);

    // Function to update the time
    const updateTimer = () => {
      const now = new Date();
      const diffInMs = Math.max(now.getTime() - initialDate.getTime(), 0);
      const diffInSeconds = Math.floor(diffInMs / 1000);
      setCount(diffInMs);

      const minutes = Math.floor(diffInSeconds / 60);
      const seconds = diffInSeconds % 60;

      // Format time mm:ss
      const formattedTime = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
      setTime(formattedTime);
    };
    updateTimer();
    // Set interval to update time every second
    const intervalId = setInterval(updateTimer, 1000);

    // Clear interval on component unmount
    return () => clearInterval(intervalId);
  }, [initialTime]);

  const countProgress = Math.min(100, (count / countDuration) * 100);
  const progressRing = (size = 'size-16', stroke = '10', label = '') => (
    <div className={cn('relative', size)}>
      <svg
        className="w-full h-full"
        viewBox="0 0 100 100"
      >
        <circle
          className="text-gray-200 stroke-current"
          strokeWidth={stroke}
          cx="50"
          cy="50"
          r="40"
          fill="transparent"
        />
        <circle
          className={cn(
            'progress-ring__circle stroke-current origin-center -rotate-90',
            countProgress >= 99 && 'animate-pulse',
            label && 'fill-transparent',
            !label && countProgress >= 99
              ? 'fill-status-danger'
              : 'fill-transparent',
            countProgress >= 90 && 'text-status-danger',
            countProgress >= 50 && countProgress < 90 && 'text-primary-600',
            countProgress < 50 && 'text-status-success'
          )}
          strokeWidth={stroke}
          strokeLinecap="round"
          cx="50"
          cy="50"
          r="40"
          strokeDasharray="251.2"
          strokeDashoffset={`calc(251.2px - (251.2px * ${countProgress}) / 100)`}
        />
        {label && (
          <text
            x="50"
            y="50"
            textAnchor="middle"
            alignmentBaseline="middle"
            className={
              !label && countProgress >= 99 ? 'fill-white' : 'fill-black'
            }
            fontSize={label.length > 5 ? 18 : 22}
          >
            {label}
          </text>
        )}
      </svg>
    </div>
  );

  if (!show) return;

  if (showAsProgressRing) {
    return progressRing('size-12', undefined, time);
  }

  return (
    <div className="inline-flex gap-x-1 items-center">
      {countProgress >= 99 ? (
        <div className="font-bold rounded-full size-4 bg-status-danger text-white flex items-center justify-center animate-pulse">
          !
        </div>
      ) : (
        <div>{progressRing('size-4', '20')}</div>
      )}

      <div className={cn(className, '-mb-px')}>{time}</div>
    </div>
  );
};

export default Timer;
