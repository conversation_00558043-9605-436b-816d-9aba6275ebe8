'use client';

import Button from '@cdss-modules/design-system/components/_ui/Button';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import Field from '@cdss-modules/design-system/components/_ui/Field';

import { useEffect, memo } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';

const loginSchema = yup
  .object({
    username: yup.string().required('Username is required'),
    password: yup.string().required('Password is required'),
  })
  .required();

export type TLoginFormProps = {
  isLoading: boolean;
  setIsLoading: (isLoading: boolean) => void;
  onSubmit: (data: any) => void;
  defaultValues?: any;
  readonlyFields?: string[];
  onBack?: () => void;
};

const LoginFormComponent = ({
  isLoading,
  onSubmit,
  defaultValues,
  readonlyFields,
  onBack,
}: TLoginFormProps) => {
  const {
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm({
    resolver: yupResolver(loginSchema),
  });

  useEffect(() => {
    if (!defaultValues) return;
    reset(defaultValues);
  }, [defaultValues, reset]);

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="h-full overflow-y-auto px-2 md:px-5"
    >
      <div className="flex flex-col gap-6">
        <div>
          <Field
            title={'Username'}
            icon={<Icon name="error" />}
            status={errors?.username?.message ? 'danger' : undefined}
            message={errors?.username?.message}
          >
            <Controller
              name="username"
              control={control}
              rules={{ required: true }}
              render={({ field }) => (
                <Input
                  placeholder="username / email"
                  {...field}
                  readOnly={
                    readonlyFields && readonlyFields?.includes('username')
                  }
                />
              )}
            />
          </Field>
        </div>
        <div>
          <Field
            title={'Password'}
            icon={<Icon name="error" />}
            status={errors?.password?.message ? 'danger' : undefined}
            message={errors?.password?.message}
          >
            <Controller
              name="password"
              control={control}
              rules={{ required: true }}
              render={({ field }) => (
                <Input
                  autoFocus={true}
                  type="password"
                  placeholder="password"
                  {...field}
                  readOnly={
                    readonlyFields && readonlyFields?.includes('password')
                  }
                />
              )}
            />
          </Field>
        </div>
        <div className="flex flex-col gap-4">
          <Button
            type="submit"
            fullWidth
            disabled={isLoading}
            variant={'primary'}
          >
            Login
          </Button>
          {onBack && (
            <Button
              onClick={() => onBack()}
              fullWidth
              disabled={isLoading}
              variant="secondary"
            >
              Back
            </Button>
          )}
        </div>
      </div>
    </form>
  );
};

export const LoginForm = memo(LoginFormComponent);

export default LoginForm;
