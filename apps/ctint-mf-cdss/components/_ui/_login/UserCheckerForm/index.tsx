'use client';

import Button from '@cdss-modules/design-system/components/_ui/Button';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import Field from '@cdss-modules/design-system/components/_ui/Field';

import { memo } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';

const loginSchema = yup
  .object({
    username: yup.string().required('Username is required'),
  })
  .required();

export type TUserCheckerFormProps = {
  isLoading: boolean;
  setIsLoading: (isLoading: boolean) => void;
  onSubmit: (data: any) => void;
};

export const UserCheckerFormComponent = ({
  isLoading,
  onSubmit,
}: TUserCheckerFormProps) => {
  const {
    handleSubmit,
    control,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(loginSchema),
  });

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="h-full overflow-y-auto px-2 md:px-5"
    >
      <div className="flex flex-col gap-6">
        <div>
          <Field
            title={'Username'}
            icon={<Icon name="error" />}
            status={errors?.username?.message ? 'danger' : undefined}
            message={errors?.username?.message}
          >
            <Controller
              name="username"
              control={control}
              rules={{ required: true }}
              render={({ field }) => (
                <Input
                  placeholder="username / email"
                  {...field}
                />
              )}
            />
          </Field>
        </div>
        <Button
          type="submit"
          fullWidth
          disabled={isLoading}
          variant={'primary'}
        >
          Next
        </Button>
      </div>
    </form>
  );
};

export const UserCheckerForm = memo(UserCheckerFormComponent);

export default UserCheckerForm;
