{"ctint-mf-cdss": {"template": "Template", "lipsum": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. <PERSON><PERSON>am nec purus nec nunc", "header": {"interactions": "Interactions", "users": "Users", "audit": "Audit", "qmAdmin": "QM Admin", "callPatch": "Call Patch", "logout": "Logout", "playbackPortal": "Playback Portal", "tts": "TTS", "singleMessage": "Single Message", "styleGuide": "Style Guide", "info": "Info"}, "login": {"username": "Username", "usernameOrEmail": "Username / Email", "password": "Password", "login": "<PERSON><PERSON>", "back": "Back"}, "langDemo": {"languageDemo": "Language Demo", "currentLanguage": "Current Language", "changeLanguage": "Change Language", "changeTo": "Change to"}, "qmAdmin": {"tabs": {"sop": "SOP", "dictionary": "Dictionary", "metaDataMapping": "Meta Data Mapping"}, "evaluationForm": "Evaluation Form", "evaluationFormPlaceholder": "Please select the evaluation form", "formVersion": "Form Version", "formVersionPlaceholder": "Please select the form version", "emptyData": "No data", "sop": {"title": "SOP: ELI Public Offer", "changeScore": "Change Score", "edit": "Edit", "back": "Back", "save": "Save", "columns": {"step": "Step", "scenarioId": "Scenario ID", "content": "Content", "doSentenceSimilarity": "Sentence Similarity", "scoreToPassed": "Score to Pass"}, "yes": "Yes", "no": "No"}, "dictionary": {"title": "Dictionary", "changeByUpload": "Change by Upload", "uploadDictionary": "Upload Dictionary", "fileName": "File Name", "fileSize": "File Size", "submit": "Submit", "remove": "Remove"}, "metaDataMapping": {"title": "Meta Data Mapping", "key": "Key", "value": "Value", "display": "Display"}}, "callPatch": {"recordingId": "Recording ID", "userId": "User ID", "userName": "User Name", "branchCode": "Branch Code", "saReferenceNumber": "SA Reference Number", "customerCIF": "Customer CIF", "investmentOrderType": "Investment Order Type", "investmentAccount": "Investment Account", "language": "Language", "officerCode": "Officer Code", "productName1": "Product Series No. 1", "productName2": "Product Series No. 2", "productName3": "Product Series No. 3", "cashSettlementAccountNumber": "Cash Settlement Account Number", "remoteNumber": "Remote Number", "localNumber": "Local Number"}, "menu": {"interactions": "Interactions", "users": "Users", "audit": "Audit", "qmadmin": "QM Admin", "DNC&Blacklist": "DNC&Blacklist", "superdashboardadmin": "Super Dashboard Admin", "superdashboard": "Super Dashboard", "callpatch": "Call Patch", "report": "Report", "campaign": "Broadcast", "email-admin": "<PERSON><PERSON>", "auto-reply": "Auto Reply Admin", "content-creation": "Admin Content Creation"}, "common": {"routingRuleEditor": {"conditionLabel": "Condition", "removeConditionButton": "Remove Condition", "subjectLabel": "Subject", "domainLabel": "Domain", "operatorEquals": "equals", "operatorContains": "contains", "valuePlaceholder": "Value", "logicMatchAll": "Match ALL conditions (AND)", "logicMatchAny": "Match ANY condition (OR)", "returnConfigurationTitle": "Return Configuration", "ruleNumberPrefix": "Rule #", "removeRuleButton": "Remove Rule", "logicTypeLabel": "Logic Type", "priorityLabel": "Priority (Lower number = higher priority)", "priorityPlaceholder": "1", "addFirstRuleButton": "Add First Rule", "noRulesDefined": "No routing rules defined. Rules determine how incoming emails are prioritized and routed.", "addConditionButton": "Add Condition", "addRuleButton": "Add Rule", "timeLabel": "Time", "noQueueOptionLabel": "(None)", "emailAddressGroupLabel": "Email Address", "operatorIn": "in", "operatorIsBusinessHour": "is business hour", "operatorWithin": "within", "operatorOutside": "outside", "operatorBefore": "before", "operatorAfter": "after"}, "templateConfigurationSelector": {"previewLabel": "Preview", "noTemplateSelected": "No template selected", "languageEN": "EN", "languageZHCN": "SC", "languageZHTW": "TC", "fileSizeKB": "KB", "loadingAttachments": "Loading attachments...", "langEnglish": "English", "langChineseSimplified": "Chinese (Simplified)", "langChineseTraditional": "Chinese (Traditional)", "noQueueOptionLabel": "(None)", "placeholder": "No Queue", "loadingTemplates": "Loading templates...", "noTemplatesForLanguage": "No templates available for the selected language", "languageLabel": "Language", "selectLanguagePlaceholder": "Select language", "searchTemplatesPlaceholder": "Search templates", "defaultTitle": "Configuration", "defaultQueueLabel": "Select Queue", "defaultTemplateLabel": "Select Template", "loadingStatus": "Loading...", "viewButton": "View", "unavailableStatus": "Unavailable"}}, "emailAdmin": {"mailboxRoutingRules": {"common": {"status": "Status", "active": "Active", "inactive": "Inactive", "allStatuses": "All Statuses", "allQueues": "All Queues", "defaultQueue": "<PERSON><PERSON><PERSON>", "view": "View", "edit": "Edit", "delete": "Delete", "cancel": "Cancel", "return": "Return", "saveRule": "Save Rule", "saving": "Saving...", "deleting": "Deleting...", "editRule": "Edit Rule", "mailboxAddress": "Mailbox Address", "saveSuccess": "Rule Saved Successfully", "saveSuccessDescription": "The mailbox routing rule has been saved successfully.", "validationError": "Validation Error", "validationErrorDescription": "Please correct the highlighted errors before saving.", "saveError": "Save Failed", "saveErrorDescription": "An error occurred while saving. Please try again.", "ok": "OK", "priorityErrorGuidance": "Check rule priorities - each rule must have a unique priority number.", "conditionErrorGuidance": "Check rule conditions - all condition values must be filled in.", "mailboxErrorGuidance": "Check the mailbox address - it must be a valid email format.", "queueErrorGuidance": "Check queue selections - a default queue must be selected."}, "listing": {"title": "Mailbox Routing Rules", "columnLastUpdated": "Last Updated", "columnLastUpdatedBy": "Last Updated By", "columnAction": "Action", "confirmDeleteTitle": "Confirm Deletion", "confirmDeleteMessage": "Are you sure you want to delete this routing rule?", "deleteSuccessMessage": "Routing rule deleted successfully", "deleteSuccessDescription": "The mailbox routing rule has been permanently removed from the system.", "deleteErrorMessage": "Error deleting routing rule: {{message}}", "searchPlaceholder": "Search by mailbox address", "createNewButton": "Create New", "emptyMessage": "No routing rules found"}, "detail": {"createTitle": "Create New Mailbox Routing Rule", "editTitle": "Edit Mailbox Routing Rule", "viewTitle": "Mailbox Routing Rule Details", "basicInformationSection": "Basic Information", "mailboxAddressPlaceholder": "Enter mailbox address", "defaultReturnConfigurationSection": "Default Return Configuration", "defaultReturnDescription": "Specify where emails should be routed if no rules match. At least one option must be selected.", "defaultAutoReplyTemplateLabel": "Default Auto-Reply Template", "routingRulesSection": "Routing Rules", "errorLoadingTitle": "Error Loading Mailbox Routing Rule", "errorLoadingMessage": "An error occurred while loading the routing rule. Please try again.", "notFoundTitle": "Mailbox Routing Rule Not Found", "notFoundMessage": "The requested routing rule could not be found.", "backToListButton": "Back to List", "generalValidationError": "Please correct the errors before saving."}}, "autoReplyExclusions": {"common": {"ruleType": "Rule Type", "pattern": "Pattern", "address": "Address", "email": "Email", "subject": "Subject", "domain": "Domain", "allRuleTypes": "All Rule Types", "cancel": "Cancel", "delete": "Delete", "deleting": "Deleting...", "saveSuccess": "Exclusion Rule Saved Successfully", "saveSuccessDescription": "The auto reply exclusion rule has been saved successfully."}, "listing": {"title": "Auto Reply Exclusions", "confirmDeleteTitle": "Confirm Deletion", "confirmDeleteMessage": "Are you sure you want to delete this exclusion rule?", "deleteSuccessMessage": "Exclusion rule deleted successfully", "deleteSuccessDescription": "The auto reply exclusion rule has been permanently removed from the system.", "deleteErrorMessage": "Error deleting exclusion rule: {{message}}", "searchPlaceholder": "Search by pattern", "emptyMessage": "No exclusion rules found"}, "detail": {"createTitle": "Create New Auto Reply Exclusion Rule", "editTitle": "Edit Auto Reply Exclusion Rule", "viewTitle": "Auto Reply Exclusion Rule Details", "errorLoadingTitle": "Error Loading Auto Reply Exclusion", "errorLoadingMessage": "An error occurred while loading the exclusion. Please try again.", "notFoundTitle": "Auto Reply Exclusion Not Found", "notFoundMessage": "The requested exclusion could not be found.", "quickSelect": {"outOfOffice": "Out of Office", "autoReply": "Auto-Reply", "vacation": "Vacation", "automaticReply": "Automatic Reply", "gmailCom": "gmail.com", "outlookCom": "outlook.com", "yahooCom": "yahoo.com", "hotmailCom": "hotmail.com", "noreplyExample": "<EMAIL>", "adminExample": "<EMAIL>", "supportExample": "<EMAIL>", "infoExample": "<EMAIL>"}, "patternLabelSubject": "Subject Pattern", "patternPlaceholderSubject": "Enter subject text", "patternLabelDomain": "Domain Pattern", "patternPlaceholderDomain": "Enter domain (e.g., example.com)", "patternLabelEmail": "Email Address", "patternPlaceholderEmail": "Enter email address", "exclusionCriteriaSection": "Exclusion Criteria", "exclusionCriteriaDescription": "Specify patterns to match emails that should be excluded from receiving auto-replies.", "ruleTypeSubjectDescription": "Match email subject lines", "ruleTypeDomainDescription": "Match sender domains", "ruleTypeEmailDescription": "Match specific senders", "quickSelectLabel": "Quick Select:"}}, "emailAddressGroups": {"common": {"groupName": "Group Name", "emailAddress": "Email Address", "action": "Action", "select": "Select", "cancel": "Cancel", "createNew": "Create New", "edit": "Edit", "delete": "Delete", "save": "Save", "add": "Add"}, "listing": {"title": "Email Address Groups", "searchByGroupName": "Search by group name", "searchByEmailAddress": "Search by email address", "emptyMessage": "No email address groups found.", "emailCount": "Email Count", "emailAddressesCount": "Email Addresses ({{count}}):", "noEmailsInGroup": "No emails in this group.", "confirmDeleteTitle": "Confirm Deletion", "confirmDeleteMessage": "Are you sure you want to delete this email address group?", "deleteSuccessMessage": "Email address group deleted successfully", "deleteErrorMessage": "Error deleting email address group: {{message}}", "searchPlaceholder": "Search by group name or email address"}, "detail": {"createTitle": "Create New Email Address Group", "editTitle": "Edit Email Address Group", "saveGroup": "Save Group", "groupNamePlaceholder": "Enter group name", "addEmailAddressLabel": "Add Email Address", "emailAddressPlaceholder": "Enter email address", "importExportSection": "Import/Export Emails", "downloadCsvTemplate": "Download CSV Template", "importFromCsvExcel": "Import from CSV/Excel:", "loadEmailsFromFile": "Load Emails from File", "emailsStagedForImport": "Emails Staged for Import ({{count}}):", "correctIssuesBelow": "Please correct the issues below:", "cancelImport": "Cancel Import", "confirmAddToGroup": "Confirm & Add to Group", "editingEmails": "Editing Emails ({{count}})", "addedEmails": "Added Emails ({{count}})", "noEmailsToEdit": "No emails to edit.", "noEmailsAdded": "No emails added yet.", "cancelEdit": "Cancel Edit", "saveEmailChanges": "Save Email Changes", "editEmails": "Edit Emails"}, "picker": {"selectGroupPlaceholder": "Select Email Address Group", "popupTitle": "Select Email Address Group"}}}}, "ctint-mf-user-admin": {"test": "test:en", "templateHome": {"title": "CDSS 3.0 Microfrontend Template", "desc": "This is a template project for microfrontend development in CDSS 3.0. Click below button to go to example detail page.", "btnLabel": "Go to Detail"}, "templateDetail": {"title": "CDSS 3.0 Microfrontend Template - Detail Page", "desc": "This is the detail page of template project for microfrontend development in CDSS 3.0.", "btnLabel": "Go back to Main Page"}, "langDemo": {"languageDemo": "Language Demo", "currentLanguage": "Current Language", "changeLanguage": "Change Language", "changeTo": "Change to"}, "filter": {"search": "Search", "clear": "Clear", "name": "Filters here"}, "tab": {"user": "User", "userGroup": "User Groups", "queueGroup": "Queue Groups", "newQueueGroup": "New Queue Group", "newUserGroup": "New User Group", "new": "New User"}, "formAction": {"discard": "Discard", "save": "Save", "edit": "Edit", "notAllowEdit": "Not Allow to Edit"}, "form": {"password": "Password", "passworPlacehoder": "Please enter your password", "invaildRequired": "Please input the necessary field"}}}