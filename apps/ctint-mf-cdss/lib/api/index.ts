import { apiConfig } from './config';
import axios, { AxiosHeaders } from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { addLoggingToAxios } from '@cdss-modules/design-system/lib/logging';
import { TAdminUserData } from '../../types/microfrontendsConfig';
import { TGroupPostData } from '../../types/group';
import { TAuditLogQueryParams } from '@cdss/types/adminAudit';
import {
  TSubmitWrapup,
  TCallControlType,
} from '@cdss-modules/design-system/@types/index';
import { TCallActionProps } from '@cdss/types/conversation';

// Helper function to get canned message spaceId from config
const getCannedMessageSpaceId = (globalConfig?: any): string => {
  // Get spaceId from global config, with fallback to configured value
  return (
    globalConfig?.cannedMessage?.spaceId ||
    '2ee62abe-3fc8-4eaa-93f2-3859784a2664'
  );
};

type UserConfigItems = {
  name: string;
  value: string;
};

type UserConfigProps = {
  filters?: UserConfigItems[];
  columns?: UserConfigItems[];
};

export const axiosInstance = axios.create({
  timeout: 20000,
  headers: {
    'Content-Type': 'application/json',
    traceId: uuidv4(),
    tenant: 'ctint',
    sourceId: 'ctint-mf-cdss',
    previousId: 'ctint-bff-cdss',
    authorization: 'Basic U3ZL1wwweiscrQPkJ9fLtU3cNHngebsJ',
  },
});

export const axiosDownloadInstance = axios.create({
  timeout: 20000,
  headers: {
    traceId: uuidv4(),
    tenant: 'ccba',
    sourceId: 'ctint-mf-interaction',
    previousId: 'ctint-bff-cdss',
  },
});
axiosDownloadInstance.interceptors.request.use(
  (config) => {
    // TODO: should call the auth api and set the token when program initialize
    if (
      process.env.NODE_ENV === 'development' &&
      localStorage.getItem('cdss-auth-token') === null
    ) {
      // localStorage.setItem('deviceId', uuidv4());
      localStorage.setItem(
        'gc-access-token',
        '4ne850UXVwj7J1oqS0yP05IqNLxnE_i81-js3tXdf5uCkuOQHE737vRCO8wHvXLC-VNZKOUnUctkT7k8RyvYbg'
      );
      // localStorage.setItem(
      //   'cdss-auth-token',
      //   'EdOImMlG47o8McBVtqY2QKHtIoT7OPznIInDv7cOXG+0DnbPZAcG6Ra+604VzhdT+fX3I2pbhPKWfQSrGpd+hZ5OuZOcptbvJ01GpJkpzCbiiT6TpuU='
      // );
    }
    const cdssAuthToken = localStorage.getItem('cdss-auth-token') || '';
    const gcAccessToken = localStorage.getItem('gc-access-token') || '';
    const deviceId = localStorage.getItem('deviceId') || '';
    config.headers['tenant'] = localStorage.getItem('tenant') || 'ctint';
    if (cdssAuthToken) {
      config.headers['cdss-authorization'] = 'Bearer ' + cdssAuthToken;
    }
    if (deviceId) {
      config.headers['deviceId'] = deviceId;
    }
    return config;
  },
  (error) => {
    Promise.reject(error);
  }
);
axiosInstance.interceptors.request.use(
  (config) => {
    if (
      process.env.NODE_ENV === 'development' &&
      localStorage.getItem('cdss-auth-token') === null
    ) {
      // localStorage.setItem('deviceId', uuidv4());
      // localStorage.setItem(
      //   'gc-access-token',
      //   'ofROc0Va1i74x3CKxPJ-et8g9jwo08wFkRgCzCcIYDwdAEmJMCIh-HDn9Sgw_U17WxTvtwnngeOksDWN8Sbatw'
      // );
      // localStorage.setItem(
      //   'cdss-auth-token',
      //   // 'EdOImMlG47o8McBVtqYyQbz7d4nte/7lO8HE+qEKFHupHznCcQMNoFOo+gYGxBtX8viFDj0Zgb6upeTMWiHvnNr09Q=='
      //   'EdOImMlG47o8McBVtqY2QKHtIoT7OPznIInDv7cOXG+0DnbPZAcG6Ra+604VzhdT+fX3I2pbhPKWfQSrGpd+hZ5OuZOcptbvJ01GpJkpzCbiiT6TpuU='
      // );
    }
    const cdssAuthToken = localStorage.getItem('cdss-auth-token') || '';
    const gcAccessToken = localStorage.getItem('gc-access-token') || '';
    const deviceId = localStorage.getItem('deviceId') || '';
    const loginPlatform = localStorage.getItem('loginPlatform') || '';
    config.headers['tenant'] = localStorage.getItem('tenant') || 'ctint';
    if (cdssAuthToken) {
      config.headers['cdss-authorization'] = 'Bearer ' + cdssAuthToken;
    }
    if (deviceId) {
      config.headers['deviceId'] = deviceId;
    }
    if (loginPlatform) {
      config.headers['loginPlatform'] = loginPlatform;
    }
    if (config.url?.includes('/interaction/recordings/transcript/')) {
      config.headers['requester'] = 'agent1';
    }
    return config;
  },

  (error) => {
    Promise.reject(error);
  }
);

axiosInstance.interceptors.response.use(
  (response) => {
    // Return response as is if the request is successful
    return response;
  },
  (error) => {
    const errorCode = (localStorage.getItem('errorCode') || '').split(',');
    if (errorCode && errorCode.includes(error?.response?.status.toString())) {
      const currUrl = window.location.href;
      if (currUrl?.indexOf('login') === -1) {
        const basePath = (window as any)?.GLOBAL_BASE_PATH;
        window.location.href = `${basePath}/login`;
      }
      localStorage.removeItem('cdss-auth-token');
      localStorage.removeItem('gc-access-token');
      localStorage.removeItem('permissions');
    }
    return Promise.reject(error);
  }
);

addLoggingToAxios(axiosInstance);

export const fireLogin = (
  username: string,
  password: string | null | undefined,
  deviceId: string,
  authCode?: string,
  basePath = ''
) => {
  const port = window.location.port ? `:${window.location.port}` : '';
  const redirectUri = window
    ? `${window?.location?.protocol}//${window?.location?.hostname}${port}${basePath}/login`
    : '';

  const queryParams = new URLSearchParams({
    grant_type: 'password',
    username,
    deviceId,
    redirectUrl: redirectUri,
    ...(authCode ? { authCode } : {}),
    ...(password ? { password } : {}),
  });

  return axiosInstance.post(`${basePath}${apiConfig.paths.login}`, {
    data: decodeURIComponent(queryParams.toString()),
  });
};

export const fireCheckAuthUser = (username: string, basePath = '') => {
  return axiosInstance.post(`${basePath}${apiConfig.paths.checkAuthUser}`, {
    username,
  });
};

export const geVerifyUser = (key: string, value: string, basePath = '') => {
  return axiosInstance.post(`${basePath}${apiConfig.paths.geVerifyUser}`, {
    key,
    value,
  });
};

// Admin
export const GetUserList = (basePath = '') =>
  axiosInstance.get(`${basePath}${apiConfig.paths.userList}`);

export const GetRolesList = (basePath = '') =>
  axiosInstance.get(`${basePath}${apiConfig.paths.getRoles}`);

export const GetQueueList = (basePath = '') =>
  axiosInstance.get(`${basePath}${apiConfig.paths.getQueues}`);

// user-queue-group-portal
export const GetGroupList = (basePath = '', params: 'user' | 'queue') =>
  axiosInstance.get(
    `${basePath}${apiConfig.paths.aboutAllGroupApi}?type=${params}`
  );

export const GetGroupDetail = (basePath = '', groupId: string) =>
  axiosInstance.get(
    `${basePath}${apiConfig.paths.aboutAllGroupApi}/${groupId}`
  );
export const AddGroup = (basePath = '', data: TGroupPostData) =>
  axiosInstance.post(
    `${basePath}${apiConfig.paths.aboutAllGroupApi}?type=${data.type}`,
    data
  );

export const UpdateGroup = (basePath = '', data: TGroupPostData) =>
  axiosInstance.post(
    `${basePath}${apiConfig.paths.aboutAllGroupApi}/${data.id}`,
    data
  );

export const CreateUser = (basePath = '', data: TAdminUserData) =>
  axiosInstance.post(`${basePath}${apiConfig.paths.userCreate}`, data);

export const UpdateUser = (basePath = '', data: TAdminUserData) =>
  axiosInstance.put(`${basePath}${apiConfig.paths.userUpdate}`, data);
export const GetUserAuditLogs = (basePath = '', data: TAuditLogQueryParams) =>
  axiosInstance.post(`${basePath}${apiConfig.paths.userAuditLogs}`, data);

export const fireSaveSession = (key: string, data: any, basePath = '') => {
  return axiosInstance.post(`${basePath}${apiConfig.paths.session}`, {
    session: key,
    data: {
      userSession: data,
    },
  });
};

export const fireUpdateSession = (key: string, data: any, basePath = '') => {
  return axiosInstance.put(`${basePath}${apiConfig.paths.session}`, {
    session: key,
    data,
  });
};

export const fireGetSession = (key: string, basePath = '') => {
  return axiosInstance.get(`${basePath}${apiConfig.paths.session}?key=${key}`);
};

export const fireDeleteSession = (key: string, basePath = '') => {
  return axiosInstance.delete(`${basePath}${apiConfig.paths.session}`, {
    data: {
      session: key,
    },
  });
};

export const fireLogout = (basePath = '') => {
  return axiosInstance.post(`${basePath}${apiConfig.paths.logout}`);
};

export const fireCallControlAction = (
  conversationsId: string,
  participantId: string,
  action: TCallControlType,
  basePath = ''
) => {
  return axiosInstance.patch(
    `${basePath}${apiConfig.paths.callControl.action}/${conversationsId}/participants/${participantId}`,
    {
      state: action,
    }
  );
};
// QM
export const fireGetStandScriptResult = (
  formId: string | undefined,
  basePath = ''
) => {
  if (!formId || formId.length === 0) return;
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.qm.stand_script_result}?formId=${formId}`
  );
};

export const fireGetFormOptions = (basePath = '') => {
  return axiosInstance.get(`${basePath}${apiConfig.paths.qm.evaluation_form}`);
};

export const fireUpdateStandardScriptScore = (basePath = '', data: any) => {
  return axiosInstance.put(
    `${basePath}${apiConfig.paths.qm.update_standard_script_score}`,
    data
  );
};

export const fireUploadDictionary = (basePath = '', data: FormData) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.qm.upload_dictionary}`,
    data,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
  );
};

export const GetActiveConversations = (
  basePath = '',
  userId?: string,
  deviceId?: string | null
) =>
  axiosInstance.get(
    `${basePath}${apiConfig.paths.getActiveConversations}?userId=${userId}&deviceId=${deviceId}`
  );

export const GetQueues = (basePath = '') =>
  axiosInstance.get(`${basePath}${apiConfig.paths.getQueues}`);

export const PickUpConversationAction = (
  conversationsId: string,
  participantId: string,
  action: TCallActionProps,
  basePath = ''
) => {
  return axiosInstance.patch(
    `${basePath}${apiConfig.paths.conversationAlertAction}/${conversationsId}/participants/${participantId}`,
    {
      state: action,
    }
  );
};

export const fireGetUserRoutingStatus = (userId: string, basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.callControl.userRoutingStatus}/${userId}`
  );
};

export const fireUpdateUserRoutingStatus = (
  userId: string,
  basePath = '',
  statusId: string
) => {
  return axiosInstance.patch(
    `${basePath}${apiConfig.paths.callControl.updateRoutingStatus}/${userId}`,
    { presenceId: statusId }
  );
};

export const fireGetAllAgentStatus = (basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.callControl.getAllAgentStatus}`
  );
};
export const fireGetAllStations = (basePath = '', page = 1, name?: string) => {
  const params = new URLSearchParams();
  params.append('pageNum', page.toString());
  if (name) {
    params.append('name', name);
  }
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.callControl.getAllStations}?${params}`
  );
};

export const fireGetCurrentStation = (userId: string, basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.callControl.getAllStations}/users/${userId}`
  );
};

export const fireGetUpdateStation = (
  userId: string,
  newStationId: string,
  oldStationId: string | undefined,
  basePath = ''
) => {
  return axiosInstance.put(
    `${basePath}${apiConfig.paths.callControl.getAllStations}/users/${userId}/associate`,
    {
      newStationId,
      oldStationId,
    }
  );
};

export const fireDeleteCurrentStation = (
  userId: string,
  stationId: string,
  basePath = ''
) => {
  return axiosInstance.delete(
    `${basePath}${apiConfig.paths.callControl.getAllStations}/users/${userId}/available/${stationId}`
  );
};

export const fireMakeCall = (
  payload: { phoneNumber?: string; callUserId?: string; callQueueId?: string },
  basePath = ''
) => {
  return axiosInstance.post(`${basePath}${apiConfig.paths.callControl.call}`, {
    ...payload,
  });
};

export const fireTransferCall = (
  conversationId: string,
  agentParticipantId: string,
  payload: {
    destinationUserId?: string;
    destinationAddress?: string;
    destinationQueueId?: string;
  },
  basePath = ''
) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.callControl.blindTransfer}`,
    {
      conversationId,
      agentParticipantId,
      ...payload,
    }
  );
};

export const fireConsultCall = (
  conversationId: string,
  customerParticipantId: string,
  payload: {
    destinationUserId?: string;
    destinationAddress?: string;
    destinationQueueId?: string;
  },
  basePath = ''
) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.callControl.consult}`,
    {
      conversationId,
      customerParticipantId,
      ...payload,
    }
  );
};

export const fireConsultDisconnect = (
  state: string,
  conversationId: string,
  agentParticipantId: string,
  basePath = ''
) => {
  return axiosInstance.patch(
    `${basePath}${apiConfig.paths.callControl.consultDisconnect}`,
    {
      state: state,
      conversationId: conversationId,
      agentParticipantId: agentParticipantId,
    }
  );
};

export const fireConsultCancel = (
  conversationId: string,
  customerParticipantId: string,
  basePath = ''
) => {
  return axiosInstance.delete(
    `${basePath}${apiConfig.paths.callControl.consultCancel}`,
    {
      data: {
        conversationId: conversationId,
        customerParticipantId: customerParticipantId,
      },
    }
  );
};

export const fireConferenceCall = (
  speakTo: string,
  conversationId: string,
  customerParticipantId: string,
  basePath = ''
) => {
  return axiosInstance.patch(
    `${basePath}${apiConfig.paths.callControl.conference}`,
    {
      speakTo: speakTo,
      conversationId: conversationId,
      customerParticipantId: customerParticipantId,
    }
  );
};

export const fireIVRConferenceCall = (
  conversationId: string,
  destinationAddress: string,
  basePath = ''
) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.callControl.conference}`,
    {
      conversationId: conversationId,
      destinationAddress: destinationAddress,
    }
  );
};

export const fireGetDirectory = (basePath = '', page = 1, keyword?: string) => {
  const params = new URLSearchParams();
  params.append('pageNum', page.toString());
  params.append('filterType', 'all');

  if (keyword) {
    return axiosInstance.post(
      `${basePath}${apiConfig.paths.users.searchUsers}`,
      {
        keyword,
      }
    );
  } else {
    return axiosInstance.get(
      `${basePath}${apiConfig.paths.users.getAllUsers}?${params}`
    );
  }
};

export const fireGetCurrentUser = (basePath = '') => {
  return axiosInstance
    .get(`${basePath}${apiConfig.paths.users.getCurrentUser}`)
    .then((res) => res.data);
};

export const fireGetAllActiveConverstaions = (
  basePath = '',
  userId = '',
  deviceId = ''
) => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.callControl.getAllActiveConversations}?userId=${userId}&deviceId=${deviceId}`
  );
};

export const fireGetAllConversations = (
  basePath = '',
  userId = '',
  nextLink = '',
  conversationType = '',
  phoneNumber = ''
) => {
  return axiosInstance
    .get(
      `${basePath}${apiConfig.paths.callControl.getAllConversations}?userId=${userId}&nextLink=${nextLink}&type=${conversationType}&phoneNumber=${phoneNumber}`
    )
    .then((res) => res?.data);
};

export const fireGetAllWorkgroups = (
  basePath = '',
  page = 1,
  keyword?: string
) => {
  const params = new URLSearchParams();
  params.append('pageNumber', page.toString());
  params.append('filterType', 'all');

  if (keyword) {
    params.append('keyword', keyword);

    return axiosInstance.get(
      `${basePath}${apiConfig.paths.users.searchWorkgroups}?${params}`
    );
  } else {
    return axiosInstance.get(
      `${basePath}${apiConfig.paths.users.getAllWorkgroups}?${params}`
    );
  }
};

export const fireGetWorkgroupsByUser = (
  basePath = '',
  page = 1,
  userId: string
) => {
  const params = new URLSearchParams();
  params.append('filterType', 'user');
  params.append('userId', userId);
  params.append('pageNumber', page.toString());

  return axiosInstance.get(
    `${basePath}${apiConfig.paths.users.getWorkGroupsByUser}?${params}`
  );
};

export const fireGetMiniWallboardUserAggregates = (basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.miniWallboard.getUserAggregates}`
  );
};
export const fireGetMiniWallboardQueueAggregates = (basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.miniWallboard.getQueueAggregates}`
  );
};

export const fireGetWrapupCode = (
  basePath = '',
  participantId: string,
  conversationId: string
) => {
  const params = new URLSearchParams();
  params.append('participantId', participantId);
  params.append('conversationId', conversationId);

  return axiosInstance.get(
    `${basePath}${apiConfig.paths.callControl.getWrapup}?${params}`
  );
};

export const fireSubmitWrapupCode = (
  basePath = '',
  wrapUpList: TSubmitWrapup[],
  participantId: string,
  conversationId: string,
  remark?: string,
  state?: string
) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.callControl.submitWrapup}`,
    {
      wrapUpList,
      participantId,
      conversationId,
      remark,
      state,
    }
  );
};

export const fireUpdateWrapupCode = (
  basePath = '',
  wrapUpList: TSubmitWrapup[],
  participantId: string,
  conversationId: string,
  remark?: string,
  state?: string
) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.callControl.updateWrapup}`,
    {
      wrapUpList,
      participantId,
      conversationId,
      remark,
      state,
    }
  );
};

export const fireGetWrapupCategory = (userId: string, basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.callControl.getWrapupCategory}?userId=${userId}`
  );
};

export const fireSubmitAttributes = (
  conversationId: string,
  participantId: string,
  attributes: object,
  basePath = ''
) => {
  return axiosInstance.patch(
    `${basePath}${apiConfig.paths.callControl.attributes}`,
    {
      conversationId,
      participantId,
      attributes,
    }
  );
};

export const fireGetTenantConfig = (basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.config.getTenatConfig}`
  );
};
export const GetAgentRoutingStatus = (userId: string, basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.userRoutingStatus}/${userId}`
  );
};

// export const UpdateAgentRoutingStatus = (
//   userId: string,
//   basePath = '',
//   statusId: string
// ) => {
//   return axiosInstance.patch(
//     `${basePath}${apiConfig.paths.updateRoutingStatus}/${userId}`,
//     { presenceId: statusId }
//   );
// };

export const UpdateAgentRoutingStatus = (basePath = '') => {
  return axiosInstance.patch(
    `${basePath}${apiConfig.paths.updateRoutingStatus}`
  );
};

export const GetAllAgentStatus = (basePath = '') => {
  return axiosInstance.get(`${basePath}${apiConfig.paths.getAllAgentStatus}`);
};
export const getUatMessage = (basePath = '', conversationId = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.getUatMessage.replace('{conversationId}', conversationId)}`
  );
};

export const GetTenantConfig = (basePath = '') =>
  axiosInstance.get(
    `${basePath}${apiConfig.paths.getTenantConfig}?key=adminSuperDashboad`
  );

export const UpdateTenantConfig = (basePath = '', data: any) =>
  axiosInstance.post(`${basePath}${apiConfig.paths.getTenantConfig}`, data);

export const UploadAvatar = (basePath = '', formData: FormData) =>
  axiosInstance.post(`${basePath}${apiConfig.paths.uploadAvatar}`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

export const GetImage = (basePath = '') =>
  axiosInstance.get(`${basePath}${apiConfig.paths.getImage}`, {
    responseType: 'blob',
  });

export const sendOutboundMessage = (
  basePath = '',
  conversationId = '',
  reqId = '',
  payload = {}
) => {
  const headers = new AxiosHeaders(axiosInstance.defaults.headers);
  headers.set('reqId', reqId);
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.sendMessage.replace('{conversationId}', conversationId)}`,
    payload,
    { headers }
  );
};
export const GetEmailQueue = (basePath = '') =>
  axiosInstance.get(`${basePath}${apiConfig.paths.manualQueue.emailQueue}`);

export const NewEmailConversation = (basePath = '', payload = {}) => {
  axiosInstance.post(
    `${basePath}${apiConfig.paths.manualQueue.newEmailConversation}`,
    payload
  );
};
export const fireGetFilteredRecordings = (
  queryDatas: object | undefined,
  basePath = ''
) => {
  // fix: if the queryDatas does not contain conversationStart and conversationEnd, will not call the api
  if (
    queryDatas &&
    !('conversationStart' in queryDatas) &&
    !('conversationEnd' in queryDatas)
  )
    return;

  // call api to for GC
  return fireGCRecordingsByFilters(queryDatas, basePath);

  // if (
  //   queryParams.includes('isInbound') ||
  //   queryParams.includes('isOutbound') ||
  //   queryParams.includes('duration') ||
  //   queryParams.includes('order')
  // ) {
  //   return fireGetSortedRecordings(queryParams, basePath);
  // } else if (queryParams.includes('id')) {
  //   const urlParams = new URLSearchParams(queryParams);
  //   const id = urlParams?.get('id') ?? '';

  //   return fireGetSingleRecording(id, basePath);
  // } else {
  //   return fireGetAllRecordings(queryParams, basePath);
  // }
};

export const fireGetSortedRecordings = (queryParams: string, basePath = '') =>
  axiosInstance.get(`${apiConfig.paths.sort}?${queryParams}`);

export const fireGetAllRecordings = (queryParams: string, basePath = '') =>
  axiosInstance.get(`${apiConfig.paths.recordings}?${queryParams}`);

export const fireGetSingleRecordingStream = (
  recordingId: string,
  basePath = ''
) =>
  axiosDownloadInstance({
    method: 'GET',
    url: basePath + `${apiConfig.paths.recordings}/${recordingId}`,
    responseType: 'blob',
  });

export const fireGetSingleRecording = (id: string, basePath = '') =>
  axiosInstance.get(`${basePath}${apiConfig.paths.detail.info}/${id}`);

export const fireGetRecordingMedia = (conversationId: string, basePath = '') =>
  axiosInstance.get(
    `${basePath}${apiConfig.paths.detail.media}/${conversationId}`
  );

export const fireGetTranscript = (conversationId: string, basePath = '') =>
  axiosInstance.get(
    `${basePath}${apiConfig.paths.detail.transcript}/${conversationId}`
  );

export const fireExportRecordings = (conversationId: string[], basePath = '') =>
  axiosInstance.post(`${basePath}${apiConfig.paths.export}/recording/export`, {
    conversationId,
  });

/**
 *  Download recordings as a zip file.
 *
 *  @param {string[]} conversationId The list of conversation IDs.
 *  @param {string} [basePath] The base URL of the API.
 *  @param {() => void} callback The callback function to be called after the download is completed.
 */
export const downloadRecordingZipFile = (
  conversationId: string[],
  basePath = '',
  callback: () => void
) =>
  axiosDownloadInstance({
    method: 'POST',
    url: basePath + apiConfig.paths.export + `/recording/export`,
    responseType: 'blob',
    data: {
      conversationId,
    },
  })
    .then((response) => {
      // 从响应头中提取 Content-Disposition
      const contentDisposition = response.headers['content-disposition'];
      let fileName = 'default-filename.zip'; // 默认文件名

      // 使用正则表达式提取文件名
      if (contentDisposition) {
        const matches = contentDisposition.match(/filename=([^;]+)/);
        if (matches && matches[1]) {
          fileName = matches[1].trim(); // 确保去除多余的空格
        }
      }
      // 创建一个 Blob URL 并触发下载
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName); // 使用从响应头中提取的文件名
      document.body.appendChild(link);
      link.click();
      link.remove(); // 下载完成后移除 link 元素
      console.info('文件下载成功！文件名为：', fileName);
    })
    .catch((error) => {
      console.error('文件下载失败：', error);
    })
    .finally(() => {
      callback();
    });

export const fireGetRecordingTranscript = (mediaUri: string, basePath = '') =>
  axiosInstance.get(`${apiConfig.paths.transcript}`, {
    timeout: 300000,
  });

export const fireGetUserConfig = (basePath = '') =>
  axiosInstance.get(`${basePath}${apiConfig.paths.userconfig}`);

export const fireCreateUserConfig = (data: UserConfigProps, basePath = '') =>
  axiosInstance.post(`${basePath}${apiConfig.paths.userconfig}`, data);

export const fireUpdateUserConfig = (data: UserConfigProps, basePath = '') =>
  axiosInstance.put(`${basePath}${apiConfig.paths.userconfig}`, data);

export const fireGCRecordingsByFilters = (data: any, basePath = '') => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.gc_recordings}`,
    data
  );
};

// CCBA API: Get completed calls in pureconnect DB

export const fireGetCompletedCalls = (
  queryDatas: object | undefined,
  basePath = ''
) => {
  // fix: if the queryDatas does not contain conversationStart and conversationEnd, will not call the api
  if (
    queryDatas &&
    !('conversationStart' in queryDatas) &&
    !('conversationEnd' in queryDatas)
  )
    return;

  // call api to for GC
  return fireGetCompletedCallsPureconnect(queryDatas, basePath);
};

export const fireGetCompletedCallsPureconnect = (data: any, basePath = '') => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.completed_calls_pureconnect}`,
    data
  );
};

export const fireGetSingleCompletedCallPureconnect = (
  id: string,
  basePath = ''
) =>
  axiosInstance.get(
    `${basePath}${apiConfig.paths.completed_calls_pureconnect_detail.info}/${id}`
  );

export const updateCompletedCallPureconnect = (
  id: string,
  data: any,
  basePath = ''
) =>
  axiosInstance.put(
    `${basePath}${apiConfig.paths.completed_calls_pureconnect_detail.update}/${id}`,
    data
  );

export const fireGetMetaDataMapping = (formId: string, basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.qm.meta_data_mapping}?formId=${formId}`
  );
};
export const fireBlocking = (body: any, basePath = '') => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.callControl.blocking}`,
    body
  );
};
export const fireGetDnc = (body: any, basePath = '') => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.users.getDncList}`,
    body
  );
};
export const fireGetTemplate = (basePath = '', type: string) =>
  axiosDownloadInstance({
    method: 'GET',
    url: `${basePath}${apiConfig.paths.users.getTemplate}?type=${type}`,
    responseType: 'blob',
  });
export const fireCreateDnc = (body: any, type: string, basePath = '') => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.users.createDnc}?type=${type}`,
    body
  );
};
export const fireDeleteDnc = (body: any, basePath = '') => {
  return axiosInstance.delete(`${basePath}${apiConfig.paths.users.deleteDnc}`, {
    data: { ids: body },
  });
};
export const fireImportDnc = (body: any, type: string, basePath = '') => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.users.importDnc}?type=${type}`,
    body,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
  );
};
export const fireExportDnc = (body: any, basePath = '') => {
  return axiosDownloadInstance({
    method: 'POST',
    url: `${basePath}${apiConfig.paths.users.exportDnc}`,
    responseType: 'blob',
    data: body,
  });
};
export const fireGetChannelData = (basePath = '', type: string) => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.users.getChannelData}?type=${type}`
  );
};
export const fireGetEmailRules = (basePath = '', queryParams: string) => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.manualQueue.getEmailRules}?${queryParams}`
  );
};
export const fireGetAutoReplyFilterRules = (
  basePath = '',
  queryParams: string
) => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.manualQueue.getAutoReplyFilterRule}?${queryParams}`
  );
};

export const fireGetEmailRuleDetail = (emailRuleId: string, basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.manualQueue.getEmailRuleDetail}?emailRuleId=${emailRuleId}`
  );
};

export const fireGetAutoReplyTemplateList = (basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.getAutoReplyTemplateList}?spaceType=emailTemplate`
  );
};

export const fireGetAutoReplyTemplateDetail = (id: string, basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.getAutoReplyTemplateDetail}?contentId=${id}`
  );
};

export const fireUpdateEmailRule = (data: any, basePath = '') => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.manualQueue.updateEmailRule}`,
    data
  );
};

export const fireCreateEmailRule = (data: any, basePath = '') => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.manualQueue.createEmailRule}`,
    data
  );
};

export const fireGetQueueOptions = (
  basePath = '',
  site = 'supervisordashboard'
) => {
  const params = new URLSearchParams();
  if (site) {
    params.append('site', site);
  }
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.getQueues}${params.toString() ? '?' + params.toString() : ''}`
  );
};

export const fireGetAutoReplyFilterRuleDetail = (id: string, basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.manualQueue.getAutoReplyFilterRuleDetail}?id=${id}`
  );
};

export const fireCreateAutoReplyFilterRule = (data: any, basePath = '') => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.manualQueue.updateAutoReplyFilterRule}`,
    data
  );
};

export const fireUpdateAutoReplyFilterRule = (data: any, basePath = '') => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.manualQueue.updateAutoReplyFilterRule}`,
    data
  );
};

/**
 * Delete an email rule
 * @param emailRuleId - The ID of the email rule to delete
 * @param basePath - Optional base path
 */
export const fireDeleteEmailRule = (emailRuleId: string, basePath = '') => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.manualQueue.deleteEmailRule}`,
    { emailRuleId }
  );
};

/**
 * Delete an auto reply filter rule
 * @param id - The ID of the auto reply filter rule to delete
 * @param basePath - Optional base path
 */
export const fireDeleteAutoReplyFilterRule = (id: string, basePath = '') => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.manualQueue.deleteAutoReplyFilterRule}`,
    { id }
  );
};

export const fireGetAutoReplyTemplateAttachment = (
  attachmentIds: string[],
  basePath = ''
) => {
  const body = {
    templateAttachmentIds: attachmentIds,
  };

  return axiosInstance.post(
    `${basePath}${apiConfig.paths.manualQueue.getAutoReplyTemplateAttachment}`,
    body
  );
};

// Auto Reply Rules
export const fireGetAutoReplyRuleList = (
  basePath = '',
  queryParams: URLSearchParams
) => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.manualQueue.getAutoReplyRuleList}?${queryParams}`
  );
};

export const fireGetAutoReplyRuleDetail = (ruleId: string, basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.manualQueue.getAutoReplyRuleDetail}?ruleId=${ruleId}`
  );
};

export const fireCreateAutoReplyRule = (data: any, basePath = '') => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.manualQueue.updateAutoReplyRule}`,
    data
  );
};

export const fireCreateOrUpdateFullAddress = (
  data: {
    fullAddressList: string[];
    groupName: string;
    id?: string;
  },
  basePath = ''
) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.manualQueue.createOrUpdateFullAddress}`,
    data
  );
};

export const fireUpdateAutoReplyRule = (data: any, basePath = '') => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.manualQueue.updateAutoReplyRule}`,
    data
  );
};

/**
 * Delete an auto reply rule
 * @param ruleId - The ID of the auto reply rule to delete
 * @param basePath - Optional base path
 */
export const fireDeleteAutoReplyRule = (ruleId: string, basePath = '') => {
  return axiosInstance.delete(
    `${basePath}${apiConfig.paths.manualQueue.deleteAutoReplyRule}`,
    { data: { ruleId } }
  );
};

// Canned Messages
export const fireGetCannedMessage = (
  dataType = 'full',
  channelType: string,
  basePath = '',
  globalConfig?: any
) => {
  const params = new URLSearchParams();
  // Get spaceId from configuration
  params.append('spaceId', getCannedMessageSpaceId(globalConfig));
  params.append('dataType', dataType);
  params.append('channelType', channelType);

  return axiosInstance.get(
    // Reusing getAutoReplyTemplateList path as it matches the base URL for contents
    `${basePath}${apiConfig.paths.getAutoReplyTemplateList}?${params}`
  );
};

export const fireGetFullAddressList = (
  params: {
    page?: number;
    pageSize?: number;
    groupName?: string;
    fullAddress?: string;
  },
  basePath = ''
) => {
  const queryParams = new URLSearchParams();
  if (params.page) queryParams.append('page', params.page.toString());
  if (params.pageSize)
    queryParams.append('pageSize', params.pageSize.toString());
  if (params.groupName) queryParams.append('groupName', params.groupName);
  if (params.fullAddress) queryParams.append('fullAddress', params.fullAddress);

  return axiosInstance.get(
    `${basePath}${apiConfig.paths.manualQueue.getFullAddressList}?${queryParams}`
  );
};

export const fireGetFullAddressGroupById = (id: string, basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.manualQueue.getFullAddressGroupById}?id=${id}`
  );
};

export const fireGetEmailQueueOptions = (basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.manualQueue.getEmailDefaultQueues}`
  );
};

export const fireGetPlatformAccounts = (countryCode = '852', basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.manualQueue.getPlatformAccounts}?dataType=pullDownData&phoneNumber=${countryCode}`
  );
};

export default axiosInstance;
