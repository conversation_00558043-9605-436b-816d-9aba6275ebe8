/**
 * 增强版 O-Spy 包装器
 * 提供会话管理、数据持久化等功能
 */

import { OSpySessionDB, SessionData } from './session-db';
import { <PERSON>rrorHandler } from './error-handler';

export class EnhancedOSpy {
  private sessionDB: OSpySessionDB;
  private errorHandler: <PERSON>rrorHandler;
  private currentSession: SessionData | null = null;
  private isRecording = false;
  private originalOSpy: any = null;
  private sessionId: string | null = null;
  private ui: any = null;
  private sessionTimer: NodeJS.Timeout | null = null;
  private startTime: number = 0;

  constructor(originalOSpy?: any) {
    this.sessionDB = new OSpySessionDB();
    this.errorHandler = new ErrorHandler();
    this.originalOSpy = originalOSpy;
    this.errorHandler.logInfo('EnhancedOSpy 初始化开始');
  }

  /**
   * 初始化增强版 O-Spy
   */
  async init(): Promise<void> {
    try {
      await this.sessionDB.init();
      await this.setupPageLifecycleHandlers();
      await this.checkForUnfinishedSession();
      await this.sessionDB.cleanupOldSessions();
      
      // 动态创建UI组件
      const { EnhancedOSpyUI } = await import('./ui-components');
      this.ui = new EnhancedOSpyUI(this);
      this.ui.createFloatingPanel();
      
      console.log('EnhancedOSpy 初始化完成');
    } catch (error) {
      console.error('EnhancedOSpy 初始化失败:', error);
    }
  }

  /**
   * 开始记录
   */
  async startRecording(): Promise<void> {
    if (this.isRecording) {
      console.warn('已经在记录中');
      return;
    }

    try {
      this.sessionId = this.generateSessionId();
      this.startTime = Date.now();
      
      this.currentSession = {
        sessionId: this.sessionId,
        startTime: this.startTime,
        timestamp: this.startTime,
        status: 'recording',
        data: {},
        metadata: {
          userAgent: navigator.userAgent,
          url: window.location.href,
          title: document.title,
          userId: this.getCurrentUserId(),
        }
      };
      
      this.isRecording = true;
      await this.sessionDB.saveSession(this.currentSession);
      
      // 启动原始 o-spy 记录
      this.startOriginalOSpyRecording();
      
      // 启动定时器
      this.startSessionTimer();
      
      // 更新UI状态
      if (this.ui) {
        this.ui.updateRecordingState(true);
      }
      
      console.log('开始记录会话:', this.sessionId);
    } catch (error) {
      console.error('开始记录失败:', error);
      this.isRecording = false;
      this.currentSession = null;
    }
  }

  /**
   * 停止记录
   */
  async stopRecording(): Promise<void> {
    if (!this.isRecording || !this.currentSession) {
      console.warn('当前没有在记录');
      return;
    }

    try {
      const endTime = Date.now();
      this.currentSession.endTime = endTime;
      this.currentSession.status = 'completed';
      
      // 获取 o-spy 数据
      this.currentSession.data = await this.getOSpyData();
      
      await this.sessionDB.saveSession(this.currentSession);
      
      // 停止原始 o-spy 记录
      this.stopOriginalOSpyRecording();
      
      // 停止定时器
      this.stopSessionTimer();
      
      // 更新UI状态
      if (this.ui) {
        this.ui.updateRecordingState(false);
      }
      
      console.log('停止记录会话:', this.currentSession.sessionId);
      
      this.isRecording = false;
      this.currentSession = null;
      this.sessionId = null;
    } catch (error) {
      console.error('停止记录失败:', error);
    }
  }

  /**
   * 强制结束会话（页面刷新/崩溃时）
   */
  async forceEndSession(): Promise<void> {
    if (!this.isRecording || !this.currentSession) {
      return;
    }

    try {
      this.currentSession.endTime = Date.now();
      this.currentSession.status = 'interrupted';
      this.currentSession.data = await this.getOSpyData();
      
      await this.sessionDB.saveSession(this.currentSession);
      
      console.log('强制结束会话:', this.currentSession.sessionId);
    } catch (error) {
      console.error('强制结束会话失败:', error);
    }
  }

  /**
   * 设置页面生命周期处理器
   */
  private async setupPageLifecycleHandlers(): Promise<void> {
    // 页面刷新/关闭时自动结束当前会话
    window.addEventListener('beforeunload', () => {
      if (this.isRecording) {
        // 尝试同步保存到 IndexedDB
        this.forceEndSession();
      }
    });

    // 页面可见性变化检测
    document.addEventListener('visibilitychange', () => {
      if (document.hidden && this.isRecording) {
        this.saveCurrentSessionState();
      }
    });

    // 监听页面错误
    window.addEventListener('error', () => {
      if (this.isRecording) {
        this.forceEndSession();
      }
    });
  }

  /**
   * 检查未完成的会话
   */
  private async checkForUnfinishedSession(): Promise<void> {
    try {
      const unfinishedSessions = await this.sessionDB.getUnfinishedSessions();
      
      if (unfinishedSessions.length > 0) {
        console.log(`发现 ${unfinishedSessions.length} 个未完成的会话，将标记为中断`);
        
        for (const session of unfinishedSessions) {
          session.status = 'interrupted';
          session.endTime = Date.now();
          await this.sessionDB.saveSession(session);
        }
      }
    } catch (error) {
      console.error('检查未完成会话失败:', error);
    }
  }

  /**
   * 启动原始 O-Spy 记录
   */
  private startOriginalOSpyRecording(): void {
    try {
      if (this.originalOSpy && typeof this.originalOSpy.start === 'function') {
        this.originalOSpy.start();
      }
    } catch (error) {
      console.error('启动原始 O-Spy 记录失败:', error);
    }
  }

  /**
   * 停止原始 O-Spy 记录
   */
  private stopOriginalOSpyRecording(): void {
    try {
      if (this.originalOSpy && typeof this.originalOSpy.stop === 'function') {
        this.originalOSpy.stop();
      }
    } catch (error) {
      console.error('停止原始 O-Spy 记录失败:', error);
    }
  }

  /**
   * 获取 O-Spy 数据
   */
  private async getOSpyData(): Promise<any> {
    try {
      // 尝试从原始 O-Spy 实例获取数据
      if (this.originalOSpy && typeof this.originalOSpy.getData === 'function') {
        return await this.originalOSpy.getData();
      }
      
      // 如果没有可用的方法，返回空对象
      return {};
    } catch (error) {
      console.error('获取 O-Spy 数据失败:', error);
      return {};
    }
  }

  /**
   * 保存当前会话状态
   */
  private async saveCurrentSessionState(): Promise<void> {
    if (!this.currentSession) return;
    
    try {
      this.currentSession.data = await this.getOSpyData();
      await this.sessionDB.saveSession(this.currentSession);
    } catch (error) {
      console.error('保存当前会话状态失败:', error);
    }
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取当前用户ID
   */
  private getCurrentUserId(): string {
    try {
      const user = JSON.parse(localStorage.getItem('session-user') || '{}');
      return user?.id || localStorage.getItem('cdss-gc-username') || 'anonymous';
    } catch {
      return 'anonymous';
    }
  }

  /**
   * 启动会话计时器
   */
  private startSessionTimer(): void {
    this.sessionTimer = setInterval(() => {
      if (this.ui && this.isRecording) {
        const elapsed = Date.now() - this.startTime;
        this.ui.updateTimer(elapsed);
      }
    }, 1000);
  }

  /**
   * 停止会话计时器
   */
  private stopSessionTimer(): void {
    if (this.sessionTimer) {
      clearInterval(this.sessionTimer);
      this.sessionTimer = null;
    }
  }

  // 公共方法供UI组件使用
  public getSessionDB(): OSpySessionDB {
    return this.sessionDB;
  }

  public getCurrentSession(): SessionData | null {
    return this.currentSession;
  }

  public getIsRecording(): boolean {
    return this.isRecording;
  }
}

// 扩展 Window 接口
declare global {
  interface Window {
    $enhancedOSpy?: EnhancedOSpy;
  }
}
