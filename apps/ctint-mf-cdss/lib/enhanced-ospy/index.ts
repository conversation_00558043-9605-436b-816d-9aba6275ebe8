/**
 * 增强版 O-Spy 主入口文件
 * 导出所有核心类和接口
 */

export { EnhancedOSpy } from './enhanced-ospy';
export { OSpySessionDB } from './session-db';
export { EnhancedOSpyUI } from './ui-components';
export { ExportManager } from './export-manager';

export type { SessionData } from './session-db';

/**
 * 初始化增强版 O-Spy
 * @param originalOSpy 原始 O-Spy 实例（可选）
 * @returns Promise<EnhancedOSpy>
 */
export async function initEnhancedOSpy(originalOSpy?: any) {
  const { EnhancedOSpy } = await import('./enhanced-ospy');
  const enhancedOSpy = new EnhancedOSpy(originalOSpy);
  await enhancedOSpy.init();
  return enhancedOSpy;
}

/**
 * 快速启动函数
 * 自动检测并集成现有的 O-Spy 实例
 */
export async function quickStart() {
  try {
    // 动态导入原始 O-Spy
    const ospyModule = await import('@huolala-tech/page-spy-plugin-ospy');
    const OSpy = ospyModule.default;

    // 创建原始实例但不自动渲染
    const originalOSpy = new OSpy({ autoRender: false });

    // 初始化增强版
    const enhancedOSpy = await initEnhancedOSpy(originalOSpy);

    // 挂载到全局对象
    if (typeof window !== 'undefined') {
      window.$enhancedOSpy = enhancedOSpy;
    }

    console.log('增强版 O-Spy 快速启动完成');
    return enhancedOSpy;

  } catch (error) {
    console.error('增强版 O-Spy 快速启动失败:', error);

    // 降级到无原始实例模式
    const enhancedOSpy = await initEnhancedOSpy();

    if (typeof window !== 'undefined') {
      window.$enhancedOSpy = enhancedOSpy;
    }

    return enhancedOSpy;
  }
}

// 默认导出
export default {
  initEnhancedOSpy,
  quickStart
};
