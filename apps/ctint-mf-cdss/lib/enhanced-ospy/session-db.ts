/**
 * IndexedDB 数据库管理器
 * 用于存储和管理 O-Spy 会话数据
 */

export interface SessionData {
  sessionId: string;
  startTime: number;
  endTime?: number;
  timestamp: number;
  status: 'recording' | 'completed' | 'interrupted';
  data: any;
  metadata: {
    userAgent: string;
    url: string;
    title: string;
    userId?: string;
  };
}

export class OSpySessionDB {
  private dbName = 'ospy-sessions';
  private version = 1;
  private db: IDBDatabase | null = null;

  constructor() {
    this.init();
  }

  /**
   * 初始化数据库
   */
  async init(): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);
      
      request.onerror = () => {
        console.error('IndexedDB 初始化失败:', request.error);
        reject(request.error);
      };
      
      request.onsuccess = () => {
        this.db = request.result;
        console.log('IndexedDB 初始化成功');
        resolve(this.db);
      };
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        // 创建会话存储表
        if (!db.objectStoreNames.contains('sessions')) {
          const sessionStore = db.createObjectStore('sessions', {
            keyPath: 'sessionId'
          });
          
          // 创建索引
          sessionStore.createIndex('timestamp', 'timestamp', { unique: false });
          sessionStore.createIndex('status', 'status', { unique: false });
          sessionStore.createIndex('startTime', 'startTime', { unique: false });
          
          console.log('创建 sessions 对象存储');
        }
      };
    });
  }

  /**
   * 保存会话数据
   */
  async saveSession(sessionData: SessionData): Promise<void> {
    if (!this.db) {
      await this.init();
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['sessions'], 'readwrite');
      const store = transaction.objectStore('sessions');
      
      const request = store.put(sessionData);
      
      request.onsuccess = () => {
        console.log('会话数据保存成功:', sessionData.sessionId);
        resolve();
      };
      
      request.onerror = () => {
        console.error('会话数据保存失败:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * 获取单个会话
   */
  async getSession(sessionId: string): Promise<SessionData | null> {
    if (!this.db) {
      await this.init();
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['sessions'], 'readonly');
      const store = transaction.objectStore('sessions');
      
      const request = store.get(sessionId);
      
      request.onsuccess = () => {
        resolve(request.result || null);
      };
      
      request.onerror = () => {
        console.error('获取会话失败:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * 获取所有会话
   */
  async getAllSessions(): Promise<SessionData[]> {
    if (!this.db) {
      await this.init();
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['sessions'], 'readonly');
      const store = transaction.objectStore('sessions');
      
      const request = store.getAll();
      
      request.onsuccess = () => {
        const sessions = request.result || [];
        // 按时间倒序排列
        sessions.sort((a, b) => b.timestamp - a.timestamp);
        resolve(sessions);
      };
      
      request.onerror = () => {
        console.error('获取所有会话失败:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * 删除会话
   */
  async deleteSession(sessionId: string): Promise<void> {
    if (!this.db) {
      await this.init();
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['sessions'], 'readwrite');
      const store = transaction.objectStore('sessions');
      
      const request = store.delete(sessionId);
      
      request.onsuccess = () => {
        console.log('会话删除成功:', sessionId);
        resolve();
      };
      
      request.onerror = () => {
        console.error('会话删除失败:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * 清理30天前的旧会话
   */
  async cleanupOldSessions(): Promise<number> {
    if (!this.db) {
      await this.init();
    }

    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
    let deletedCount = 0;

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['sessions'], 'readwrite');
      const store = transaction.objectStore('sessions');
      const index = store.index('timestamp');
      
      const range = IDBKeyRange.upperBound(thirtyDaysAgo);
      const request = index.openCursor(range);
      
      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;
        if (cursor) {
          cursor.delete();
          deletedCount++;
          cursor.continue();
        } else {
          console.log(`清理完成，删除了 ${deletedCount} 个旧会话`);
          resolve(deletedCount);
        }
      };
      
      request.onerror = () => {
        console.error('清理旧会话失败:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * 获取数据库统计信息
   */
  async getStats(): Promise<{
    totalSessions: number;
    recordingSessions: number;
    completedSessions: number;
    oldestSession?: Date;
    newestSession?: Date;
  }> {
    const sessions = await this.getAllSessions();
    
    const stats = {
      totalSessions: sessions.length,
      recordingSessions: sessions.filter(s => s.status === 'recording').length,
      completedSessions: sessions.filter(s => s.status === 'completed').length,
      oldestSession: sessions.length > 0 ? new Date(Math.min(...sessions.map(s => s.timestamp))) : undefined,
      newestSession: sessions.length > 0 ? new Date(Math.max(...sessions.map(s => s.timestamp))) : undefined,
    };
    
    return stats;
  }

  /**
   * 检查是否有未完成的会话
   */
  async getUnfinishedSessions(): Promise<SessionData[]> {
    const sessions = await this.getAllSessions();
    return sessions.filter(session => session.status === 'recording');
  }
}
