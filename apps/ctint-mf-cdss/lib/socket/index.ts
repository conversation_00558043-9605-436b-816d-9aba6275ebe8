import { Socket, io } from 'socket.io-client';
import { v4 as uuidv4 } from 'uuid';

const isBrowser = typeof window !== 'undefined';

export const getHeaders = () => {
  return {
    'Content-Type': 'application/json',
    traceId: uuidv4(),
    tenant: 'ccba',
    sourceId: 'ctint-mf-cdss',
    previousId: 'ctint-bff-cdss',
    'cdss-authorization': 'Bearer ' + localStorage.getItem('cdss-auth-token'),
  };
};

export const createSocket = (deviceId?: string | undefined): Socket => {
  const socket =
    isBrowser && deviceId
      ? io(`ws://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com`, {
          extraHeaders: getHeaders(),
        })
      : ({} as Socket);

  return socket;
};
