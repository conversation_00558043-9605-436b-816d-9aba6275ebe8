// pages/api/user.ts

import type { NextApiRequest, NextApiResponse } from 'next';

type DataResponse = {
  data: any;
  isSuccess: boolean;
  totalCount?: number;
  error: any;
};

const DUMMY_DATA = {
  id: 'E0DBMNLAM52MJ0GUEKVQ8I0ODO00000T',
  conversationId: 'E0DBMNLAM52MJ0GUEKVQ8I0ODO00000T',
  transcripts: [
    {
      start: 'start',
      end: 'end',
      speaker: 'speaker',
      text: 'text',
    },
    {
      start: '1.420',
      end: '3.420',
      speaker: 'Agent',
      text: 'Main Menu',
    },
    {
      start: '3.420',
      end: '5.420',
      speaker: 'Agent',
      text: 'To check your account balance, press 1.',
    },
    {
      start: '5.420',
      end: '7.420',
      speaker: 'Agent',
      text: 'To pay bills, press 2.',
    },
    {
      start: '7.420',
      end: '9.420',
      speaker: 'Agent',
      text: 'Transfer funds, press 3.',
    },
    {
      start: '9.420',
      end: '11.420',
      speaker: 'Customer',
      text: 'Review transactions,',
    },
    {
      start: '11.420',
      end: '13.420',
      speaker: 'Customer',
      text: 'press 4.',
    },
    {
      start: '13.420',
      end: '15.420',
      speaker: 'Agent',
      text: 'Schedule an appointment, press 5.',
    },
    {
      start: '15.420',
      end: '17.420',
      speaker: 'Agent',
      text: 'To transfer to an advisor, press 0.',
    },
    {
      start: '22.220',
      end: '25.200',
      speaker: 'Customer',
      text: "Sorry, I didn't get that. Let's try again.",
    },
    {
      start: '25.520',
      end: '27.500',
      speaker: 'Agent',
      text: 'To check your account balance, press 1.',
    },
    {
      start: '27.980',
      end: '29.540',
      speaker: 'Agent',
      text: 'To pay bills, press 2.',
    },
    {
      start: '30.100',
      end: '31.760',
      speaker: 'Agent',
      text: 'Transfer funds, press 3.',
    },
    {
      start: '32.360',
      end: '34.320',
      speaker: 'Customer',
      text: 'Review transactions, press 4.',
    },
    {
      start: '34.960',
      end: '36.780',
      speaker: 'Customer',
      text: 'Schedule an appointment, press 5.',
    },
    {
      start: '37.160',
      end: '39.660',
      speaker: 'Customer',
      text: 'To transfer to an advisor, press 0.',
    },
  ],
};

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse<DataResponse>
) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader(
    'Access-Control-Allow-Methods',
    'GET, POST, PUT, DELETE, OPTIONS'
  );
  res.setHeader('Access-Control-Allow-Headers', '*');
  try {
    // If everything is okay, return the user data
    res.status(200).json({
      data: DUMMY_DATA,
      isSuccess: true,
      error: null,
    });
  } catch (error) {
    // If decryption fails or sourceId does not match
    res.status(403).json({
      data: null,
      isSuccess: false,
      error: 'Failed to get transcripts',
    });
  }
}
