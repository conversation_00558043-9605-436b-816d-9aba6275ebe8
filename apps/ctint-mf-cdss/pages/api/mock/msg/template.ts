// pages/api/user.ts

import type { NextApiRequest, NextApiResponse } from 'next';

const DUMMY_DATA = {
  data: {
    categoryList: [
      {
        pkey: 'AllowAgent',
        code: 'AllowAgent',
        dbUser: 'sa',
        name: 'Allow Agent Access',
        parentCode: 'WhatsappTemplatePermission',
        status: 'ACTIVE',
        createUserCode: 'SYSTEM',
        createDatetime: '2024-03-20T08:55:01.805Z',
        latestUpdateUserCode: null,
        latestUpdateDatetime: null,
      },
      {
        pkey: 'AllowSupervisor',
        code: 'AllowSupervisor',
        dbUser: 'sa',
        name: 'Only Allow Supervisor Access',
        parentCode: 'WhatsappTemplatePermission',
        status: 'ACTIVE',
        createUserCode: 'SYSTEM',
        createDatetime: '2024-03-20T08:55:01.805Z',
        latestUpdateUserCode: null,
        latestUpdateDatetime: null,
      },
    ],
    categoryTree: {
      AllowSupervisor: {},
      AllowAgent: {},
    },
    messageTemplateList: [
      {
        pkey: 'a_promotion_en_US',
        code: 'a_promotion_en_US',
        dbUser: 'sa',
        name: 'a_promotion (en_US)',
        category: 'AllowSupervisor',
        lockCounter: 25,
        messageJson:
          '[{"body":{"text":"Shop now through {{1}} and use code {{2}} to get {{3}} off of all merchandise."},"option":null,"image":null,"language":"en_US"}]',
        messageParameters:
          '[{"id":"1","defaultValue":"the end of August"},{"id":"2","defaultValue":"25OFF"},{"id":"3","defaultValue":"25%"}]',
        isOffline: true,
        realTemplateId: 'a_promotion',
        status: 'ACTIVE',
        createUserCode: 'SYSTEM',
        createDatetime: '2024-03-07T05:45:36.310Z',
        latestUpdateUserCode: 'SYSTEM',
        latestUpdateDatetime: '2024-03-28T02:13:02.931Z',
        channelType: 'WHATSAPP',
        channelAccountNumber: '***********',
        templateType: 'STANDARD',
        messageCategory: 'MARKETING',
        language: 'en_US',
        approvalStatus: 'APPROVED',
        rejectedReason: null,
        messageJsonHtml:
          '[{"body":{"text":"Shop now through {{1}} and use code {{2}} to get {{3}} off of all merchandise."},"option":null,"image":null,"language":"en_US"}]',
      },
      {
        pkey: 'asdfasdf_en',
        code: 'asdfasdf_en',
        dbUser: 'sa',
        name: 'asdfasdf',
        category: 'AllowAgent',
        lockCounter: 6,
        messageJson:
          '[{"body":{"text":"asdf\\n"},"option":{"type":"BUTTON","list":[{"id":0,"type":"QUICK_REPLY","text":"Go To Live Agent","content":"Go To Live Agent"},{"id":1,"type":"QUICK_REPLY","text":"Other Enquiries","content":"Other Enquiries"}]},"header":{"text":null},"language":"en"}]',
        messageParameters: '[]',
        isOffline: true,
        realTemplateId: 'asdfasdf_en',
        status: 'ACTIVE',
        createUserCode: 'ansel1',
        createDatetime: '2024-03-25T07:01:57.355Z',
        latestUpdateUserCode: 'SYSTEM',
        latestUpdateDatetime: '2024-03-28T02:13:02.712Z',
        channelType: 'WHATSAPP',
        channelAccountNumber: '***********',
        templateType: 'MEDIA',
        messageCategory: 'MARKETING',
        language: 'en',
        approvalStatus: 'APPROVED',
        rejectedReason: null,
        messageJsonHtml:
          '[{"body":{"text":"<p>asdf</p>\\n"},"option":{"type":"BUTTON","list":[{"id":0,"type":"QUICK_REPLY","text":"Go To Live Agent","content":"Go To Live Agent"},{"id":1,"type":"QUICK_REPLY","text":"Other Enquiries","content":"Other Enquiries"}]},"header":{"text":null},"image":{"fileName":null},"language":"en"}]',
      },
      {
        pkey: 'hello_world_en_US',
        code: 'hello_world_en_US',
        dbUser: 'sa',
        name: 'hello_world (en_US)',
        category: 'AllowSupervisor',
        lockCounter: 25,
        messageJson:
          '[{"body":{"text":"Welcome and congratulations!! This message demonstrates your ability to send a WhatsApp message notification from the Cloud API, hosted by Meta. Thank you for taking the time to test with us."},"option":null,"image":null,"language":"en_US","header":{"text":"Hello World"}}]',
        messageParameters: '[]',
        isOffline: true,
        realTemplateId: 'hello_world',
        status: 'ACTIVE',
        createUserCode: 'SYSTEM',
        createDatetime: '2024-03-07T05:45:36.885Z',
        latestUpdateUserCode: 'SYSTEM',
        latestUpdateDatetime: '2024-03-28T02:13:03.056Z',
        channelType: 'WHATSAPP',
        channelAccountNumber: '***********',
        templateType: 'STANDARD',
        messageCategory: 'UTILITY',
        language: 'en_US',
        approvalStatus: 'APPROVED',
        rejectedReason: null,
        messageJsonHtml:
          '[{"body":{"text":"Welcome and congratulations!! This message demonstrates your ability to send a WhatsApp message notification from the Cloud API, hosted by Meta. Thank you for taking the time to test with us."},"option":null,"image":null,"language":"en_US","header":{"text":"Hello World"}}]',
      },
      {
        pkey: '_template_test_with_buttons_en',
        code: '_template_test_with_buttons_en',
        dbUser: 'sa',
        name: '--template test with buttons',
        category: 'AllowAgent',
        lockCounter: 5,
        messageJson:
          '[{"body":{"text":"button tests\\n"},"option":{"type":"BUTTON","list":[{"id":0,"type":"QUICK_REPLY","text":"Go To Live Agent","content":"Go To Live Agent"},{"id":1,"type":"QUICK_REPLY","text":"Other Enquiries","content":"Other Enquiries"},{"id":2,"type":"QUICK_REPLY","text":"Do Not Contact Me","content":"Do Not Contact Me"}]},"header":{"text":null},"language":"en"}]',
        messageParameters: '[]',
        isOffline: true,
        realTemplateId: '_template_test_with_buttons_en',
        status: 'ACTIVE',
        createUserCode: 'ansel1',
        createDatetime: '2024-03-25T07:41:11.604Z',
        latestUpdateUserCode: 'SYSTEM',
        latestUpdateDatetime: '2024-03-28T02:13:02.681Z',
        channelType: 'WHATSAPP',
        channelAccountNumber: '***********',
        templateType: 'MEDIA',
        messageCategory: 'MARKETING',
        language: 'en',
        approvalStatus: 'APPROVED',
        rejectedReason: null,
        messageJsonHtml:
          '[{"body":{"text":"<p>button tests</p>\\n"},"option":{"type":"BUTTON","list":[{"id":0,"type":"QUICK_REPLY","text":"Go To Live Agent","content":"Go To Live Agent"},{"id":1,"type":"QUICK_REPLY","text":"Other Enquiries","content":"Other Enquiries"},{"id":2,"type":"QUICK_REPLY","text":"Do Not Contact Me","content":"Do Not Contact Me"}]},"header":{"text":null},"image":{"fileName":null},"language":"en"}]',
      },
      {
        pkey: 'test_cn_zh_cn',
        code: 'test_cn_zh_cn',
        dbUser: 'sa',
        name: 'test cn',
        category: 'AllowSupervisor',
        lockCounter: 23,
        messageJson:
          '[{"body":{"text":"asdfasdfasdf\\n"},"option":null,"header":{"text":null},"language":"zh_CN"}]',
        messageParameters: '[]',
        isOffline: true,
        realTemplateId: 'test_cn_zh_cn',
        status: 'ACTIVE',
        createUserCode: 'ansel1',
        createDatetime: '2024-03-12T06:26:10.487Z',
        latestUpdateUserCode: 'SYSTEM',
        latestUpdateDatetime: '2024-03-28T02:13:02.869Z',
        channelType: 'WHATSAPP',
        channelAccountNumber: '***********',
        templateType: 'STANDARD',
        messageCategory: 'MARKETING',
        language: 'zh_CN',
        approvalStatus: 'APPROVED',
        rejectedReason: null,
        messageJsonHtml:
          '[{"body":{"text":"<p>asdfasdfasdf</p>\\n"},"option":null,"header":{"text":null},"image":{"fileName":null},"language":"zh_CN"}]',
      },
      {
        pkey: '_test_mixed_template_en',
        code: '_test_mixed_template_en',
        dbUser: 'sa',
        name: '--test mixed template',
        category: 'AllowAgent',
        lockCounter: 1,
        messageJson:
          '[{"body":{"text":"button test 2\\n"},"option":{"type":"BUTTON","list":[{"id":0,"type":"QUICK_REPLY","text":"Other Enquiries","content":"Other Enquiries"},{"id":1,"type":"QUICK_REPLY","text":"Do Not Contact Me","content":"Do Not Contact Me"},{"id":2,"type":"QUICK_REPLY","text":"Go To Live Agent","content":"Go To Live Agent"}]},"header":{"text":null},"language":"en"}]',
        messageParameters: '[]',
        isOffline: true,
        realTemplateId: '_test_mixed_template_en',
        status: 'ACTIVE',
        createUserCode: 'ansel1',
        createDatetime: '2024-03-28T02:12:45.056Z',
        latestUpdateUserCode: 'SYSTEM',
        latestUpdateDatetime: '2024-03-28T02:13:02.650Z',
        channelType: 'WHATSAPP',
        channelAccountNumber: '***********',
        templateType: 'MEDIA',
        messageCategory: 'MARKETING',
        language: 'en',
        approvalStatus: 'APPROVED',
        rejectedReason: null,
        messageJsonHtml:
          '[{"body":{"text":"<p>button test 2</p>\\n"},"option":{"type":"BUTTON","list":[{"id":0,"type":"QUICK_REPLY","text":"Other Enquiries","content":"Other Enquiries"},{"id":1,"type":"QUICK_REPLY","text":"Do Not Contact Me","content":"Do Not Contact Me"},{"id":2,"type":"QUICK_REPLY","text":"Go To Live Agent","content":"Go To Live Agent"}]},"header":{"text":null},"image":{"fileName":null},"language":"en"}]',
      },
      {
        pkey: 'test_sunmobile_templates_en',
        code: 'test_sunmobile_templates_en',
        dbUser: 'sa',
        name: 'test sunmobile templates',
        category: 'AllowSupervisor',
        lockCounter: 26,
        messageJson:
          '[{"body":{"text":"test\\n"},"option":null,"header":{"text":null},"language":"en"}]',
        messageParameters: '[]',
        isOffline: true,
        realTemplateId: 'test_sunmobile_templates_en',
        status: 'ACTIVE',
        createUserCode: 'ansel1',
        createDatetime: '2024-03-07T05:43:53.823Z',
        latestUpdateUserCode: 'SYSTEM',
        latestUpdateDatetime: '2024-03-28T02:13:02.900Z',
        channelType: 'WHATSAPP',
        channelAccountNumber: '***********',
        templateType: 'STANDARD',
        messageCategory: 'MARKETING',
        language: 'en',
        approvalStatus: 'APPROVED',
        rejectedReason: null,
        messageJsonHtml:
          '[{"body":{"text":"<p>test</p>\\n"},"option":null,"header":{"text":null},"image":{"fileName":null},"language":"en"}]',
      },
      {
        pkey: 'test_template_2_en',
        code: 'test_template_2_en',
        dbUser: 'sa',
        name: 'test template 2',
        category: 'AllowSupervisor',
        lockCounter: 24,
        messageJson:
          '[{"body":{"text":"asdf\\n"},"option":{"type":"BUTTON","list":[{"id":0,"label":"testphone","type":"PHONE_NUMBER","text":"testphone","content":"***********"}]},"header":{"text":"test123"},"language":"en"}]',
        messageParameters: '[]',
        isOffline: true,
        realTemplateId: 'test_template_2_en',
        status: 'ACTIVE',
        createUserCode: 'ansel1',
        createDatetime: '2024-03-12T06:02:54.880Z',
        latestUpdateUserCode: 'SYSTEM',
        latestUpdateDatetime: '2024-03-28T02:13:02.884Z',
        channelType: 'WHATSAPP',
        channelAccountNumber: '***********',
        templateType: 'MEDIA',
        messageCategory: 'MARKETING',
        language: 'en',
        approvalStatus: 'APPROVED',
        rejectedReason: null,
        messageJsonHtml:
          '[{"body":{"text":"<p>asdf</p>\\n"},"option":{"type":"BUTTON","list":[{"id":0,"label":"testphone","type":"PHONE_NUMBER","text":"testphone","content":"***********"}]},"header":{"text":"test123"},"image":{"fileName":null},"language":"en"}]',
      },
      {
        pkey: 'test_test_permission_scope_en',
        code: 'test_test_permission_scope_en',
        dbUser: 'sa',
        name: 'test test permission scope',
        category: 'AllowSupervisor',
        lockCounter: 23,
        messageJson:
          '[{"body":{"text":"test123\\n"},"option":null,"header":{"text":null},"language":"en"}]',
        messageParameters: '[]',
        isOffline: true,
        realTemplateId: 'test_test_permission_scope_en',
        status: 'ACTIVE',
        createUserCode: 'ansel1',
        createDatetime: '2024-03-18T03:45:19.538Z',
        latestUpdateUserCode: 'SYSTEM',
        latestUpdateDatetime: '2024-03-28T02:13:02.837Z',
        channelType: 'WHATSAPP',
        channelAccountNumber: '***********',
        templateType: 'STANDARD',
        messageCategory: 'MARKETING',
        language: 'en',
        approvalStatus: 'APPROVED',
        rejectedReason: null,
        messageJsonHtml:
          '[{"body":{"text":"<p>test123</p>\\n"},"option":null,"header":{"text":null},"image":{"fileName":null},"language":"en"}]',
      },
      {
        pkey: 'test_test_permission_scope_allow_agent_send_en',
        code: 'test_test_permission_scope_allow_agent_send_en',
        dbUser: 'sa',
        name: 'test test permission scope - allow agent send',
        category: 'AllowAgent',
        lockCounter: 20,
        messageJson:
          '[{"body":{"text":"allow agent send\\n"},"option":null,"header":{"text":null},"language":"en"}]',
        messageParameters: '[]',
        isOffline: true,
        realTemplateId: 'test_test_permission_scope_allow_agent_send_en',
        status: 'ACTIVE',
        createUserCode: 'ansel1',
        createDatetime: '2024-03-18T05:41:30.582Z',
        latestUpdateUserCode: 'SYSTEM',
        latestUpdateDatetime: '2024-03-28T02:13:02.806Z',
        channelType: 'WHATSAPP',
        channelAccountNumber: '***********',
        templateType: 'STANDARD',
        messageCategory: 'MARKETING',
        language: 'en',
        approvalStatus: 'APPROVED',
        rejectedReason: null,
        messageJsonHtml:
          '[{"body":{"text":"<p>allow agent send</p>\\n"},"option":null,"header":{"text":null},"image":{"fileName":null},"language":"en"}]',
      },
      {
        pkey: 'test_test_permission_scope_list_en',
        code: 'test_test_permission_scope_list_en',
        dbUser: 'sa',
        name: 'test test permission scope list',
        category: 'AllowSupervisor',
        lockCounter: 17,
        messageJson:
          '[{"body":{"text":"allow agent send\\n"},"option":null,"header":{"text":null},"language":"en"}]',
        messageParameters: '[]',
        isOffline: true,
        realTemplateId: 'test_test_permission_scope_list_en',
        status: 'ACTIVE',
        createUserCode: 'ansel1',
        createDatetime: '2024-03-20T03:30:04.123Z',
        latestUpdateUserCode: 'SYSTEM',
        latestUpdateDatetime: '2024-03-28T02:13:02.791Z',
        channelType: 'WHATSAPP',
        channelAccountNumber: '***********',
        templateType: 'STANDARD',
        messageCategory: 'MARKETING',
        language: 'en',
        approvalStatus: 'APPROVED',
        rejectedReason: null,
        messageJsonHtml:
          '[{"body":{"text":"<p>allow agent send</p>\\n"},"option":null,"header":{"text":null},"image":{"fileName":null},"language":"en"}]',
      },
      {
        pkey: 'test_test_permission_scope_list_2_en',
        code: 'test_test_permission_scope_list_2_en',
        dbUser: 'sa',
        name: 'test test permission scope list - 2',
        category: 'AllowSupervisor',
        lockCounter: 13,
        messageJson:
          '[{"body":{"text":"allow agent send 2\\n"},"option":null,"header":{"text":null},"language":"en"}]',
        messageParameters: '[]',
        isOffline: true,
        realTemplateId: 'test_test_permission_scope_list_2_en',
        status: 'ACTIVE',
        createUserCode: 'ansel1',
        createDatetime: '2024-03-20T04:04:08.104Z',
        latestUpdateUserCode: 'SYSTEM',
        latestUpdateDatetime: '2024-03-28T02:13:02.775Z',
        channelType: 'WHATSAPP',
        channelAccountNumber: '***********',
        templateType: 'STANDARD',
        messageCategory: 'MARKETING',
        language: 'en',
        approvalStatus: 'APPROVED',
        rejectedReason: null,
        messageJsonHtml:
          '[{"body":{"text":"<p>allow agent send 2</p>\\n"},"option":null,"header":{"text":null},"image":{"fileName":null},"language":"en"}]',
      },
      {
        pkey: 'test_test_permission_scope_list_3_en',
        code: 'test_test_permission_scope_list_3_en',
        dbUser: 'sa',
        name: 'test test permission scope list - 3',
        category: 'AllowSupervisor',
        lockCounter: 10,
        messageJson:
          '[{"body":{"text":"allow agent send 2\\n"},"option":null,"header":{"text":null},"language":"en"}]',
        messageParameters: '[]',
        isOffline: true,
        realTemplateId: 'test_test_permission_scope_list_3_en',
        status: 'ACTIVE',
        createUserCode: 'ansel1',
        createDatetime: '2024-03-20T07:48:15.554Z',
        latestUpdateUserCode: 'SYSTEM',
        latestUpdateDatetime: '2024-03-28T02:13:02.728Z',
        channelType: 'WHATSAPP',
        channelAccountNumber: '***********',
        templateType: 'STANDARD',
        messageCategory: 'MARKETING',
        language: 'en',
        approvalStatus: 'APPROVED',
        rejectedReason: null,
        messageJsonHtml:
          '[{"body":{"text":"<p>allow agent send 2</p>\\n"},"option":null,"header":{"text":null},"image":{"fileName":null},"language":"en"}]',
      },
      {
        pkey: 'test_test_permission_scope_list_2_copy__en_en',
        code: 'test_test_permission_scope_list_2_copy__en_en',
        dbUser: 'sa',
        name: 'test_test_permission_scope_list_2_copy__en (en)',
        category: 'AllowSupervisor',
        lockCounter: 9,
        messageJson:
          '[{"body":{"text":"allow agent send 2"},"option":null,"image":null,"language":"en"}]',
        messageParameters: '[]',
        isOffline: true,
        realTemplateId: 'test_test_permission_scope_list_2_copy__en',
        status: 'ACTIVE',
        createUserCode: 'SYSTEM',
        createDatetime: '2024-03-20T07:49:47.633Z',
        latestUpdateUserCode: 'SYSTEM',
        latestUpdateDatetime: '2024-03-28T02:13:02.744Z',
        channelType: 'WHATSAPP',
        channelAccountNumber: '***********',
        templateType: 'STANDARD',
        messageCategory: 'MARKETING',
        language: 'en',
        approvalStatus: 'APPROVED',
        rejectedReason: null,
        messageJsonHtml:
          '[{"body":{"text":"allow agent send 2"},"option":null,"image":null,"language":"en"}]',
      },
    ],
  },
  isSuccess: true,
  error: null,
  resultCode: 0,
};

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader(
    'Access-Control-Allow-Methods',
    'GET, POST, PUT, DELETE, OPTIONS'
  );
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-Requested-With, Content-Type, Accept'
  );

  try {
    // If everything is okay, return the user data
    res.status(200).json(DUMMY_DATA);
  } catch (error) {
    // If decryption fails or sourceId does not match
    res.status(403).json({
      data: null,
      isSuccess: false,
      error: 'Failed to get template data',
    });
  }
}
