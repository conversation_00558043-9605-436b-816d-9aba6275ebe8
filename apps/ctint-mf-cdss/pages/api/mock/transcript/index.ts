// pages/api/user.ts

import type { NextApiRequest, NextApiResponse } from 'next';

type DataResponse = {
  isSuccess: boolean;
  error: any;
};
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<DataResponse>
) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader(
    'Access-Control-Allow-Methods',
    'GET, POST, PUT, DELETE, OPTIONS'
  );
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-Requested-With, Content-Type, Accept'
  );

  try {
    // Wait for 5000ms
    await new Promise(resolve => setTimeout(resolve, 5000));
    res.status(200).json({
      isSuccess: true,
      error: null,
    });
  } catch (error) {
    res.status(403).json({
      isSuccess: false,
      error: 'Failed to transcript.',
    });
  }
}
