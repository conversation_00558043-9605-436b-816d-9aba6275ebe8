#!/bin/sh

# Path to the YAML configuration file
CONFIG_FILE="/app/apps/$APP_NAME_ENV/public/config/$GLOBAL_CONFIG_FILE"
AWS_CONFIG_FILE="/app/apps/$APP_NAME_ENV/public/config/global-config/$GLOBAL_CONFIG_FILE"

# Restore the original .next directory from the backup
rsync -a /app/orign_dist/* /app/
cp $AWS_CONFIG_FILE $CONFIG_FILE

# Extract the basepaths and hosts using yq
CDSS_HOST=$(yq e '.microfrontends."ctint-mf-cdss".host' $CONFIG_FILE)
CDSS_BASE_PATH=$(yq e '.microfrontends."ctint-mf-cdss".basepath' $CONFIG_FILE)
INTERACTION_HOST=$(yq e '.microfrontends."ctint-mf-interaction".host' $CONFIG_FILE)
INTERACTION_BASE_PATH=$(yq e '.microfrontends."ctint-mf-interaction".basepath' $CONFIG_FILE)
MANUAL_QUEUE_HOST=$(yq e '.microfrontends."ctint-mf-manual-queue".host' $CONFIG_FILE)
MANUAL_QUEUE_BASE_PATH=$(yq e '.microfrontends."ctint-mf-manual-queue".basepath' $CONFIG_FILE)
TDC_HOST=$(yq e '.microfrontends."ctint-mf-tdc".host' $CONFIG_FILE)
TDC_BASE_PATH=$(yq e '.microfrontends."ctint-mf-tdc".basepath' $CONFIG_FILE)
SUPER_DASHBOARD_HOST=$(yq e '.microfrontends."ctint-mf-super-dashboard".host' $CONFIG_FILE)
SUPER_DASHBOARD_BASE_PATH=$(yq e '.microfrontends."ctint-mf-super-dashboard".basepath' $CONFIG_FILE)
CALL_HOST=$(yq e '.microfrontends."ctint-mf-call".host' $CONFIG_FILE)
CALL_BASE_PATH=$(yq e '.microfrontends."ctint-mf-call".basepath' $CONFIG_FILE)
MESSAGE_HOST=$(yq e '.microfrontends."ctint-mf-message".host' $CONFIG_FILE)
MESSAGE_BASE_PATH=$(yq e '.microfrontends."ctint-mf-message".basepath' $CONFIG_FILE)
REPORT_HOST=$(yq e '.microfrontends."ctint-mf-report".host' $CONFIG_FILE)
REPORT_BASE_PATH=$(yq e '.microfrontends."ctint-mf-report".basepath' $CONFIG_FILE)
CAMPAIGN_HOST=$(yq e '.microfrontends."ctint-mf-campaign".host' $CONFIG_FILE)
CAMPAIGN_BASE_PATH=$(yq e '.microfrontends."ctint-mf-campaign".basepath' $CONFIG_FILE)
CONTENT_CREATION_HOST=$(yq e '.microfrontends."ctint-mf-content-creation".host' $CONFIG_FILE)
CONTENT_CREATION_BASE_PATH=$(yq e '.microfrontends."ctint-mf-content-creation".basepath' $CONFIG_FILE)

echo "INTERACTION_HOST: $INTERACTION_HOST"
echo "INTERACTION_BASE_PATH: $INTERACTION_BASE_PATH"
echo "SUPER_DASHBOARD_HOST: $SUPER_DASHBOARD_HOST"
echo "SUPER_DASHBOARD_BASE_PATH: $SUPER_DASHBOARD_BASE_PATH"
echo "MANUAL_QUEUE_HOST: $MANUAL_QUEUE_HOST"
echo "MANUAL_QUEUE_BASE_PATH: $MANUAL_QUEUE_BASE_PATH"
echo "TDC_HOST: $TDC_HOST"
echo "TDC_BASE_PATH: $TDC_BASE_PATH"
echo "CALL_HOST: $CALL_HOST"
echo "CALL_BASE_PATH: $CALL_BASE_PATH"
echo "CONFIG_FILE: $CONFIG_FILE"
echo "MESSAGE_HOST: $MESSAGE_HOST"
echo "MESSAGE_BASE_PATH: $MESSAGE_BASE_PATH"
echo "REPORT_HOST: $REPORT_HOST"
echo "REPORT_BASE_PATH: $REPORT_BASE_PATH"
echo "CAMPAIGN_HOST: $CAMPAIGN_HOST"
echo "CAMPAIGN_BASE_PATH: $CAMPAIGN_BASE_PATH"
echo "CONTENT_CREATION_HOST: $CONTENT_CREATION_HOST"
echo "CONTENT_CREATION_BASE_PATH: $CONTENT_CREATION_BASE_PATH"

# Add slashes around the paths if they are not empty
if [ -n "$CDSS_HOST" ]; then
  TARGET_PATH_CDSS_HOST="${CDSS_HOST}"
else
  TARGET_PATH_CDSS_HOST=""
fi
if [ -n "$CDSS_BASE_PATH" ]; then
  TARGET_PATH_CDSS_BASE_PATH="${CDSS_BASE_PATH}"
else
  TARGET_PATH_CDSS_BASE_PATH=""
fi
if [ -n "$INTERACTION_HOST" ]; then
  TARGET_PATH_INTERACTION_HOST="${INTERACTION_HOST}"
else
  TARGET_PATH_INTERACTION_HOST=""
fi
if [ -n "$INTERACTION_BASE_PATH" ]; then
  TARGET_PATH_INTERACTION_BASE_PATH="${INTERACTION_BASE_PATH}"
else
  TARGET_PATH_INTERACTION_BASE_PATH=""
fi
if [ -n "$MANUAL_QUEUE_HOST" ]; then
  TARGET_PATH_MANUAL_QUEUE_HOST="${MANUAL_QUEUE_HOST}"
else
  TARGET_PATH_MANUAL_QUEUE_HOST=""
fi
if [ -n "$MANUAL_QUEUE_BASE_PATH" ]; then
  TARGET_PATH_MANUAL_QUEUE_BASE_PATH="${MANUAL_QUEUE_BASE_PATH}"
else
  TARGET_PATH_MANUAL_QUEUE_BASE_PATH=""
fi
if [ -n "$SUPER_DASHBOARD_HOST" ]; then
  TARGET_PATH_SUPER_DASHBOARD_HOST="${SUPER_DASHBOARD_HOST}"
else
  TARGET_PATH_SUPER_DASHBOARD_HOST=""
fi
if [ -n "$SUPER_DASHBOARD_BASE_PATH" ]; then
  TARGET_PATH_SUPER_DASHBOARD_BASE_PATH="${SUPER_DASHBOARD_BASE_PATH}"
else
  TARGET_PATH_SUPER_DASHBOARD_BASE_PATH=""
fi
if [ -n "$TDC_HOST" ]; then
  TARGET_PATH_TDC_HOST="${TDC_HOST}"
else
  TARGET_PATH_TDC_HOST=""
fi
if [ -n "$TDC_BASE_PATH" ]; then
  TARGET_PATH_TDC_BASE_PATH="${TDC_BASE_PATH}"
else
  TARGET_PATH_TDC_BASE_PATH=""
fi
if [ -n "$CALL_HOST" ]; then
  TARGET_PATH_CALL_HOST="${CALL_HOST}"
else
  TARGET_PATH_CALL_HOST=""
fi
if [ -n "$CALL_BASE_PATH" ]; then
  TARGET_PATH_CALL_BASE_PATH="${CALL_BASE_PATH}"
else
  TARGET_PATH_CALL_BASE_PATH=""
fi
if [ -n "$MESSAGE_HOST" ]; then
  TARGET_PATH_MESSAGE_HOST="${MESSAGE_HOST}"
else
  TARGET_PATH_MESSAGE_HOST=""
fi
if [ -n "$MESSAGE_BASE_PATH" ]; then
  TARGET_PATH_MESSAGE_BASE_PATH="${MESSAGE_BASE_PATH}"
else
  TARGET_PATH_MESSAGE_BASE_PATH=""
fi

if [ -n "$REPORT_HOST" ]; then
  TARGET_PATH_REPORT_HOST="${REPORT_HOST}"
else
  TARGET_PATH_REPORT_HOST=""
fi
if [ -n "$REPORT_BASE_PATH" ]; then
  TARGET_PATH_REPORT_BASE_PATH="${REPORT_BASE_PATH}"
else
  TARGET_PATH_REPORT_BASE_PATH=""
fi

if [ -n "$CAMPAIGN_HOST" ]; then
  TARGET_PATH_CAMPAIGN_HOST="${CAMPAIGN_HOST}"
else
  TARGET_PATH_CAMPAIGN_HOST=""
fi

if [ -n "$CAMPAIGN_BASE_PATH" ]; then
  TARGET_PATH_CAMPAIGN_BASE_PATH="${CAMPAIGN_BASE_PATH}"
else
  TARGET_PATH_CAMPAIGN_BASE_PATH=""
fi

if [ -n "$CONTENT_CREATION_HOST" ]; then
  TARGET_PATH_CONTENT_CREATION_HOST="${CONTENT_CREATION_HOST}"
else
  TARGET_PATH_CONTENT_CREATION_HOST=""
fi

if [ -n "$CONTENT_CREATION_BASE_PATH" ]; then
  TARGET_PATH_CONTENT_CREATION_BASE_PATH="${CONTENT_CREATION_BASE_PATH}"
else
  TARGET_PATH_CONTENT_CREATION_BASE_PATH=""
fi

# Replace __HOST_TBM__ and __BASE_PATH_TBM__ with the actual values in all files
for dir in /app/apps /app/dist /app/apps/$APP_NAME_ENV/public; do
  sed -i "s|http://localhost:4400|$TARGET_PATH_CDSS_HOST|g" $(find $dir -type f)
  sed -i "s|/__CDSS_BASE_PATH_TBM__|$TARGET_PATH_CDSS_BASE_PATH|g" $(find $dir -type f)
  sed -i "s|http://localhost:4900|$TARGET_PATH_INTERACTION_HOST|g" $(find $dir -type f)
  sed -i "s|/__INTERACTION_BASE_PATH_TBM__|$TARGET_PATH_INTERACTION_BASE_PATH|g" $(find $dir -type f)
  sed -i "s|http://localhost:4301|$TARGET_PATH_MANUAL_QUEUE_HOST|g" $(find $dir -type f)
  sed -i "s|/__MANUAL_QUEUE_BASE_PATH_TBM__|$TARGET_PATH_MANUAL_QUEUE_BASE_PATH|g" $(find $dir -type f)
  sed -i "s|http://localhost:4202|$TARGET_PATH_SUPER_DASHBOARD_HOST|g" $(find $dir -type f)
  sed -i "s|/__SUPER_DASHBOARD_BASE_PATH_TBM__|$TARGET_PATH_SUPER_DASHBOARD_BASE_PATH|g" $(find $dir -type f)
  sed -i "s|http://localhost:5100|$TARGET_PATH_TDC_HOST|g" $(find $dir -type f)
  sed -i "s|/__TDC_BASE_PATH_TBM__|$TARGET_PATH_TDC_BASE_PATH|g" $(find $dir -type f)
  sed -i "s|http://localhost:4401|$TARGET_PATH_CALL_HOST|g" $(find $dir -type f)
  sed -i "s|/__CALL_BASE_PATH_TBM__|$TARGET_PATH_CALL_BASE_PATH|g" $(find $dir -type f)
  sed -i "s|http://localhost:4200|$TARGET_PATH_MESSAGE_HOST|g" $(find $dir -type f)
  sed -i "s|/__MESSAGE_BASE_PATH_TBM__|$TARGET_PATH_MESSAGE_BASE_PATH|g" $(find $dir -type f)
  sed -i "s|http://localhost:4302|$TARGET_PATH_REPORT_HOST|g" $(find $dir -type f)
  sed -i "s|/__REPORT_BASE_PATH_TBM__|$TARGET_PATH_REPORT_BASE_PATH|g" $(find $dir -type f)
  sed -i "s|http://localhost:4308|$TARGET_PATH_CAMPAIGN_HOST|g" $(find $dir -type f)
  sed -i "s|/__CAMPAIGN_BASE_PATH_TBM__|$TARGET_PATH_CAMPAIGN_BASE_PATH|g" $(find $dir -type f)
  sed -i "s|http://localhost:4001|$TARGET_PATH_CONTENT_CREATION_HOST|g" $(find $dir -type f)
  sed -i "s|/__CONTENT_CREATION_BASE_PATH_TBM__|$TARGET_PATH_CONTENT_CREATION_BASE_PATH|g" $(find $dir -type f)
done
