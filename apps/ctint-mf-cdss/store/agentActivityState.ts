import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

interface AgentActivityState {
  activity: string;
  setActivity: (activity: string) => void;
}

export const useAgentActivityStore = create<AgentActivityState>()(
  immer((set, get) => ({
    activity: '',

    setActivity: (activity: string) =>
      set((state) => {
        state.activity = activity;
      }),
  }))
);
