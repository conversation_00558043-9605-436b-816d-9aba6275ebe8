import { MessageConvItem } from '@cdss-modules/design-system/@types/Conversation';
import {
  CDSSMessage,
  MessageData,
} from '@cdss-modules/design-system/@types/Message';

// Mock conversation list
// export const mockMessageConversations: MessageConvItem[] = [
//   {
//     id: 'ffbfa9d7-c63e-47bf-8c20-81fb5a28af81',
//     userName: '<PERSON>',
//     startTime: new Date('2024-12-19T10:00:00'),
//     endTime: new Date('2024-12-19T11:00:00'),
//     type: 'message',
//     icon: 'whatsapp',
//     integrationId: 'int_123',
//     originatingDirection: 'inbound',
//     hasUnread: true,
//     borderProgress: 75,
//     bgProgress: 60,
//     latestMessage: 'Hello, I need help with my order',
//     latestMessageTime: '10:00 AM',
//     isActive: true,
//   },
//   {
//     id: '26d89a3c-ed13-4b1a-93a9-e4079ea6a76c',
//     userName: 'Ronnie',
//     startTime: new Date('2024-12-19T09:30:00'),
//     endTime: new Date('2024-12-19T10:30:00'),
//     type: 'message',
//     icon: 'whatsapp',
//     integrationId: 'int_456',
//     originatingDirection: 'outbound',
//     hasUnread: false,
//     borderProgress: 100,
//     bgProgress: 100,
//     latestMessage: '谢谢您的反馈',
//     latestMessageTime: '10:30 AM',
//     isActive: false,
//   },
//   // {
//   //   id: 'conv_3',
//   //   userName: 'Mike Johnson',
//   //   startTime: new Date('2024-12-19T11:00:00'),
//   //   endTime: new Date('2024-12-19T12:00:00'),
//   //   type: 'message',
//   //   icon: 'whatsapp',
//   //   integrationId: 'int_789',
//   //   originatingDirection: 'inbound',
//   //   hasUnread: true,
//   //   borderProgress: 30,
//   //   bgProgress: 25,
//   //   latestMessage: 'When will my package arrive?',
//   //   latestMessageTime: '11:15 AM',
//   //   isActive: false,
//   // },
// ];

// Mock messages for each conversation
export const mockMessages: Record<string, CDSSMessage[]> = {
  conv_1: [
    {
      id: 'msg_1',
      conversationId: 'conv_1',
      participantId: 'part_123',
      originalPlatform: 'whatsapp',
      platform: 'whatsapp',
      platformMessageId: 'plat_123',
      externalMessageId: 'ext_123',
      channelId: 'channel_1',
      direction: 'inbound',
      messengerType: 'text',
      category: 'chat',
      type: 'text',
      userName: 'John Doe',
      userId: 'user_123',
      fromAddress: '+1234567890',
      fromName: 'John Doe',
      toAddress: '+0987654321',
      toName: 'Support',
      timestamp: new Date(),
      textBody: 'Hello, I need help with my order',
      status: 'delivered',
      metadata: '',
      tenant: 'tenant_1',
    },
    {
      id: 'msg_2',
      conversationId: 'conv_1',
      participantId: 'part_124',
      originalPlatform: 'whatsapp',
      platform: 'whatsapp',
      platformMessageId: 'plat_124',
      externalMessageId: 'ext_124',
      channelId: 'channel_1',
      direction: 'outbound',
      messengerType: 'text',
      category: 'chat',
      type: 'text',
      userName: 'Support Agent',
      userId: 'agent_123',
      fromAddress: '+0987654321',
      fromName: 'Support',
      toAddress: '+1234567890',
      toName: 'John Doe',
      timestamp: new Date(),
      textBody:
        "Hi John, I'd be happy to help. Could you provide your order number?",
      status: 'delivered',
      metadata: '',
      tenant: 'tenant_1',
    },
  ],
  conv_2: [
    {
      id: 'msg_3',
      conversationId: 'conv_2',
      participantId: 'part_125',
      originalPlatform: 'wechat',
      platform: 'wechat',
      platformMessageId: 'plat_125',
      externalMessageId: 'ext_125',
      channelId: 'channel_2',
      direction: 'inbound',
      messengerType: 'text',
      category: 'chat',
      type: 'text',
      userName: 'Jane Smith',
      userId: 'user_456',
      fromAddress: 'wechat_123',
      fromName: 'Jane Smith',
      toAddress: 'wechat_support',
      toName: 'Support',
      timestamp: new Date(),
      textBody: '您好，我想了解一下新产品',
      status: 'delivered',
      metadata: '',
      tenant: 'tenant_1',
    },
    {
      id: 'msg_4',
      conversationId: 'conv_2',
      participantId: 'part_126',
      originalPlatform: 'wechat',
      platform: 'wechat',
      platformMessageId: 'plat_126',
      externalMessageId: 'ext_126',
      channelId: 'channel_2',
      direction: 'outbound',
      messengerType: 'text',
      category: 'chat',
      type: 'text',
      userName: 'Support Agent',
      userId: 'agent_456',
      fromAddress: 'wechat_support',
      fromName: 'Support',
      toAddress: 'wechat_123',
      toName: 'Jane Smith',
      timestamp: new Date(),
      textBody: '欢迎咨询！我们的新产品有以下特点...',
      status: 'delivered',
      metadata: '',
      tenant: 'tenant_1',
    },
  ],
  conv_3: [
    {
      id: 'msg_5',
      conversationId: 'conv_3',
      participantId: 'part_127',
      originalPlatform: 'whatsapp',
      platform: 'whatsapp',
      platformMessageId: 'plat_127',
      externalMessageId: 'ext_127',
      channelId: 'channel_3',
      direction: 'inbound',
      messengerType: 'text',
      category: 'chat',
      type: 'text',
      userName: 'Mike Johnson',
      userId: 'user_789',
      fromAddress: '+1122334455',
      fromName: 'Mike Johnson',
      toAddress: '+0987654321',
      toName: 'Support',
      timestamp: new Date(),
      textBody: 'When will my package arrive?',
      status: 'delivered',
      metadata: '',
      tenant: 'tenant_1',
    },
  ],
};

// Helper function to convert messages to MessageData format
export const mockMessagesData: MessageData[] = Object.entries(mockMessages).map(
  ([conversationId, messages]) => ({
    conversationId,
    startTime: new Date().toLocaleString(),
    messages,
  })
);
