const { createGlobPatternsForDependencies } = require('@nx/react/tailwind');
import { GLOBAL_TAILWIND_CONFIG } from '../../libs/design-system/tailwind.config';

export const config = {
  ...GLOBAL_TAILWIND_CONFIG,
  content: [
    ...GLOBAL_TAILWIND_CONFIG.content,
    '../libs/design-system/src/**/*.{js,ts,jsx,tsx}',
    './pages/**/*.{js,ts,jsx,tsx}',
    './components/**/*.{js,ts,jsx,tsx}',
    './app/**/*.{js,ts,jsx,tsx}',
    ...createGlobPatternsForDependencies(__dirname),
    '../../apps/**/*.{js,ts,jsx,tsx}',
  ],
  
};

export default config;
