// user/queue-group
export type TAdminGroupData = {
  id?: string;
  name?: string;
  state?: string;
  roles?: TGroupItem[];
  roleNames?: string;
  queues?: TGroupItem[];
  queueNames?: string;
  users?: TGroupItem[];
  userNames?: string;
};
export type TAdminGroupDataResp = {
  data: TAdminGroupData[];
  error: string;
  isSuccess: boolean;
};

type TGroupItem = {
  id?: string;
  name?: string;
  code?: string;
};

type TGroupPropertyNames = 'users' | 'roles' | 'queues';

export type TGroupPostData = Partial<
  Record<TGroupPropertyNames, TGroupItem>
> & {
  id?: string;
  name: string;
  type: string;
};
