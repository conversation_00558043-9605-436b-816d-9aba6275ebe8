import { Toaster, useRouteHandler } from '@cdss-modules/design-system';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CannedMessageCategoryBody } from '../../_ui/CannedMessagePanel/Category';
import { WhitePanel } from '@cdss-modules/design-system/components/_ui/WhitePanel';
import RadixTabs from '@cdss-modules/design-system/components/_ui/RadixTabs';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';

const queryClient = new QueryClient();

interface CategoryListProp {
  spaceName: string;
}

export default function CategoryList({ spaceName }: CategoryListProp) {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const { toPath } = useRouteHandler();
  const tabs = [
    {
      tabName: 'category',
      label: t('ctint-mf-content-creation.category.tab.list'),
      content: <CannedMessageCategoryBody queryClient={queryClient} />,
    },
  ];

  return (
    <QueryClientProvider client={queryClient}>
      <div className="w-full h-full overflow-hidden">
        <WhitePanel className="h-full flex flex-col overflow-hidden">
          <label className="font-normal flex-shrink-0">
            <span
              className="cursor-pointer text-gray-400 hover:text-black"
              onClick={() => {
                toPath('/canned');
              }}
            >
              {t('ctint-mf-content-creation.main.tab.cannedMessage')}
            </span>{' '}
            / {searchParams.get('space')}
          </label>
          <div className="flex-1 min-h-0 overflow-hidden">
            <RadixTabs
              defaultOpenTab="category"
              tabs={tabs}
            />
          </div>
          <Toaster />
        </WhitePanel>
      </div>
    </QueryClientProvider>
  );
}
