import { ChevronLeft, CircleHelp, X, PlusCircle } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import { v4 as uuidv4 } from 'uuid';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import ConfirmationPopup from '@cdss-modules/design-system/components/_ui/ConfirmationPopup';
import {
  submitTemplate,
  SubmitTemplateRequest,
  getTemplateDetail,
  handleFileUpload,
} from '../../../../../../lib/api';
import { useRouteHandler } from '@cdss-modules/design-system';
import {
  TemplateList,
  EmailEditorRef,
  ExtendedEmailAttachment,
} from '../../../../../../@types/template';
import EmailEditor from '../../../../../../components/_ui/TiptapEditor/EmailEditor';
import {
  emailEditorStyles,
  sanitizeEmailBody,
} from '@cdss-modules/design-system/lib/utils';

const EmailDetail = ({
  editable,
  setEditMode,
  selectedTemplate,
}: {
  editable: boolean;
  setEditMode?: (editMode: boolean) => void;
  selectedTemplate?: TemplateList;
}) => {
  const [templateName, setTemplateName] = React.useState('');
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const { basePath } = useRouteHandler();
  const editorRef = useRef<EmailEditorRef>(null);

  const [templateType, setTemplateType] = React.useState('');
  const [variableList, setVariableList] = React.useState<
    { id: string; value: string }[]
  >([]);
  const [templateLanguage, setTemplateLanguage] = React.useState('');
  const [showTooltip, setShowTooltip] = React.useState(false);
  const [variableValues, setVariableValues] = React.useState<{
    [key: string]: string;
  }>({});
  const [tooltipPosition, setTooltipPosition] = React.useState<{
    x: number;
    y: number;
    showBelow?: boolean;
    arrowOffset?: number;
  } | null>(null);
  const helpIconRef = React.useRef<HTMLDivElement>(null);
  const [showDiscardPopup, setShowDiscardPopup] = React.useState(false);
  const [showSubmitPopup, setShowSubmitPopup] = React.useState(false);

  // 验证状态
  const [validationErrors, setValidationErrors] = React.useState({
    templateName: false,
    templateType: false,
    templateLanguage: false,
    templateContent: false,
  });

  const [attachments, setAttachments] = useState<ExtendedEmailAttachment[]>([]);
  const [loading, setLoading] = useState(false);
  const [editorContent, setEditorContent] = useState('');

  // 加载状态
  const [isLoading, setIsLoading] = React.useState(false);
  const [templateId, setTemplateId] = React.useState<string | null>(null);

  // 处理编辑器内容变化
  const handleContentChange = (html: string) => {
    console.log('EmailDetail ===> handleContentChange', html);

    setEditorContent(html);
  };

  useEffect(() => {
    console.log('EmailDetail ===> attachments:', attachments);
  }, [attachments]);

  // 图片处理方法
  const addImage = () => {
    const input = document.createElement('input');
    input.setAttribute('type', 'file');
    input.setAttribute('accept', 'image/*');
    input.click();

    input.onchange = async () => {
      if (!input.files || !input.files[0]) return;

      const formData = new FormData();
      formData.append('files', input.files[0]);

      try {
        setLoading(true);
        const response = await handleFileUpload(basePath, formData);
        if (response.data?.data?.[0]?.success) {
          // 将上传的图片添加到附件列表，并设置为内联
          const attachmentData = response.data.data[0];
          attachmentData.isInLine = true;
          attachmentData.contentId = uuidv4();
          setAttachments((prev) => [...prev, attachmentData]);

          // 创建一个临时图片对象，获取自然尺寸
          const tempImg = new Image();
          tempImg.onload = () => {
            const naturalWidth = tempImg.naturalWidth;
            const naturalHeight = tempImg.naturalHeight;

            // 使用editorRef直接插入图片到编辑器，包含宽高属性
            if (editorRef.current) {
              editorRef.current.insertImage(
                attachmentData.url,
                attachmentData.contentId,
                attachmentData.attachmentName || 'image',
                naturalWidth, // 传递宽度
                naturalHeight // 传递高度
              );
            }
          };
          tempImg.src = attachmentData.url;
        }
      } catch (error) {
        console.error('Upload failed:', error);
      } finally {
        setLoading(false);
      }
    };
  };

  // 处理 HTML 内容
  const processHtmlContent = (html: string, metadata: any) => {
    if (!html) return '';

    let processedHtml = html;

    // 处理内联图片
    if (metadata && metadata.length > 0) {
      metadata.forEach((attachment: any) => {
        if (attachment.isInLine && attachment.contentId && attachment.url) {
          const regex = new RegExp(`cid:${attachment.contentId}`, 'g');
          processedHtml = processedHtml.replace(regex, attachment.url);
        }
      });
    }

    return processedHtml;
  };

  // 获取模板详情
  const fetchTemplateDetail = React.useCallback(
    async (id: string) => {
      try {
        setIsLoading(true);
        console.log('Fetching template detail for ID:', id);

        const response = await getTemplateDetail(id, basePath);

        if (response.data.isSuccess && response.data.data) {
          const templateData = response.data.data;
          console.log('Template detail loaded:', templateData);

          // template 图片加载预处理
          const processedContent = processHtmlContent(
            templateData.content,
            templateData.metadata
          );
          templateData.content = processedContent;

          // 填充表单数据
          setTemplateName(templateData.name);
          setEditorContent(templateData.content);
          setTemplateLanguage(templateData.language);
          setAttachments(templateData.metadata || []);

          // 根据内容判断模板类型
          const hasVariables = /{{([^}]+)}}/g.test(templateData.content);
          setTemplateType(hasVariables ? 'variable-text' : 'text');

          // 如果有变量，提取变量列表
          if (hasVariables) {
            const variableRegex = /{{([^}]+)}}/g;
            const variables = new Set<string>();
            let match;

            while (
              (match = variableRegex.exec(templateData.content)) !== null
            ) {
              variables.add(match[1]);
            }

            // 创建变量列表
            const variableList = Array.from(variables).map(
              (variable, index) => ({
                id: `${Date.now()}-${index}`,
                value: variable,
              })
            );

            setVariableList(variableList);
          }
        }
      } catch (error) {
        console.error('Failed to fetch template detail:', error);
      } finally {
        setIsLoading(false);
      }
    },
    [basePath]
  );

  // 检查 selectedTemplate 中是否有模板 ID
  React.useEffect(() => {
    if (!selectedTemplate) return;
    const templateIdFromProps = selectedTemplate?.id;
    if (templateIdFromProps && templateIdFromProps !== templateId) {
      setTemplateId(templateIdFromProps);
      fetchTemplateDetail(templateIdFromProps);
    }
  }, [selectedTemplate, templateId, fetchTemplateDetail]);

  // 添加新的变量输入框
  const addVariable = () => {
    const newVariable = {
      id: Date.now().toString(),
      value: '',
    };
    setVariableList((prev) => [...prev, newVariable]);
  };

  // 删除变量输入框
  const removeVariable = (id: string) => {
    setVariableList((prev) => prev.filter((variable) => variable.id !== id));
  };

  // 更新变量值
  const updateVariable = (id: string, value: string) => {
    setVariableList((prev) =>
      prev.map((variable) =>
        variable.id === id ? { ...variable, value } : variable
      )
    );
  };

  // 获取内容中的所有变量
  const getVariablesFromContent = () => {
    if (!editorContent.trim()) return [];

    const variableRegex = /{{([^}]+)}}/g;
    const variables = new Set<string>();
    let match;

    while ((match = variableRegex.exec(editorContent)) !== null) {
      variables.add(match[1]);
    }

    return Array.from(variables);
  };

  // 更新预览变量值
  const updateVariableValue = (variableName: string, value: string) => {
    setVariableValues((prev) => ({
      ...prev,
      [variableName]: value,
    }));
  };

  // 处理 tooltip 显示
  const handleTooltipShow = () => {
    if (helpIconRef.current) {
      const rect = helpIconRef.current.getBoundingClientRect();
      const tooltipWidth = 256; // w-64 = 16rem = 256px
      const tooltipHeight = 80; // 估算的 tooltip 高度
      const margin = 16; // 边距

      // 计算初始位置
      const iconCenterX = rect.left + rect.width / 2; // CircleHelp 图标的中心位置
      let tooltipX = iconCenterX;
      let y = rect.top;
      let showBelow = false;

      // 检查左边界
      if (tooltipX - tooltipWidth / 2 < margin) {
        tooltipX = margin + tooltipWidth / 2;
      }

      // 检查右边界
      if (tooltipX + tooltipWidth / 2 > window.innerWidth - margin) {
        tooltipX = window.innerWidth - margin - tooltipWidth / 2;
      }

      // 检查上边界
      if (y - tooltipHeight - 10 < margin) {
        y = rect.bottom + 10; // 显示在下方
        showBelow = true;
      }

      // 计算箭头相对于 tooltip 的偏移量
      // 箭头应该指向 CircleHelp 图标的中心位置
      const tooltipLeft = tooltipX - tooltipWidth / 2;
      const arrowOffset = iconCenterX - tooltipLeft; // 箭头距离 tooltip 左边的距离

      setTooltipPosition({ x: tooltipX, y, showBelow, arrowOffset });
      setShowTooltip(true);
    }
  };

  const handleTooltipHide = () => {
    setShowTooltip(false);
    setTooltipPosition(null);
  };

  // 处理丢弃确认
  const handleDiscardConfirm = () => {
    // 清空所有表单数据
    setTemplateName('');
    setTemplateType('');
    setVariableList([]);
    setTemplateLanguage('');
    setEditorContent('');
    setVariableValues({});
    // 返回上一页
    setEditMode && setEditMode(false);
  };

  // 验证表单并显示提交弹窗
  const handleSubmitClick = () => {
    // 验证表单
    const errors = {
      templateName: !templateName || templateName.trim() === '',
      templateType: !templateType || templateType.trim() === '',
      templateLanguage: !templateLanguage || templateLanguage.trim() === '',
      templateContent: !editorContent || editorContent.trim() === '',
    };

    setValidationErrors(errors);

    // 如果有验证错误，不显示弹窗
    if (
      errors.templateName ||
      errors.templateType ||
      errors.templateLanguage ||
      errors.templateContent
    ) {
      return;
    }

    // 验证通过，显示确认弹窗
    setShowSubmitPopup(true);
  };

  // HTML解码函数，将&amp;等HTML实体转换为实际字符
  const decodeHtmlEntities = (str: string): string => {
    try {
      const parser = new DOMParser();
      const doc = parser.parseFromString(str, 'text/html');
      return doc.documentElement.textContent || str;
    } catch (e) {
      console.warn('Failed to decode HTML entities:', str, e);
      return str;
    }
  };

  // 处理editorContent中的图片URL，将匹配的URL替换为cid格式
  const processEditorContentForSave = (content: string): string => {
    if (!content || !attachments.length) return content;

    let processedContent = content;

    // 使用正则表达式找到所有img标签的src属性
    const imgRegex = /<img[^>]+src=["']([^"']+)["'][^>]*>/gi;

    processedContent = processedContent.replace(imgRegex, (match, srcUrl) => {
      try {
        // 首先进行HTML实体解码，将&amp;转换为&
        const htmlDecodedSrcUrl = decodeHtmlEntities(srcUrl);

        // 然后进行URL解码
        let urlDecodedSrcUrl;
        try {
          urlDecodedSrcUrl = decodeURIComponent(htmlDecodedSrcUrl);
        } catch (e) {
          // 如果URL解码失败，使用HTML解码后的URL
          urlDecodedSrcUrl = htmlDecodedSrcUrl;
        }

        // 在attachments中查找匹配的URL
        const matchingAttachment = attachments.find((attachment) => {
          if (!attachment.url || !attachment.contentId) return false;

          // 尝试多种匹配方式
          const attachmentUrl = attachment.url;
          let decodedAttachmentUrl;

          try {
            decodedAttachmentUrl = decodeURIComponent(attachmentUrl);
          } catch (e) {
            decodedAttachmentUrl = attachmentUrl;
          }

          console.log('processEditorContentForSave ==> srcUrl', srcUrl);
          console.log(
            'processEditorContentForSave ==> htmlDecodedSrcUrl',
            htmlDecodedSrcUrl
          );
          console.log(
            'processEditorContentForSave ==> urlDecodedSrcUrl',
            urlDecodedSrcUrl
          );
          console.log(
            'processEditorContentForSave ==> attachmentUrl',
            attachmentUrl
          );
          console.log(
            'processEditorContentForSave ==> decodedAttachmentUrl',
            decodedAttachmentUrl
          );

          // 比较多种组合：
          // 1. 原始srcUrl vs 原始attachmentUrl
          // 2. 原始srcUrl vs 解码attachmentUrl
          // 3. HTML解码srcUrl vs 原始attachmentUrl
          // 4. HTML解码srcUrl vs 解码attachmentUrl
          // 5. URL解码srcUrl vs 原始attachmentUrl
          // 6. URL解码srcUrl vs 解码attachmentUrl
          const isMatch =
            srcUrl === attachmentUrl ||
            srcUrl === decodedAttachmentUrl ||
            htmlDecodedSrcUrl === attachmentUrl ||
            htmlDecodedSrcUrl === decodedAttachmentUrl ||
            urlDecodedSrcUrl === attachmentUrl ||
            urlDecodedSrcUrl === decodedAttachmentUrl;

          console.log('processEditorContentForSave ==> isMatch', isMatch);
          return isMatch;
        });

        // 如果找到匹配的attachment，替换src为cid格式
        if (matchingAttachment) {
          return match.replace(srcUrl, `cid:${matchingAttachment.contentId}`);
        }

        return match;
      } catch (e) {
        // 如果decodeURIComponent失败，返回原始匹配
        console.warn('Failed to decode URL:', srcUrl, e);
        return match;
      }
    });

    return processedContent;
  };

  // 过滤attachments，只保留在processedContent中存在contentId的附件
  const filterAttachmentsByContent = (
    processedContent: string,
    attachments: ExtendedEmailAttachment[]
  ): ExtendedEmailAttachment[] => {
    return attachments.filter((attachment) => {
      // 如果没有contentId，保留附件（可能是非内联附件）
      if (!attachment.contentId) {
        return true;
      }

      // 检查processedContent中是否包含cid:${contentId}
      const cidPattern = `cid:${attachment.contentId}`;
      const exists = processedContent.includes(cidPattern);

      console.log(
        `filterAttachmentsByContent ==> contentId: ${attachment.contentId}, exists: ${exists}`
      );

      return exists;
    });
  };

  // 处理提交确认
  const handleSubmitConfirm = async () => {
    try {
      // 从 searchParams 获取必要的参数
      const spaceId = searchParams.get('spaceId') || '';
      const channelType = searchParams.get('type') || '';
      const parentId = searchParams.get('id') || '';
      const id = selectedTemplate?.id;

      // 处理editorContent中的图片URL
      const processedContent = processEditorContentForSave(editorContent);

      // 过滤attachments，只保留在processedContent中存在的附件
      const filteredAttachments = filterAttachmentsByContent(
        processedContent,
        attachments
      );

      console.log(
        'handleSubmitConfirm ==> original attachments count:',
        attachments.length
      );
      console.log(
        'handleSubmitConfirm ==> filtered attachments count:',
        filteredAttachments.length
      );

      // 构建 API 请求数据
      const requestData: SubmitTemplateRequest = {
        name: templateName,
        spaceId: spaceId,
        parentId: parentId,
        content: processedContent,
        language: templateLanguage,
        channelType: channelType,
        isPublished: true, // submit => true
        metadata: filteredAttachments,
        type: 'template',
      };

      if (id) {
        requestData.id = id;
      }

      console.log('Submitting template:', requestData);

      // 调用 API
      const response = await submitTemplate(requestData, basePath);

      console.log('Template submitted successfully:', response.data);

      // 提交成功后返回上一页
      setEditMode && setEditMode(false);
    } catch (error) {
      console.error('Failed to submit template:', error);
      // 这里可以添加错误处理，比如显示错误消息
    }
  };

  // 处理保存（不发布）
  const handleSave = async () => {
    // 验证表单 - 和 Submit 一样的验证
    const errors = {
      templateName: !templateName || templateName.trim() === '',
      templateType: !templateType || templateType.trim() === '',
      templateLanguage: !templateLanguage || templateLanguage.trim() === '',
      templateContent: !editorContent || editorContent.trim() === '',
    };

    setValidationErrors(errors);

    // 如果有验证错误，不执行保存
    if (
      errors.templateName ||
      errors.templateType ||
      errors.templateLanguage ||
      errors.templateContent
    ) {
      return;
    }

    try {
      // 从 searchParams 获取必要的参数
      const spaceId = searchParams.get('spaceId') || '';
      const channelType = searchParams.get('type') || '';
      const parentId = searchParams.get('id') || '';
      const id = selectedTemplate?.id;

      // 处理editorContent中的图片URL
      const processedContent = processEditorContentForSave(editorContent);

      // 过滤attachments，只保留在processedContent中存在的附件
      const filteredAttachments = filterAttachmentsByContent(
        processedContent,
        attachments
      );

      console.log(
        'handleSave ==> original attachments count:',
        attachments.length
      );
      console.log(
        'handleSave ==> filtered attachments count:',
        filteredAttachments.length
      );

      // 构建 API 请求数据
      const requestData: SubmitTemplateRequest = {
        name: templateName,
        spaceId: spaceId,
        parentId: parentId,
        content: processedContent,
        language: templateLanguage,
        channelType: channelType,
        isPublished: false, // save => false
        metadata: filteredAttachments,
        type: 'template',
      };

      if (id) {
        requestData.id = id;
      }

      console.log('Saving template:', requestData);

      // 调用 API
      const response = await submitTemplate(requestData, basePath);

      console.log('Template saved successfully:', response.data);

      // 保存成功后可以显示成功消息
      // 暂时返回上一页
      setEditMode && setEditMode(false);
    } catch (error) {
      console.error('Failed to save template:', error);
      // 这里可以添加错误处理，比如显示错误消息
    }
  };

  // 清除特定字段的验证错误
  const clearValidationError = (field: keyof typeof validationErrors) => {
    if (validationErrors[field]) {
      setValidationErrors((prev) => ({
        ...prev,
        [field]: false,
      }));
    }
  };

  // 生成预览内容
  const generatePreviewContent = () => {
    if (!editorContent.trim()) {
      return t('ctint-mf-content-creation.template.preview.noContent');
    }

    let content = editorContent;

    // 替换变量
    const variableRegex = /{{([^}]+)}}/g;
    content = content.replace(variableRegex, (_, variableName) => {
      const value = variableValues[variableName];
      return value || `{{${variableName}}}`;
    });

    return content;
  };

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p className="text-gray-600">
            {t('ctint-mf-content-creation.template.loading')}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full overflow-y-auto">
      {/* 注入预览样式 */}
      <style dangerouslySetInnerHTML={{ __html: emailEditorStyles }} />
      {/* template name and operator button */}
      <section className="mt-4 flex flex-row items-center">
        <div className="flex-1 flex flex-row items-center">
          <ChevronLeft
            className="cursor-pointer"
            onClick={() => {
              setEditMode && setEditMode(false);
            }}
          />
          <input
            className={`w-2/5 border-2 rounded-md p-1 focus:outline-none ${
              validationErrors.templateName
                ? 'border-red-500 focus:ring-2 focus:ring-red-500'
                : 'border-gray-300 focus:ring-2 focus:ring-blue-500'
            } ${!editable ? 'bg-gray-100 cursor-not-allowed' : ''}`}
            type="text"
            placeholder={t(
              'ctint-mf-content-creation.template.name.placeholder'
            )}
            value={templateName}
            readOnly={!editable}
            onChange={(e) => {
              if (editable) {
                console.log('e: ', e.target.value);
                setTemplateName(e.target.value);
                clearValidationError('templateName');
              }
            }}
          />
        </div>
        {editable && (
          <div className="flex-1 flex flex-row items-center justify-end gap-x-2">
            <button
              className="p-1 border border-black rounded-md hover:bg-gray-50 transition-colors"
              onClick={() => setShowDiscardPopup(true)}
            >
              {t('ctint-mf-content-creation.template.button.discard')}
            </button>
            <button
              className="p-1 border border-black text-white bg-black rounded-md hover:bg-gray-800 transition-colors"
              onClick={handleSubmitClick}
            >
              {t('ctint-mf-content-creation.template.button.submit')}
            </button>
            <button
              className="p-1 border border-black text-white bg-black rounded-md hover:bg-gray-800 transition-colors"
              onClick={handleSave}
            >
              {t('ctint-mf-content-creation.template.button.save')}
            </button>
          </div>
        )}
      </section>
      {/* template info input */}
      <section className="flex-1 mt-4 flex flex-row w-full">
        {/* Left */}
        <div className="flex-1 flex flex-col mr-4 gap-2">
          {/* Template Type */}
          <div>
            <label>{t('ctint-mf-content-creation.template.label.type')}</label>
            <div className="relative w-full">
              <select
                value={templateType}
                disabled={!editable}
                className={`bg-white border rounded px-2 py-1 w-full text-black text-sm leading-[1.17] focus:outline-none appearance-none pr-10 ${
                  validationErrors.templateType
                    ? 'border-red-500 focus:ring-2 focus:ring-red-500'
                    : 'border-[#DEDEDE] focus:ring-2 focus:ring-blue-500'
                } ${!editable ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                onChange={(e) => {
                  if (editable) {
                    setTemplateType(e.target.value);
                    clearValidationError('templateType');
                  }
                }}
              >
                <option
                  value=""
                  disabled
                  className="text-gray-500"
                >
                  {t('ctint-mf-content-creation.template.placeholder.type')}
                </option>
                <option value="text">
                  {t('ctint-mf-content-creation.template.email.text')}
                </option>
                <option value="variable-text">
                  {t('ctint-mf-content-creation.template.email.variable-text')}
                </option>
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="m6 9 6 6 6-6" />
                </svg>
              </div>
            </div>
            {templateType === 'variable-text' && (
              <div className="mt-2">
                <label className="block text-sm font-medium mb-2">
                  {t('ctint-mf-content-creation.template.label.variables')}
                </label>

                {/* 显示已添加的变量输入框 */}
                {variableList.map((variable) => (
                  <div
                    key={variable.id}
                    className="flex flex-row gap-2 mb-2"
                  >
                    <input
                      type="text"
                      value={variable.value}
                      placeholder={t(
                        'ctint-mf-content-creation.template.variable.placeholder'
                      )}
                      readOnly={!editable}
                      className={`flex-1 bg-white border border-[#DEDEDE] rounded px-2 py-1 text-black text-sm leading-[1.4] focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        !editable ? 'bg-gray-100 cursor-not-allowed' : ''
                      }`}
                      onChange={(e) => {
                        if (editable) {
                          updateVariable(variable.id, e.target.value);
                        }
                      }}
                    />
                    {editable && (
                      <button
                        className="px-2 py-1 border border-red-500 text-red-500 hover:bg-red-500 hover:text-white rounded-md transition-colors"
                        onClick={() => {
                          removeVariable(variable.id);
                        }}
                        title="删除变量"
                      >
                        <X size={16} />
                      </button>
                    )}
                  </div>
                ))}

                {/* 添加新变量按钮 */}
                {editable && (
                  <button
                    className="w-full flex items-center gap-2 px-2 py-1 border border-[#DEDEDE] bg-white text-[#949494] hover:bg-gray-50 rounded transition-colors"
                    onClick={addVariable}
                  >
                    <PlusCircle
                      size={16}
                      className="text-white fill-black"
                    />
                    <span className="text-sm font-normal">
                      {t(
                        'ctint-mf-content-creation.template.variable.button.add'
                      )}
                    </span>
                  </button>
                )}
              </div>
            )}
          </div>
          {/* Template Language */}
          <div>
            <label>
              {t('ctint-mf-content-creation.template.label.language')}
            </label>
            <div className="relative w-full">
              <select
                value={templateLanguage}
                disabled={!editable}
                className={`bg-white border rounded px-2 py-1 w-full text-black text-sm leading-[1.17] focus:outline-none appearance-none pr-10 ${
                  validationErrors.templateLanguage
                    ? 'border-red-500 focus:ring-2 focus:ring-red-500'
                    : 'border-[#DEDEDE] focus:ring-2 focus:ring-blue-500'
                } ${!editable ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                onChange={(e) => {
                  if (editable) {
                    setTemplateLanguage(e.target.value);
                    clearValidationError('templateLanguage');
                  }
                }}
              >
                <option
                  value=""
                  disabled
                  className="text-gray-500"
                >
                  {t('ctint-mf-content-creation.template.placeholder.language')}
                </option>
                <option value="en">
                  {t('ctint-mf-content-creation.template.language.en')}
                </option>
                <option value="zh-CN">
                  {t('ctint-mf-content-creation.template.language.zh-CN')}
                </option>
                <option value="zh-HK">
                  {t('ctint-mf-content-creation.template.language.zh-HK')}
                </option>
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="m6 9 6 6 6-6" />
                </svg>
              </div>
            </div>
          </div>
          {/* input component */}
          {templateLanguage && templateLanguage.trim() !== '' && (
            <div className="flex flex-col">
              <div className="flex items-center gap-2">
                <label>
                  {t(
                    `ctint-mf-content-creation.template.language.${templateLanguage}`
                  )}
                </label>
                {templateType === 'variable-text' && (
                  <div
                    ref={helpIconRef}
                    className="relative inline-block"
                    onMouseEnter={handleTooltipShow}
                    onMouseLeave={handleTooltipHide}
                  >
                    <CircleHelp
                      className="text-grey-500 cursor-help"
                      size={16}
                    />
                  </div>
                )}

                {/* Portal Tooltip */}
                {showTooltip &&
                  tooltipPosition &&
                  createPortal(
                    <div
                      className="fixed px-3 py-2 bg-gray-800 text-white text-sm rounded-lg shadow-xl w-64 text-center pointer-events-none"
                      style={{
                        zIndex: 99999,
                        left: tooltipPosition.x - 128, // 128 = w-64 / 2
                        top: tooltipPosition.showBelow
                          ? tooltipPosition.y
                          : tooltipPosition.y - 10,
                        transform: tooltipPosition.showBelow
                          ? 'translateY(0%)'
                          : 'translateY(-100%)',
                      }}
                    >
                      <div className="whitespace-normal">
                        {t(
                          'ctint-mf-content-creation.template.tooltip.variableUsage'
                        )}
                      </div>
                      {/* Tooltip arrow */}
                      <div
                        className={`absolute w-0 h-0 ${
                          tooltipPosition.showBelow
                            ? 'border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent border-b-gray-800'
                            : 'border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-gray-800'
                        }`}
                        style={{
                          [tooltipPosition.showBelow ? 'bottom' : 'top']:
                            '100%',
                          left: tooltipPosition.arrowOffset
                            ? `${tooltipPosition.arrowOffset}px`
                            : '50%',
                          transform: 'translateX(-50%)',
                        }}
                      ></div>
                    </div>,
                    document.body
                  )}
              </div>
              {/* Tiptap Editor */}
              <div className="border border-[#DEDEDE] rounded-md">
                <EmailEditor
                  ref={editorRef}
                  initialContent={editorContent}
                  onContentChange={handleContentChange}
                  onAddImage={addImage}
                  placeholder="Compose your email..."
                  editable={editable}
                />
              </div>
            </div>
          )}
        </div>
        {/* Right - Preview */}
        <div className="flex-1 flex flex-col min-h-[500px] border border-[#DEDEDE] rounded bg-white">
          <h3 className="text-sm font-bold text-black p-3 pb-0">
            {t('ctint-mf-content-creation.template.preview.title')}
          </h3>
          <div className="flex-1 bg-[#FFF5DA] m-3 mt-3 rounded p-[14px]">
            <div className="flex flex-col gap-[14px]">
              {/* 变量列表 - 只在 variable-text 类型时显示 */}
              {templateType === 'variable-text' &&
                getVariablesFromContent().map((variableName) => (
                  <div
                    key={variableName}
                    className="flex gap-[14px]"
                    style={{ alignItems: 'center' }}
                  >
                    <div
                      className="w-[71px] text-sm text-black font-normal leading-[1.17]"
                      style={{
                        wordBreak: 'break-word',
                        overflowWrap: 'break-word',
                        hyphens: 'auto',
                        display: 'flex',
                        alignItems: 'center',
                        minHeight: '29px',
                      }}
                    >
                      {variableName}
                    </div>
                    <div className="flex-1 bg-white border border-[#DEDEDE] rounded px-2 py-1 h-[29px] flex items-center">
                      <input
                        type="text"
                        className="w-full text-sm text-black font-normal outline-none bg-transparent"
                        value={variableValues[variableName] || ''}
                        onChange={(e) =>
                          updateVariableValue(variableName, e.target.value)
                        }
                        placeholder={t(
                          'ctint-mf-content-creation.template.preview.sampleVariable'
                        )}
                      />
                    </div>
                  </div>
                ))}

              {/* 内容预览 */}
              {editorContent.trim() && (
                <div
                  className="ProseMirror email-preview-content"
                  style={{
                    fontSize: '16px',
                    lineHeight: '1.6',
                    color: '#374151',
                  }}
                  dangerouslySetInnerHTML={{
                    __html: sanitizeEmailBody(generatePreviewContent()),
                  }}
                />
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Discard Confirmation Popup */}
      <ConfirmationPopup
        isOpen={showDiscardPopup}
        onClose={() => setShowDiscardPopup(false)}
        onConfirm={handleDiscardConfirm}
        message={t('ctint-mf-content-creation.popup.discard.message')}
        confirmText={t('ctint-mf-content-creation.popup.discard.yes')}
        cancelText={t('ctint-mf-content-creation.popup.discard.no')}
      />

      {/* Submit Confirmation Popup */}
      <ConfirmationPopup
        isOpen={showSubmitPopup}
        onClose={() => setShowSubmitPopup(false)}
        onConfirm={handleSubmitConfirm}
        message={t('ctint-mf-content-creation.popup.submit.message')}
        confirmText={t('ctint-mf-content-creation.popup.submit.yes')}
        cancelText={t('ctint-mf-content-creation.popup.submit.no')}
      />
    </div>
  );
};

export default EmailDetail;
