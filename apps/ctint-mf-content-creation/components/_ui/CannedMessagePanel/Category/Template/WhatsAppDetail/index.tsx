import { ChevronLeft, CircleHelp, X, PlusCircle } from 'lucide-react';
import React from 'react';
import { createPortal } from 'react-dom';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import ConfirmationPopup from '@cdss-modules/design-system/components/_ui/ConfirmationPopup';
import {
  submitTemplate,
  SubmitTemplateRequest,
  getTemplateDetail,
} from '../../../../../../lib/api';
import { useRouteHandler } from '@cdss-modules/design-system';
import { TemplateList } from '../../../../../../@types/template';

const WhatsAppDetail = ({
  editable,
  setEditMode,
  selectedTemplate,
}: {
  editable: boolean;
  setEditMode?: (editMode: boolean) => void;
  selectedTemplate?: TemplateList;
}) => {
  const [templateName, setTemplateName] = React.useState('');
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const { basePath } = useRouteHandler();

  const [templateType, setTemplateType] = React.useState('');
  const [variableList, setVariableList] = React.useState<
    { id: string; value: string }[]
  >([]);
  const [templateLanguage, setTemplateLanguage] = React.useState('');
  const [showTooltip, setShowTooltip] = React.useState(false);
  const [templateContent, setTemplateContent] = React.useState('');
  const [variableValues, setVariableValues] = React.useState<{
    [key: string]: string;
  }>({});
  const [tooltipPosition, setTooltipPosition] = React.useState<{
    x: number;
    y: number;
    showBelow?: boolean;
    arrowOffset?: number;
  } | null>(null);
  const helpIconRef = React.useRef<HTMLDivElement>(null);
  const [showDiscardPopup, setShowDiscardPopup] = React.useState(false);
  const [showSubmitPopup, setShowSubmitPopup] = React.useState(false);

  // 验证状态
  const [validationErrors, setValidationErrors] = React.useState({
    templateName: false,
    templateType: false,
    templateLanguage: false,
    templateContent: false,
  });

  // 加载状态
  const [isLoading, setIsLoading] = React.useState(false);
  const [templateId, setTemplateId] = React.useState<string | null>(null);

  // 获取模板详情
  const fetchTemplateDetail = React.useCallback(
    async (id: string) => {
      try {
        setIsLoading(true);
        console.log('Fetching template detail for ID:', id);

        const response = await getTemplateDetail(id, basePath);

        if (response.data.isSuccess && response.data.data) {
          const templateData = response.data.data;
          console.log('Template detail loaded:', templateData);

          // 填充表单数据
          setTemplateName(templateData.name);
          setTemplateContent(templateData.content);
          setTemplateLanguage(templateData.language);

          // 根据内容判断模板类型
          const hasVariables = /{{([^}]+)}}/g.test(templateData.content);
          setTemplateType(hasVariables ? 'variable-text' : 'text');

          // 如果有变量，提取变量列表
          if (hasVariables) {
            const variableRegex = /{{([^}]+)}}/g;
            const variables = new Set<string>();
            let match;

            while (
              (match = variableRegex.exec(templateData.content)) !== null
            ) {
              variables.add(match[1]);
            }

            // 创建变量列表
            const variableList = Array.from(variables).map(
              (variable, index) => ({
                id: `${Date.now()}-${index}`,
                value: variable,
              })
            );

            setVariableList(variableList);
          }
        }
      } catch (error) {
        console.error('Failed to fetch template detail:', error);
      } finally {
        setIsLoading(false);
      }
    },
    [basePath]
  );

  // 检查 selectedTemplate 中是否有模板 ID
  React.useEffect(() => {
    if (!selectedTemplate) return;
    const templateIdFromProps = selectedTemplate?.id;
    if (templateIdFromProps && templateIdFromProps !== templateId) {
      setTemplateId(templateIdFromProps);
      fetchTemplateDetail(templateIdFromProps);
    }
  }, [selectedTemplate, templateId, fetchTemplateDetail]);

  // 添加新的变量输入框
  const addVariable = () => {
    const newVariable = {
      id: Date.now().toString(),
      value: '',
    };
    setVariableList((prev) => [...prev, newVariable]);
  };

  // 删除变量输入框
  const removeVariable = (id: string) => {
    setVariableList((prev) => prev.filter((variable) => variable.id !== id));
  };

  // 更新变量值
  const updateVariable = (id: string, value: string) => {
    setVariableList((prev) =>
      prev.map((variable) =>
        variable.id === id ? { ...variable, value } : variable
      )
    );
  };

  // 获取内容中的所有变量
  const getVariablesFromContent = () => {
    if (!templateContent.trim()) return [];

    const variableRegex = /{{([^}]+)}}/g;
    const variables = new Set<string>();
    let match;

    while ((match = variableRegex.exec(templateContent)) !== null) {
      variables.add(match[1]);
    }

    return Array.from(variables);
  };

  // 更新预览变量值
  const updateVariableValue = (variableName: string, value: string) => {
    setVariableValues((prev) => ({
      ...prev,
      [variableName]: value,
    }));
  };

  // 处理 tooltip 显示
  const handleTooltipShow = () => {
    if (helpIconRef.current) {
      const rect = helpIconRef.current.getBoundingClientRect();
      const tooltipWidth = 256; // w-64 = 16rem = 256px
      const tooltipHeight = 80; // 估算的 tooltip 高度
      const margin = 16; // 边距

      // 计算初始位置
      const iconCenterX = rect.left + rect.width / 2; // CircleHelp 图标的中心位置
      let tooltipX = iconCenterX;
      let y = rect.top;
      let showBelow = false;

      // 检查左边界
      if (tooltipX - tooltipWidth / 2 < margin) {
        tooltipX = margin + tooltipWidth / 2;
      }

      // 检查右边界
      if (tooltipX + tooltipWidth / 2 > window.innerWidth - margin) {
        tooltipX = window.innerWidth - margin - tooltipWidth / 2;
      }

      // 检查上边界
      if (y - tooltipHeight - 10 < margin) {
        y = rect.bottom + 10; // 显示在下方
        showBelow = true;
      }

      // 计算箭头相对于 tooltip 的偏移量
      // 箭头应该指向 CircleHelp 图标的中心位置
      const tooltipLeft = tooltipX - tooltipWidth / 2;
      const arrowOffset = iconCenterX - tooltipLeft; // 箭头距离 tooltip 左边的距离

      setTooltipPosition({ x: tooltipX, y, showBelow, arrowOffset });
      setShowTooltip(true);
    }
  };

  const handleTooltipHide = () => {
    setShowTooltip(false);
    setTooltipPosition(null);
  };

  // 处理丢弃确认
  const handleDiscardConfirm = () => {
    // 清空所有表单数据
    setTemplateName('');
    setTemplateType('');
    setVariableList([]);
    setTemplateLanguage('');
    setTemplateContent('');
    setVariableValues({});
    // 返回上一页
    setEditMode && setEditMode(false);
  };

  // 验证表单并显示提交弹窗
  const handleSubmitClick = () => {
    // 验证表单
    const errors = {
      templateName: !templateName || templateName.trim() === '',
      templateType: !templateType || templateType.trim() === '',
      templateLanguage: !templateLanguage || templateLanguage.trim() === '',
      templateContent: !templateContent || templateContent.trim() === '',
    };

    setValidationErrors(errors);

    // 如果有验证错误，不显示弹窗
    if (
      errors.templateName ||
      errors.templateType ||
      errors.templateLanguage ||
      errors.templateContent
    ) {
      return;
    }

    // 验证通过，显示确认弹窗
    setShowSubmitPopup(true);
  };

  // 处理提交确认
  const handleSubmitConfirm = async () => {
    try {
      // 从 searchParams 获取必要的参数
      const spaceId = searchParams.get('spaceId') || '';
      const channelType = searchParams.get('type') || '';
      const parentId = searchParams.get('id') || '';
      const id = selectedTemplate?.id;

      // 构建 API 请求数据
      const requestData: SubmitTemplateRequest = {
        name: templateName,
        spaceId: spaceId,
        parentId: parentId,
        content: templateContent,
        language: templateLanguage,
        channelType: channelType,
        isPublished: true, // submit => true
        type: 'template',
      };

      if (id) {
        requestData.id = id;
      }

      console.log('Submitting template:', requestData);

      // 调用 API
      const response = await submitTemplate(requestData, basePath);

      console.log('Template submitted successfully:', response.data);

      // 提交成功后返回上一页
      setEditMode && setEditMode(false);
    } catch (error) {
      console.error('Failed to submit template:', error);
      // 这里可以添加错误处理，比如显示错误消息
    }
  };

  // 处理保存（不发布）
  const handleSave = async () => {
    // 验证表单 - 和 Submit 一样的验证
    const errors = {
      templateName: !templateName || templateName.trim() === '',
      templateType: !templateType || templateType.trim() === '',
      templateLanguage: !templateLanguage || templateLanguage.trim() === '',
      templateContent: !templateContent || templateContent.trim() === '',
    };

    setValidationErrors(errors);

    // 如果有验证错误，不执行保存
    if (
      errors.templateName ||
      errors.templateType ||
      errors.templateLanguage ||
      errors.templateContent
    ) {
      return;
    }

    try {
      // 从 searchParams 获取必要的参数
      const spaceId = searchParams.get('spaceId') || '';
      const channelType = searchParams.get('type') || '';
      const parentId = searchParams.get('id') || '';
      const id = selectedTemplate?.id;

      // 构建 API 请求数据
      const requestData: SubmitTemplateRequest = {
        name: templateName,
        spaceId: spaceId,
        parentId: parentId,
        content: templateContent,
        language: templateLanguage,
        channelType: channelType,
        isPublished: false, // save => false
        type: 'template',
      };

      if (id) {
        requestData.id = id;
      }

      console.log('Saving template:', requestData);

      // 调用 API
      const response = await submitTemplate(requestData, basePath);

      console.log('Template saved successfully:', response.data);

      // 保存成功后可以显示成功消息
      // 暂时返回上一页
      setEditMode && setEditMode(false);
    } catch (error) {
      console.error('Failed to save template:', error);
      // 这里可以添加错误处理，比如显示错误消息
    }
  };

  // 清除特定字段的验证错误
  const clearValidationError = (field: keyof typeof validationErrors) => {
    if (validationErrors[field]) {
      setValidationErrors((prev) => ({
        ...prev,
        [field]: false,
      }));
    }
  };

  // 生成预览内容
  const generatePreviewContent = () => {
    if (!templateContent.trim()) {
      return t('ctint-mf-content-creation.template.preview.noContent');
    }

    let content = templateContent;

    // 替换变量
    const variableRegex = /{{([^}]+)}}/g;
    content = content.replace(variableRegex, (_, variableName) => {
      const value = variableValues[variableName];
      return value || `{{${variableName}}}`;
    });

    return content;
  };

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p className="text-gray-600">
            {t('ctint-mf-content-creation.template.loading')}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full overflow-y-auto">
      {/* template name and operator button */}
      <section className="mt-4 flex flex-row items-center">
        <div className="flex-1 flex flex-row items-center">
          <ChevronLeft
            className="cursor-pointer"
            onClick={() => {
              setEditMode && setEditMode(false);
            }}
          />
          <input
            className={`w-2/5 border-2 rounded-md p-1 focus:outline-none ${
              validationErrors.templateName
                ? 'border-red-500 focus:ring-2 focus:ring-red-500'
                : 'border-gray-300 focus:ring-2 focus:ring-blue-500'
            } ${!editable ? 'bg-gray-100 cursor-not-allowed' : ''}`}
            type="text"
            placeholder={t(
              'ctint-mf-content-creation.template.name.placeholder'
            )}
            value={templateName}
            readOnly={!editable}
            onChange={(e) => {
              if (editable) {
                console.log('e: ', e.target.value);
                setTemplateName(e.target.value);
                clearValidationError('templateName');
              }
            }}
          />
        </div>
        {editable && (
          <div className="flex-1 flex flex-row items-center justify-end gap-x-2">
            <button
              className="p-1 border border-black rounded-md hover:bg-gray-50 transition-colors"
              onClick={() => setShowDiscardPopup(true)}
            >
              {t('ctint-mf-content-creation.template.button.discard')}
            </button>
            <button
              className="p-1 border border-black text-white bg-black rounded-md hover:bg-gray-800 transition-colors"
              onClick={handleSubmitClick}
            >
              {t('ctint-mf-content-creation.template.button.submit')}
            </button>
            <button
              className="p-1 border border-black text-white bg-black rounded-md hover:bg-gray-800 transition-colors"
              onClick={handleSave}
            >
              {t('ctint-mf-content-creation.template.button.save')}
            </button>
          </div>
        )}
      </section>
      {/* template info input */}
      <section className="flex-1 mt-4 flex flex-row w-full">
        {/* Left */}
        <div className="flex-1 flex flex-col mr-4 gap-2">
          {/* Template Type */}
          <div>
            <label>{t('ctint-mf-content-creation.template.label.type')}</label>
            <div className="relative w-full">
              <select
                value={templateType}
                disabled={!editable}
                className={`bg-white border rounded px-2 py-1 w-full text-black text-sm leading-[1.17] focus:outline-none appearance-none pr-10 ${
                  validationErrors.templateType
                    ? 'border-red-500 focus:ring-2 focus:ring-red-500'
                    : 'border-[#DEDEDE] focus:ring-2 focus:ring-blue-500'
                } ${!editable ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                onChange={(e) => {
                  if (editable) {
                    setTemplateType(e.target.value);
                    clearValidationError('templateType');
                  }
                }}
              >
                <option
                  value=""
                  disabled
                  className="text-gray-500"
                >
                  {t('ctint-mf-content-creation.template.placeholder.type')}
                </option>
                <option value="text">
                  {t('ctint-mf-content-creation.template.whatsapp.text')}
                </option>
                <option value="variable-text">
                  {t(
                    'ctint-mf-content-creation.template.whatsapp.variable-text'
                  )}
                </option>
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="m6 9 6 6 6-6" />
                </svg>
              </div>
            </div>
            {templateType === 'variable-text' && (
              <div className="mt-2">
                <label className="block text-sm font-medium mb-2">
                  {t('ctint-mf-content-creation.template.label.variables')}
                </label>

                {/* 显示已添加的变量输入框 */}
                {variableList.map((variable) => (
                  <div
                    key={variable.id}
                    className="flex flex-row gap-2 mb-2"
                  >
                    <input
                      type="text"
                      value={variable.value}
                      placeholder={t(
                        'ctint-mf-content-creation.template.variable.placeholder'
                      )}
                      readOnly={!editable}
                      className={`flex-1 bg-white border border-[#DEDEDE] rounded px-2 py-1 text-black text-sm leading-[1.4] focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        !editable ? 'bg-gray-100 cursor-not-allowed' : ''
                      }`}
                      onChange={(e) => {
                        if (editable) {
                          updateVariable(variable.id, e.target.value);
                        }
                      }}
                    />
                    {editable && (
                      <button
                        className="px-2 py-1 border border-red-500 text-red-500 hover:bg-red-500 hover:text-white rounded-md transition-colors"
                        onClick={() => {
                          removeVariable(variable.id);
                        }}
                        title="删除变量"
                      >
                        <X size={16} />
                      </button>
                    )}
                  </div>
                ))}

                {/* 添加新变量按钮 */}
                {editable && (
                  <button
                    className="w-full flex items-center gap-2 px-2 py-1 border border-[#DEDEDE] bg-white text-[#949494] hover:bg-gray-50 rounded transition-colors"
                    onClick={addVariable}
                  >
                    <PlusCircle
                      size={16}
                      className="text-white fill-black"
                    />
                    <span className="text-sm font-normal">
                      {t(
                        'ctint-mf-content-creation.template.variable.button.add'
                      )}
                    </span>
                  </button>
                )}
              </div>
            )}
          </div>
          {/* Template Language */}
          <div>
            <label>
              {t('ctint-mf-content-creation.template.label.language')}
            </label>
            <div className="relative w-full">
              <select
                value={templateLanguage}
                disabled={!editable}
                className={`bg-white border rounded px-2 py-1 w-full text-black text-sm leading-[1.17] focus:outline-none appearance-none pr-10 ${
                  validationErrors.templateLanguage
                    ? 'border-red-500 focus:ring-2 focus:ring-red-500'
                    : 'border-[#DEDEDE] focus:ring-2 focus:ring-blue-500'
                } ${!editable ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                onChange={(e) => {
                  if (editable) {
                    setTemplateLanguage(e.target.value);
                    clearValidationError('templateLanguage');
                  }
                }}
              >
                <option
                  value=""
                  disabled
                  className="text-gray-500"
                >
                  {t('ctint-mf-content-creation.template.placeholder.language')}
                </option>
                <option value="en">
                  {t('ctint-mf-content-creation.template.language.en')}
                </option>
                <option value="zh-CN">
                  {t('ctint-mf-content-creation.template.language.zh-CN')}
                </option>
                <option value="zh-HK">
                  {t('ctint-mf-content-creation.template.language.zh-HK')}
                </option>
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="m6 9 6 6 6-6" />
                </svg>
              </div>
            </div>
          </div>
          {/* input component */}
          {templateLanguage && templateLanguage.trim() !== '' && (
            <div className="flex flex-col">
              <div className="flex items-center gap-2">
                <label>
                  {t(
                    `ctint-mf-content-creation.template.language.${templateLanguage}`
                  )}
                </label>
                {templateType === 'variable-text' && (
                  <div
                    ref={helpIconRef}
                    className="relative inline-block"
                    onMouseEnter={handleTooltipShow}
                    onMouseLeave={handleTooltipHide}
                  >
                    <CircleHelp
                      className="text-grey-500 cursor-help"
                      size={16}
                    />
                  </div>
                )}

                {/* Portal Tooltip */}
                {showTooltip &&
                  tooltipPosition &&
                  createPortal(
                    <div
                      className="fixed px-3 py-2 bg-gray-800 text-white text-sm rounded-lg shadow-xl w-64 text-center pointer-events-none"
                      style={{
                        zIndex: 99999,
                        left: tooltipPosition.x - 128, // 128 = w-64 / 2
                        top: tooltipPosition.showBelow
                          ? tooltipPosition.y
                          : tooltipPosition.y - 10,
                        transform: tooltipPosition.showBelow
                          ? 'translateY(0%)'
                          : 'translateY(-100%)',
                      }}
                    >
                      <div className="whitespace-normal">
                        {t(
                          'ctint-mf-content-creation.template.tooltip.variableUsage'
                        )}
                      </div>
                      {/* Tooltip arrow */}
                      <div
                        className={`absolute w-0 h-0 ${
                          tooltipPosition.showBelow
                            ? 'border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent border-b-gray-800'
                            : 'border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-gray-800'
                        }`}
                        style={{
                          [tooltipPosition.showBelow ? 'bottom' : 'top']:
                            '100%',
                          left: tooltipPosition.arrowOffset
                            ? `${tooltipPosition.arrowOffset}px`
                            : '50%',
                          transform: 'translateX(-50%)',
                        }}
                      ></div>
                    </div>,
                    document.body
                  )}
              </div>
              <textarea
                className={`w-full h-[200px] border rounded-md p-1 focus:outline-none ${
                  validationErrors.templateContent
                    ? 'border-red-500 focus:ring-2 focus:ring-red-500'
                    : 'border-gray-300 focus:ring-2 focus:ring-blue-500'
                } ${!editable ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                placeholder={t(
                  'ctint-mf-content-creation.template.placeholder.content'
                )}
                value={templateContent}
                readOnly={!editable}
                onChange={(e) => {
                  if (editable) {
                    setTemplateContent(e.target.value);
                    clearValidationError('templateContent');
                  }
                }}
              />
            </div>
          )}
        </div>
        {/* Right - Preview */}
        <div className="flex-1 flex flex-col min-h-[500px] border border-[#DEDEDE] rounded bg-white">
          <h3 className="text-sm font-bold text-black p-3 pb-0">
            {t('ctint-mf-content-creation.template.preview.title')}
          </h3>
          <div className="flex-1 bg-[#FFF5DA] m-3 mt-3 rounded p-[14px]">
            <div className="flex flex-col gap-[14px]">
              {/* 变量列表 - 只在 variable-text 类型时显示 */}
              {templateType === 'variable-text' &&
                getVariablesFromContent().map((variableName) => (
                  <div
                    key={variableName}
                    className="flex gap-[14px]"
                    style={{ alignItems: 'center' }}
                  >
                    <div
                      className="w-[71px] text-sm text-black font-normal leading-[1.17]"
                      style={{
                        wordBreak: 'break-word',
                        overflowWrap: 'break-word',
                        hyphens: 'auto',
                        display: 'flex',
                        alignItems: 'center',
                        minHeight: '29px',
                      }}
                    >
                      {variableName}
                    </div>
                    <div className="flex-1 bg-white border border-[#DEDEDE] rounded px-2 py-1 h-[29px] flex items-center">
                      <input
                        type="text"
                        className="w-full text-sm text-black font-normal outline-none bg-transparent"
                        value={variableValues[variableName] || ''}
                        onChange={(e) =>
                          updateVariableValue(variableName, e.target.value)
                        }
                        placeholder={t(
                          'ctint-mf-content-creation.template.preview.sampleVariable'
                        )}
                      />
                    </div>
                  </div>
                ))}

              {/* 内容预览 */}
              {templateContent.trim() && (
                <div className="text-sm text-black leading-[1.17] whitespace-pre-wrap">
                  {templateType === 'text'
                    ? templateContent
                    : generatePreviewContent()}
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Discard Confirmation Popup */}
      <ConfirmationPopup
        isOpen={showDiscardPopup}
        onClose={() => setShowDiscardPopup(false)}
        onConfirm={handleDiscardConfirm}
        message={t('ctint-mf-content-creation.popup.discard.message')}
        confirmText={t('ctint-mf-content-creation.popup.discard.yes')}
        cancelText={t('ctint-mf-content-creation.popup.discard.no')}
      />

      {/* Submit Confirmation Popup */}
      <ConfirmationPopup
        isOpen={showSubmitPopup}
        onClose={() => setShowSubmitPopup(false)}
        onConfirm={handleSubmitConfirm}
        message={t('ctint-mf-content-creation.popup.submit.message')}
        confirmText={t('ctint-mf-content-creation.popup.submit.yes')}
        cancelText={t('ctint-mf-content-creation.popup.submit.no')}
      />
    </div>
  );
};

export default WhatsAppDetail;
