import React, { useState, useRef, useEffect } from 'react';
import { Editor } from '@tiptap/react';
import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Heading1,
  Heading2,
  Link as LinkIcon,
  Image as ImageIcon,
  List,
  ListOrdered,
  Undo,
  Redo,
  Type,
  Highlighter,
  ChevronDown,
} from 'lucide-react';

// 简单的工具栏按钮组件
interface ToolbarButtonProps {
  onClick: () => void;
  children: React.ReactNode;
  isActive?: boolean;
  tooltip: string;
  disabled?: boolean; // 新增disabled属性
}

const ToolbarButton: React.FC<ToolbarButtonProps> = ({
  onClick,
  children,
  isActive = false,
  tooltip,
  disabled = false,
}) => (
  <button
    type="button"
    onClick={disabled ? undefined : onClick}
    disabled={disabled}
    className={`p-1.5 rounded-sm mr-1 ${
      disabled
        ? 'opacity-50 cursor-not-allowed text-gray-400'
        : isActive
          ? 'bg-blue-100 text-blue-700'
          : 'hover:bg-gray-100'
    }`}
    title={disabled ? '编辑器处于只读模式' : tooltip}
  >
    {children}
  </button>
);

// Updated ColorPickerButton component with fixed background color handling
const ColorPickerButton: React.FC<{
  editor: Editor;
  type: 'text' | 'background';
  icon: React.ReactNode;
  tooltip: string;
  colors?: string[];
  disabled?: boolean; // 新增disabled属性
}> = ({
  editor,
  type,
  icon,
  tooltip,
  disabled = false,
  colors = [
    '#000000',
    '#FFFFFF',
    '#F44336',
    '#E91E63',
    '#9C27B0',
    '#673AB7',
    '#3F51B5',
    '#2196F3',
    '#03A9F4',
    '#00BCD4',
    '#009688',
    '#4CAF50',
    '#8BC34A',
    '#CDDC39',
    '#FFEB3B',
    '#FFC107',
  ],
}) => {
  const [showColors, setShowColors] = useState(false);
  const pickerRef = useRef<HTMLDivElement | null>(null);
  const buttonRef = useRef<HTMLButtonElement | null>(null);

  // 处理点击外部关闭选择器
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        pickerRef.current &&
        !pickerRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setShowColors(false);
      }
    };

    if (showColors) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showColors]);

  const applyColor = (color: string) => {
    if (type === 'text') {
      // 文本颜色应用 - 直接使用Color扩展
      editor.chain().focus().setColor(color).run();
    } else {
      // 背景色应用 - 修复版本
      const { empty, from, to } = editor.state.selection;

      // 先获取当前选区的所有样式属性（保留其他样式，如文本颜色等）
      const currentAttributes = editor.getAttributes('textStyle');

      // 准备更新的属性，合并现有样式和新背景色
      const newAttributes = {
        ...currentAttributes,
        backgroundColor: color,
        // 添加额外的属性确保背景色被正确应用
        'data-bg-color': color,
      };

      // 直接应用更新后的textStyle，保留其他样式
      editor.chain().focus().setMark('textStyle', newAttributes).run();

      // 为了确保背景色在所有情况下都正确显示，我们可以添加一个额外的CSS类
      // 这需要在EmailEditor.tsx中的CSS部分确保.has-bg-color类被正确样式化
      try {
        const transaction = editor.state.tr;
        const mark = editor.schema.marks.textStyle.create({
          ...newAttributes,
          class: 'has-bg-color',
        });

        // 应用到当前选区
        if (!empty) {
          transaction.addMark(from, to, mark);
          editor.view.dispatch(transaction);
        }
      } catch (e) {
        console.error('应用背景色时出错:', e);
      }
    }
    setShowColors(false);
  };

  const handleClick = (e: React.MouseEvent) => {
    if (disabled) return; // 如果禁用，直接返回
    e.stopPropagation();
    e.preventDefault();
    setShowColors(!showColors);
  };

  return (
    <div className="relative">
      <button
        ref={buttonRef}
        type="button"
        onClick={handleClick}
        disabled={disabled}
        className={`p-1.5 rounded-sm mr-1 ${
          disabled
            ? 'opacity-50 cursor-not-allowed text-gray-400'
            : 'hover:bg-gray-100'
        }`}
        title={disabled ? '编辑器处于只读模式' : tooltip}
      >
        {icon}
      </button>

      {showColors && (
        <div
          ref={pickerRef}
          className="absolute top-full left-0 z-[100] bg-white border border-gray-200 rounded shadow-lg p-2 grid grid-cols-4 gap-1 mt-1 w-[130px]"
          style={{ minWidth: '130px' }}
        >
          {colors.map((color) => (
            <button
              key={color}
              onClick={() => applyColor(color)}
              className="w-6 h-6 rounded-sm border border-gray-300 hover:border-gray-500"
              style={{ backgroundColor: color }}
              title={color}
            />
          ))}
        </div>
      )}
    </div>
  );
};
// 字体大小选择器组件
const FontSizeSelector: React.FC<{
  editor: Editor;
  disabled?: boolean; // 新增disabled属性
}> = ({ editor, disabled = false }) => {
  const [showSizes, setShowSizes] = useState(false);
  const sizesRef = useRef<HTMLDivElement | null>(null);
  const buttonRef = useRef<HTMLButtonElement | null>(null);

  const fontSizes = [
    { label: 'Small', value: '12px' },
    { label: 'Normal', value: '16px' },
    { label: 'Large', value: '20px' },
    { label: 'X-Large', value: '24px' },
    { label: 'Heading', value: '32px' },
  ];

  // 处理点击外部关闭选择器
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        sizesRef.current &&
        !sizesRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setShowSizes(false);
      }
    };

    if (showSizes) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showSizes]);

  // 改进的字体大小设置函数
  const setFontSize = (fontSize: string) => {
    try {
      // 尝试使用FontSize扩展提供的命令
      const customEditor = editor as Editor & {
        commands: {
          setFontSize: (fontSize: string) => boolean;
        };
      };

      if (typeof customEditor.commands.setFontSize === 'function') {
        // 如果命令存在，使用扩展命令
        customEditor.commands.setFontSize(fontSize);
      } else {
        // 否则使用标准的textStyle mark
        editor.chain().focus().setMark('textStyle', { fontSize }).run();
      }
    } catch (err) {
      console.error('设置字体大小失败:', err);
      // 最后的备用方法
      editor.chain().focus().setMark('textStyle', { fontSize }).run();
    }

    setShowSizes(false);

    // 调试辅助
    console.log('应用的字体大小:', fontSize);
    console.log('当前textStyle属性:', editor.getAttributes('textStyle'));
  };

  return (
    <div className="relative">
      <button
        ref={buttonRef}
        type="button"
        onClick={disabled ? undefined : () => setShowSizes(!showSizes)}
        disabled={disabled}
        className={`flex items-center p-1.5 rounded-sm mr-1 ${
          disabled
            ? 'opacity-50 cursor-not-allowed text-gray-400'
            : 'hover:bg-gray-100'
        }`}
        title={disabled ? '编辑器处于只读模式' : 'Font Size'}
      >
        <span className="text-sm">Size</span>
        <ChevronDown size={14} />
      </button>

      {showSizes && (
        <div
          ref={sizesRef}
          className="absolute top-full left-0 z-[100] bg-white border border-gray-200 rounded shadow-lg p-1 mt-1 min-w-[100px]"
        >
          {fontSizes.map((size) => (
            <button
              key={size.value}
              onClick={() => setFontSize(size.value)}
              className="block w-full text-left px-3 py-1.5 hover:bg-gray-100 rounded"
            >
              {size.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

// 分隔线
const Divider: React.FC = () => (
  <div className="mx-1 border-r border-gray-300 h-6" />
);

interface TiptapToolbarProps {
  editor: Editor | null;
  onAddLink?: () => void;
  onAddImage?: () => void;
  editable?: boolean; // 新增editable属性，控制工具栏是否可操作
}

const TiptapToolbar: React.FC<TiptapToolbarProps> = ({
  editor,
  onAddLink,
  onAddImage,
  editable = true, // 默认为可编辑状态
}) => {
  if (!editor) return null;

  const handleAddLink = (): void => {
    if (onAddLink) {
      onAddLink();
    } else {
      const url = window.prompt('URL:');
      if (url) {
        editor.chain().focus().setLink({ href: url }).run();
      } else {
        editor.chain().focus().unsetLink().run();
      }
    }
  };

  const handleAddImage = (): void => {
    if (onAddImage) {
      onAddImage();
    } else {
      const url = window.prompt('Image URL:');
      if (url) {
        editor.chain().focus().setImage({ src: url }).run();
      }
    }
  };

  // 新增：处理标题的切换
  const toggleHeading = (level: 1 | 2) => {
    // 如果已经是指定级别的标题，则切换回普通段落
    if (editor.isActive('heading', { level })) {
      editor.chain().focus().setParagraph().run();
    } else {
      editor.chain().focus().setHeading({ level }).run();
    }
  };

  return (
    <div
      className={`border-b p-1 flex flex-wrap items-center ${
        editable ? 'bg-white' : 'bg-gray-100'
      }`}
    >
      {/* 文本格式化 */}
      <ToolbarButton
        onClick={() => editor.chain().focus().toggleBold().run()}
        isActive={editor.isActive('bold')}
        tooltip="Bold"
        disabled={!editable}
      >
        <Bold size={16} />
      </ToolbarButton>

      <ToolbarButton
        onClick={() => editor.chain().focus().toggleItalic().run()}
        isActive={editor.isActive('italic')}
        tooltip="Italic"
        disabled={!editable}
      >
        <Italic size={16} />
      </ToolbarButton>

      <ToolbarButton
        onClick={() => editor.chain().focus().toggleUnderline().run()}
        isActive={editor.isActive('underline')}
        tooltip="Underline"
        disabled={!editable}
      >
        <UnderlineIcon size={16} />
      </ToolbarButton>

      <Divider />

      {/* 字体大小选择器 */}
      <FontSizeSelector
        editor={editor}
        disabled={!editable}
      />

      <Divider />

      {/* 标题 - 修改为使用toggleHeading函数 */}
      <ToolbarButton
        onClick={() => toggleHeading(1)}
        isActive={editor.isActive('heading', { level: 1 })}
        tooltip="Heading 1"
        disabled={!editable}
      >
        <Heading1 size={16} />
      </ToolbarButton>

      <ToolbarButton
        onClick={() => toggleHeading(2)}
        isActive={editor.isActive('heading', { level: 2 })}
        tooltip="Heading 2"
        disabled={!editable}
      >
        <Heading2 size={16} />
      </ToolbarButton>

      <Divider />

      {/* 列表 */}
      <ToolbarButton
        onClick={() => editor.chain().focus().toggleBulletList().run()}
        isActive={editor.isActive('bulletList')}
        tooltip="Bullet List"
        disabled={!editable}
      >
        <List size={16} />
      </ToolbarButton>

      <ToolbarButton
        onClick={() => editor.chain().focus().toggleOrderedList().run()}
        isActive={editor.isActive('orderedList')}
        tooltip="Ordered List"
        disabled={!editable}
      >
        <ListOrdered size={16} />
      </ToolbarButton>

      <Divider />

      {/* 对齐方式 */}
      <ToolbarButton
        onClick={() => editor.chain().focus().setTextAlign('left').run()}
        isActive={editor.isActive({ textAlign: 'left' })}
        tooltip="Align Left"
        disabled={!editable}
      >
        <AlignLeft size={16} />
      </ToolbarButton>

      <ToolbarButton
        onClick={() => editor.chain().focus().setTextAlign('center').run()}
        isActive={editor.isActive({ textAlign: 'center' })}
        tooltip="Align Center"
        disabled={!editable}
      >
        <AlignCenter size={16} />
      </ToolbarButton>

      <ToolbarButton
        onClick={() => editor.chain().focus().setTextAlign('right').run()}
        isActive={editor.isActive({ textAlign: 'right' })}
        tooltip="Align Right"
        disabled={!editable}
      >
        <AlignRight size={16} />
      </ToolbarButton>

      <Divider />

      {/* 改进的文本颜色和背景颜色选择器 */}
      <ColorPickerButton
        editor={editor}
        type="text"
        icon={<Type size={16} />}
        tooltip="Text Color"
        disabled={!editable}
      />

      <ColorPickerButton
        editor={editor}
        type="background"
        icon={<Highlighter size={16} />}
        tooltip="Background Color"
        disabled={!editable}
      />

      <Divider />

      {/* 插入 */}
      <ToolbarButton
        onClick={handleAddLink}
        isActive={editor.isActive('link')}
        tooltip="Insert Link"
        disabled={!editable}
      >
        <LinkIcon size={16} />
      </ToolbarButton>

      <ToolbarButton
        onClick={handleAddImage}
        tooltip="Insert Image"
        disabled={!editable}
      >
        <ImageIcon size={16} />
      </ToolbarButton>

      <Divider />

      {/* 撤销/重做 */}
      <ToolbarButton
        onClick={() => editor.chain().focus().undo().run()}
        tooltip="Undo"
        disabled={!editable}
      >
        <Undo size={16} />
      </ToolbarButton>

      <ToolbarButton
        onClick={() => editor.chain().focus().redo().run()}
        tooltip="Redo"
        disabled={!editable}
      >
        <Redo size={16} />
      </ToolbarButton>
    </div>
  );
};

export { TiptapToolbar };
