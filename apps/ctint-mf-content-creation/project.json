{"name": "ctint-mf-content-creation", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/ctint-mf-content-creation", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/next:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"root": "apps/ctint-mf-content-creation", "outputPath": "dist/apps/ctint-mf-content-creation"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "ctint-mf-content-creation:build", "dev": true, "port": 4001, "host": "0.0.0.0"}, "configurations": {"development": {"buildTarget": "ctint-mf-content-creation:build:development", "dev": true}, "production": {"buildTarget": "ctint-mf-content-creation:build:production", "dev": false}}}, "export": {"executor": "@nx/next:export", "options": {"buildTarget": "ctint-mf-content-creation:build:production"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["coverage/apps/ctint-mf-content-creation"], "options": {"jestConfig": "apps/ctint-mf-content-creation/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/ctint-mf-content-creation/**/*.{ts,tsx,js,jsx}"]}}}}