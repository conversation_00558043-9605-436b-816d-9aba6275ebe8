/* eslint-disable react/no-unescaped-entities */
'use client';
import React, {
  startTransition,
  useState,
  useEffect,
  useCallback,
} from 'react';
import {
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle,
} from '@cdss-modules/design-system/components/_ui/Resizable';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import AuthChecker from '@cdss-modules/design-system/components/_ui/AuthChecker';
import Panel from '@cdss-modules/design-system/components/_ui/Panel';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import AudioPlayer from '@cdss-modules/design-system/components/_ui/AudioPlayer';
import {
  Loader,
  LoadingBlock,
  useRole,
  useRouteHandler,
  QMProvider,
  useQM,
} from '@cdss-modules/design-system';
import Breadcrumb from '@cdss-modules/design-system/components/_ui/Breadcrumb';
import {
  QueryClient,
  QueryClientProvider,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import dayjs from 'dayjs';
import { GLOBAL_DATETIME_FORMAT } from '@cdss-modules/design-system/lib/constants';
import { AudioLoadOptions, useGlobalAudioPlayer } from 'react-use-audio-player';
import {
  cn,
  secondsToFormat,
  secondsToTimeDisplay,
} from '@cdss-modules/design-system/lib/utils';
import { apiConfig } from '../../../lib/api/config';
import {
  fireGetRecordingMedia,
  fireGetRecordingTranscript,
  fireGetSingleRecording,
} from '../../../lib/api';

import {
  Tabs,
  TabsContent,
} from '@cdss-modules/design-system/components/_ui/Tabs';
import QMPanel from '../../_ui/QMPanel';
import AssignEvaluationPopup from '../../_ui/AssignEvaluationPopup';
import ReAssignEvaluationPopup from '../../_ui/ReAssignEvaluationPopup';
import QMSOPStages from '../../_ui/QMSOPStages';
import {
  DUMMUY_QA_DATA,
  DUMMY_CS_EVALUATION_FORM,
  DUMMY_DEFAULT_ANSWERS,
} from '../../../lib/dummy/qa';
import { getQALiveStat } from '../../../lib/qa/index';
import QMAudioPlayerActiveSection from '../../_ui/QMAudioPlayerActiveSection';
import QMStandardScript from '../../_ui/QMStandardScript';

// TODO: consider move to global helper
const getFilteredTranscripts = (transcripts?: any[]) => {
  if (!Array.isArray(transcripts)) return null;
  return transcripts?.filter((transcript) => {
    return transcript?.speaker !== 'speaker';
  });
};
const getSpeakers = (transcripts?: any[]) => {
  if (!Array.isArray(transcripts)) return null;
  return transcripts?.reduce((acc, curr) => {
    const speaker = curr?.speaker;
    if (!acc.includes(speaker) && speaker !== 'speaker') {
      acc.push(speaker);
    }
    return acc;
  }, []);
};

export const TableDemoDetailBody = () => {
  const { toPath, searchParams, basePath } = useRouteHandler();
  const { t } = useTranslation();
  const { globalConfig } = useRole();
  const queryClient = useQueryClient();
  const [transcriptLoading, setTranscriptLoading] = useState(false);
  const [transcript, setTranscript] = useState();

  // const isUAT = apiConfig.isUAT;
  const id = searchParams?.get('id') || '';
  const tab = searchParams?.get('tab') || 'info';
  const qaId = searchParams?.get('qaId') || '';

  const { data, isLoading, error } = useQuery({
    queryKey: ['recordings', id],
    queryFn: async () =>
      await fireGetSingleRecording(id, basePath).then((res) => res.data),
    enabled: !!id,
  });

  const details = data?.data?.[0];
  const hasTranscript = globalConfig?.services?.['ctint-stt']?.active;

  const callDirection = details?.direction?.toLowerCase() ?? 'inbound';

  useEffect(() => {
    if (details?.transcript) {
      setTranscript(details?.transcript);
    }
  }, [details]);

  const speakers = getSpeakers(transcript);

  const { load, isReady, seek, error: loadMp3Error } = useGlobalAudioPlayer();

  const [pos, setPos] = useState(0);

  const { data: mp3Data, isLoading: isLoadingMp3 } = useQuery({
    queryKey: [details?.mediaUri],
    queryFn: async () => {
      const result = await fireGetRecordingMedia(
        `${apiConfig.paths.recordings}/media?mediaUri=${details?.mediaUri}`,
        basePath
      ).then((res) => res.data);
      return result;
    },
    enabled: !!details?.mediaUri,
  });
  const formatMp3FileUrl = (rawMediaUrl?: string) => {
    if (!rawMediaUrl) return '';
    // replace \u0026 to &
    const result = rawMediaUrl?.replace(/\\u0026/g, '&');
    const cdssAuthToken = (
      localStorage.getItem('cdss-auth-token') || ''
    ).replace('Bearer ', '');
    return `${basePath}/api/bff/ctint-conv/recordings/media?url=${result}&cdssAuthToken=${encodeURIComponent(cdssAuthToken)}`;
  };
  const mp3FileUrl = formatMp3FileUrl(mp3Data?.data?.mediaFile);

  const loadMp3 = useCallback(() => {
    if (!mp3FileUrl) return;
    const loadOptions: AudioLoadOptions = { initialVolume: 0.5 };
    loadOptions.html5 = true;
    loadOptions.format = 'mp3' || 'wav';

    load(mp3FileUrl, loadOptions);
  }, [load, mp3FileUrl]);

  useEffect(() => {
    if (!mp3FileUrl) return;
    startTransition(() => {
      loadMp3();
    });
  }, [mp3FileUrl, load, loadMp3]);

  const getLettersFromUsername = (username: string, length = 2) => {
    let output = '';
    for (let i = 0; i < length; i++) {
      const letter = username?.[i];
      if (letter) {
        output += letter.toUpperCase();
      }
    }
    return output || 'N/A';
  };

  // const lastSpeaker = '';

  // QA / Evaluations
  const [assignEvaluationPopupOpen, setAssignEvaluationPopupOpen] =
    useState(false);
  const [reAssignEvaluationPopupOpen, setReAssignEvaluationPopupOpen] =
    useState(false);
  const openAssignEvaluationPopup = () => {
    setAssignEvaluationPopupOpen(true);
  };
  const openReAssignEvaluationPopup = () => {
    setReAssignEvaluationPopupOpen(true);
  };
  const { highlightedScript } = useQM();

  return (
    <div className="relative flex flex-col h-full">
      <div className="mb-4 flex items-center gap-2">
        <Button
          asSquare
          variant="back"
          onClick={() => toPath(`/`)}
          beforeIcon={<Icon name="back" />}
        />
        <Breadcrumb
          items={[
            {
              label: t('ctint-mf-cpp.filter.search'),
              link: '/',
            },
            {
              label: error || !id ? 'Error' : `${id}`,
              link: '',
            },
          ]}
        />
      </div>
      {error || !id ? (
        <Panel containerClassName="h-full">
          <div className="p-4">
            <h2 className="mb-2 text-t6 font-bold">{`Error loading data: ${
              id ? error?.message : 'No valid ID.'
            }`}</h2>
          </div>
        </Panel>
      ) : (
        <>
          {isLoading ? (
            <Panel
              loading={isLoading}
              containerClassName="h-full"
            />
          ) : (
            <>
              <ResizablePanelGroup
                autoSaveId="playback-detail-panel"
                direction="horizontal"
                className="flex-1 flex w-full h-0 gap-x-3 overflow-auto"
              >
                <ResizablePanel minSize={30}>
                  <Panel
                    loading={isLoading}
                    containerClassName="h-full"
                  >
                    <Tabs
                      defaultTab={tab}
                      triggers={[
                        {
                          value: 'info',
                          label: 'Info',
                        },
                        {
                          value: 'qa',
                          label: 'QM',
                        },
                      ]}
                      triggerClassName="py-1 px-2 text-body"
                    >
                      <TabsContent
                        value={'info'}
                        className="overflow-y-auto p-0"
                      >
                        <div className="p-4 flex flex-col gap-6">
                          <div className="flex gap-2">
                            <Icon
                              name="user"
                              size={24}
                            />
                            <strong>
                              {t('ctint-mf-cpp.columns.username')}:
                            </strong>
                            <p>{details?.username || 'N/A'}</p>
                          </div>

                          <div className="flex gap-2 items-center">
                            <div className="flex gap-2">
                              <Icon
                                name="calendar"
                                size={24}
                              />
                              <strong>
                                {t('ctint-mf-cpp.columns.startTime')}:
                              </strong>
                              <p>
                                {details?.startTime
                                  ? dayjs(details?.startTime).format(
                                      GLOBAL_DATETIME_FORMAT
                                    )
                                  : 'N/A'}{' '}
                              </p>
                            </div>
                          </div>

                          <div className="flex gap-2 items-center">
                            <div className="flex gap-2">
                              <Icon
                                name="calendar"
                                size={24}
                              />
                              <strong>
                                {t('ctint-mf-cpp.columns.endTime')}:
                              </strong>
                              <p>
                                {details?.endTime
                                  ? dayjs(details?.endTime).format(
                                      GLOBAL_DATETIME_FORMAT
                                    )
                                  : 'N/A'}
                              </p>
                            </div>
                          </div>

                          <div className="flex gap-2">
                            <Icon
                              name="file"
                              size={24}
                            />
                            <strong>{t('ctint-mf-cpp.columns.id')}:</strong>
                            <p>{details?.id || 'N/A'}</p>
                          </div>

                          <div className="flex gap-2">
                            <Icon
                              name="phone"
                              size={24}
                            />
                            <strong>
                              {t('ctint-mf-cpp.columns.callerNumber')}:
                            </strong>
                            <p>{details?.callerNumber || 'N/A'}</p>
                          </div>

                          <div className="flex gap-2">
                            <Icon
                              name="phone"
                              size={24}
                            />
                            <strong>
                              {t('ctint-mf-cpp.columns.dialedNumber')}:
                            </strong>
                            <p>{details?.dialedNumber || 'N/A'}</p>
                          </div>

                          <div className="flex gap-2">
                            <Icon
                              name="inbound"
                              size={24}
                            />
                            <strong>
                              {t('ctint-mf-cpp.columns.direction')}:
                            </strong>
                            <p>{callDirection}</p>
                          </div>

                          {details?.mediaSource && (
                            <div className="flex gap-2">
                              <Icon
                                name="file"
                                size={24}
                              />
                              <strong>
                                {t('ctint-mf-cpp.columns.mediaSource')}:
                              </strong>
                              <p>{details?.mediaSource || 'N/A'}</p>
                            </div>
                          )}

                          <div className="flex gap-2">
                            <Icon
                              name="prev-10s"
                              size={24}
                            />
                            <strong>
                              {t('ctint-mf-cpp.columns.duration')}:
                            </strong>
                            <p>
                              {secondsToTimeDisplay(details?.duration, [
                                ' hours',
                                ' minutes',
                                ' seconds',
                              ]) || 'N/A'}
                            </p>
                          </div>
                        </div>
                      </TabsContent>
                      <TabsContent
                        value={'qa'}
                        className="overflow-y-auto p-0 h-full"
                      >
                        <QMPanel
                          defaultOpenedQA={qaId}
                          onAssign={openAssignEvaluationPopup}
                          onReassign={openReAssignEvaluationPopup}
                          data={DUMMUY_QA_DATA.map((item: any) => {
                            const id = item?.id || '';
                            const answers =
                              (DUMMY_DEFAULT_ANSWERS as any)?.[id] || {};
                            let progress = '';
                            const liveStat = getQALiveStat(
                              DUMMY_CS_EVALUATION_FORM,
                              answers
                            );
                            if (
                              liveStat?.totalQuestionsAnswered &&
                              liveStat?.totalQuestions
                            ) {
                              progress = `${liveStat?.totalQuestionsAnswered}/${liveStat?.totalQuestions}`;
                            }

                            return {
                              ...item,
                              score: getQALiveStat(
                                DUMMY_CS_EVALUATION_FORM,
                                answers
                              )?.totalScore,
                              progress,
                              progressPercent:
                                ((liveStat?.totalQuestionsAnswered ?? 0) /
                                  (liveStat?.totalQuestions ?? 1)) *
                                100,
                            };
                          })}
                        />
                      </TabsContent>
                    </Tabs>
                  </Panel>
                </ResizablePanel>
                <ResizableHandle />
                <ResizablePanel
                  minSize={30}
                  className="h-full"
                >
                  <Panel
                    loading={!mp3FileUrl || isLoadingMp3}
                    containerClassName="h-full"
                  >
                    <ResizablePanelGroup
                      direction="vertical"
                      className="flex flex-col w-full"
                    >
                      <ResizablePanel>
                        <div className="h-full flex flex-col p-4">
                          <div className="flex justify-between w-full">
                            <AuthChecker
                              // requiredPemissions={{
                              //   global: {
                              //     portals: ['ctint-mf-cpp'],
                              //   },
                              //   user: {
                              //     permissions: [
                              //       'ctint-mf-cpp.recording.download',
                              //     ],
                              //   },
                              // }}
                              emptyWhenUnauthorized
                            >
                              {loadMp3Error && (
                                <a
                                  href={mp3FileUrl}
                                  download={`${id}.mp3`}
                                >
                                  <Button
                                    size="s"
                                    variant="secondary"
                                  >
                                    {t('ctint-mf-cpp.filter.download')}:
                                  </Button>
                                </a>
                              )}
                            </AuthChecker>
                          </div>
                          {loadMp3Error && (
                            <div className="w-full">
                              <p className="mb-4">
                                {`${t('ctint-mf-cpp.filter.recordingError')}(${loadMp3Error})`}
                              </p>
                              <Button onClick={() => loadMp3()}>
                                {t('ctint-mf-cpp.filter.reload')}
                              </Button>
                            </div>
                          )}
                          {isReady || !loadMp3Error ? (
                            <>
                              <div className="w-full pt-4">
                                <div className="relative w-full">
                                  <AudioPlayer
                                    updatePos={(p) => setPos(p)}
                                    seekBarCustomComponent={
                                      <QMAudioPlayerActiveSection />
                                    }
                                    label={{
                                      jumpToTime: t(
                                        'ctint-mf-cpp.audio.jumpToTime'
                                      ),
                                      go: t('ctint-mf-cpp.audio.go'),
                                      speed: t('ctint-mf-cpp.audio.speed'),
                                      invalidTime: t(
                                        'ctint-mf-cpp.audio.invalidTime'
                                      ),
                                    }}
                                  />
                                </div>
                              </div>
                              <QMSOPStages />
                              <div
                                className={cn(
                                  'flex-1 h-0 flex flex-col w-full mt-4',
                                  !hasTranscript && 'hidden'
                                )}
                              >
                                {transcriptLoading ? (
                                  <div className="text-center">
                                    <div className="w-full h-full flex flex-col items-center justify-center py-12 border-t border-grey-200">
                                      <Loader size={64} />
                                      <p className="text-body mt-4">
                                        {t(
                                          'ctint-mf-cpp.transcript.processing'
                                        )}
                                      </p>
                                    </div>
                                  </div>
                                ) : transcript ? (
                                  <div className="flex-1 h-0 flex flex-col border-t border-grey-200">
                                    <div className="w-full flex-1 h-0 flex flex-col mt-4">
                                      {speakers && (
                                        <div className="flex-col gap-2 hidden">
                                          {speakers?.map((speaker: any) => {
                                            return (
                                              <div
                                                key={`speaker${speaker}`}
                                                className="flex flex-col gap-2"
                                              >
                                                <strong>{speaker}</strong>
                                              </div>
                                            );
                                          })}
                                        </div>
                                      )}
                                      <div className="w-full flex-1 h-0">
                                        <div className="relative h-full overflow-auto">
                                          <div
                                            className={cn(
                                              'flex flex-col gap-4'
                                            )}
                                          >
                                            {getFilteredTranscripts(
                                              transcript
                                            )?.map((item, i) => {
                                              const key = `${item?.start}-${item?.end}-${item?.speaker}-${item?.text}`;
                                              const start = parseFloat(
                                                item?.start
                                              );
                                              const end = parseFloat(item?.end);
                                              const isPlaying =
                                                pos > start && pos < end;
                                              const filteredTranscript =
                                                getFilteredTranscripts(
                                                  transcript
                                                );

                                              const isFirstMessage = i === 0;
                                              const theSameSpeaker =
                                                item.speaker ===
                                                (filteredTranscript !== null &&
                                                  filteredTranscript[i - 1]
                                                    ?.speaker);
                                              const notTheSameSpeaker =
                                                item.speaker !==
                                                (filteredTranscript !== null &&
                                                  filteredTranscript[i - 1]
                                                    ?.speaker);
                                              const isSameAsFirstSpeaker =
                                                item.speaker ===
                                                (filteredTranscript !== null &&
                                                  filteredTranscript[0]
                                                    ?.speaker);

                                              const displayRight =
                                                item?.speaker === 'Agent';

                                              return (
                                                <div
                                                  key={key}
                                                  className={cn(
                                                    'flex gap-x-2',
                                                    i !== 0 &&
                                                      theSameSpeaker &&
                                                      '-mt-3',
                                                    displayRight &&
                                                      'pr-2 flex-row-reverse'
                                                  )}
                                                >
                                                  {details?.username &&
                                                  notTheSameSpeaker ? (
                                                    <div
                                                      className={cn(
                                                        'w-8 h-8 rounded-full flex justify-center items-center font-bold flex-none',
                                                        displayRight &&
                                                          'flex-row-reverse',
                                                        isFirstMessage ||
                                                          item.speaker ===
                                                            (filteredTranscript !==
                                                              null &&
                                                              filteredTranscript[0]
                                                                ?.speaker)
                                                          ? 'bg-primary-200'
                                                          : 'bg-tertiary-100'
                                                      )}
                                                    >
                                                      {getLettersFromUsername(
                                                        item?.speaker
                                                      )}
                                                    </div>
                                                  ) : (
                                                    <div className="w-8 h-8 flex-none" />
                                                  )}

                                                  <div
                                                    className={cn(
                                                      'flex items-end gap-x-3',
                                                      displayRight &&
                                                        'flex-row-reverse'
                                                    )}
                                                  >
                                                    <div className="flex flex-col">
                                                      {notTheSameSpeaker && (
                                                        <div
                                                          className={cn(
                                                            ' text-footnote text-grey-500',
                                                            displayRight
                                                              ? 'text-right'
                                                              : 'text-left'
                                                          )}
                                                        >
                                                          {item?.speaker}
                                                        </div>
                                                      )}
                                                      <div
                                                        className={cn(
                                                          'transcript px-4 py-2 rounded-lg',
                                                          isSameAsFirstSpeaker
                                                            ? 'bg-primary-100'
                                                            : 'bg-common-bg',
                                                          isPlaying &&
                                                            'bg-primary-300',
                                                          displayRight
                                                            ? 'ml-10'
                                                            : 'mr-10',
                                                          highlightedScript &&
                                                            'active'
                                                        )}
                                                      >
                                                        <span
                                                          dangerouslySetInnerHTML={{
                                                            __html: item?.text,
                                                          }}
                                                        />
                                                        {highlightedScript &&
                                                          item?.text?.indexOf(
                                                            '<mark>'
                                                          ) > -1 && (
                                                            <sup className="bg-primary-900 px-1 py-[2px] rounded-md text-white ml-1">
                                                              {
                                                                highlightedScript
                                                              }
                                                            </sup>
                                                          )}
                                                      </div>
                                                    </div>
                                                    <button
                                                      onClick={() =>
                                                        seek(start)
                                                      }
                                                      className="block text-left text-footnote text-status-info underline group hidden"
                                                    >
                                                      <span className="group-hover:text-primary-500 group-hover:underline">
                                                        {secondsToFormat(start)}{' '}
                                                        - {secondsToFormat(end)}
                                                      </span>
                                                    </button>
                                                  </div>
                                                </div>
                                              );
                                            })}
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                ) : (
                                  <div className="flex flex-col items-center pt-4 border-t border-grey-200">
                                    <Button
                                      className="mx-auto"
                                      variant="blank"
                                      beforeIcon={<Icon name="transcript" />}
                                      onClick={() => {
                                        setTranscriptLoading(true);
                                        fireGetRecordingTranscript(
                                          details?.mediaUri,
                                          basePath
                                        )
                                          .then((res) => {
                                            setTranscript(
                                              res?.data?.data?.transcripts
                                            );
                                            queryClient.invalidateQueries({
                                              queryKey: ['recordings', id],
                                            });
                                          })
                                          .catch((err) => {
                                            console.error(err);
                                          })
                                          .finally(() => {
                                            setTranscriptLoading(false);
                                          });
                                      }}
                                    >
                                      <span className="ml-2">
                                        {t('ctint-mf-cpp.transcript.generate')}
                                      </span>
                                    </Button>
                                  </div>
                                )}
                              </div>
                            </>
                          ) : (
                            <>{!loadMp3Error && <LoadingBlock />}</>
                          )}
                        </div>
                      </ResizablePanel>
                      <ResizableHandle />
                      <ResizablePanel defaultSize={30}>
                        <div className="h-full flex items-center gap-4 px-4 overflow-auto border-t border-grey-200">
                          <QMStandardScript />
                        </div>
                      </ResizablePanel>
                    </ResizablePanelGroup>
                  </Panel>
                </ResizablePanel>
              </ResizablePanelGroup>
              <AssignEvaluationPopup
                selectedIntergrations={[details]}
                open={assignEvaluationPopupOpen}
                onOpenChange={(v) => setAssignEvaluationPopupOpen(v)}
              />
              <ReAssignEvaluationPopup
                selectedQA={{
                  id: 'QADBMNLAM52MJ0GUEKVQ8I0ODO000005',
                  form: 'Customer Service Evaluation Form',
                  evaluator: 'Admin',
                  score: null,
                  assignedAt: '2024-07-01T12:00:00Z',
                  status: 'assigned',
                }}
                open={reAssignEvaluationPopupOpen}
                onOpenChange={(v) => setReAssignEvaluationPopupOpen(v)}
              />
            </>
          )}
        </>
      )}
    </div>
  );
};
// Create a client
const queryClient = new QueryClient();

export const TableDemoDetail = () => (
  <QueryClientProvider client={queryClient}>
    <QMProvider defaultFormAns={DUMMY_DEFAULT_ANSWERS}>
      <TableDemoDetailBody />
    </QMProvider>
  </QueryClientProvider>
);

export default TableDemoDetail;
