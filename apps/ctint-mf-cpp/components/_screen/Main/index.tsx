'use client';
import React, { useEffect, useMemo } from 'react';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import _ from 'lodash';
import dayjs from 'dayjs';
import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';
import {
  QueryClient,
  QueryClientProvider,
  useQuery,
} from '@tanstack/react-query';
import { Table as TableType } from '@tanstack/react-table';
import SortingButton from '@cdss-modules/design-system/components/_ui/SortingButton';
import { useState } from 'react';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import { RCustomer as Customer } from '../../../lib/graphql/schema';
import { ColumnDef } from '@tanstack/react-table';
import {
  camelCaseToWords,
  downloadFileFromUrl,
  secondsToTimeDisplay,
} from '@cdss-modules/design-system/lib/utils';
import Checkbox from '@cdss-modules/design-system/components/_ui/Checkbox';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';

import Panel from '@cdss-modules/design-system/components/_ui/Panel';
import {
  mfLogger,
  useRole,
  useRouteHandler,
} from '@cdss-modules/design-system';
import { toast } from '@cdss-modules/design-system/components/_ui/Toast/use-toast';
import Pagination from '@cdss-modules/design-system/components/_ui/Pagination';
import {
  BaseSheetClose,
  Sheet,
  SideType,
} from '@cdss-modules/design-system/components/_ui/Sheet';
import Draggable, {
  TDragableItemsProps,
} from '@cdss-modules/design-system/components/_ui/Draggable';
import { GLOBAL_DATETIME_FORMAT } from '@cdss-modules/design-system/lib/constants';
import {
  fireCreateUserConfig,
  fireExportRecordings,
  fireGetFilteredRecordings,
  fireGetUserConfig,
  fireUpdateUserConfig,
  fireGetSortedRecordings,
} from '../../../lib/api';
import EvaluationAction from '../../_ui/EvaluationAction';

import AssignEvaluationPopup from '../../_ui/AssignEvaluationPopup';
import TableSelectedMenu from '../../_ui/TableSelectedMenu';
import { WhitePanel } from '@cdss-modules/design-system/components/_ui/WhitePanel';
import SearchInput from '@cdss-modules/design-system/components/_ui/SearchInput';
import PopoverMenu from '@cdss-modules/design-system/components/_ui/PopoverMenu';
import { cn } from '@cdss-modules/design-system/lib/utils';
import FilterInput from '../../_ui/FilterInput';

// Types
type TCustomerColumn = keyof Customer;

type TFilterFields = TCustomerColumn | 'isInbound' | 'isOutbound';

export type TFilterItem = {
  name: TFilterFields;
  label: string;
  type: string;
};

export type TSortOrder = 'ASC' | 'DESC';

type TOrderByInput = {
  [K in TCustomerColumn]?: TSortOrder;
};
type TColumnLabel = {
  [K in TCustomerColumn]?: string;
};

type TColumnOrderItem = {
  name: TCustomerColumn;
} & TDragableItemsProps;

type TSheetProps = {
  id: string;
  open: boolean;
  side: SideType;
};

// TODOS: from config?
const AVAILABLE_COLUMNS = [
  'id',
  'conversationId',
  'startTime',
  'endTime',
  'duration',
  'direction',
  'mediaType',
  'username',
  'dialedNumber',
  'mediaUri',
  'callerNumber',
  'mediaSource',
  'evaluation',
];
const DEFAULT_COLUMNS = [
  'id',
  'startTime',
  'endTime',
  'duration',
  'direction',
  'username',
  'dialedNumber',
  'callerNumber',
  'mediaSource',
  'evaluation',
];
const COLUMN_LABEL: TColumnLabel = {
  id: 'id',
  conversationId: 'conversationId',
  startTime: 'startTime',
  endTime: 'endTime',
  duration: 'duration',
  direction: 'direction',
  users: 'users',
  mediaType: 'mediaType',
  username: 'username',
  dialedNumber: 'dialedNumber',
  mediaUri: 'mediaUri',
  callerNumber: 'callerNumber',
  mediaSource: 'mediaSource',
  evaluation: 'evaluation',
};

const AVAILABLE_FILTERS: TFilterItem[] = [
  {
    name: 'id',
    label: COLUMN_LABEL?.['id'] ?? '--',
    type: 'text',
  },
  {
    name: 'startTime',
    label: COLUMN_LABEL?.['startTime'] ?? '--',
    type: 'datetime',
  },
  {
    name: 'endTime',
    label: COLUMN_LABEL?.['endTime'] ?? '--',
    type: 'datetime',
  },
  {
    name: 'callerNumber',
    label: COLUMN_LABEL?.['callerNumber'] ?? '--',
    type: 'text',
  },
  {
    name: 'dialedNumber',
    label: COLUMN_LABEL?.['dialedNumber'] ?? '--',
    type: 'text',
  },
  {
    name: 'username',
    label: COLUMN_LABEL?.['username'] ?? '--',
    type: 'text',
  },
  {
    name: 'duration',
    label: COLUMN_LABEL?.['duration'] ?? '--',
    type: 'duration',
  },
  {
    name: 'mediaSource',
    label: COLUMN_LABEL?.['mediaSource'] ?? '--',
    type: 'mediaSource',
  },
  {
    name: 'evaluation',
    label: COLUMN_LABEL?.['evaluation'] ?? '--',
    type: 'evaluation',
  },
  {
    name: 'isInbound',
    label: 'inbound',
    type: 'boolean',
  },
  {
    name: 'isOutbound',
    label: 'outbound',
    type: 'boolean',
  },
];

const generateColumns = (
  columns: TCustomerColumn[],
  columnOrdering: TColumnOrderItem[],
  sortOrder: TOrderByInput | undefined,
  setSortOrder: (input: TOrderByInput) => void,
  basePath = '',
  t: any,
  actions: any
) => {
  const selectionCol = {
    id: 'select',
    header: ({ table }: any) => (
      <div
        className="bg-white z-30"
        onClick={(e) => e.stopPropagation()}
      >
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onChange={(e: any) => {
            const isSelected = e?.target.checked;
            table.toggleAllPageRowsSelected(isSelected);
          }}
        />
      </div>
    ),
    cell: ({ row }: any) => (
      <div
        className="z-0"
        onClick={(e) => e.stopPropagation()}
      >
        <Checkbox
          checked={row.getIsSelected()}
          onChange={(e: any) => {
            const isSelected = e?.target.checked;
            row.toggleSelected(isSelected);
          }}
        />
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  };
  const actionCol = {
    id: 'action',
    header: () => <div className="w-full"></div>,
    cell: () => (
      <div
        className="flex gap-x-2 z-0"
        // onClick={(e) => e.stopPropagation()}
      >
        <Button
          size="s"
          // onClick={(e: any) => {
          //   e.stopPropagation();
          //   actions?.assignEvaluation();
          // }}
        >
          View
        </Button>
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  };

  const orderedColumns = columnOrdering
    ? columns.sort((firstEl, secondEl) => {
        const firstIndex = columnOrdering?.findIndex(
          (item) => item.name === firstEl
        );
        const secondIndex = columnOrdering?.findIndex(
          (item) => item.name === secondEl
        );
        return firstIndex - secondIndex;
      })
    : columns;

  const formattedColumns = orderedColumns.map((column) => {
    const isDate = column === 'startTime' || column === 'endTime';
    const isDuration = column === 'duration';

    return {
      id: column,
      accessorKey: column,
      header: () => {
        return (
          <SortingButton
            sorting={
              sortOrder?.[column]
                ? sortOrder?.[column] === 'ASC'
                  ? 'asc'
                  : 'desc'
                : false
            }
            onClick={async () => {
              const targetSortOrder =
                sortOrder?.[column] === 'ASC' ? 'DESC' : 'ASC';
              setSortOrder({
                [column]: targetSortOrder,
              });
              await fireGetSortedRecordings(
                `startTime=1990-03-01T12%3A00%3A00Z&orderby=${column}&order=${targetSortOrder.toLowerCase()}`,
                basePath
              );
            }}
          >
            {COLUMN_LABEL?.[column]
              ? t(`ctint-mf-cpp.columns.${COLUMN_LABEL?.[column]}`)
              : camelCaseToWords(column)}
          </SortingButton>
        );
      },
      cell: ({ row }) => {
        const thisId = row.getValue('id') as string;
        let val = row.getValue(column) as any;
        if (isDate) val = dayjs(val).format(GLOBAL_DATETIME_FORMAT);
        if (isDuration) val = secondsToTimeDisplay(parseFloat(val));

        if (column === 'evaluation') {
          return (
            <div className="flex gap-2 items-center z-0">
              <EvaluationAction
                evaluation={{ ...val, interactionId: thisId }}
                openAssignEvaluationPopup={() =>
                  actions?.assignEvaluation(thisId)
                }
              />
            </div>
          );
        }

        return <div className="capitalize">{val}</div>;
      },
    } as ColumnDef<Customer>;
  });

  return [
    selectionCol,
    ...formattedColumns,
    //actionCol
  ];
};

export const TableDemoBody = () => {
  const { userConfig } = useRole();
  const { t } = useTranslation();
  const userPermissions = userConfig?.permissions;
  const permission = userPermissions?.join(',');
  const [table, setTable] = useState<TableType<Customer>>();
  const [rowSelection, setRowSelection] = useState({});
  const [downloadLoading, setDownloadLoading] = useState(false);
  const allAvailableColumns = (AVAILABLE_COLUMNS ?? []) as TCustomerColumn[];
  const defaultColumns = DEFAULT_COLUMNS as TCustomerColumn[];

  const { toPath, basePath } = useRouteHandler();
  const [sortOrder, setSortOrder] = useState<TOrderByInput>();
  const [tempShownColumns, setTempShownColumns] = useState<TCustomerColumn[]>(
    []
  );

  const [shownColumns, setShownColumns] = useState<TCustomerColumn[]>([]);
  const [filterFields, setFilterFields] = useState<TFilterFields[]>([]);
  const [filterInput, setFilterInput] = useState<any>({});

  const [tempColumnOrdering, setTempColumnOrdering] = useState<
    TColumnOrderItem[]
  >([]);

  const [columnOrdering, setColumnOrdering] =
    useState<TColumnOrderItem[]>(tempColumnOrdering);

  const [panelOneDropdownOpen, setPanelOneDropdownOpen] = useState(false);
  const [panelTwoDropdownOpen, setPanelTwoDropdownOpen] = useState(false);
  const [isSheetOpen, setIsSheetOpen] = useState<TSheetProps>({
    id: 'right-side',
    open: false,
    side: 'right',
  });

  const userconfig = useQuery({
    queryKey: ['userconfig'],
    queryFn: async () => fireGetUserConfig(basePath).then((res) => res.data),
  });

  const userPreference = userconfig?.data;
  const [operatorValue, setOperatorValue] = useState('');

  // Global Config
  const { globalConfig } = useRole();
  const isEngage =
    globalConfig?.services['ctint-conv']?.provider[0] === 'pureengage';

  const handleOperatorValue = (e: any) => {
    setOperatorValue(e);
  };

  const handlePanelOneDropdownMenu = () => {
    setPanelOneDropdownOpen(!panelOneDropdownOpen);
  };

  const handlePanelTwoDropdownMenu = () => {
    setPanelTwoDropdownOpen(!panelTwoDropdownOpen);
  };

  const handleSheetOpen = (
    id = 'right-side',
    open: boolean,
    side: SideType = 'right'
  ) => {
    setIsSheetOpen({ id: id, open: open, side: side });
  };

  const handleOrder = (allColumns: any) => {
    const newShowColumn = allColumns
      .filter((col: TColumnOrderItem) => col.show)
      .map((col: TColumnOrderItem) => col.name);

    setTempColumnOrdering(allColumns);
    setTempShownColumns(newShowColumn);
  };

  const applyOrdering = () => {
    setColumnOrdering(tempColumnOrdering);
    setShownColumns(tempShownColumns);
  };

  const [filterPanelOpen, setFilterPanelOpen] = useState<boolean>(false);
  const handleFilterPanel = (open: boolean) => setFilterPanelOpen(open);

  const [appliedFilter, setAppliedFilter] = useState<any>({
    filterFields: [],
    filterInput: {},
    searchByEmail: '',
  });

  const appliedFilterCount = appliedFilter?.filterFields?.filter(
    (filterField: any) => !!filterInput?.[filterField]
  ).length;

  const applyFilter = () => {
    setAppliedFilter({
      filterFields,
      filterInput,
    });
    handleFilterPanel(false);
    mfLogger.info({ detail: filterInput });
  };

  const clearFilter = () => {
    setFilterInput({});
    setFilterFields([]);
    setAppliedFilter({
      filterFields: [],
      filterInput: {},
    });
    setSortOrder(undefined);
  };

  const allAvailableColumnsOrdering = allAvailableColumns.map((col: any) => {
    return {
      id: `column-${col}`,
      name: col,
      label: col,
      show: defaultColumns.includes(col),
    };
  });

  const savePreference = (
    target: 'filters' | 'columns',
    clearMode?: boolean
  ) => {
    let config = {};
    let jsonParsedUserConfig;

    const filters = filterFields.map((item) => {
      return {
        name: item,
        value: filterInput[item],
      };
    });

    const columns = columnOrdering.map((item) => {
      return {
        name: item.name,
        value: item.show,
      };
    });

    if (userPreference?.data?.userConfig) {
      jsonParsedUserConfig = JSON.parse(
        userPreference?.data?.userConfig || '{}'
      ); // { filters: []} || { columns: []}

      config =
        target === 'filters'
          ? {
              filters: clearMode ? [] : filters,
              columns: columns,
            }
          : {
              filters: [...(jsonParsedUserConfig.filters ?? []), ...filters],
              columns: clearMode ? [] : columns,
            };
    }

    if (userPreference?.config?.length === 0) {
      fireCreateUserConfig(config, basePath).then(() => {
        toast({
          title: 'Success',
          description: 'Saved preference',
        });
      });
    } else {
      fireUpdateUserConfig(config, basePath).then(() => {
        toast({
          title: 'Success',
          description: 'Saved preference',
        });
      });
    }
  };

  const loadPreference = () => {
    const targetDefaultColumns = defaultColumns ?? [];

    const savedPreference = userPreference?.data;

    if (!savedPreference?.username || !savedPreference?.config) {
      setShownColumns(targetDefaultColumns);
      setColumnOrdering(allAvailableColumnsOrdering);
      setTempColumnOrdering(allAvailableColumnsOrdering);
    }

    if (savedPreference) {
      const jsonParsedUserConfig = JSON.parse(
        userPreference?.data?.userConfig || '{}'
      );

      const filters =
        jsonParsedUserConfig?.filters?.map((item: any) => item.name) ?? [];
      const filterInput = jsonParsedUserConfig?.filters?.reduce(
        (acc: any, cur: any) => {
          const { name, value } = cur;

          acc[name] = value;

          return acc;
        },
        {}
      );

      const columns =
        jsonParsedUserConfig?.columns
          ?.filter((col: any) => col.value)
          ?.map((item: any) => item.name) ?? [];

      const columnOrderingFromApi = columnOrdering.map((col) => ({
        ...col,
        show: columns?.includes(col.name),
      }));

      setFilterFields(filters ?? []);
      setFilterInput(filterInput ?? {});
      setShownColumns(columns.length > 0 ? columns : targetDefaultColumns);
      setTempColumnOrdering(
        columns.length > 0 ? columnOrderingFromApi : allAvailableColumnsOrdering
      );

      setAppliedFilter({
        filterInput: filterInput ?? {},
        filterFields: filters ?? [],
      });
    }
  };

  const clearPreference = (target: 'filters' | 'columns') => {
    savePreference(target, true);
    if (target === 'filters') {
      clearFilter();
    } else {
      setShownColumns(defaultColumns);
      setTempColumnOrdering(allAvailableColumnsOrdering);
      setColumnOrdering(allAvailableColumnsOrdering);
    }
    toast({
      title: 'Success',
      description:
        target === 'filters'
          ? 'Cleared saved filters'
          : 'Cleared saved columns',
    });
  };

  useEffect(() => {
    loadPreference();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [permission, defaultColumns, userPreference]);
  // const hasNextPage = data?.customers?.hasNextPage;
  // const hasPreviousPage = cursorStack.length > 0;

  const [currentPage, setCurrentPage] = useState<number>(1);
  const [perPage, setPerPage] = useState<number>(5);

  const limit = perPage;
  const offset = (currentPage - 1) * perPage;

  const queryParams = useMemo(() => {
    const appliedInput = appliedFilter?.filterInput;
    const appliedFields = appliedFilter?.filterFields;
    const defaultResult = new URLSearchParams({
      startTime: '1990-03-01T12:00:00Z',
      limit: `${limit}`,
      offset: `${offset}`,
    }).toString();
    const key = sortOrder ? Object.keys(sortOrder)[0] : '';
    const value = sortOrder && (sortOrder as any)?.[key]?.toLowerCase();
    if (appliedFields?.length && !_.isEmpty(appliedInput)) {
      const formattedFilter = shownColumns.reduce((acc: any, cur) => {
        const formattedFilterValue = appliedInput?.[cur];

        if (appliedFields.includes(cur) && formattedFilterValue) {
          return {
            ...acc,
            [cur]: formattedFilterValue,
          };
        } else if (appliedFields.includes('duration')) {
          return {
            ...acc,
            ...appliedInput,
            durationOperator: operatorValue,
          };
        } else if (
          appliedFields?.length &&
          !_.isEmpty(appliedInput) &&
          sortOrder
        ) {
          return {
            ...acc,
            ...appliedInput,
            orderby: key,
            order: value,
          };
        } else {
          return {
            ...acc,
            ...appliedInput,
          };
        }
      }, {});

      const result = new URLSearchParams({
        startTime: '2023-03-01T12:00:00Z',
        limit: `${limit}`,
        offset: `${offset}`,
        ...formattedFilter,
      }).toString();

      return result;
    }
    if (sortOrder) {
      const sortedResult = defaultResult + `&orderby=${key}&order=${value}`;

      return sortedResult;
    }
    return defaultResult;
  }, [
    appliedFilter?.filterInput,
    appliedFilter?.filterFields,
    shownColumns,
    limit,
    offset,
    operatorValue,
    sortOrder,
  ]);

  const {
    data,
    isLoading: loading,
    error,
  } = useQuery({
    queryKey: ['todos', limit, offset, queryParams],
    queryFn: async () =>
      fireGetFilteredRecordings(queryParams, basePath).then((res) => res.data),
  });

  const customerData = (data?.data?.recordings ?? data?.data)?.filter(
    (item: any) => {
      // TODO: this hardcoded filter for demo evaluation only, should be remove later
      const isChecked = filterFields.includes('evaluation'); //default is false
      const filterValue = filterInput?.['evaluation'];
      if (isChecked && filterValue) {
        if (filterValue === 'assignedToYou') {
          return item?.evaluation?.evaluator === 'Admin';
        }
        return item.evaluation.status === filterValue;
      }
      return true;
    }
  );

  // QA / Evaluations
  const [assignEvaluationPopupOpen, setAssignEvaluationPopupOpen] =
    useState(false);
  const [selectedForAssignEvaluation, setSelectedForAssignEvaluation] =
    useState([]);
  const openAssignEvaluationPopup = (targetIds: string[]) => {
    const targetInteractions = customerData.filter((item: any) =>
      targetIds.includes(item.id)
    );
    setSelectedForAssignEvaluation(targetInteractions);
    setAssignEvaluationPopupOpen(true);
  };

  // Pagination
  const totalCount = data?.data?.totalCount ?? data?.totalCount ?? 0;
  const totalPages = Math.ceil(totalCount / perPage);

  const handleNext = () => {
    let tar = currentPage + 1;
    if (tar > totalPages) tar = totalPages;
    setCurrentPage(tar);
  };

  const handlePrevious = () => {
    let tar = currentPage - 1;
    if (tar < 1) tar = 1;
    setCurrentPage(tar);
  };

  const renderTagItems = (items: (string | null)[]) => {
    // only show the Interaction ID item when it had been filtered
    if (items[0]?.includes('Interaction ID')) {
      return (
        <div
          key={0}
          className="p-1 mr-1 mt-1 flex flex-row items-center border border-black rounded-[4px] text-[12px] text-black"
        >
          <span className="truncate">{items[0]}</span>
          <span
            onClick={() => {
              const updateFilterInput = { ...filterInput };
              delete updateFilterInput[filterFields[0]];
              setFilterFields((prev) =>
                prev.filter((pre) => pre !== filterFields[0])
              );
              setFilterInput(updateFilterInput);
            }}
            className="ml-1 cursor-pointer"
          >
            <Icon name="cross" />
          </span>
        </div>
      );
    }

    return items.map((item, index) => {
      if (item) {
        return (
          <div
            key={index}
            className="p-1 mr-1 mt-1 flex flex-row items-center border border-black rounded-[4px] text-[12px] text-black"
          >
            <span className="truncate">{item}</span>
            <span
              onClick={() => {
                const updateFilterInput = { ...filterInput };
                delete updateFilterInput[filterFields[index]];
                setFilterFields((prev) =>
                  prev.filter((pre) => pre !== filterFields[index])
                );
                setFilterInput(updateFilterInput);
              }}
              className="ml-1 cursor-pointer"
            >
              <Icon name="cross" />
            </span>
          </div>
        );
      }
    });
  };

  const conbineFiltersTagName = () => {
    let tags = [];
    tags = filterFields.map((item) => {
      const filterValue = filterInput?.[item];
      if (!filterValue) return null;
      const columnName = (COLUMN_LABEL as any)?.[item];
      const label = columnName
        ? t(`ctint-mf-cpp.columns.${columnName}`)
        : camelCaseToWords(item);

      return `${label}: ${filterValue}`;
    });

    return tags;
  };

  return (
    <div
      data-testid="cypress-panel-title-filter"
      id="panelContainer"
      className="flex flex-col h-full w-full gap-4"
    >
      <WhitePanel className="inline-flex flex-row overflow-x-auto">
        <div className="flex-1 flex flex-row">
          {/* Search Filter Input Component */}
          <SearchInput tags={conbineFiltersTagName()}>
            {/* Filter Operation Popover Content */}
            <section>
              {/* Filter Operation Scroll Block */}
              <section className="max-h-[409px] min-w-[600px] max-w-[800px] overflow-y-auto">
                {/* Popover selected filters items */}
                <section className="p-4">
                  {filterFields.length > 0 && (
                    <div className="flex flex-wrap flex-row">
                      {renderTagItems(conbineFiltersTagName())}
                    </div>
                  )}
                </section>
                {/* Popover filter input form */}
                <section className="px-4">
                  <h2 className="text-[14px] mb-2">
                    {t('ctint-mf-cpp.filter.available')}:
                  </h2>
                  <div className="flex flex-col gap-y-2">
                    {AVAILABLE_FILTERS.map((item) => {
                      const filterName = item?.name;
                      const isChecked = filterFields.includes(filterName); //default is false
                      const filterValue = filterInput?.[filterName];

                      return (
                        <div
                          key={`filter-${filterName}-key`}
                          className={cn(
                            ` items-center gap-x-4 `,
                            filterFields.includes('id') && filterName !== 'id'
                              ? 'hidden'
                              : 'flex'
                          )}
                        >
                          <div className="w-[280px]">
                            <Checkbox
                              id={`filter-${filterName}`}
                              label={t(`ctint-mf-cpp.columns.${item?.label}`)}
                              value={filterName}
                              checked={isChecked}
                              onChange={(e: any) => {
                                const isSelected = e?.target.checked;
                                const value = e?.target.value;

                                if (
                                  filterName === 'isInbound' ||
                                  filterName === 'isOutbound'
                                ) {
                                  isSelected
                                    ? setFilterInput({
                                        ...filterInput,
                                        [value]: isSelected,
                                      })
                                    : delete filterInput[value];
                                }

                                setFilterFields((prev) => {
                                  const newColumns = isSelected
                                    ? [...prev, value]
                                    : prev.filter((pre) => pre !== value);
                                  return _.sortBy(newColumns, (column) =>
                                    allAvailableColumns.indexOf(column)
                                  );
                                });
                              }}
                            />
                          </div>
                          <FilterInput
                            type={item.type}
                            filterName={filterName}
                            filterValue={filterValue}
                            isChecked={isChecked}
                            operatorValue={operatorValue}
                            setFilterInput={setFilterInput}
                            handleOperatorValue={handleOperatorValue}
                          />
                        </div>
                      );
                    })}
                  </div>
                </section>
              </section>
              {/* Filter Items Operation Button */}
              <section className="max-h-[45px] px-4 py-1 flex flex-row-reverse w-full ">
                <Button
                  className="mx-1 z-0"
                  bodyClassName="py-[0.375rem]"
                  variant={'orange'}
                  onClick={() => clearPreference('filters')}
                  size="s"
                >
                  {t('ctint-mf-cpp.filter.clearSave')}
                </Button>
                <Button
                  className="mx-1 z-0"
                  bodyClassName="py-[0.375rem]"
                  variant={'orange'}
                  onClick={() => savePreference('filters')}
                  size="s"
                >
                  {t('ctint-mf-cpp.filter.save')}
                </Button>
              </section>
            </section>
          </SearchInput>
          {/* Search Button */}
          <Button
            className="self-center ml-2"
            bodyClassName="border border-black py-[0.375rem]"
            onClick={() => applyFilter()}
            size="s"
          >
            {t('ctint-mf-cpp.filter.search')}
          </Button>
          {/* Clear Tags Button */}
          <Button
            className="self-center ml-2"
            bodyClassName="border border-black py-[0.375rem]"
            onClick={() => clearFilter()}
            variant="blank"
            size="s"
          >
            {t('ctint-mf-cpp.filter.clear')}
          </Button>
        </div>
        {/* Table Operation Menu */}
        <PopoverMenu
          icon={
            <Icon
              name="verticalDots"
              className="self-center justify-end cursor-pointer mx-1 flex-shrink-0"
              size={23}
            />
          }
        >
          <div className="flex flex-col bg-white shadow-[0px_2px_10px_0px_rgba(0,0,0,0.18)]">
            <button
              className="m-2 flex gap-2 items-center w-full"
              onClick={() =>
                handleSheetOpen('right-side', !isSheetOpen.open, 'right')
              }
            >
              <Icon name="eye" />
              <span>{t('ctint-mf-cpp.filter.addColumns')}</span>
            </button>
            <div className="w-full h-[1px] bg-black"></div>
            <button
              onClick={() => savePreference('columns')}
              className="mx-2 mt-2 flex gap-2 items-center w-full"
            >
              <Icon name="save" />
              <span>{t('ctint-mf-cpp.filter.saveColumns')}</span>
            </button>
            <button
              onClick={() => clearPreference('columns')}
              className="m-2 flex gap-2 items-center w-full"
            >
              <Icon name="cross" />
              <span>{t('ctint-mf-cpp.filter.clearColumns')}</span>
            </button>
          </div>
        </PopoverMenu>
      </WhitePanel>
      {/* <Panel
        collapsible
        collapsibleOpen={filterPanelOpen}
        collapsibleHandle={handleFilterPanel}
        // authCheck={{
        //   requiredPemissions: {
        //     global: {
        //       portals: ['ctint-mf-cpp'],
        //     },
        //     user: {
        //       permissions: ['ctint-mf-cpp.application.visit'],
        //     },
        //   },
        // }}
        header={{
          title: t('ctint-mf-cpp.filter.title'),
          customTitle:
            appliedFilterCount > 0 ? (
              <>
                {` - `}
                <button
                  onClick={() => handleFilterPanel(true)}
                  className="inline-flex text-body font-semibold cursor-pointer"
                >{`${appliedFilterCount} ${t(
                  `ctint-mf-cpp.filter.${appliedFilterCount > 1 ? 'filters' : 'filter'}`
                )}${t('ctint-mf-cpp.filter.applied')}`}</button>
              </>
            ) : (
              <></>
            ),
          menu: (
            <>
              <DropdownMenuGroup>
                <DropdownMenuItem>
                  <button
                    onClick={() => savePreference('filters')}
                    className="flex gap-2 items-center w-full"
                  >
                    <Icon name="save" />
                    <span>{t('ctint-mf-cpp.filter.save')}</span>
                  </button>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <button
                    onClick={() => clearPreference('filters')}
                    className="flex gap-2 items-center w-full"
                  >
                    <Icon name="cross" />
                    <span>{t('ctint-mf-cpp.filter.clearSave')}</span>
                  </button>
                </DropdownMenuItem>
              </DropdownMenuGroup>
            </>
          ),
        }}
        footer={
          <div className="w-full flex gap-x-4 items-center">
            <Button
              onClick={() => applyFilter()}
              size="s"
            >
              {t('ctint-mf-cpp.filter.search')}
            </Button>
            <Button
              onClick={() => clearFilter()}
              variant="blank"
              size="s"
            >
              {t('ctint-mf-cpp.filter.clear')}
            </Button>
          </div>
        }
        containerClassName="flex flex-col"
        dropdownMenuOpen={panelOneDropdownOpen}
        handleDropdownMenuOpen={handlePanelOneDropdownMenu}
      >
        <div className="flex flex-col max-h-full p-4">
          <div className="w-full flex items-center gap-x-4">
            {filterFields.map((p) => {
              const filterValue = filterInput?.[p];
              if (!filterValue) return null;
              const columnName = (COLUMN_LABEL as any)?.[p];
              const label = columnName
                ? t(`ctint-mf-cpp.columns.${columnName}`)
                : camelCaseToWords(p);
              return (
                <button
                  key={`remove-filter-${p}`}
                  onClick={() => {
                    const updateFilterInput = { ...filterInput };
                    delete updateFilterInput[p];
                    setFilterFields((prev) => prev.filter((pre) => pre !== p));
                    setFilterInput(updateFilterInput);
                  }}
                  className="flex items-center gap-x-2 border border-black rounded-[4px] px-2 h-[37px]"
                >
                  <span>{`${label}: ${filterValue}`}</span>
                  <Icon name="cross" />
                </button>
              );
            })}
            <div className="relative z-50 flex items-center gap-x-4">
              <Filters addBtnLabel={t('ctint-mf-cpp.filter.add')}>
                <h2 className="text-t6 mb-2 font-bold">
                  {t('ctint-mf-cpp.filter.available')}:
                </h2>
                <div className="flex flex-col gap-y-2">
                  {AVAILABLE_FILTERS.map((item) => {
                    const filterName = item?.name;
                    const isChecked = filterFields.includes(filterName); //default is false
                    const filterValue = filterInput?.[filterName];

                    return (
                      <div
                        key={`filter-${filterName}-key`}
                        className={cn(
                          ` items-center gap-x-4 `,
                          filterFields.includes('id') && filterName !== 'id'
                            ? 'hidden'
                            : 'flex'
                        )}
                      >
                        <div className="w-[280px]">
                          <Checkbox
                            id={`filter-${filterName}`}
                            label={t(`ctint-mf-cpp.columns.${item?.label}`)}
                            value={filterName}
                            checked={isChecked}
                            onChange={(e: any) => {
                              const isSelected = e?.target.checked;
                              const value = e?.target.value;

                              if (
                                filterName === 'isInbound' ||
                                filterName === 'isOutbound'
                              ) {
                                isSelected
                                  ? setFilterInput({
                                      ...filterInput,
                                      [value]: isSelected,
                                    })
                                  : delete filterInput[value];
                              }

                              setFilterFields((prev) => {
                                const newColumns = isSelected
                                  ? [...prev, value]
                                  : prev.filter((pre) => pre !== value);
                                return _.sortBy(newColumns, (column) =>
                                  allAvailableColumns.indexOf(column)
                                );
                              });
                            }}
                          />
                        </div>
                        <FilterInput
                          type={item.type}
                          filterName={filterName}
                          filterValue={filterValue}
                          isChecked={isChecked}
                          operatorValue={operatorValue}
                          setFilterInput={setFilterInput}
                          handleOperatorValue={handleOperatorValue}
                        />
                      </div>
                    );
                  })}
                </div>
              </Filters>
            </div>
          </div>
        </div>
      </Panel> */}
      <Panel
        // header={{
        //   title: `${t('ctint-mf-cpp.filter.result')} - ${shownColumns?.length} ${t('ctint-mf-cpp.filter.columns')}`,
        //   customTitle: (
        //     <div className="flex items-center gap-4">
        //       <button
        //         className="flex gap-2 items-center w-full"
        //         onClick={() =>
        //           handleSheetOpen('left-side', !isSheetOpen.open, 'left')
        //         }
        //       >
        //         <Icon
        //           name="eye"
        //           size={16}
        //         />
        //       </button>
        //       <TableSelectedMenu
        //         data={customerData}
        //         rowSelection={rowSelection}
        //         show={!_.isEmpty(rowSelection)}
        //         onClear={() => table?.toggleAllRowsSelected(false)}
        //         onClearOne={(rowId) => {
        //           setRowSelection((prevSelection) => {
        //             const updatedSelection: any = { ...prevSelection };
        //             delete updatedSelection[rowId];
        //             return updatedSelection;
        //           });
        //         }}
        //         onDownload={() => {
        //           setDownloadLoading(true);
        //           const mediaUri = Object.keys(rowSelection).map((k) => {
        //             const key = parseInt(k);
        //             return customerData?.[key]?.mediaUri;
        //           });

        //           fireExportRecordings(mediaUri, basePath)
        //             .then((res) => {
        //               const data = res?.data?.data;
        //               // Export recording with today date time
        //               downloadFileFromUrl(
        //                 data?.zipFile,
        //                 `exported-recordings_${dayjs().format(
        //                   'YYYY-MM-DD-hh-mm-ss'
        //                 )}.zip`
        //               );
        //             })
        //             .catch((error) => {
        //               toast({
        //                 title: 'Error downloading media',
        //                 description: `Error: ${error.message}`,
        //               });
        //             })
        //             .finally(() => {
        //               setDownloadLoading(false);
        //             });
        //         }}
        //         onAssign={(data) => openAssignEvaluationPopup(data)}
        //         isDownloading={downloadLoading}
        //       />
        //     </div>
        //   ),
        //   menu: (
        //     <>
        //       <DropdownMenuGroup>
        //         <DropdownMenuItem className="border-black border-b rounded-none">
        //           <button
        //             className="flex gap-2 items-center w-full"
        //             onClick={() =>
        //               handleSheetOpen('right-side', !isSheetOpen.open, 'right')
        //             }
        //           >
        //             <Icon name="eye" />
        //             <span>{t('ctint-mf-cpp.filter.addColumns')}</span>
        //           </button>
        //         </DropdownMenuItem>
        //         <DropdownMenuItem>
        //           <button
        //             onClick={() => savePreference('columns')}
        //             className="flex gap-2 items-center w-full"
        //           >
        //             <Icon name="save" />
        //             <span>{t('ctint-mf-cpp.filter.saveColumns')}</span>
        //           </button>
        //         </DropdownMenuItem>
        //         <DropdownMenuItem>
        //           <button
        //             onClick={() => clearPreference('columns')}
        //             className="flex gap-2 items-center w-full"
        //           >
        //             <Icon name="cross" />
        //             <span>{t('ctint-mf-cpp.filter.clearColumns')}</span>
        //           </button>
        //         </DropdownMenuItem>
        //       </DropdownMenuGroup>
        //     </>
        //   ),
        // }}
        headerClassName="bg-white border-b border-grey-200"
        containerClassName="flex flex-col flex-1 h-0"
        dropdownMenuOpen={panelTwoDropdownOpen}
        handleDropdownMenuOpen={handlePanelTwoDropdownMenu}
      >
        <div className="overflow-auto px-4 flex-1 h-full pt-4">
          <DataTable<Customer>
            data={loading ? [] : customerData ?? []}
            columns={generateColumns(
              shownColumns,
              columnOrdering,
              sortOrder,
              (input) => {
                setSortOrder(input);
              },
              basePath,
              t,
              {
                assignEvaluation: (id: string) =>
                  openAssignEvaluationPopup([id]),
              }
            )}
            loading={loading}
            error={error?.message}
            rowSelection={rowSelection}
            setRowSelection={setRowSelection}
            onClickRow={(row) => {
              toPath(`/detail?id=${row.getValue('id')}`);
            }}
            onTableSetUp={(table) => setTable(table)}
          />
        </div>
        {!loading &&
          customerData &&
          (totalPages > 0 || customerData.length > 0) && (
            <div className="flex items-center justify-between p-4">
              <div className="">
                <TableSelectedMenu
                  data={customerData}
                  popupAlign="start"
                  rowSelection={rowSelection}
                  show={!_.isEmpty(rowSelection)}
                  onClear={() => table?.toggleAllRowsSelected(false)}
                  onClearOne={(rowId) => {
                    setRowSelection((prevSelection) => {
                      const updatedSelection: any = { ...prevSelection };
                      delete updatedSelection[rowId];
                      return updatedSelection;
                    });
                  }}
                  onDownload={() => {
                    setDownloadLoading(true);
                    const mediaUri = Object.keys(rowSelection).map((k) => {
                      const key = parseInt(k);
                      return customerData?.[key]?.mediaUri;
                    });

                    fireExportRecordings(mediaUri, basePath)
                      .then((res) => {
                        const data = res?.data?.data;
                        // Export recording with today date time
                        downloadFileFromUrl(
                          data?.zipFile,
                          `exported-recordings_${dayjs().format(
                            'YYYY-MM-DD-hh-mm-ss'
                          )}.zip`
                        );
                      })
                      .catch((error) => {
                        toast({
                          title: 'Error downloading media',
                          description: `Error: ${error.message}`,
                        });
                      })
                      .finally(() => {
                        setDownloadLoading(false);
                      });
                  }}
                  onAssign={(data) => openAssignEvaluationPopup(data)}
                  isDownloading={downloadLoading}
                />
              </div>
              {totalPages > 0 && (
                <section className="flex-1 flex-row">
                  <div>
                    <Pagination
                      current={currentPage}
                      perPage={perPage}
                      total={totalPages}
                      totalCount={totalCount}
                      onChange={(v) => setCurrentPage(v)}
                      handleOnPrevious={() => handlePrevious()}
                      handleOnNext={() => handleNext()}
                      handlePerPageSetter={(p: number) => {
                        setPerPage(p);
                        setCurrentPage(1);
                      }}
                    />
                  </div>
                  {/* <div className="">
                <PerPageSetter
                  perPage={perPage}
                  currentPage={currentPage}
                  totalCount={totalCount}
                  handleChange={(p: number) => {
                    setPerPage(p);
                    setCurrentPage(1);
                  }}
                />
              </div> */}
                </section>
              )}
            </div>
          )}
        <Sheet
          open={isSheetOpen.open}
          onOpenChange={() =>
            handleSheetOpen(isSheetOpen.id, !isSheetOpen.open, isSheetOpen.side)
          }
          headerTitle={t('ctint-mf-cpp.filter.addColumns')}
          side={isSheetOpen.side}
          content={
            <>
              <Draggable
                items={tempColumnOrdering?.map((item) => ({
                  ...item,
                  label: t(
                    `ctint-mf-cpp.columns.${COLUMN_LABEL?.[item.name] ?? item.label}`
                  ),
                }))}
                onChange={handleOrder}
                toogleSwitch
              />
            </>
          }
          footer={
            <>
              <div />
              <BaseSheetClose asChild>
                <Button onClick={applyOrdering}>
                  {t('ctint-mf-cpp.filter.apply')}
                </Button>
              </BaseSheetClose>
            </>
          }
        />
      </Panel>
      <AssignEvaluationPopup
        selectedIntergrations={selectedForAssignEvaluation}
        open={assignEvaluationPopupOpen}
        onOpenChange={(v) => setAssignEvaluationPopupOpen(v)}
      />
    </div>
  );
};

// Create a client
const queryClient = new QueryClient();

export const TableDemo = () => (
  <QueryClientProvider client={queryClient}>
    <TableDemoBody />
  </QueryClientProvider>
);

export default TableDemo;
