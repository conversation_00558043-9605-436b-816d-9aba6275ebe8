'use client';

import { Button } from '@cdss-modules/design-system';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { useRouter } from 'next/router';
import Draggable from '@cdss-modules/design-system/components/_ui/Draggable';
import { useState } from 'react';

const templates = [
  {
    id: 'view1',
    label: 'View 1',
  },
  {
    id: 'view2',
    label: 'View 2',
  },
  {
    id: 'view3',
    label: 'View 3',
  },
  {
    id: 'view4',
    label: 'View 4',
  },
  {
    id: 'view5',
    label: 'View 5',
  },
];

const ManageTemplate = () => {
  const [templateOrder, setTemplateOrder] = useState(templates);
  const { push, pathname } = useRouter();
  const convertedPathname = pathname
    .replace(/-/g, ' ') // Replace hyphens with spaces
    .replace(/\b\w/g, (match) => match.toUpperCase())
    .split('/'); // Capitalize the first letter of each word

  return (
    <div className="w-full h-full flex flex-col gap-4">
      <div className="flex w-full justify-between items-center">
        <div className="flex items-center gap-2">
          <button
            onClick={() => push('/')}
            className="bg-white p-2 rounded-[5px] hover:bg-grey-200"
          >
            <Icon
              name="back"
              size={20}
            />
          </button>
          <p>/ {convertedPathname[1]}</p>
        </div>
        <Button
          variant="blank"
          beforeIcon={
            <Icon
              name="plus"
              size={20}
            />
          }
        >
          <span>Create Template</span>
        </Button>
      </div>
      <div className="flex gap-6 h-full">
        <div className="bg-white rounded-2xl w-1/3">
          <Draggable
            items={templateOrder}
            onChange={setTemplateOrder}
          />
        </div>
        <div className="bg-white rounded-2xl w-2/3 flex flex-col">
          {templateOrder.map((t) => (
            <p key={t.id}>{t.label}</p>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ManageTemplate;
