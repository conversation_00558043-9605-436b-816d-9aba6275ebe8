import React from 'react';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import dayjs from 'dayjs';

import Input from '@cdss-modules/design-system/components/_ui/Input';

import DateTimePicker from '@cdss-modules/design-system/components/_ui/DateTimePicker';

import { useRole } from '@cdss-modules/design-system';

import { Select } from '@cdss-modules/design-system/components/_ui/Select';

const operators = [
  {
    id: 'greater than',
    label: 'greater',
    value: 'gt',
  },
  {
    id: 'less than',
    label: 'less',
    value: 'lt',
  },
];

type TFilterInputProps = {
  type: string;
  filterName: any;
  filterValue: any;
  isChecked: boolean;
  operatorValue: any;
  handleOperatorValue: (v: any) => void;
  setFilterInput: (prev: any) => void;
};

export const FilterInput = ({
  type,
  filterName,
  filterValue,
  isChecked,
  operatorValue,
  handleOperatorValue,
  setFilterInput,
}: TFilterInputProps) => {
  const { t } = useTranslation();

  // Global Config
  const { globalConfig } = useRole();
  const mediaSourcePossibleOptions = globalConfig?.recording?.mediaSource ?? [];
  const mediaSourceOptions = [
    {
      key: 'mediaSourceOption-all',
      id: 'mediaSourceOption-all',
      label: t('ctint-mf-cpp.filter.all'),
      value: '_all',
    },
    ...(mediaSourcePossibleOptions?.map((item: string) => {
      const mediaSourceItemKey = `mediaSourceOption-${item}`;
      return {
        key: mediaSourceItemKey,
        id: mediaSourceItemKey,
        label: item,
        value: item,
      };
    }) || []),
  ];

  if (type === 'mediaSource') {
    return (
      <div className="w-full">
        <Select
          mode="single"
          placeholder={t('ctint-mf-cpp.filter.allMediaSource')}
          options={mediaSourceOptions}
          value={filterValue ?? ''}
          onChange={(v) => {
            setFilterInput((prev: any) => ({
              ...prev,
              [filterName]: v === '_all' ? '' : v,
            }));
          }}
        />
      </div>
    );
  }

  if (type === 'evaluation') {
    return (
      <div className="w-full">
        <Select
          mode="single"
          placeholder="Select type of evaluation"
          options={[
            {
              id: 'evaopt-all',
              label: 'All',
              value: '_all',
            },
            {
              id: 'evaopt-unassigned',
              label: 'Unassigned',
              value: 'unassigned',
            },
            {
              id: 'evaopt-assigned',
              label: 'Assigned',
              value: 'assigned',
            },
            {
              id: 'evaopt-assignedToYou',
              label: 'Assigned to you',
              value: 'assignedToYou',
            },
            {
              id: 'evaopt-evaluated',
              label: 'Evaluted',
              value: 'evaluated',
            },
            {
              id: 'evaopt-release',
              label: 'Released',
              value: 'released',
            },
          ]}
          value={filterValue ?? ''}
          onChange={(v) => {
            setFilterInput((prev: any) => ({
              ...prev,
              [filterName]: v === '_all' ? '' : v,
            }));
          }}
        />
      </div>
    );
  }

  if (type === 'duration') {
    return (
      <div className="flex justify-between items-center gap-4 w-full">
        <Select
          mode="single"
          placeholder={t('ctint-mf-cpp.filter.greaterOrLess')}
          options={operators.map((operator) => ({
            key: operator.id,
            id: operator.id,
            label: t(`ctint-mf-cpp.filter.${operator.label}`),
            value: operator.value,
          }))}
          value={operatorValue}
          onChange={handleOperatorValue}
        />
        <Input
          value={filterValue ?? ''}
          onChange={(v) => {
            setFilterInput((prev: any) => ({
              ...prev,
              [filterName]: v,
            }));
          }}
          disabled={!isChecked}
        />
      </div>
    );
  }

  if (type === 'boolean') {
    return <div className="w-full h-10"></div>;
  }

  if (type === 'datetime') {
    return (
      <DateTimePicker
        date={filterValue ? dayjs(filterValue).toDate() : null}
        onChange={(d) => {
          setFilterInput((prev: any) => ({
            ...prev,
            [filterName]: d ? dayjs(d).toISOString() : '',
          }));
        }}
        disabled={!isChecked}
      />
    );
  }

  return (
    <Input
      value={filterValue ?? ''}
      onChange={(v) => {
        setFilterInput((prev: any) => ({
          ...prev,
          [filterName]: v,
        }));
      }}
      disabled={!isChecked}
      placeholder={`${
        filterName === 'id' ? t('ctint-mf-cpp.filter.onlyId') : ''
      }`}
    />
  );
};

export default FilterInput;
