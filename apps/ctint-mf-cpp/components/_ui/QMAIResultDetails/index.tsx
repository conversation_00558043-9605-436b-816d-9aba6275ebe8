import { Button, useQM } from '@cdss-modules/design-system';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import { cn, secondsToFormat } from '@cdss-modules/design-system/lib/utils';
import { memo, useState } from 'react';
import { useGlobalAudioPlayer } from 'react-use-audio-player';
import Input from '@cdss-modules/design-system/components/_ui/Input';

type TQMAIResultDetailsProps = {
  qid: string;
};

const QMAIResultDetails = memo(({ qid }: TQMAIResultDetailsProps) => {
  const { qaFormAnswers, openedQA, expandedQAResults, layoutMode } = useQM();
  const [overrideResult, setOverrideResult] = useState('passed');

  const showDetails = expandedQAResults?.includes(qid);

  const tags = qaFormAnswers?.[`${openedQA}`]?.tags;
  const thisTag = tags?.[qid];
  const { seek } = useGlobalAudioPlayer();

  return (
    <div
      className={cn(
        'flex flex-col gap-y-1 text-remark',
        layoutMode === '2' && 'py-1 pl-4 border-l-2 border-grey-200',
        !showDetails && 'hidden'
      )}
    >
      <div className="flex gap-x-2">
        <strong>Time: </strong>
        <button
          onClick={() => seek(thisTag?.time)}
          className="underline text-status-info hover:text-primary-600"
        >
          <i>{secondsToFormat(thisTag?.time)}</i>
        </button>
      </div>
      <div className="flex gap-x-2">
        <strong>Notes: </strong>
        <i>{thisTag?.desc}</i>
      </div>
      <div className="flex gap-x-2">
        <div className="flex gap-x-2">
          <strong>Override to </strong>
          <Select
            placeholder="Override to"
            mode="single"
            options={[
              {
                value: 'passed',
                label: 'Passed',
                id: 'result-passed',
              },
              {
                value: 'failed',
                label: 'Failed',
                id: 'result-failed',
              },
            ]}
            showSearch={false}
            value={overrideResult ?? ''}
            onChange={(e) => setOverrideResult(e)}
            labelClassName="h-full text-footnote"
            labelContainerClassName="h-6"
          />
        </div>
        <div className="flex gap-x-2">
          <strong>because </strong>
          <Input
            size="xs"
            placeholder="Overriding Reason"
          />
        </div>
        <Button size="xs">Submit</Button>
      </div>
    </div>
  );
});

QMAIResultDetails.displayName = 'QMAIResultDetails';

export default QMAIResultDetails;
