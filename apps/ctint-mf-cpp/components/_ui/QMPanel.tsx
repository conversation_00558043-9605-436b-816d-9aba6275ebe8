/* eslint-disable react-hooks/exhaustive-deps */
import { cn } from '@cdss-modules/design-system/lib/utils';
import ScoreCircle from './ScoreCircle';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import {
  <PERSON><PERSON><PERSON>,
  ClipboardCheck,
  ClipboardPen,
  Frown,
  Rocket,
  Send,
  Smile,
  Trash2,
  UserCheck,
  UserPen,
} from 'lucide-react';
import { GLOBAL_DATETIME_FORMAT } from '@cdss-modules/design-system/lib/constants';
import dayjs from 'dayjs';
import { Button, Tooltip, useQM } from '@cdss-modules/design-system';
import {
  ResizablePanelGroup,
  ResizablePanel,
} from '@cdss-modules/design-system/components/_ui/Resizable';
import { Fragment, memo, useEffect, useMemo, useState } from 'react';
import { DUMMY_CS_EVALUATION_FORM } from '../../lib/dummy/qa';
import { getQALiveStat } from '../../lib/qa/index';
import QMQuestionArea from './QMQuestionArea';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';

type TQMPanelProps = {
  data: any[];
  defaultOpenedQA?: string;
  onAssign?: () => void;
  onReassign?: () => void;
};

const QMPanel = memo(
  ({ data, onAssign, onReassign, defaultOpenedQA }: TQMPanelProps) => {
    const [editable, setEditable] = useState<boolean>(false);

    const {
      updateQMActiveSection,
      qaFormAnswers,
      openedQA,
      openQA,
      closeQA,
      resultFilter,
      updateResultFilter,
    } = useQM();

    useEffect(() => {
      if (defaultOpenedQA) {
        openQA(defaultOpenedQA);
      }
    }, [defaultOpenedQA]);

    const openQADetails = useMemo(() => {
      return data.find((qa) => qa.id === openedQA);
    }, [openedQA, data]);

    useEffect(() => {
      if (openedQA) {
        setEditable(openQADetails?.status === 'assigned');
        updateQMActiveSection(DUMMY_CS_EVALUATION_FORM?.questions?.[0]?.id);
      } else {
        updateQMActiveSection('');
      }
    }, [openedQA]);

    const qaLiveStat = useMemo(() => {
      const form = DUMMY_CS_EVALUATION_FORM;
      const answers = qaFormAnswers?.[`${openedQA}`] || {};
      console.log('answers', answers);
      if (openedQA && form && answers) {
        return getQALiveStat(form, answers);
      }
      return null;
    }, [openedQA, qaFormAnswers]);

    const qaScore = qaLiveStat?.totalScore || 0;
    const criticalScore = qaLiveStat?.criticalScore || 0;
    const answeredAll =
      qaLiveStat?.totalQuestionsAnswered === qaLiveStat?.totalQuestions;

    if (openedQA && openQADetails) {
      return (
        <div className="flex flex-col h-full">
          <div className="bg-white z-10">
            <div className="flex items-center gap-4 py-2 px-4 border-b border-grey-200">
              <div className="w-full flex items-center justify-between">
                <div className="flex items-center gap-x-4">
                  <Button
                    onClick={() => closeQA()}
                    variant="back"
                    beforeIcon={<Icon name="back" />}
                    bodyClassName="p-1 min-w-0"
                  />
                  <div className="flex items-center gap-x-2">
                    <UserCheck size={28} />
                    <strong>{openQADetails.evaluator}</strong>
                  </div>
                  <div className="flex items-center gap-x-2">
                    {/* {hasFatalZeroPoint ? (
                  <Tooltip
                    trigger={
                      <span className="inline-block text-status-danger">
                        <Bomb />
                      </span>
                    }
                    content="Fatal answer leads to zero point"
                  />
                ) : ( */}
                    <>
                      {qaScore >= 50 && criticalScore >= 50 && (
                        <Smile
                          className="text-status-success"
                          size={28}
                        />
                      )}
                      {/* {qaScore < 70 && qaScore >= 50 && criticalScore >= 50 && (
                      <Meh
                        className="text-primary-600"
                        size={28}
                      />
                    )} */}
                      {(qaScore < 50 || criticalScore < 50) && (
                        <Frown
                          className="text-status-danger"
                          size={28}
                        />
                      )}
                    </>
                    {/* )} */}
                    {/* {qaScore} / 100 */}
                    {qaScore >= 50 ? 'Passed' : 'Failed'}
                  </div>
                  {/* <div
                className={cn(
                  // criticalScore >= 70 && 'text-status-success',
                  criticalScore < 70 &&
                    criticalScore >= 50 &&
                    'text-primary-600',
                  criticalScore < 50 && 'text-status-danger'
                )}
              >
                <Tooltip
                  trigger={
                    <span className="inline-block -mb-1 text-primary-600">
                      <TriangleAlert />
                    </span>
                  }
                  content="Critical score"
                />{' '}
                {criticalScore} / 100
              </div> */}
                </div>
                {openQADetails.status === 'released' && (
                  <div className="flex items-center gap-x-2">
                    <Select
                      placeholder="All results"
                      mode="single"
                      options={[
                        {
                          value: 'all',
                          label: 'All results',
                          id: 'result-all',
                        },
                        {
                          value: 'passed',
                          label: 'Passed',
                          id: 'result-passed',
                        },
                        {
                          value: 'failed',
                          label: 'Failed',
                          id: 'result-failed',
                        },
                      ]}
                      showSearch={false}
                      value={resultFilter ?? ''}
                      onChange={updateResultFilter}
                      labelClassName="h-full text-remark"
                      labelContainerClassName="h-8"
                    />
                    <Button
                      onClick={onAssign}
                      variant="blank"
                      disabled
                      className="disabled:opacity-100"
                      beforeIcon={
                        <Rocket
                          className="text-primary-600"
                          size={24}
                        />
                      }
                    >
                      <span>Released</span>
                    </Button>
                  </div>
                )}
                {openQADetails.status === 'evaluated' && (
                  <div className="flex items-center gap-x-2">
                    <Button
                      onClick={() => {
                        setEditable(!editable);
                      }}
                      variant="blank"
                      beforeIcon={<ClipboardPen size={24} />}
                    >
                      <span>{editable ? 'Confirm' : 'Review'}</span>
                    </Button>
                    <Button
                      onClick={() => {
                        setEditable(false);
                      }}
                      variant="blank"
                      beforeIcon={<Rocket size={24} />}
                    >
                      <span>Release</span>
                    </Button>
                  </div>
                )}
                {openQADetails.status === 'assigned' && (
                  <>
                    {answeredAll ? (
                      <Button
                        onClick={() => {
                          setEditable(false);
                        }}
                        variant="blank"
                        beforeIcon={<Send />}
                      >
                        <span>Submit</span>
                      </Button>
                    ) : (
                      <div className="flex items-center gap-x-2">
                        <ClipboardCheck size={24} />
                        <strong>
                          {qaLiveStat?.totalQuestionsAnswered} /{' '}
                          {qaLiveStat?.totalQuestions}
                        </strong>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
          <ResizablePanelGroup
            direction="vertical"
            className="flex flex-col w-full flex-1 h-0"
          >
            <ResizablePanel>
              <div className="size-full px-4 py-4">
                <QMQuestionArea editable={editable} />
              </div>
            </ResizablePanel>
          </ResizablePanelGroup>
        </div>
      );
    }

    return (
      <div className={cn('relative')}>
        <div className="flex items-center justify-between gap-4 py-2 px-4 border-b border-grey-200 sticky top-0 bg-white z-30">
          <div className="flex items-center gap-x-6">
            <div className="flex items-center gap-x-2">
              <ClipboardCheck size={28} />
              <strong>Evaluated: 3/4</strong>
            </div>
            <div className="flex items-center gap-x-2">
              <Smile
                className="text-status-success"
                size={28}
              />
              <strong>Passed</strong>
              {/* <strong>Quality:</strong>
            {releasedData.reduce((acc, curr) => acc + curr.score, 0) /
              releasedData.length}{' '}
            / 100 */}
            </div>
          </div>
          <Button
            onClick={onAssign}
            variant="blank"
            beforeIcon={
              <Icon
                name="plus"
                size={20}
              />
            }
          >
            <span>Add New</span>
          </Button>
        </div>
        {data.map((qa, i) => {
          let currentStatus = '';
          if (qa.status === 'released') {
            currentStatus = `Released at ${dayjs(qa.releasedAt).format(GLOBAL_DATETIME_FORMAT)}`;
            if (i === 0) {
              currentStatus += ` / No Action.`;
            } else {
              currentStatus += ` / Training Required.`;
            }
          } else if (qa.status === 'evaluated') {
            currentStatus = `Evaluated at ${dayjs(qa.date).format(GLOBAL_DATETIME_FORMAT)}`;
          } else {
            currentStatus = `Assigned at ${dayjs(qa.assignedAt).format(GLOBAL_DATETIME_FORMAT)}`;
          }
          return (
            <div
              key={qa.id}
              className="flex items-stretch gap-x-4 p-4 border-b border-grey-200 hover:bg-primary-200"
              onClick={() => openQA(qa.id)}
            >
              <div className="flex gap-4 items-center">
                <ScoreCircle
                  score={qa.score}
                  status={qa.status}
                  progress={qa.progress}
                  progressPercent={qa.progressPercent}
                />
              </div>
              <div className="w-full flex flex-wrap gap-3 items-start">
                <div className="w-full flex gap-2">
                  <Tooltip
                    trigger={
                      <div>
                        <Icon
                          name="file"
                          size={24}
                        />
                      </div>
                    }
                    content="Evaluation Form"
                  />
                  <strong className="hidden">Evaluation Form:</strong>
                  <p>
                    {qa.form} <i>({qa.formVersion})</i>
                  </p>
                </div>
                <div className="w-full flex gap-2">
                  <Tooltip
                    trigger={<UserCheck />}
                    content="Evaluator"
                  />
                  <strong className="hidden">Evaluator:</strong>
                  <p>{qa.evaluator || 'N/A'}</p>
                </div>
                <div className="w-full flex gap-2">
                  <Tooltip
                    trigger={<ChartPie size={24} />}
                    content="Status / Action"
                  />
                  <strong className="hidden">Status:</strong>
                  <p>{currentStatus}</p>
                </div>
              </div>
              <div className="flex gap-3 items-start justify-end">
                {/* <Tooltip
                trigger={
                  <button className="hover:text-primary">
                    <Eye size={24} />
                  </button>
                }
                content="View"
              /> */}
                {qa.status === 'evaluated' && (
                  <Tooltip
                    trigger={
                      <button className="hover:text-primary">
                        <Rocket size={24} />
                      </button>
                    }
                    content="Release"
                  />
                )}
                {qa.status === 'assigned' && (
                  <Tooltip
                    trigger={
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          e.preventDefault();
                          onReassign && onReassign();
                        }}
                        className="hover:text-primary"
                      >
                        <UserPen size={24} />
                      </button>
                    }
                    content="Re-assign"
                  />
                )}
                {qa.status === 'assigned' && (
                  <Tooltip
                    trigger={
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          e.preventDefault();
                        }}
                        className="hover:text-primary"
                      >
                        <Trash2 size={24} />
                      </button>
                    }
                    content="Delete"
                  />
                )}
              </div>
            </div>
          );
        })}
      </div>
    );
  }
);

QMPanel.displayName = 'QMPanel';

export default QMPanel;
