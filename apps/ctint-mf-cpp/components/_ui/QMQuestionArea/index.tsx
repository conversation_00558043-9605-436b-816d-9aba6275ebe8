/* eslint-disable react-hooks/exhaustive-deps */
import { cn } from '@cdss-modules/design-system/lib/utils';
import { useQM } from '@cdss-modules/design-system';
import QMQuestion from '../QMQuestion';
import { DUMMY_CS_EVALUATION_FORM } from '../../../lib/dummy/qa';
import { useInView } from 'react-intersection-observer';
import { useEffect } from 'react';
import QMAIResultDetails from '../QMAIResultDetails';

const QMQuestionScrollTag = ({ id }: { id: string }) => {
  const { ref, inView } = useInView({
    /* Optional options */
    threshold: 1,
  });
  const {
    updateQMActiveSection,
    activeQMSection,
    scrollToScriptClickHandler,
    scrollingStage,
  } = useQM();
  useEffect(() => {
    if (inView && activeQMSection !== id && !scrollingStage) {
      updateQMActiveSection(id);
      scrollToScriptClickHandler(`script-${id}`);
    }
  }, [inView]);

  return (
    <div
      ref={ref}
      className="absolute top-[300px] left-0 w-full h-0"
    />
  );
};

const QMQuestionArea = ({ editable }: { editable: boolean }) => {
  const {
    qaFormAnswers,
    updateQMAnswer: updateAnswer,
    openedQA,
    getScrollToStageRef,
    resultFilter,
    layoutMode,
    expandedQAResults,
  } = useQM();
  const activeQA = openedQA || '';

  return (
    <>
      <div className="h-full overflow-auto">
        {DUMMY_CS_EVALUATION_FORM.questions?.map((stage, stageIndex) => {
          const stageId = `${stageIndex + 1}`;
          const stageKey = `stage-${stage.id}`;
          const stageTitle = `${stageId}. ${stage.title}`;
          const hasFailed = stage?.subSections?.some((sec) =>
            sec.questions.some((q) => {
              const answer =
                qaFormAnswers[activeQA]?.[stage.id]?.[sec.id]?.[q.id];
              return answer?.indexOf('_y') === -1;
            })
          );
          const hasPassed = stage?.subSections?.some((sec) =>
            sec.questions.every((q) => {
              const answer =
                qaFormAnswers[activeQA]?.[stage.id]?.[sec.id]?.[q.id];
              return answer?.indexOf('_y') > -1;
            })
          );
          if (resultFilter === 'passed' && !hasPassed) return null;
          if (resultFilter === 'failed' && !hasFailed) return null;
          return (
            <div
              className="relative"
              id={stageKey}
              ref={getScrollToStageRef(stageKey)}
              key={stageKey}
            >
              <QMQuestionScrollTag id={stage.id} />
              {stage?.subSections?.map((sec, si) => {
                const sid = `${si + 1}`;
                const skey = `sec-${sid}`;
                return (
                  <div
                    key={skey}
                    className="pb-4 mb-4 border-b border-grey-200"
                  >
                    <div
                      className={cn(
                        'font-bold text-body',
                        !editable && 'text-grey-500',
                        layoutMode === '1' && 'mb-2',
                        layoutMode === '2' && 'mb-2'
                      )}
                    >
                      {stageTitle}
                    </div>
                    <div>
                      {sec.questions.map((q: any, qi) => {
                        const qid = `${qi + 1}`;
                        const qkey = sid + `${qid}`;
                        const questionText = `${stageId}.${qid}. ${q.question}`;
                        const answer =
                          qaFormAnswers[activeQA]?.[stage.id]?.[sec.id]?.[q.id];
                        const isPassed = answer?.indexOf('_y') > -1;
                        const isExpanded = expandedQAResults?.includes(q.id);
                        if (resultFilter === 'passed' && !isPassed) return null;
                        if (resultFilter === 'failed' && isPassed) return null;
                        return (
                          <div
                            key={qkey}
                            className={cn(
                              'pr-4 last:mb-0',
                              layoutMode === '1' && 'pl-8 mb-4',
                              layoutMode === '2' && 'pl-4 py-2',
                              isExpanded &&
                                layoutMode === '2' &&
                                'bg-primary-100'
                            )}
                          >
                            <QMQuestion
                              type={q.type}
                              questionType={q.questionType}
                              disabled={!editable}
                              qid={q.id}
                              question={questionText}
                              options={q.answers}
                              onAnswer={(ans) =>
                                updateAnswer(
                                  activeQA,
                                  stage.id,
                                  sec.id,
                                  q.id,
                                  ans
                                )
                              }
                              answer={answer}
                            />
                            {layoutMode === '2' && (
                              <QMAIResultDetails qid={q.id} />
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                );
              })}
            </div>
          );
        })}
      </div>
    </>
  );
};

export default QMQuestionArea;
