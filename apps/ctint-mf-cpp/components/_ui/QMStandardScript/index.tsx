/* eslint-disable react/no-unescaped-entities */
import React from 'react';
import { useQM } from '@cdss-modules/design-system';
import { cn } from '@cdss-modules/design-system/lib/utils';
import PDFViewer from '@cdss-modules/design-system/components/_ui/PDFViewer';

const DUMMY_SCRIPT = [
  {
    tag: 's_1',
    fragments: [
      {
        script:
          '你好，今日是 YYYY/MM/DD，就您打算認購的股票掛鈎產品開始錄音。我是銀行職員 XXX。',
      },
    ],
  },
  {
    tag: 's_2',
    fragments: [
      {
        script:
          'XXX 先生/小姐，您的全名是 XXX。請您提供全名、身份證號碼後面的 4 位數字及以下其中 2 項資料核對身份：出生日期（日子及月份）、住址或公司地址。',
      },
    ],
  },
  {
    tag: 's_3',
    fragments: [
      {
        script:
          '有關您的弱勢客戶評估，您表示在本行以外有複雜產品及其他產品的投資經驗。加上本行紀錄，您的評估結果為非弱勢客戶。',
      },
    ],
  },
  {
    tag: 's_4',
    fragments: [
      {
        script:
          '本行建議，您應由「客戶見證人」陪同協助您明白產品內容及風險，並進行有關投資。客戶見證人 XXX 先生/小姐是您的______，並確認是 65 歲以下、教育水平是中學程度或以上，並確認對此產品有認識及有足夠理解能力協助您了解此交易。',
      },
    ],
  },
  {
    tag: 's_5',
    fragments: [
      {
        script:
          '被授權人 XXX 先生/小姐，您對此產品有認識，擁有對衍生工具投資產品的認識，並有足夠理解能力進行此交易，請您確認。',
      },
    ],
  },
  {
    tag: 's_6',
    fragments: [
      {
        script:
          '您向本行作出聲明確認您/戶口持有人並非首次購買此產品類別，及已有此類投資產品經驗，是不受「落單冷靜期」安排所覆蓋。請確認。',
      },
    ],
  },
  {
    tag: 's_7',
    fragments: [
      {
        script:
          '我們現在進行產品適合性評估。您是否有足夠時間考慮投資適合性，明白及接受此產品之產品的特質、運作模式、相關風險、潛在損失及確定進行交易? 請確認。',
      },
    ],
  },
  {
    tag: 's_8',
    fragments: [
      {
        script:
          '根據產品適合性評估，您會預留多於 6 個月但少於或等如 12 個月的家庭開支備用現金或高流動性資產。在本行已擁有此類產品的投資經驗。請確認您同意以上適合性評估結果。是次投資金額 CCY。股票掛鈎投資戶口。現金結算戶口。當此申請獲接納便不可撤回，如果您接受交易，銀行會即時凍結資金。您是否確認此申請? 我們會盡快通知您此產品的發行結果，錄音完成，多謝您的申請。',
      },
    ],
  },
];

export const QMStandardScript = () => {
  const {
    activeQMSection,
    getScrollToScriptRef,
    highlightQuestion,
    scrollToStageClickHandler,
    updateQMActiveSection,
  } = useQM();

  return (
    <div className="relative w-full h-full flex flex-col">
      {/* <h2 className="font-bold mb-2">Standard Script:</h2> */}
      <div className="flex-1 h-0 space-y-2 py-4">
        <PDFViewer pdf="/ctint/mf-cpp/pdf/dummy-standard-script.pdf" />
        <div className="hidden">
          {DUMMY_SCRIPT.map((section) => (
            <div
              className="leading-loose"
              ref={getScrollToScriptRef(`script-${section.tag}`)}
              key={section.tag}
            >
              <mark
                className={cn(
                  // 'py-2',
                  'bg-transparent'
                  // activeQMSection === section.tag
                  //   ? 'bg-primary-400'
                  //   : 'bg-transparent'
                )}
              >
                {section.fragments.map((fragment: any) => (
                  <>
                    <mark
                      key={fragment?.tag?.join('') || ''}
                      className={cn(
                        // 'p-1',
                        // 'hover:bg-primary-200 first:pl-0 cursor-pointer',
                        'bg-transparent'
                        // activeQMSection === section.tag
                        //   ? 'bg-primary-100'
                        //   : 'bg-transparent'
                      )}
                      onClick={() => {
                        highlightQuestion(fragment?.targetId);
                        scrollToStageClickHandler(`stage-${section.tag}`);
                        updateQMActiveSection(section.tag);
                      }}
                    >
                      {fragment.script}{' '}
                      {fragment?.tag && (
                        <sup className="bg-primary-900 px-1 py-[2px] rounded-md text-white">
                          {fragment?.tag?.join(', ')}
                        </sup>
                      )}
                    </mark>
                    {` `}
                  </>
                ))}
              </mark>
            </div>
          ))}
        </div>
        {/* <p>
          <mark
            ref={getScrollToScriptRef('script-s_1')}
            className={cn(
              activeQMSection === 's_1' ? 'bg-primary-300' : 'bg-transparent'
            )}
          >
            Hello, thank you for calling [Company Name]. My name is [Agent's
            Name], and I'll be assisting you today. May I have your name,
            please?
          </mark>
        </p>
        <p>
          <mark
            ref={getScrollToScriptRef('script-s_2')}
            className={cn(
              activeQMSection === 's_2' ? 'bg-primary-300' : 'bg-transparent'
            )}
          >
            Could you please describe the issue you're experiencing? I’ll listen
            carefully to ensure I understand. Just to confirm, you're
            experiencing [restate problem]. Is that correct? Based on what
            you've told me, here’s what we can do to resolve this: [provide
            solution].
          </mark>
        </p>
        <p>
          <mark
            ref={getScrollToScriptRef('script-s_3')}
            className={cn(
              activeQMSection === 's_3' ? 'bg-primary-300' : 'bg-transparent'
            )}
          >
            Is there anything else I can assist you with regarding this issue?
            If there are any other problems or questions, I’m here to help.
            Thank you for contacting [Company Name]. Have a great day, goodbye!
          </mark>
        </p>
        <p>
          <mark
            className={cn(
              activeQMSection === 's_3' ? 'bg-primary-300' : 'bg-transparent'
            )}
          >
            If you have any additional comments or feedback, please feel free to
            share. Based on our conversation, the next steps may include [No
            Action/Training Required/etc.]. The result of this interaction has
            been overridden to [Passed/Failed] because [reason].
          </mark>
        </p> */}
      </div>
    </div>
  );
};

export default QMStandardScript;
