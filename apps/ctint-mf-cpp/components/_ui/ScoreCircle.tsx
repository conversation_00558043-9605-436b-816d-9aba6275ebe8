import { cn } from '@cdss-modules/design-system/lib/utils';
import { C<PERSON><PERSON><PERSON><PERSON><PERSON>, ClipboardPen, Rocket } from 'lucide-react';

type TScoreCircleProps = {
  score: number;
  status?: string;
  progress?: string;
  progressPercent?: number;
};

const ScoreCircle = ({
  score,
  status,
  progress,
  progressPercent,
}: TScoreCircleProps) => {
  const size = 'size-24';
  const stroke = '10';

  const isPassed = score >= 50;
  return (
    <div className={cn('relative', size)}>
      <svg
        className="w-full h-full"
        viewBox="0 0 100 100"
      >
        <circle
          className="text-gray-200 stroke-current"
          strokeWidth={stroke}
          cx="50"
          cy="50"
          r="40"
          fill="white"
        />
        <circle
          className={cn(
            'progress-ring__circle progress-ring__ani stroke-current origin-center -rotate-90 fill-transparent',
            // score >= 70 && 'text-status-success',
            // score >= 50 && score < 70 && 'text-primary-600',
            // score < 50 && 'text-status-danger'
            status === 'released'
              ? [
                  isPassed && 'text-status-success',
                  !isPassed && 'text-status-danger',
                ]
              : 'text-primary-600'
          )}
          strokeWidth={stroke}
          strokeLinecap="round"
          cx="50"
          cy="50"
          r="40"
          strokeDasharray="251.2"
          style={
            {
              '--score': status === 'released' ? 100 : progressPercent || 0,
            } as React.CSSProperties
          }
        />
        {/* <text
          x="50"
          y="50"
          textAnchor="middle"
          alignmentBaseline="middle"
          fontSize={22}
        >
          {score}
          <Check />
        </text> */}
      </svg>
      <div
        className={cn(
          'absolute top-0 left-0 text-t6 size-full flex flex-col justify-center items-center font-bold',
          status !== 'released' && 'text-grey-600'
        )}
      >
        {status === 'released' || status === 'evaluated' ? (
          <div className="flex flex-col justify-center items-center -mb-1">
            {status === 'released' && (
              <>
                <Rocket
                  size={20}
                  strokeWidth="2"
                />
                <div className="-mt-[2px] text-remark">
                  {isPassed ? 'Passed' : 'Failed'}
                </div>
              </>
            )}
            {status === 'evaluated' && (
              <>
                <ClipboardCheck
                  size={20}
                  strokeWidth="2"
                />
                <div className="text-remark">{progress}</div>
              </>
            )}
          </div>
        ) : (
          <ClipboardPen
            size={32}
            strokeWidth="2"
          />
        )}
      </div>
    </div>
  );
};

export default ScoreCircle;
