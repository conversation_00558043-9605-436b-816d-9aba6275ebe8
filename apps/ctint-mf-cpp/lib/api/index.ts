import { addLoggingToAxios } from '@cdss-modules/design-system/lib/logging';
import { apiConfig } from './config';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';

type UserConfigItems = {
  name: string;
  value: string;
};

type UserConfigProps = {
  filters?: UserConfigItems[];
  columns?: UserConfigItems[];
};

export const axiosInstance = axios.create({
  timeout: 20000,
  headers: {
    'Content-Type': 'application/json',
    traceId: uuidv4(),
    tenant: 'ccba',
    sourceId: 'ctint-mf-cpp',
    previousId: 'ctint-bff-cdss',
  },
});

axiosInstance.interceptors.request.use(
  (config) => {
    const cdssAuthToken = localStorage.getItem('cdss-auth-token') || '';
    const gcAccessToken = localStorage.getItem('gc-access-token') || '';
    const deviceId = localStorage.getItem('deviceId') || '';
    config.headers['tenant'] = localStorage.getItem('tenant') || 'ctint';
    if (cdssAuthToken) {
      config.headers['cdss-authorization'] = 'Bearer ' + cdssAuthToken;
    }
    if (deviceId) {
      config.headers['deviceId'] = deviceId;
    }
    return config;
  },
  (error) => {
    Promise.reject(error);
  }
);

addLoggingToAxios(axiosInstance);

export const fireGetFilteredRecordings = (
  queryParams: string,
  basePath = ''
) => {
  if (
    queryParams.includes('isInbound') ||
    queryParams.includes('isOutbound') ||
    queryParams.includes('duration') ||
    queryParams.includes('order')
  ) {
    return fireGetSortedRecordings(queryParams, basePath);
  } else if (queryParams.includes('id')) {
    const urlParams = new URLSearchParams(queryParams);
    const id = urlParams?.get('id') ?? '';

    return fireGetSingleRecording(id, basePath);
  } else {
    return fireGetAllRecordings(queryParams, basePath);
  }
};

export const fireGetSortedRecordings = (queryParams: string, basePath = '') =>
  axiosInstance.get(`${apiConfig.paths.sort}?${queryParams}`);

export const fireGetAllRecordings = (queryParams: string, basePath = '') =>
  axiosInstance.get(`${apiConfig.paths.recordings}?${queryParams}`);

export const fireGetSingleRecording = (id: string, basePath = '') =>
  axiosInstance.get(`${apiConfig.paths.recordings}/abc`);

export const fireGetRecordingMedia = (mediaUri: string, basePath = '') =>
  axiosInstance.get(`${apiConfig.paths.recordings}/media?mediaUri=${mediaUri}`);

export const fireExportRecordings = (mediaUris: string[], basePath = '') =>
  axiosInstance.post(`${apiConfig.paths.recordings}/media/export`, {
    mediaUris,
  });

export const fireGetRecordingTranscript = (mediaUri: string, basePath = '') =>
  axiosInstance.get(`${apiConfig.paths.transcript}`, {
    timeout: 300000,
  });

export const fireGetUserConfig = (basePath = '') =>
  axiosInstance.get(`${basePath}${apiConfig.paths.config}`);

export const fireCreateUserConfig = (data: UserConfigProps, basePath = '') =>
  axiosInstance.post(`${basePath}${apiConfig.paths.config}`, data);

export const fireUpdateUserConfig = (data: UserConfigProps, basePath = '') =>
  axiosInstance.put(`${basePath}${apiConfig.paths.config}`, data);

export default axiosInstance;
