import { PageRenderer } from '@cdss-modules/design-system';
import PlaybackModule from '../components/_screen/Main';
import PlaybackModuleDetail from '../components/_screen/Detail';
import { basePath, mfName } from '../lib/appConfig';
import loadGlobalConfig from '@cdss-modules/design-system/lib/globalConfig';

export const Page = () => {
  return (
    <div className="relative p-6 w-full h-screen">
      <PageRenderer
        routes={[
          {
            path: '/',
            group: 'playback',
            component: <PlaybackModule />,
            subroutes: [
              {
                path: '/detail',
                component: <PlaybackModuleDetail />,
              },
            ],
          },
        ]}
        basePath={basePath}
      />
    </div>
  );
};

export const getServerSideProps = async () => {
  const globalConfig = loadGlobalConfig(mfName);
  const publicEnvVars = Object.keys(process.env).reduce(
    (publicVars: any, key) => {
      if (key.startsWith('CDSS_PUBLIC_')) {
        publicVars[key] = process.env[key];
      }
      return publicVars;
    },
    {}
  ) as any;
  return {
    props: {
      globalConfig,
      publicEnvVars,
    },
  };
};

export default Page;
