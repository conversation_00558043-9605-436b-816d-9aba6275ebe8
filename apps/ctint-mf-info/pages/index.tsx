import { PageRenderer } from '@cdss-modules/design-system';
import Main from '../components/_screen/Main';
import Detail from '../components/_screen/Detail';
import { basePath } from '../lib/appConfig';

export const Page = () => {
  return (
    <div className="p-6 w-full h-screen">
      <PageRenderer
        routes={[
          {
            path: '/',
            group: 'ctint-mf-info',
            component: <Main />,
          },
          {
            path: '/detail',
            group: 'ctint-mf-info',
            component: <Detail />,
          },
      ]} basePath={basePath} />
    </div>
  );
};

export const getServerSideProps = async () => {
  const publicEnvVars = Object.keys(process.env).reduce(
    (publicVars: any, key) => {
      if (key.startsWith('CDSS_PUBLIC_')) {
        publicVars[key] = process.env[key];
      }
      return publicVars;
    },
    {}
  ) as any;
  return {
    props: {
      publicEnvVars,
    },
  };
};

export default Page;
