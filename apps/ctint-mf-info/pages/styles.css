@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body{
    @apply bg-common-bg;
  }

  /* width */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  /* Track */
  ::-webkit-scrollbar-track {
    @apply bg-common-divider rounded-full;
  }

  /* Handle */
  ::-webkit-scrollbar-thumb {
    @apply bg-primary rounded-full;
    background-clip: padding-box;
  }

  /* Handle on hover */
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-black;
  }
}

@layer components {

  @keyframes spinner-animation {
    0% {
      stroke-dasharray: 1 98;
      stroke-dashoffset: -105;
    }
    50% {
      stroke-dasharray: 80 10;
      stroke-dashoffset: -160;
    }
    100% {
      stroke-dasharray: 1 98;
      stroke-dashoffset: -300;
    }
  }
  .spinner-animation {
    transform-origin: center;
    animation-name: animation;
    animation: spinner-animation 1.2s infinite;
  }

  .size-container {
    position: relative;
    height: 100%;
  }

  .profile-panel {
    height: 100%;
    container-type: size;
    container-name: profile-panel;
    display: flex;
  }

  .profile-panel__info {
   
    @container profile-panel (max-height: 180px) and (max-width: 880px) {
      display: none !important;
    }
  }

  .profile-panel__toolbar {
    @container profile-panel (max-height: 180px) and (max-width: 880px) {
      display: flex !important;
    }
  }

  .profile-panel__toolbar_name {
    @container profile-panel (max-width: 520px) and (max-width: 880px) {
      display: none !important;
    }
  }

  .profile-panel__toolbar_vrinfo {
    @container profile-panel (max-width: 480px) and (max-width: 880px) {
      display: none !important;
    }
  }
}
