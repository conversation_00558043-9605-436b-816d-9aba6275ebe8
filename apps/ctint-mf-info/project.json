{"name": "ctint-mf-info", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/ctint-mf-info", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/next:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"root": "apps/ctint-mf-info", "outputPath": "dist/apps/ctint-mf-info"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "ctint-mf-info:build", "dev": true, "port": 4800, "host": "0.0.0.0"}, "configurations": {"development": {"buildTarget": "ctint-mf-info:build:development", "dev": true}, "production": {"buildTarget": "ctint-mf-info:build:production", "dev": false}}}, "export": {"executor": "@nx/next:export", "options": {"buildTarget": "ctint-mf-info:build:production"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["coverage/apps/ctint-mf-info"], "options": {"jestConfig": "apps/ctint-mf-info/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/ctint-mf-info/**/*.{ts,tsx,js,jsx}"]}}}}