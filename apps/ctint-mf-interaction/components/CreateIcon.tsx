import { lazy, Suspense } from 'react';

type TCreateIconProps = {
  iconType?: string;
};

const TIcon = {
  voice: 'IconVoice',
  callback: 'IconCallback',
  chat: 'IconChat',
  email: 'IconEmail',
  message: 'IconMessage',
  whatsapp: 'IconWhatsapp',
};
// 按需动态导入图标
const CreateIcon = ({ iconType }: TCreateIconProps) => {
  const iconFileKey = TIcon?.[iconType as keyof typeof TIcon];
  if (!iconType || !iconFileKey) {
    return null;
  }

  const IconComponent = lazy(
    () =>
      import(
        `@cdss-modules/design-system/components/_ui/Icon/${iconFileKey}.tsx`
      )
  );

  const getIconColor = (iconType: string) => {
    switch (iconType) {
      case 'whatsapp':
        return '#1CC500';
      case 'email':
        return '#ffac4a';
      default:
        return 'black';
    }
  };

  return (
    <div>
      <Suspense fallback={<div>Loading...</div>}>
        <IconComponent
          size={16}
          color={getIconColor(iconType)}
        />
      </Suspense>
    </div>
  );
};

export { CreateIcon, TIcon };
