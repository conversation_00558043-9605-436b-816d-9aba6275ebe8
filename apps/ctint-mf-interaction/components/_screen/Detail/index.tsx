/* eslint-disable react/no-unescaped-entities */
'use client';
import React, { useState } from 'react';
import {
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle,
} from '@cdss-modules/design-system/components/_ui/Resizable';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import Panel from '@cdss-modules/design-system/components/_ui/Panel';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import {
  useRole,
  useRouteHandler,
  QMProvider,
  EvaluationFormListProvider,
  useQM,
} from '@cdss-modules/design-system';
import Breadcrumb from '@cdss-modules/design-system/components/_ui/Breadcrumb';
import {
  QueryClient,
  QueryClientProvider,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import dayjs from 'dayjs';
import { GLOBAL_DATETIME_FORMAT } from '@cdss-modules/design-system/lib/constants';
import {
  camelCaseToWords,
  secondsToTimeDisplay,
} from '@cdss-modules/design-system/lib/utils';
import {
  fireGetSingleRecording,
  fireGetEvaluationFormTemplate,
  fireGetQmEvaluationList,
  fireGetStandScriptResult,
} from '../../../lib/api';

import {
  Tabs,
  TabsContent,
} from '@cdss-modules/design-system/components/_ui/Tabs';
import QMPanel from '../../_ui/QMPanel';
import AssignEvaluationPopup from '../../_ui/AssignEvaluationPopup';
import { Wrapup, Wrapups } from '../../../types/wrapups';
import { TParticipants } from '../../../types/participants';
import StorageBox from '@cdss-modules/design-system/components/_ui/StorageBox';
import IconType from '@cdss-modules/design-system/components/_ui/Icon/IconType';
import IconStartTime from '@cdss-modules/design-system/components/_ui/Icon/IconStartTime';
import IconEndTime from '@cdss-modules/design-system/components/_ui/Icon/IconEndTime';
import IconInteractionId from '@cdss-modules/design-system/components/_ui/Icon/IconInteractionId';
import IconDirection from '@cdss-modules/design-system/components/_ui/Icon/IconDirection';
import IconDuration from '@cdss-modules/design-system/components/_ui/Icon/IconDuration';
import IconQueue from '@cdss-modules/design-system/components/_ui/Icon/IconQueue';
import IconUsers from '@cdss-modules/design-system/components/_ui/Icon/IconUsers';
import WrapUp from '../../_ui/WrapUp';
import { QmEvaluationListItem, QmFormList } from '../../../types/autoqm';
import { DUMMY_DEFAULT_ANSWERS } from '../../../lib/dummy/qa';
import { QmVisitPermission } from '@cdss-modules/design-system/@types/QmVisitPermission';
import { mfName } from '../../../lib/appConfig/index';
import { usePermission } from '@cdss-modules/design-system/context/PremissionContext';
import CallPanel from '../../_ui/CallPanel';
import { WhatsAppPanel } from '../../_ui/WhatsAppPanel/index';
import { EmailPanel } from '../../_ui/EmailPanel/index';

// TODO: consider move to global helper
const getFilteredTranscripts = (transcripts?: any[]) => {
  if (!Array.isArray(transcripts)) return null;
  return transcripts?.filter((transcript) => {
    return transcript?.speaker !== 'speaker';
  });
};
const getSpeakers = (transcripts?: any[]) => {
  if (!Array.isArray(transcripts)) return null;
  return transcripts?.reduce((acc, curr) => {
    const speaker = curr?.speaker;
    if (!acc.includes(speaker) && speaker !== 'speaker') {
      acc.push(speaker);
    }
    return acc;
  }, []);
};

export const TableDemoDetailBody = () => {
  const { toPath, searchParams, basePath } = useRouteHandler();
  const { t } = useTranslation();
  const { globalConfig } = useRole();
  const queryClient = useQueryClient();
  const [transcriptLoading, setTranscriptLoading] = useState(false);
  const [transcript, setTranscript] = useState();
  const [currentRecordingId, setCurrentRecordingId] = useState('');
  const [audioUrl, setAudioUrl] = useState('');
  // const isUAT = apiConfig.isUAT;
  const id = searchParams?.get('id') || '';
  const type = searchParams?.get('type') || undefined;
  const tab = searchParams?.get('tab') || 'info';
  const qaId = searchParams?.get('qaId') || '';

  const {
    showStrandScript,
    openedQA,
    formId,
    standardScriptDirection,
    toggleDirection,
    currentPosition,
    updateCurrentPosition,
  } = useQM();

  const { permissions } = usePermission();

  // call the assign evaluation form template API, method: GET
  const { data: evaluationFormData, isLoading: isLoadingEvaluationForm } =
    useQuery({
      queryKey: ['evaluationForm', currentRecordingId],
      queryFn: async () =>
        await fireGetEvaluationFormTemplate(basePath).then((res) => res.data),
      enabled: !!currentRecordingId,
    });

  // call the conversation detail info API, method: GET
  const { data, isLoading, error } = useQuery({
    queryKey: ['recordings', id],
    queryFn: async () =>
      await fireGetSingleRecording(id, basePath).then((res) => res.data),
    enabled: !!id,
  });

  const { data: qmEvaluationList } = useQuery({
    queryKey: ['qmEvaluationList', currentRecordingId],
    queryFn: async () =>
      await fireGetQmEvaluationList(currentRecordingId, basePath).then(
        (res) => res.data
      ),
    enabled: !!currentRecordingId,
    refetchInterval: 5 * 60 * 1000, // 5 minutes
  });

  const details = data?.data;

  const qmVisitPermission = new QmVisitPermission(globalConfig, permissions);

  // useEffect(() => {
  //   if (details?.transcript) {
  //     setTranscript(details?.transcript);
  //   }
  // }, [details]);

  const speakers = getSpeakers(transcript);

  const [pos, setPos] = useState(0);

  // query strand script result by formId
  const { data: standScriptResult, isPending: isLoadingStandScriptResult } =
    useQuery({
      queryKey: ['standScriptResult', openedQA],
      queryFn: async () => {
        const res = await fireGetStandScriptResult(formId, basePath);
        return res?.data ? res.data : { data: [] };
      },
      enabled: !!openedQA,
    });

  // FOR STREAM
  // const recordingId = mp3Data?.data ? mp3Data?.data[0]?.id : null;

  // FOR STREAM
  // useEffect(() => {
  //   if (recordingId) {
  //     console.log('recordingId', recordingId);
  //     fireGetSingleRecordingStream(recordingId, basePath).then((res) => {
  //       console.log('fireGetSingleRecordingStream ===> res', res);
  //       if (res.status === 200) {
  //         const audioBlob = new Blob([res.data], { type: 'audio/mpeg' });
  //         const audioUrl = URL.createObjectURL(audioBlob);
  //         console.log('audioUrl', audioUrl);
  //         setAudioUrl(audioUrl);
  //       }
  //     });
  //   }
  // }, [recordingId, basePath]);

  const getLettersFromUsername = (username: string, length = 2) => {
    let output = '';
    for (let i = 0; i < length; i++) {
      const letter = username?.[i];
      if (letter) {
        output += letter.toUpperCase();
      }
    }
    return output || 'N/A';
  };

  // const lastSpeaker = '';

  // QA / Evaluations
  const [assignEvaluationPopupOpen, setAssignEvaluationPopupOpen] =
    useState(false);
  const [reAssignEvaluationPopupOpen, setReAssignEvaluationPopupOpen] =
    useState(false);
  const openAssignEvaluationPopup = () => {
    setAssignEvaluationPopupOpen(true);
  };
  const openReAssignEvaluationPopup = () => {
    setReAssignEvaluationPopupOpen(true);
  };

  /**
   * Render wrapups as a list of WrapUp components.
   * @param {Wrapups} wrapups A dictionary of wrapup names to lists of wrapup items.
   * @returns A list of WrapUp components.
   *
   * If wrapups is an empty object or null, return a message indicating that the wrapups are not available.
   * Otherwise, render a list of WrapUp components, where each component represents a wrapup and contains a list of wrapup items.
   */
  const renderWrapups = (wrapups: Wrapups) => {
    if (!wrapups || Object.keys(wrapups).length == 0 || !wrapups?.wrapList)
      return (
        <div className="truncate text-[#636363]">
          {t('ctint-mf-interaction.details.notAvailable')}
        </div>
      );

    if (wrapups?.wrapList) {
      return wrapups?.wrapList?.map((wrapup: Wrapup[], index: number) => {
        return (
          <WrapUp
            key={index}
            wrapup={wrapup}
          />
        );
      });
    }
  };

  const renderParticipants = (participants: TParticipants) => {
    if (!participants || Object.keys(participants).length == 0)
      return (
        <div className="truncate text-[#636363]">
          {t('ctint-mf-interaction.details.notAvailable')}
        </div>
      );
    return Object.keys(participants).map((key: string) => {
      return (
        <StorageBox
          key={key}
          title={key}
          data={participants[key] ? participants[key] : { name: 'N/A' }}
        />
      );
    });
  };

  // 定义一个映射函数，将字符串状态映射到允许的状态
  const mapStatusToAllowedValues = (
    status: string
  ): 'published' | 'completed' | 'inprogress' | 'init' | 'failed' => {
    switch (status) {
      case 'published':
      case 'completed':
      case 'inprogress':
      case 'init':
      case 'failed':
        return status;
      default:
        return 'failed'; // 或者选择一个默认值
    }
  };

  const renderRightPanel = (type: string | undefined) => {
    console.log('renderRightPanel ===> type', type);
    if (!type) return null;
    switch (type.toLowerCase()) {
      case 'whatsapp':
        return <WhatsAppPanel conversationId={id} />;
      case 'voice':
        return (
          <CallPanel
            conversationType={type}
            standardScriptDirection={standardScriptDirection}
            showStrandScript={showStrandScript || false}
            setCurrentRecordingId={setCurrentRecordingId}
            updateCurrentPosition={updateCurrentPosition}
          />
        );
      case 'email':
        return <EmailPanel conversationId={id} />;
      default:
        return null;
    }
  };

  return (
    <div className="relative flex flex-col h-full">
      <div className="mb-4 flex items-center gap-2">
        <Button
          asSquare
          variant="back"
          onClick={() => toPath(`/interaction`)}
          beforeIcon={<Icon name="back" />}
        />
        <Breadcrumb
          items={[
            {
              label: t('ctint-mf-interaction.filter.search'),
              link: '/',
            },
            {
              label: error || !id ? 'Error' : `${id}`,
              link: '',
            },
          ]}
        />
      </div>
      {error || !id ? (
        <Panel containerClassName="h-full">
          <div className="p-4">
            <h2 className="mb-2 text-t6 font-bold">{`Error loading data: ${
              id ? error?.message : 'No valid ID.'
            }`}</h2>
          </div>
        </Panel>
      ) : (
        <>
          {isLoading ? (
            <Panel
              loading={isLoading}
              containerClassName="h-full"
            />
          ) : (
            <>
              <ResizablePanelGroup
                autoSaveId="playback-detail-panel"
                direction="horizontal"
                className="flex-1 flex w-full h-0 gap-x-3 overflow-auto"
              >
                <ResizablePanel minSize={30}>
                  <Panel
                    loading={isLoading}
                    containerClassName="h-full"
                  >
                    <Tabs
                      defaultTab={tab}
                      triggers={
                        // 如果沒有權限，就不顯示QM的tab
                        qmVisitPermission.isPermissionEnabled(
                          mfName,
                          'qm',
                          'visit'
                        )
                          ? [
                              {
                                value: 'info',
                                label: t('ctint-mf-interaction.details.info'),
                              },
                              {
                                value: 'qa',
                                label: t('ctint-mf-interaction.details.qm'),
                              },
                            ]
                          : [
                              {
                                value: 'info',
                                label: t('ctint-mf-interaction.details.info'),
                              },
                            ]
                      }
                      triggerClassName="py-1 px-2 text-body"
                    >
                      <TabsContent
                        value={'info'}
                        className="overflow-y-auto p-0"
                      >
                        {/* Interaction Metrics */}
                        <section>
                          <div>
                            <h2 className="p-4 text-t6 font-bold text-other-orange">
                              {t('ctint-mf-interaction.details.metrics')}
                            </h2>
                          </div>
                          <div className="px-4 grid grid-cols-2 gap-6">
                            {/* Conversation Type */}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconType
                                  alt="Media Type"
                                  size="24"
                                  color="black"
                                />
                              </div>
                              <section className="grid grid-rows-2 grid-flow-col gap-2">
                                <div className="row-span-1 col-span-11">
                                  <strong>
                                    {t(
                                      'ctint-mf-interaction.columns.mediaType'
                                    )}
                                    :
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>
                                    {camelCaseToWords(
                                      details?.conversationType
                                    ) || 'N/A'}
                                  </p>
                                </div>
                              </section>
                            </section>

                            {/* Start Time */}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconStartTime
                                  alt="Start Time"
                                  size="24"
                                  color="black"
                                />
                              </div>
                              <section className="grid grid-rows-2 grid-flow-col gap-2">
                                <div className="row-span-1 col-span-11">
                                  <strong>
                                    {t(
                                      'ctint-mf-interaction.columns.conversationStart'
                                    )}
                                    :
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>
                                    {details?.conversationStart
                                      ? dayjs(
                                          details?.conversationStart
                                        ).format(GLOBAL_DATETIME_FORMAT)
                                      : 'N/A'}{' '}
                                  </p>
                                </div>
                              </section>
                            </section>

                            {/* End Time */}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconEndTime
                                  alt="End Time"
                                  size="24"
                                  color="black"
                                />
                              </div>
                              <section className="grid grid-rows-2 grid-flow-col gap-2">
                                <div className="row-span-1 col-span-11">
                                  <strong>
                                    {t(
                                      'ctint-mf-interaction.columns.conversationEnd'
                                    )}
                                    :
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>
                                    {details?.conversationEnd
                                      ? dayjs(details?.conversationEnd).format(
                                          GLOBAL_DATETIME_FORMAT
                                        )
                                      : 'N/A'}{' '}
                                  </p>
                                </div>
                              </section>
                            </section>

                            {/* Conversation ID */}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconInteractionId
                                  alt="Interaction Id"
                                  size="24"
                                  color="black"
                                />
                              </div>
                              <section className="grid grid-rows-2 grid-flow-col gap-2">
                                <div className="row-span-1 col-span-11">
                                  <strong>
                                    {t('ctint-mf-interaction.columns.id')}:
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>{details?.conversationId || 'N/A'}</p>
                                </div>
                              </section>
                            </section>

                            {/* Initiator */}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconDirection
                                  alt="Direction"
                                  size="24"
                                  color="black"
                                />
                              </div>
                              <section className="grid grid-rows-2 grid-flow-col gap-2">
                                <div className="row-span-1 col-span-11">
                                  <strong>
                                    {t(
                                      'ctint-mf-interaction.columns.direction'
                                    )}
                                    :
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>
                                    {details?.originatingDirection || 'N/A'}
                                  </p>
                                </div>
                              </section>
                            </section>

                            {/* Duration */}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconDuration
                                  alt="Duration"
                                  size="24"
                                  color="black"
                                />
                              </div>
                              <section className="grid grid-rows-2 grid-flow-col gap-2">
                                <div className="row-span-1 col-span-11">
                                  <strong>
                                    {t(
                                      'ctint-mf-interaction.columns.conversationDuration'
                                    )}
                                    :
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>
                                    {secondsToTimeDisplay(details?.duration, [
                                      t('ctint-mf-interaction.details.hours'),
                                      t('ctint-mf-interaction.details.minutes'),
                                      t('ctint-mf-interaction.details.seconds'),
                                    ]) || 'N/A'}
                                  </p>
                                </div>
                              </section>
                            </section>

                            {/* Queue */}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconQueue
                                  alt="Queue"
                                  size="24"
                                  color="black"
                                />
                              </div>
                              <section className="grid grid-rows-4 grid-flow-col gap-2">
                                <div className="row-span-1 col-span-11">
                                  <strong>
                                    {t('ctint-mf-interaction.columns.queues')}:
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>{details?.queue || 'N/A'}</p>
                                </div>
                              </section>
                            </section>

                            {/* Internal Participant(s) */}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconUsers
                                  alt="Internal Participant"
                                  size="24"
                                  color="black"
                                />
                              </div>
                              <section className="grid grid-rows-4 grid-flow-col gap-2">
                                <div className="row-span-1 col-span-11">
                                  <strong>
                                    {t(
                                      'ctint-mf-interaction.details.internalParticipantTitle'
                                    )}
                                    :
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>
                                    {details?.internalName &&
                                    details?.internalName.length > 0
                                      ? details?.internalName
                                      : '--'}
                                  </p>
                                </div>
                                <div className="row-span-1 col-span-11">
                                  <strong>
                                    {t(
                                      'ctint-mf-interaction.details.disconnectReasonTitle'
                                    )}
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>
                                    {details?.internalDisconnectReason &&
                                    details?.internalDisconnectReason.length > 0
                                      ? details?.internalDisconnectReason
                                      : '--'}
                                  </p>
                                </div>
                              </section>
                            </section>

                            {/* External Participant(s) */}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconUsers
                                  alt="Internal Participant"
                                  size="24"
                                  color="black"
                                />
                              </div>
                              <section className="grid grid-rows-4 grid-flow-col gap-2">
                                <div className="row-span-1 col-span-11">
                                  <strong>
                                    {t(
                                      'ctint-mf-interaction.details.externalParticipantTitle'
                                    )}
                                    :
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>
                                    {details?.externalName &&
                                    details?.externalName.length > 0
                                      ? details?.externalName
                                      : '--'}
                                  </p>
                                </div>
                                <div className="row-span-1 col-span-11">
                                  <strong>
                                    {t(
                                      'ctint-mf-interaction.details.disconnectReasonTitle'
                                    )}
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>
                                    {details?.externalDisconnectType &&
                                    details?.externalDisconnectType.length > 0
                                      ? details?.externalDisconnectType
                                      : '--'}
                                  </p>
                                </div>
                              </section>
                            </section>
                          </div>
                        </section>

                        {/* Wrap-ups */}
                        <section>
                          <div>
                            <h2 className="p-4 text-t6 font-bold text-other-orange">
                              {t('ctint-mf-interaction.details.wrapups')}
                            </h2>
                          </div>
                          <section className="px-4">
                            {/* {renderWrapups(details?.wrapups || {})} */}
                            {renderWrapups(details?.wrapups || {})}
                            {/* render Wrapups remark */}
                            <p className="px-3">
                              {details?.wrapups?.remark
                                ? details?.wrapups?.remark
                                : ''}
                            </p>
                          </section>
                        </section>

                        {/* Participant Data */}
                        <section>
                          <div>
                            <h2 className="p-4 text-t6 font-bold text-other-orange">
                              {t('ctint-mf-interaction.details.participant')}
                            </h2>
                          </div>
                          <section className="px-4">
                            {renderParticipants(details?.attributes || {})}
                          </section>
                        </section>
                      </TabsContent>
                      <TabsContent
                        value={'qa'}
                        className="overflow-y-auto p-0 h-full"
                      >
                        <QMPanel
                          defaultOpenedQA={qaId}
                          currentRecordingId={currentRecordingId}
                          onAssign={openAssignEvaluationPopup}
                          onReassign={openReAssignEvaluationPopup}
                          data={
                            qmEvaluationList?.data.map(
                              (item: QmEvaluationListItem) => ({
                                ...item,
                                status: mapStatusToAllowedValues(item.status),
                              })
                            ) || []
                          }
                        />
                      </TabsContent>
                    </Tabs>
                  </Panel>
                </ResizablePanel>
                <ResizableHandle />
                <ResizablePanel
                  minSize={30}
                  className="h-full"
                >
                  {renderRightPanel(type)}
                </ResizablePanel>
              </ResizablePanelGroup>
              <EvaluationFormListProvider<QmFormList>
                formList={
                  evaluationFormData?.data ? evaluationFormData : { data: [] }
                }
              >
                <AssignEvaluationPopup
                  currentRecordingId={currentRecordingId}
                  selectedIntergrations={[details]}
                  open={assignEvaluationPopupOpen}
                  onOpenChange={(v) => setAssignEvaluationPopupOpen(v)}
                />

                <AssignEvaluationPopup
                  currentRecordingId={currentRecordingId}
                  selectedIntergrations={[details]}
                  open={reAssignEvaluationPopupOpen}
                  onOpenChange={(v) => setReAssignEvaluationPopupOpen(v)}
                />
              </EvaluationFormListProvider>
            </>
          )}
        </>
      )}
    </div>
  );
};
// Create a client
const queryClient = new QueryClient();

export const TableDemoDetail = () => (
  <QueryClientProvider client={queryClient}>
    <QMProvider defaultFormAns={DUMMY_DEFAULT_ANSWERS}>
      <TableDemoDetailBody />
    </QMProvider>
  </QueryClientProvider>
);

export default TableDemoDetail;
