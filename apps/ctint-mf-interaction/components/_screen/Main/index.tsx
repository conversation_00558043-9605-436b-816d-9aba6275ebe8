'use client';
import React, { useCallback, useEffect } from 'react';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import _ from 'lodash';
import dayjs from 'dayjs';
import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';
import {
  QueryClient,
  QueryClientProvider,
  useQuery,
} from '@tanstack/react-query';
import { Table as TableType } from '@tanstack/react-table';
import SortingButton from '@cdss-modules/design-system/components/_ui/SortingButton';
import { useState } from 'react';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import { RCustomer as Customer } from '../../../lib/graphql/schema';
import { ColumnDef } from '@tanstack/react-table';
import {
  camelCaseToWords,
  secondsToTimeDisplay,
} from '@cdss-modules/design-system/lib/utils';
import Checkbox from '@cdss-modules/design-system/components/_ui/Checkbox';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';

import Panel from '@cdss-modules/design-system/components/_ui/Panel';
import {
  mfLogger,
  useRole,
  useRouteHandler,
} from '@cdss-modules/design-system';
import { toast } from '@cdss-modules/design-system/components/_ui/Toast/use-toast';
import Pagination from '@cdss-modules/design-system/components/_ui/Pagination';
import {
  Sheet,
  SideType,
} from '@cdss-modules/design-system/components/_ui/Sheet';
import Draggable from '@cdss-modules/design-system/components/_ui/Draggable';
import {
  GLOBAL_DATETIME_FORMAT,
  GLOBAL_DATETIME_SECOND_FORMAT,
} from '@cdss-modules/design-system/lib/constants';
import {
  fireGetFilteredRecordings,
  fireGetUserConfig,
  downloadRecordingZipFile,
  fireUpdateUserConfig,
  fireCreateUserConfig,
} from '../../../lib/api';

import TableSelectedMenu from '../../_ui/TableSelectedMenu';
import { WhitePanel } from '@cdss-modules/design-system/components/_ui/WhitePanel';
import SearchInput from '@cdss-modules/design-system/components/_ui/SearchInput';
import PopoverMenu from '@cdss-modules/design-system/components/_ui/PopoverMenu';
import { TIcon, CreateIcon } from '../../CreateIcon';
import EvaluationAction from '../../_ui/EvaluationAction';
import { ICriteria } from '@cdss-modules/design-system/@types/config';
import FilterComponent, {
  Condition,
} from '@cdss-modules/design-system/components/_ui/FilterComponent';
import QueryBuilder from '@cdss-modules/design-system/@types/QueryBuilder';
import { Toaster } from '@cdss-modules/design-system/components/_ui/Toast/toaster';
import utc from 'dayjs/plugin/utc';
dayjs.extend(utc);
// dayjs.extend(timezone);
// const tz = 'Asia/Shanghai';

// Types
type TFilterFields = string | 'isInbound' | 'isOutbound';

export type TFilterItem = {
  name: TFilterFields;
  label: string;
  type: string;
};

export type TSortOrder = 'asc' | 'desc';

type TOrderByInput = {
  [K in string]?: TSortOrder;
};

type TSheetProps = {
  id: string;
  open: boolean;
  side: SideType;
};

const generateColumns = (
  columns: ICriteria[],
  sortOrder: TOrderByInput | undefined,
  setSortOrder: (input: TOrderByInput) => void,
  basePath = '',
  t: any,
  actions: any,
  i18n: any
) => {
  const selectionCol = {
    id: 'select',
    header: ({ table }: any) => (
      <div
        className="bg-white z-30"
        onClick={(e) => e.stopPropagation()}
      >
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onChange={(e: any) => {
            const isSelected = e?.target.checked;
            table.toggleAllPageRowsSelected(isSelected);
          }}
        />
      </div>
    ),
    cell: ({ row }: any) => (
      <div
        className="z-0"
        onClick={(e) => e.stopPropagation()}
      >
        <Checkbox
          checked={
            row.getIsSelected() &&
            row.original.mediaType !== 'whatsapp' &&
            row.original.mediaType !== 'email'
          }
          disabled={
            row.original.mediaType === 'whatsapp' ||
            row.original.mediaType === 'email'
          }
          onChange={(e: any) => {
            const isSelected = e?.target.checked;
            row.toggleSelected(isSelected);
          }}
        />
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  };
  const actionCol = {
    id: 'action',
    header: () => <div className="w-full"></div>,
    cell: () => (
      <div
        className="flex gap-x-2 z-0"
        // onClick={(e) => e.stopPropagation()}
      >
        <Button
          size="s"
          // onClick={(e: any) => {
          //   e.stopPropagation();
          //   actions?.assignEvaluation();
          // }}
        >
          View
        </Button>
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  };

  const formattedColumns = columns.map((column) => {
    const isDate =
      column.value === 'conversationStart' ||
      column.value === 'conversationEnd';
    const isDuration = column.value === 'conversationDuration';

    return {
      id: column.value,
      accessorKey: column.value,
      header: () => {
        if (column.value === 'mediaType') {
          return (
            <strong className="truncate">
              {i18n.language === 'en'
                ? column.labelEn
                : column.labelCh
                  ? column.labelCh
                  : camelCaseToWords(column.value)}
            </strong>
          );
        }

        return (
          <SortingButton
            sorting={
              sortOrder?.[column.value]
                ? sortOrder?.[column.value] === 'asc'
                  ? 'asc'
                  : 'desc'
                : false
            }
            onClick={async () => {
              const targetSortOrder =
                sortOrder?.[column.value] === 'asc' ? 'desc' : 'asc';
              setSortOrder({
                [column.value]: targetSortOrder,
              });
            }}
          >
            <div className="truncate">
              {i18n.language === 'en'
                ? column.labelEn
                : column.labelCh
                  ? column.labelCh
                  : camelCaseToWords(column.value)}
            </div>
          </SortingButton>
        );
      },
      cell: ({ row }) => {
        const thisId = row.original.conversationId;
        let val = row.getValue(column.value) as any;
        if (isDate)
          val = val == '' ? '--' : dayjs(val).format(GLOBAL_DATETIME_FORMAT);
        if (isDuration) val = secondsToTimeDisplay(parseFloat(val));

        // Dynamic show media type icon
        if (column.value === 'mediaType') {
          const matchedKey = Object.keys(TIcon).find((key) => key === val);
          if (matchedKey) {
            return <CreateIcon iconType={matchedKey} />;
          }

          return <div className="capitalize">{val ? val : '--'}</div>;
        }

        if (column.value === 'recording' || column.value === 'attachment') {
          return <>{val === true ? 'YES' : 'NO'}</>;
        }

        if (column.value === 'evaluation') {
          return (
            <div className="flex gap-2 items-center z-0">
              <EvaluationAction
                evaluation={{ ...val, interactionId: thisId }}
                openAssignEvaluationPopup={() =>
                  actions?.assignEvaluation(thisId)
                }
              />
            </div>
          );
        }

        return <div className="capitalize">{val ? val : '--'}</div>;
      },
    } as ColumnDef<Customer>;
  });

  return [
    selectionCol,
    ...formattedColumns,
    //actionCol
  ];
};

export const TableDemoBody = () => {
  const { userConfig, globalConfig } = useRole();
  const { t, i18n } = useTranslation();
  const userPermissions = userConfig?.permissions;
  const permission = userPermissions?.join(',');
  const [table, setTable] = useState<TableType<Customer>>();
  const [rowSelection, setRowSelection] = useState({});
  const [downloadLoading, setDownloadLoading] = useState(false);
  const [defaultColumns, setDefaultColumns] = useState<ICriteria[]>([]);

  const { toPath, basePath } = useRouteHandler();
  const [sortOrder, setSortOrder] = useState<TOrderByInput>();
  const [filterFields, setFilterFields] = useState<TFilterFields[]>([]);
  const [filterInput, setFilterInput] = useState<any>({});

  const [tempColumnOrdering, setTempColumnOrdering] = useState<ICriteria[]>([]);

  const [columnOrdering, setColumnOrdering] =
    useState<ICriteria[]>(tempColumnOrdering);

  const [panelTwoDropdownOpen, setPanelTwoDropdownOpen] = useState(false);
  const [isSheetOpen, setIsSheetOpen] = useState<TSheetProps>({
    id: 'right-side',
    open: false,
    side: 'right',
  });

  const initCriteria =
    globalConfig?.services['ctint-conv']?.criteriaSearch ?? [];
  // display column 和 filter 的 criteria，后面会根据userconfig来更新
  const [criteria, setCriteria] = useState<ICriteria[]>(initCriteria);

  const [filterValues, setFilterValues] = useState<Record<string, Condition>>(
    {}
  );

  const [searchConditions, setSearchConditions] = useState<Record<string, any>>(
    {}
  );
  const [searchTrigger, setSearchTrigger] = useState(0);

  const [showPdf, setShowPdf] = useState(false);

  useEffect(() => {
    // 设置默认显示噶filter的field
    setDefaultColumns(criteria.filter((item) => item.active));
  }, [criteria]);

  useEffect(() => {
    generatePostData();
  }, [sortOrder]);

  useEffect(() => {
    // generatePostData();
  }, [filterValues]);

  const [isPreferenceLoaded, setIsPreferenceLoaded] = useState(false);

  useEffect(() => {
    // 第一次加载时，isPreferenceLoaded 为 true，会调用 generatePostData
    if (isPreferenceLoaded) {
      generatePostData();
      setIsPreferenceLoaded(false);
    }
  }, [isPreferenceLoaded]);

  const userconfig = useQuery({
    queryKey: ['userconfig'],
    queryFn: async () => fireGetUserConfig(basePath).then((res) => res.data),
  });

  const userPreference = userconfig?.data;

  const handlePanelTwoDropdownMenu = () => {
    setPanelTwoDropdownOpen(!panelTwoDropdownOpen);
  };

  const handleSheetOpen = (
    id = 'right-side',
    open: boolean,
    side: SideType = 'right'
  ) => {
    setIsSheetOpen({ id: id, open: open, side: side });
  };

  const handleOrder = (allColumns: ICriteria[]) => {
    const newFilterValues = allColumns
      .filter((item) => item.active)
      .reduce(
        (acc, item) => {
          acc[item.value] = {
            ...item,
            data: filterValues[item.value]?.data ?? '',
            checked: filterValues[item.value]?.checked ?? false,
            labelCh: item.labelCh ?? '',
            labelEn: item.labelEn ?? '',
          };
          return acc;
        },
        {} as Record<string, Condition>
      );
    setFilterValues(newFilterValues);

    setCriteria(allColumns);
  };

  const applyFilter = () => {
    setCurrentPage(1); // reset page to 1，avoid the reuslt show empty issue
    generatePostData();
    setSearchTrigger(searchTrigger + 1);
    mfLogger.info({ detail: filterInput });
  };

  const clearFilter = () => {
    setFilterValues(
      Object.keys(filterValues).reduce(
        (acc: Record<string, Condition>, key: string) => {
          if (
            filterValues[key].value === 'conversationStart' ||
            filterValues[key].value === 'conversationEnd'
          ) {
            acc[key] = {
              ...filterValues[key],
              data:
                filterValues[key].value === 'conversationStart'
                  ? dayjs().startOf('day').format(GLOBAL_DATETIME_SECOND_FORMAT)
                  : dayjs()
                      .startOf('day')
                      .add(1, 'day')
                      .format(GLOBAL_DATETIME_SECOND_FORMAT),
            };
          } else {
            acc[key] = {
              ...filterValues[key],
              data: undefined,
              checked: false,
            };
          }
          return acc;
        },
        {}
      )
    );
    setSortOrder(undefined);
  };

  const savePreference = (
    target: 'filters' | 'columns',
    clearMode?: boolean
  ) => {
    let config = {};
    let jsonParsedUserConfig;

    const filters = filterValues;

    const columns = criteria;

    config = {
      filters,
      columns,
    };

    if (userPreference?.data?.userConfig) {
      jsonParsedUserConfig = JSON.parse(
        userPreference?.data?.userConfig || '{}'
      ); // { filters: []} || { columns: []}

      const removeLabel = (columns: ICriteria[]) => {
        return columns.map(({ labelCh, labelEn, ...rest }) => rest);
      };

      const removeLabelFromFilters = (filters: Record<string, Condition>) => {
        return Object.fromEntries(
          Object.entries(filters).map(
            ([key, { labelCh, labelEn, ...rest }]) => [key, rest]
          )
        );
      };

      config =
        target === 'filters'
          ? {
              filters: clearMode ? [] : removeLabelFromFilters(filters),
              columns: removeLabel(columns),
            }
          : {
              filters: {
                ...(jsonParsedUserConfig.filters ?? {}),
                ...removeLabelFromFilters(filters),
              },
              columns: clearMode ? [] : removeLabel(columns),
            };
    }

    if (userPreference?.data?.userConfig) {
      fireUpdateUserConfig(config, basePath).then(() => {
        queryClient.invalidateQueries({
          queryKey: ['userconfig'],
        });
        setCriteria(initCriteria);
        toast({
          title: 'Success',
          description: 'Saved preference',
        });
      });
    } else {
      fireCreateUserConfig(config, basePath).then(() => {
        queryClient.invalidateQueries({
          queryKey: ['userconfig'],
        });
        setCriteria(initCriteria);
        toast({
          title: 'Success',
          description: 'Created preference',
        });
      });
    }
  };

  const loadPreference = () => {
    const savedPreference = userPreference?.data;

    if (savedPreference) {
      const jsonParsedUserConfig = JSON.parse(
        userPreference?.data?.userConfig || '{}'
      );

      /**
       * Add label to the columns.
       * Since the Chinese and English characters in
       * the API error message are garbled, read the
       * text of the global config
       * @param columns remote config columns
       * @returns columns with label
       */
      const addLabel = (columns: ICriteria[]) => {
        return columns.map((column) => {
          const matchedCriteria = criteria.find(
            (item) => item.value === column.value
          );
          return {
            ...column,
            labelEn: matchedCriteria?.labelEn,
            labelCh: matchedCriteria?.labelCh,
          };
        });
      };

      const addLabelToFilters = (filters: Record<string, Condition>) => {
        const result = Object.fromEntries(
          Object.entries(filters).map(
            ([key, { labelCh, labelEn, ...rest }]) => {
              const matchedCriteria = criteria.find(
                (item) => item.value === key
              );
              return [
                key,
                {
                  ...rest,
                  labelCh: matchedCriteria?.labelCh,
                  labelEn: matchedCriteria?.labelEn,
                },
              ];
            }
          )
        );
        return result;
      };

      const columns =
        jsonParsedUserConfig?.columns &&
        jsonParsedUserConfig?.columns?.length > 0
          ? addLabel(jsonParsedUserConfig?.columns)
          : criteria;

      const filters = jsonParsedUserConfig?.filters;

      setCriteria(columns);

      // 获取东八区今日 12:00 AM
      const today12AM = dayjs()
        .startOf('day')
        .format(GLOBAL_DATETIME_SECOND_FORMAT);

      // 获取东八区明日 12:00 AM
      const tomorrow12AM = dayjs()
        .startOf('day')
        .add(1, 'day')
        .format(GLOBAL_DATETIME_SECOND_FORMAT);

      const initialFilterValues = columns
        .filter((filter: ICriteria) => filter.active)
        .reduce(
          (acc: Record<string, Condition>, filter: ICriteria) => {
            acc[filter.value] = {
              ...filter,
              labelCh: filter.labelCh ?? '',
              labelEn: filter.labelEn ?? '',
              data:
                filter.value === 'conversationStart'
                  ? today12AM
                  : filter.value === 'conversationEnd'
                    ? tomorrow12AM
                    : undefined,
              checked:
                filter.value === 'conversationStart' ||
                filter.value === 'conversationEnd'
                  ? true
                  : false,
            }; // Initialize all filter values to empty
            return acc;
          },
          {} as Record<string, Condition>
        );

      if (filters && Object.keys(filters).length > 0) {
        setFilterValues(
          addLabelToFilters(filters) as Record<string, Condition>
        );
      } else {
        setFilterValues(
          addLabelToFilters(initialFilterValues) as Record<string, Condition>
        );
      }
    } else {
      // call userconfig api fail
      // 获取东八区今日 12:00 AM
      const today12AM = dayjs()
        .startOf('day')
        .format(GLOBAL_DATETIME_SECOND_FORMAT);

      // 获取东八区明日 12:00 AM
      const tomorrow12AM = dayjs()
        .startOf('day')
        .add(1, 'day')
        .format(GLOBAL_DATETIME_SECOND_FORMAT);

      const initialFilterValues = defaultColumns.reduce(
        (acc, filter) => {
          acc[filter.value] = {
            ...filter,
            labelEn: filter.labelEn ?? '',
            labelCh: filter.labelCh ?? '',
            data:
              filter.value === 'conversationStart'
                ? today12AM
                : filter.value === 'conversationEnd'
                  ? tomorrow12AM
                  : undefined,
            checked:
              filter.value === 'conversationStart' ||
              filter.value === 'conversationEnd'
                ? true
                : false,
          }; // Initialize all filter values to empty
          return acc;
        },
        {} as Record<string, Condition>
      );
      setFilterValues(initialFilterValues as Record<string, Condition>);
    }

    // 设置 isPreferenceLoaded 为 true，以便在 useEffect 中调用 generatePostData
    setIsPreferenceLoaded(true);
  };

  const clearPreference = (target: 'filters' | 'columns') => {
    savePreference(target, true);
    if (target === 'filters') {
      clearFilter();
    } else {
      setTempColumnOrdering(defaultColumns);
      setColumnOrdering(defaultColumns);
    }
    toast({
      title: 'Success',
      description:
        target === 'filters'
          ? 'Cleared saved filters'
          : 'Cleared saved columns',
    });
  };

  useEffect(() => {
    loadPreference();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [permission, userPreference]);

  const [currentPage, setCurrentPage] = useState<number>(1);
  const [perPage, setPerPage] = useState<number>(10);

  const pageSize = perPage;
  const page = currentPage;

  useEffect(() => {
    generatePostData();
  }, [pageSize, page]);

  const generatePostData = useCallback(() => {
    const today12AM = dayjs()
      .startOf('day')
      .format(GLOBAL_DATETIME_SECOND_FORMAT);

    const queryBuilder = new QueryBuilder(
      dayjs(today12AM).utc().format('YYYY-MM-DDTHH:mm:ss[Z]'),
      pageSize,
      1
    );
    queryBuilder.setFilters(
      (filterValues['conversationId']?.data as string) &&
        filterValues['conversationId'].checked
        ? (filterValues['conversationId']?.data as string)
        : '',
      filterValues['conversationStart']?.data
        ? dayjs(filterValues['conversationStart']?.data as string)
            .utc()
            .format('YYYY-MM-DDTHH:mm:ss[Z]')
        : queryBuilder.build()['conversationStart'],
      filterValues['conversationEnd']?.data
        ? dayjs(filterValues['conversationEnd']?.data as string)
            .utc()
            .format('YYYY-MM-DDTHH:mm:ss[Z]')
        : '',
      Object.keys(filterValues)
        .filter(
          (key) =>
            key !== 'conversationStart' &&
            key !== 'conversationEnd' &&
            key !== 'conversationId' &&
            filterValues[key].data &&
            filterValues[key].checked &&
            filterValues[key].data !== ''
        )
        .map((key) => {
          const item: {
            type: string;
            name: string;
            value: any;
            rule?: string;
          } = {
            type: 'and',
            name: filterValues[key].value,
            value: filterValues[key].data ?? '',
          };
          if (filterValues[key].rule) {
            item['rule'] = filterValues[key].rule;
          }

          return item;
        })
    );
    queryBuilder.setPagination({
      pageSize: Number(pageSize),
      page: Number(page),
    });
    queryBuilder.setOrdering({
      order: sortOrder?.[Object.keys(sortOrder)[0]] ?? 'desc',
      orderBy: Object.keys(sortOrder ?? {})[0] ?? '',
    });
    setSearchConditions(queryBuilder.build());
  }, [filterValues, pageSize, page, sortOrder]);

  const {
    data,
    isLoading: loading,
    error,
  } = useQuery({
    queryKey: ['todos', searchConditions, searchTrigger],
    queryFn: () =>
      fireGetFilteredRecordings(searchConditions, basePath)?.then((res) => {
        return res.data;
      }),
  });

  const customerData =
    data?.data &&
    data?.data?.interactions &&
    (data?.data?.interactions ?? data?.data)?.filter((item: any) => {
      // TODO: this hardcoded filter for demo evaluation only, should be remove later
      const isChecked = filterFields.includes('evaluation'); //default is false
      const filterValue = filterInput?.['evaluation'];
      if (isChecked && filterValue) {
        if (filterValue === 'assignedToYou') {
          return item?.evaluation?.evaluator === 'Admin';
        }
        return item.evaluation.status === filterValue;
      }
      return true;
    });

  // QA / Evaluations
  const [assignEvaluationPopupOpen, setAssignEvaluationPopupOpen] =
    useState(false);
  const [selectedForAssignEvaluation, setSelectedForAssignEvaluation] =
    useState([]);
  const openAssignEvaluationPopup = (targetIds: string[]) => {
    const targetInteractions = customerData.filter((item: any) =>
      targetIds.includes(item.id)
    );
    setSelectedForAssignEvaluation(targetInteractions);
    setAssignEvaluationPopupOpen(true);
  };

  // Pagination
  const totalCount = data?.data?.totalCount ?? data?.totalCount ?? 0;
  const totalPages = Math.ceil(totalCount / perPage);

  const handleNext = () => {
    let tar = currentPage + 1;
    if (tar > totalPages) tar = totalPages;
    setCurrentPage(tar);
  };

  const handlePrevious = () => {
    let tar = currentPage - 1;
    if (tar < 1) tar = 1;
    setCurrentPage(tar);
  };

  /**
   * Get the comparison symbol based on the rule
   * @param rule - The rule to get the symbol for
   * @returns The comparison symbol
   *
   * eq: =
   * ge: ≥
   * le: ≤
   */
  const getComparisonSymbol = (rule: string | undefined) => {
    let symbol = '';
    switch (rule) {
      case 'eq':
        symbol = '=';
        break;
      case 'ge':
        symbol = '≥';
        break;
      case 'le':
        symbol = '≤';
        break;
      default:
        symbol = '=';
    }
    return symbol;
  };

  const renderTagItems = () => {
    // only show the Conversation ID item when it had been filtered
    if (
      filterValues['conversationId'] &&
      filterValues['conversationId'].checked &&
      filterValues['conversationId'].data &&
      filterValues['conversationId'].data !== ''
    ) {
      return (
        <div
          key={0}
          className="p-1 mr-1 mt-1 flex flex-row items-center border border-black rounded-[4px] text-[12px] text-black"
        >
          <span className="truncate">
            {i18n.language === 'en'
              ? filterValues['conversationId'].labelEn
              : filterValues['conversationId'].labelCh +
                ':' +
                filterValues['conversationId'].data}
          </span>
          <span
            onClick={() => {
              setFilterValues((prev) => {
                const newFilterValues = { ...prev };
                delete newFilterValues['conversationId'].data;
                newFilterValues['conversationId'].checked = false;
                return newFilterValues;
              });
            }}
          >
            <Icon name="cross" />
          </span>
        </div>
      );
    }

    const sortedFilters = [...Object.values(filterValues)].sort((a, b) => {
      if (a.value === 'conversationStart') return -1;
      if (b.value === 'conversationStart') return 1;
      if (a.value === 'conversationEnd') return -1;
      if (b.value === 'conversationEnd') return 1;
      return 0;
    });

    return sortedFilters.map((item, i) => {
      const key = item.value;
      if (!filterValues[key].checked || !filterValues[key].data) return null;
      return (
        <div
          key={i}
          className="p-1 mr-1 mt-1 flex flex-row items-center border border-black rounded-[4px] text-[12px] text-black"
        >
          {key === 'conversationDuration' && filterValues[key].rule ? (
            <span className="truncate">
              {(i18n.language === 'en'
                ? filterValues[key].labelEn
                : filterValues[key].labelCh) +
                ' ' +
                getComparisonSymbol(filterValues[key].rule) +
                ' ' +
                filterValues[key].data}
            </span>
          ) : (
            <span className="truncate">
              {(i18n.language === 'en'
                ? filterValues[key].labelEn
                : filterValues[key].labelCh) +
                ':' +
                (key === 'conversationStart' || key === 'conversationEnd'
                  ? dayjs(filterValues[key].data as string).format(
                      GLOBAL_DATETIME_SECOND_FORMAT
                    )
                  : filterValues[key].data)}
            </span>
          )}
          {key !== 'conversationStart' && key !== 'conversationEnd' && (
            <span
              onClick={() => {
                setFilterValues((prev) => {
                  const newFilterValues = { ...prev };
                  delete newFilterValues[key].data;
                  newFilterValues[key].checked = false;
                  return newFilterValues;
                });
              }}
              className="ml-1 cursor-pointer"
            >
              <Icon name="cross" />
            </span>
          )}
        </div>
      );
    });
  };

  const conbineFiltersTagName = () => {
    const tags: string[] = [];

    if (
      filterValues['conversationId'] &&
      filterValues['conversationId'].checked &&
      filterValues['conversationId'].data
    ) {
      tags.push(
        `${i18n.language === 'en' ? filterValues['conversationId'].labelEn : filterValues['conversationId'].labelCh}: ${filterValues['conversationId'].data}`
      );
      return tags;
    }

    const sortedFilters = [...Object.values(filterValues)].sort((a, b) => {
      if (a.value === 'conversationStart') return -1;
      if (b.value === 'conversationStart') return 1;
      if (a.value === 'conversationEnd') return -1;
      if (b.value === 'conversationEnd') return 1;
      return 0;
    });

    sortedFilters.map((item) => {
      const key = item.value;

      const symbol = getComparisonSymbol(filterValues[key].rule);
      if (filterValues[key].checked && filterValues[key].data) {
        if (key === 'conversationDuration' && filterValues[key].rule) {
          tags.push(
            `${i18n.language == 'en' ? filterValues[key].labelEn : filterValues[key].labelCh} ${symbol} ${filterValues[key].data}`
          );
        } else {
          tags.push(
            `${i18n.language == 'en' ? filterValues[key].labelEn : filterValues[key].labelCh}: ${
              key === 'conversationStart' || key === 'conversationEnd'
                ? dayjs(filterValues[key].data as string).format(
                    GLOBAL_DATETIME_SECOND_FORMAT
                  )
                : filterValues[key].data
            }`
          );
        }
      }
    });

    return tags;
  };

  return (
    <div
      data-testid="cypress-panel-title-filter"
      id="panelContainer"
      className="flex flex-col h-full w-full gap-4"
    >
      {/* Search Bar */}
      <WhitePanel className="inline-flex flex-row overflow-x-auto">
        <div className="flex-1 flex flex-row">
          {/* Search Filter Input Component */}
          <SearchInput tags={conbineFiltersTagName()}>
            {/* Filter Operation Popover Content */}
            <section>
              {/* Filter Operation Scroll Block */}
              <section className="max-h-[409px] w-[700px] overflow-y-auto">
                {/* Popover selected filters items */}
                <section className="p-4">
                  {Object.keys(filterValues).filter(
                    (key) => filterValues[key].data !== undefined
                  ).length > 0 && (
                    <div className="flex flex-wrap flex-row">
                      {renderTagItems()}
                    </div>
                  )}
                </section>
                {/* Popover filter input form */}
                <section className="px-4">
                  <h2 className="text-[14px] mb-2">
                    {t('ctint-mf-interaction.filter.available')}:
                  </h2>
                  <div className="flex flex-col gap-y-2">
                    <FilterComponent
                      filterValues={filterValues}
                      setFilterValues={setFilterValues}
                    />
                  </div>
                </section>
              </section>
              {/* Filter Items Operation Button */}
              <section className="max-h-[45px] px-4 py-1 flex flex-row-reverse w-full ">
                <Button
                  className="mx-1 z-0"
                  bodyClassName="py-[0.375rem]"
                  variant={'orange'}
                  onClick={() => clearPreference('filters')}
                  size="s"
                >
                  {t('ctint-mf-interaction.filter.clearSave')}
                </Button>
                <Button
                  className="mx-1 z-0"
                  bodyClassName="py-[0.375rem]"
                  variant={'orange'}
                  onClick={() => savePreference('filters')}
                  size="s"
                >
                  {t('ctint-mf-interaction.filter.save')}
                </Button>
              </section>
            </section>
          </SearchInput>
          {/* Search Button */}
          <Button
            className="self-center ml-2"
            bodyClassName="border border-black py-[0.375rem]"
            onClick={() => applyFilter()}
            size="s"
          >
            {t('ctint-mf-interaction.filter.search')}
          </Button>
          {/* Clear Tags Button */}
          <Button
            className="self-center ml-2"
            bodyClassName="border border-black py-[0.375rem]"
            onClick={() => clearFilter()}
            variant="blank"
            size="s"
          >
            {t('ctint-mf-interaction.filter.clear')}
          </Button>
        </div>
        {/* Table Operation Menu */}
        <PopoverMenu
          icon={
            <Icon
              name="verticalDots"
              className="self-center justify-end cursor-pointer mx-1 flex-shrink-0"
              size={23}
            />
          }
        >
          <div className="flex flex-col bg-white shadow-[0px_2px_10px_0px_rgba(0,0,0,0.18)]">
            <button
              className="m-2 flex gap-2 items-center w-full"
              onClick={() =>
                handleSheetOpen('right-side', !isSheetOpen.open, 'right')
              }
            >
              <Icon name="eye" />
              <span>{t('ctint-mf-interaction.filter.addColumns')}</span>
            </button>
            <div className="w-full h-[1px] bg-black"></div>
            <button
              onClick={() => savePreference('columns')}
              className="mx-2 mt-2 flex gap-2 items-center w-full"
            >
              <Icon name="save" />
              <span>{t('ctint-mf-interaction.filter.saveColumns')}</span>
            </button>
            <button
              onClick={() => clearPreference('columns')}
              className="m-2 flex gap-2 items-center w-full"
            >
              <Icon name="cross" />
              <span>{t('ctint-mf-interaction.filter.clearColumns')}</span>
            </button>
          </div>
        </PopoverMenu>
      </WhitePanel>
      {/* Interaction List Table */}
      <Panel
        headerClassName="bg-white border-b border-grey-200"
        containerClassName="flex flex-col flex-1 h-0"
        dropdownMenuOpen={panelTwoDropdownOpen}
        handleDropdownMenuOpen={handlePanelTwoDropdownMenu}
      >
        <div className="overflow-auto px-4 flex-1 h-full pt-4">
          <DataTable<Customer>
            data={loading ? [] : customerData ?? []}
            columns={generateColumns(
              defaultColumns,
              sortOrder,
              (input) => {
                setSortOrder(input);
              },
              basePath,
              t,
              {
                assignEvaluation: (id: string) =>
                  openAssignEvaluationPopup([id]),
              },
              i18n
            )}
            loading={loading}
            error={error?.message}
            rowSelection={rowSelection}
            setRowSelection={setRowSelection}
            onClickRow={(row) => {
              toPath(
                `/interaction/detail?id=${row.original.conversationId}&type=${row.original.mediaType}`
              );
            }}
            onTableSetUp={(table) => setTable(table)}
            resize
          />
        </div>
        {!loading &&
          customerData &&
          (totalPages > 0 || customerData.length > 0) && (
            <div className="flex items-center justify-between p-4">
              <div className="">
                <TableSelectedMenu
                  data={customerData}
                  popupAlign="start"
                  rowSelection={rowSelection}
                  show={
                    !_.isEmpty(
                      customerData.filter(
                        (item: any) =>
                          item.mediaType !== 'whatsapp' &&
                          item.mediaType !== 'email'
                      )
                    ) && !_.isEmpty(rowSelection)
                  }
                  onClear={() => table?.toggleAllRowsSelected(false)}
                  onClearOne={(rowId) => {
                    setRowSelection((prevSelection) => {
                      const updatedSelection: any = { ...prevSelection };
                      delete updatedSelection[rowId];
                      return updatedSelection;
                    });
                  }}
                  onDownload={() => {
                    setDownloadLoading(true);
                    const conversationIds = Object.keys(rowSelection).map(
                      (k) => {
                        const key = parseInt(k);
                        return customerData?.[key]?.conversationId;
                      }
                    );

                    downloadRecordingZipFile(conversationIds, basePath, () =>
                      setDownloadLoading(false)
                    );
                  }}
                  onAssign={(data) => openAssignEvaluationPopup(data)}
                  isDownloading={downloadLoading}
                />
              </div>
              {totalPages > 0 && (
                <section className="flex-1 flex-row">
                  <div>
                    <Pagination
                      current={currentPage}
                      perPage={perPage}
                      total={totalPages}
                      totalCount={totalCount}
                      onChange={(v) => setCurrentPage(v)}
                      handleOnPrevious={() => handlePrevious()}
                      handleOnNext={() => handleNext()}
                      handlePerPageSetter={(p: number) => {
                        setPerPage(p);
                        setCurrentPage(1);
                      }}
                    />
                  </div>
                </section>
              )}
            </div>
          )}
        <Sheet
          open={isSheetOpen.open}
          onOpenChange={() =>
            handleSheetOpen(isSheetOpen.id, !isSheetOpen.open, isSheetOpen.side)
          }
          headerTitle={t('ctint-mf-interaction.filter.addColumns')}
          side={isSheetOpen.side}
          content={
            <>
              <Draggable
                items={criteria}
                onChange={handleOrder}
                toogleSwitch
              />
            </>
          }
          footer={null}
        />
      </Panel>
    </div>
  );
};

// Create a client
const queryClient = new QueryClient();

export const TableDemo = () => (
  <QueryClientProvider client={queryClient}>
    <TableDemoBody />
    <Toaster />
  </QueryClientProvider>
);

export default TableDemo;
