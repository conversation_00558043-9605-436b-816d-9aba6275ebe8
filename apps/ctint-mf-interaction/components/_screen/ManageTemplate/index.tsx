'use client';

import { Button } from '@cdss-modules/design-system';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { useRouter } from 'next/router';
import Draggable from '@cdss-modules/design-system/components/_ui/Draggable';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
const templates = [
  {
    labelEn: 'View 1',
    labelCh: 'View 1',
    value: 'view1',
    data: 'view1',
    filterType: 'input',
    active: true,
  },
  {
    labelEn: 'View 2',
    labelCh: 'View 2',
    value: 'view2',
    data: 'view2',
    filterType: 'input',
    active: true,
  },
  {
    labelEn: 'View 3',
    labelCh: 'View 3',
    value: 'view3',
    data: 'view3',
    filterType: 'input',
    active: true,
  },
  {
    labelEn: 'View 4',
    labelCh: 'View 4',
    value: 'view4',
    data: 'view4',
    filterType: 'input',
    active: true,
  },
  {
    labelEn: 'View 5',
    labelCh: 'View 5',
    value: 'view5',
    data: 'view5',
    filterType: 'input',
    active: true,
  },
];

const ManageTemplate = () => {
  const [templateOrder, setTemplateOrder] = useState(templates);
  const { push, pathname } = useRouter();
  const convertedPathname = pathname
    .replace(/-/g, ' ') // Replace hyphens with spaces
    .replace(/\b\w/g, (match) => match.toUpperCase())
    .split('/'); // Capitalize the first letter of each word
  const { i18n } = useTranslation();
  return (
    <div className="w-full h-full flex flex-col gap-4">
      <div className="flex w-full justify-between items-center">
        <div className="flex items-center gap-2">
          <button
            onClick={() => push('/')}
            className="bg-white p-2 rounded-[5px] hover:bg-grey-200"
          >
            <Icon
              name="back"
              size={20}
            />
          </button>
          <p>/ {convertedPathname[1]}</p>
        </div>
        <Button
          variant="blank"
          beforeIcon={
            <Icon
              name="plus"
              size={20}
            />
          }
        >
          <span>Create Template</span>
        </Button>
      </div>
      <div className="flex gap-6 h-full">
        <div className="bg-white rounded-2xl w-1/3">
          <Draggable
            items={templateOrder}
            onChange={setTemplateOrder}
          />
        </div>
        <div className="bg-white rounded-2xl w-2/3 flex flex-col">
          {templateOrder.map((t) => (
            <p key={t.value}>
              {i18n.language === 'en' ? t.labelEn : t.labelCh}
            </p>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ManageTemplate;
