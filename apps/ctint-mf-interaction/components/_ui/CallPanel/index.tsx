import {
  <PERSON><PERSON>,
  <PERSON>,
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
  useRole,
  useRouteHandler,
} from '@cdss-modules/design-system';
import { AudioPlayer } from '@cdss-modules/design-system/components/_ui/AudioPlayer';
import IconEmptyRecords from '@cdss-modules/design-system/components/_ui/Icon/IconEmptyRecords';
import PDFViewer from '@cdss-modules/design-system/components/_ui/PDFViewer';
import { RefreshCw } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import {
  QueryClient,
  QueryClientProvider,
  useQuery,
} from '@tanstack/react-query';
import {
  fireGetRecordingMedia,
  fireGetTranscript,
} from '../../../lib/api/index';
import { useCallback } from 'react';
import { useEffect } from 'react';
import { AudioLoadOptions, useGlobalAudioPlayer } from 'react-use-audio-player';
import { startTransition } from 'react';
import { cn } from '@cdss-modules/design-system/lib/utils';
import { RootObject, TTranscriptStatus } from '../../../types/transcript';
import { TranscriptList } from '../TranscriptList';
import InputSearch from '@cdss-modules/design-system/components/_ui/InputSearch';

interface CallPanelProps {
  conversationType: string | undefined;
  standardScriptDirection: 'vertical' | 'horizontal';
  showStrandScript: boolean;
  setCurrentRecordingId: (id: string) => void;
  updateCurrentPosition: (position: number) => void;
}

type TTranscript = RootObject;
const renderTranscript = (transcript: TTranscript) => {
  if (!transcript || transcript?.transcripts?.length === 0) return null;

  // when the transcript in progressing
  if (
    transcript?.status == TTranscriptStatus.IN_PROGRESS ||
    transcript?.status == TTranscriptStatus.INIT
  )
    return (
      <div className="w-full h-full flex flex-col items-center justify-center">
        <IconEmptyRecords size="78" />
        <div className="text-grey-500">Transcript in progressing ....</div>
      </div>
      // <div className="text-center h-full">
      //   <div className="w-full h-full flex flex-col items-center justify-center py-12">
      //     <Loader size={64} />
      //     <p className="text-body mt-4">
      //       {t('ctint-mf-interaction.transcript.processing')}
      //     </p>
      //   </div>
      // </div>
    );

  // when the transcript ready
  if (transcript?.status == TTranscriptStatus.COMPLETED)
    return <TranscriptList transcript={transcript} />;

  // when the transcript fail
  if (transcript?.status == TTranscriptStatus.FAILED)
    return (
      <div className="w-full h-full flex flex-col items-center justify-center">
        <IconEmptyRecords size="78" />
        <div className="text-grey-500">Transcript Failed.</div>
      </div>
    );
};

const CallPanelBody = ({
  conversationType,
  standardScriptDirection,
  showStrandScript,
  setCurrentRecordingId,
  updateCurrentPosition,
}: CallPanelProps) => {
  const { t } = useTranslation();
  const { toPath, searchParams, basePath } = useRouteHandler();
  const { load, isReady, seek, error: loadMp3Error } = useGlobalAudioPlayer();
  const { globalConfig } = useRole();

  const hasTranscript = globalConfig?.services?.['ctint-stt']?.active;
  const id = searchParams.get('id') || '';

  const { data: transcriptData, isPending: isLoadingTranscript } = useQuery({
    queryKey: ['transcript', id],
    queryFn: async () => {
      const result = await fireGetTranscript(id, basePath).then(
        (res) => res.data
      );
      return result;
    },
    enabled: !!id,
    refetchInterval: 1000 * 60 * 5, // 5 minutes
  });

  const { data: mp3Data, isPending: isLoadingMp3 } = useQuery({
    queryKey: ['mediaUris', id],
    queryFn: async () => {
      const result = await fireGetRecordingMedia(id, basePath).then(
        (res) => res.data
      );
      return result;
    },
    enabled: !!id,
    refetchOnWindowFocus: false,
  });

  // 默认拿第一个 recording
  const mp3FileUrl = mp3Data?.data ? mp3Data?.data[0]?.mediaUri : null;
  // const mp3FileUrl = audioUrl; // FOR STREAM
  console.log('mp3FileUrl', mp3FileUrl);

  useEffect(() => {
    if (mp3Data?.data && mp3Data?.data[0]?.id) {
      setCurrentRecordingId(mp3Data?.data[0]?.id);
    }
  }, [mp3Data]);

  const loadMp3 = useCallback(() => {
    if (!mp3FileUrl) return;
    const loadOptions: AudioLoadOptions = { initialVolume: 0.5 };
    loadOptions.html5 = true;
    loadOptions.format = 'mp3';

    load(mp3FileUrl, loadOptions);
  }, [load, mp3FileUrl]);

  useEffect(() => {
    if (!mp3FileUrl) return;
    startTransition(() => {
      const loadOptions: AudioLoadOptions = { initialVolume: 0.5 };
      loadOptions.html5 = true;
      loadOptions.format = 'mp3';

      load(mp3FileUrl, loadOptions);
    });
  }, [mp3FileUrl, load, loadMp3]);

  // 20241022 - For now hard coded standard script PDF (beacause initally first client CCB has only one standard script)
  // TODO: should be dynamic based on the formId when backend ready
  const port = window?.location?.port ? `:${window.location.port}` : '';
  const standardScriptPdf = `${window?.location?.protocol}//${window?.location?.hostname}${port}${basePath}/images/standard-script-eli.pdf`;

  /**
   * AudioPlayer Log Start
   */
  useEffect(() => {
    console.log('AudioPlayer Log => isReady', isReady);
  }, [isReady]);

  useEffect(() => {
    console.log('AudioPlayer Log => error', loadMp3Error);
  }, [loadMp3Error]);

  useEffect(() => {
    console.log('AudioPlayer Log => mp3FileUrl', mp3FileUrl);
  }, [mp3FileUrl]);

  useEffect(() => {
    console.log('AudioPlayer Log => isLoadingMp3', isLoadingMp3);
  }, [isLoadingMp3]);
  /**
   * AudioPlayer Log End
   */

  return (
    <Panel
      loading={isLoadingMp3}
      containerClassName="h-full"
    >
      <ResizablePanelGroup
        direction="vertical"
        className="flex flex-col w-full"
      >
        <ResizablePanel>
          <div className="h-full flex flex-col px-4 py-1">
            <div className="flex justify-between w-full">
              <h2 className="text-t6">
                {t('ctint-mf-interaction.details.recordings')}
              </h2>
              <Button
                className="shrink-0"
                onClick={() => {
                  // update stand script result
                  queryClient.invalidateQueries({
                    queryKey: ['transcript', id],
                  });
                }}
                variant="blank"
              >
                <RefreshCw size={14} />
                <span className="active:text-other-orange">Refresh</span>
              </Button>
            </div>
            {/* <div>
          <InputSearch
            value={''}
            placeholder="Search..."
            options={[
              {
                labelEn: 'Call',
                labelCh: 'Call',
                value: 'Call',
              },
              {
                labelEn: 'Chat',
                labelCh: 'Chat',
                value: 'Chat',
              },
              {
                labelEn: 'Email',
                labelCh: 'Email',
                value: 'Email',
              },
            ]}
            onItemSelected={(value) => {
              console.log(value);
            }}
          />
        </div> */}
            {/* remove loading */}
            {/* {!isReady && (
          <div className="w-full">
            <p className="mb-4">
              {`${t('ctint-mf-interaction.details.loading')}`}
            </p>
          </div>
        )} */}
            {loadMp3Error && conversationType?.includes('Call') && (
              <div className="w-full">
                <p className="mb-4">
                  {`${t('ctint-mf-interaction.filter.recordingError')}(${loadMp3Error})`}
                </p>
                <Button onClick={() => loadMp3()}>
                  {t('ctint-mf-interaction.filter.reload')}
                </Button>
              </div>
            )}
            {loadMp3Error && !conversationType?.includes('Call') && (
              <div className="w-full pt-4 border-t border-grey-200">
                <p className="mb-4">
                  {`${t('ctint-mf-interaction.details.notAvailable')}`}
                </p>
              </div>
            )}
            {!isLoadingMp3 && mp3FileUrl ? (
              <>
                <div className="w-full pt-4 border-t border-grey-200">
                  <div className="relative w-full">
                    <AudioPlayer
                      updatePos={(p) => updateCurrentPosition(p)}
                      // seekBarCustomComponent={
                      //   <QMAudioPlayerActiveSection />
                      // }
                      label={{
                        jumpToTime: t('ctint-mf-interaction.audio.jumpToTime'),
                        go: t('ctint-mf-interaction.audio.go'),
                        speed: t('ctint-mf-interaction.audio.speed'),
                        invalidTime: t(
                          'ctint-mf-interaction.audio.invalidTime'
                        ),
                      }}
                    />
                  </div>
                </div>
                {/* <QMSOPStages /> */}
                <ResizablePanelGroup
                  direction={standardScriptDirection}
                  className="flex flex-col w-full flex-1 h-0"
                >
                  {/* transcript panel */}
                  <ResizablePanel defaultSize={showStrandScript ? 50 : 100}>
                    <div
                      className={cn(
                        'flex-1 h-full flex flex-col w-full overflow-y-auto',
                        !hasTranscript && 'hidden'
                      )}
                    >
                      {transcriptData ? (
                        <div className="h-full">
                          {renderTranscript(
                            transcriptData?.data?.transcriptList?.find(
                              (item: TTranscript) =>
                                item.recordingId === mp3Data?.data[0]?.id
                            )
                          )}
                        </div>
                      ) : (
                        <div className="flex flex-col items-center pt-4 border-t border-grey-200">
                          <IconEmptyRecords size="78" />
                          <div className="text-grey-500">
                            No selected interaction.
                          </div>
                        </div>
                      )}
                    </div>
                  </ResizablePanel>
                  <ResizableHandle className="mx-2" />
                  {/* standard script panel */}
                  {showStrandScript && (
                    <ResizablePanel
                      defaultSize={showStrandScript ? 50 : 0}
                      className="pt-6 pb-2"
                    >
                      <div className="relative flex flex-col p-2 h-[100%] border border-[#EEEFF1] bg-[#FCFCFD] rounded-lg">
                        {/* <IconSwitchDirection
                      size="20"
                      className="absolute top-2 right-2 bg-[#FCFCFD] cursor-pointer"
                      onClick={() => {
                        toggleDirection && toggleDirection();
                      }}
                    /> */}
                        <article className="flex flex-col gap-2 mt-2">
                          <PDFViewer pdf={standardScriptPdf} />
                          {/* {standScriptResult?.data.map(
                        (
                          item: StandardScriptItem,
                          index: number
                        ) => (
                          <p
                            key={item.scenarioId}
                            className={`text-clip ${index == 0 ? 'text-[#FFAC4A] font-bold' : ''}`}
                          >
                            {item.scenarioId}
                            {item.content}
                          </p>
                        )
                      )} */}
                        </article>
                      </div>
                    </ResizablePanel>
                  )}
                </ResizablePanelGroup>
              </>
            ) : (
              <>
                {!loadMp3Error && (
                  <div className="w-full h-full flex flex-col items-center justify-center">
                    <IconEmptyRecords size="78" />
                    <div className="text-grey-500">
                      No selected interaction.
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </ResizablePanel>
      </ResizablePanelGroup>
    </Panel>
  );
};

const queryClient = new QueryClient();

const CallPanel = (props: CallPanelProps) => {
  if (props.conversationType?.toLowerCase() === 'voice') {
    return (
      <QueryClientProvider client={queryClient}>
        <CallPanelBody {...props} />
      </QueryClientProvider>
    );
  }
  return null;
};

export default CallPanel;
