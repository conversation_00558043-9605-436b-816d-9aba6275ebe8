import { Loader, useRouteHandler } from '@cdss-modules/design-system';
import { getEmailDetails } from '../../../lib/api';
import { QueryClientProvider, useQuery } from '@tanstack/react-query';
import { QueryClient } from '@tanstack/react-query';
import { PropsWithChildren } from 'react';
import { useTranslation } from 'react-i18next';
import EmailItem from '@cdss-modules/design-system/components/_ui/EmailPanel/EmailItem';
import IconEmptyRecords from '@cdss-modules/design-system/components/_ui/Icon/IconEmptyRecords';
interface EmailPanelProps {
  conversationId: string;
}

const EmailPanelBody: React.FC<PropsWithChildren & EmailPanelProps> = ({
  children,
  conversationId,
}) => {
  const { t } = useTranslation();
  const { basePath } = useRouteHandler();
  const { data: emailDetails, isLoading } = useQuery({
    queryKey: ['email-details', conversationId],
    queryFn: () => getEmailDetails(conversationId, basePath),
    refetchOnWindowFocus: false,
  });

  console.log('EmailPanelBody ===> data', emailDetails?.data);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center w-full h-full">
        <Loader size={64} />
      </div>
    );
  }

  return (
    <div className="flex flex-col w-full h-full bg-white rounded-lg relative">
      {/* Title */}
      <div className="flex justify-between items-center w-full border-b border-gray-200 p-1">
        <h2 className="text-t6">{t('ctint-mf-interaction.details.email')}</h2>
      </div>
      {/* Email History List */}
      <div className="flex flex-col w-full h-full overflow-y-auto p-4 space-y-4 hide-scrollbar">
        <div className="flex-1">
          {emailDetails &&
          emailDetails?.data &&
          emailDetails?.data?.data &&
          emailDetails?.data?.data?.length > 0 ? (
            emailDetails?.data?.data?.map((email: any) => {
              return (
                <EmailItem
                  key={email.id}
                  email={email}
                />
              );
            })
          ) : (
            <div className="w-full h-full flex flex-col items-center justify-center">
              <IconEmptyRecords size="78" />
              <div className="text-grey-500">
                {t('ctint-mf-interaction.whatsapp.noSelectedInteraction')}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const queryClient = new QueryClient();

export const EmailPanel: React.FC<PropsWithChildren & EmailPanelProps> = ({
  children,
  conversationId,
}) => {
  return (
    <QueryClientProvider client={queryClient}>
      <EmailPanelBody conversationId={conversationId}>
        {children}
      </EmailPanelBody>
    </QueryClientProvider>
  );
};
