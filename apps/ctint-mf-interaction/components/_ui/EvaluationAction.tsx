import { Button, useRouteHandler } from '@cdss-modules/design-system';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@cdss-modules/design-system/components/_ui/DropdownMenu';
import { ClipboardCheck, Frown, Smile } from 'lucide-react';

export type TEvaluationActionProps = {
  menu?: React.ReactNode;
  evaluation: any;
  openAssignEvaluationPopup: () => void;
};

export const EvaluationAction = ({
  evaluation,
  menu,
  openAssignEvaluationPopup,
}: TEvaluationActionProps) => {
  const { toPath } = useRouteHandler();
  const interactionId = evaluation?.interactionId;
  const toBeAssigned = evaluation?.status === 'unassigned';

  let releasedResult = 'Failed';
  if (evaluation?.status === 'released' && evaluation?.score >= 50) {
    releasedResult = 'Passed';
  }
  if (toBeAssigned) {
    return (
      <Button
        size="s"
        onClick={(e: any) => {
          e.stopPropagation();
          openAssignEvaluationPopup();
        }}
      >
        Add New
      </Button>
    );
  }
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="flex items-center gap-x-2 hover:text-primary-600">
          {/* {evaluation?.status === 'assigned' && <UserCheck />} */}
          {(evaluation?.status === 'evaluated' ||
            evaluation?.status === 'assigned') && <ClipboardCheck />}
          {evaluation?.status === 'released' && evaluation?.score >= 50 && (
            <Smile className="text-status-success" />
          )}
          {/* {evaluation?.status === 'released' &&
            evaluation?.score >= 50 &&
            evaluation?.score < 70 && <Meh className="text-primary" />} */}
          {evaluation?.status === 'released' && evaluation?.score < 50 && (
            <Frown className="text-status-danger" />
          )}

          {evaluation?.status === 'assigned' && <>3 / 4</>}
          {evaluation?.status === 'evaluated' && <>4 / 4</>}
          {/* {evaluation?.status === 'released' && <>{evaluation?.score} / 100</>} */}
          {evaluation?.status === 'released' && <>{releasedResult}</>}
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {menu || (
          <>
            <DropdownMenuGroup>
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  openAssignEvaluationPopup();
                }}
                className="text-remark"
              >
                Assign New
              </DropdownMenuItem>
              {evaluation?.status === 'assigned' &&
                evaluation?.evaluator === 'Admin' && (
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.stopPropagation();
                      toPath(
                        `/interaction/detail?id=${interactionId}&tab=qa&qaId=QADBMNLAM52MJ0GUEKVQ8I0ODO000005`
                      );
                    }}
                    className="text-remark"
                  >
                    Evaluate
                  </DropdownMenuItem>
                )}
              <DropdownMenuItem
                className="text-remark"
                onClick={(e) => {
                  e.stopPropagation();
                  toPath(`/interaction/detail?id=${interactionId}&tab=qa`);
                }}
              >
                {evaluation?.status === 'evaluated' ? 'Review' : 'View'}
              </DropdownMenuItem>
            </DropdownMenuGroup>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default EvaluationAction;
