import { Tooltip, useQM } from '@cdss-modules/design-system';
import RadioGroup from '@cdss-modules/design-system/components/_ui/RadioGroup';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import { cn, secondsToFormat } from '@cdss-modules/design-system/lib/utils';
import { Bomb, Frown, Smile, TriangleAlert } from 'lucide-react';
import { memo } from 'react';
import { useGlobalAudioPlayer } from 'react-use-audio-player';
import QMQuestionTag from './QMQuestionTag';

type TQMQuestionProps = {
  type?: string;
  questionType?: string;
  disabled?: boolean;
  qid: string;
  question: string;
  options?: any[];
  onAnswer: (answer: string) => void;
  answer: string;
  showPoints?: boolean;
  showQuestionType?: boolean;
};
const QATag = ({
  qid,
  type,
  target,
  time,
  label,
  desc,
}: {
  qid: string;
  type?: string;
  target: string;
  time: number;
  label: string;
  desc: string;
}) => {
  const { seek } = useGlobalAudioPlayer();
  return (
    <div className="inline-block ml-2 translate-y-1 font-normal">
      <Tooltip
        side="bottom"
        trigger={
          <button
            onClick={() => seek(time)}
            className={cn(
              'inline-flex items-center gap-x-1 text-footnote  text-status-info hover:text-primary-600'
            )}
          >
            <div
              className={cn(
                'text-white p-[2px] rounded-full',
                type === 'positive' ? 'bg-status-success' : 'bg-status-danger'
              )}
            >
              {type === 'positive' ? <Smile size={16} /> : <Frown size={16} />}
            </div>
            <span className="block text-left text-footnote underline">
              {secondsToFormat(time)}
            </span>
          </button>
        }
        content={`${label}: ${desc}`}
      />
    </div>
  );
};

const QMQuestion = memo(
  ({
    type = 'radio',
    questionType,
    question,
    disabled,
    options,
    qid,
    onAnswer,
    answer,
    showPoints,
    showQuestionType,
  }: TQMQuestionProps) => {
    const { highlightedQuestion, qaFormAnswers, openedQA } = useQM();
    const tags = qaFormAnswers?.[`${openedQA}`]?.tags;
    const thisTag = tags?.[qid];
    const { seek } = useGlobalAudioPlayer();
    return (
      <div
        className={cn(highlightedQuestion?.includes(qid) && 'bg-primary-100')}
      >
        <div className={cn('mb-2 transition-all', disabled && 'text-grey-500')}>
          <strong>
            {question}
            {thisTag && !thisTag?.ai ? (
              <QATag
                qid={qid}
                type={thisTag.type}
                target={thisTag.target}
                time={thisTag.time}
                label={thisTag.label}
                desc={thisTag.desc}
              />
            ) : (
              <>
                {questionType === 'fatal' && !disabled && (
                  <div className="inline-block font-normal ml-2">
                    <QMQuestionTag
                      updateStartTime={() => null}
                      startTime={0}
                    />
                  </div>
                )}
              </>
            )}
            {questionType === 'fatal' && showQuestionType && (
              <Tooltip
                trigger={
                  <span className="inline-block -mb-1 text-status-danger">
                    <Bomb />
                  </span>
                }
                content="Fatal question"
              />
            )}
            {questionType === 'critical' && showQuestionType && (
              <Tooltip
                trigger={
                  <span className="inline-block -mb-1 text-primary-600">
                    <TriangleAlert />
                  </span>
                }
                content="Critical question"
              />
            )}
          </strong>
        </div>
        <div>
          {thisTag?.ai ? (
            <div className="flex flex-col gap-1 pl-4 border-l-2 border-grey-200  text-remark">
              <div className="flex gap-x-2">
                <div className="flex gap-x-1 items-center">
                  {thisTag.type === 'positive' ? (
                    <>
                      <Smile
                        className="text-status-success -mt-1"
                        size={20}
                      />
                      Passed - {thisTag.accuracy * 100}% matched with standard
                    </>
                  ) : (
                    <>
                      <Frown
                        className="text-status-danger"
                        size={20}
                      />
                      Failed - {thisTag.accuracy * 100}% matched with standard
                    </>
                  )}
                </div>
                <div className="flex gap-x-2">
                  <strong>at</strong>
                  <button
                    onClick={() => seek(thisTag.time)}
                    className={cn(
                      'inline-flex items-center gap-x-1 text-status-info hover:text-primary-600'
                    )}
                  >
                    <span className="block text-left underline">
                      {secondsToFormat(thisTag.time)}
                    </span>
                  </button>
                </div>
              </div>
              <div className="flex gap-x-6">
                <div className="flex gap-x-2">
                  <strong>Notes: </strong>
                  <i>{thisTag.desc}</i>
                </div>
              </div>
            </div>
          ) : (
            <>
              {type === 'radio' && (
                <RadioGroup
                  items={
                    options?.map((item) => {
                      let label = `${item.label}`;
                      if (showPoints) {
                        label = `${item.label} (${item.point})`;
                      }
                      return {
                        id: item.id,
                        label,
                        value: item.id,
                      };
                    }) || []
                  }
                  disabled={disabled}
                  name={qid}
                  value={answer}
                  onChange={(e) => {
                    if (typeof e === 'string') {
                      onAnswer(e);
                    } else {
                      onAnswer(e.target.value);
                    }
                  }}
                  direction="vertical"
                />
              )}
              {type === 'select' && (
                <>
                  <Select
                    placeholder="Please select an action"
                    mode="single"
                    options={
                      options?.map((item) => ({
                        id: item.id,
                        label: item.label,
                        value: item.id,
                      })) || []
                    }
                    disabled={disabled}
                    showSearch={true}
                    onChange={(e) => {
                      if (typeof e === 'string') {
                        onAnswer(e);
                      } else {
                        onAnswer(e.target.value);
                      }
                    }}
                    value={answer}
                  />
                </>
              )}
              {type === 'textarea' && (
                <textarea
                  className="w-full h-[100px] border border-gray-300 rounded-md p-2 focus:outline-none focus:border-primary-900 focus:shadow-field resize-none"
                  name={qid}
                  disabled={disabled}
                  value={answer}
                  onChange={(e) => onAnswer(e.target.value)}
                />
              )}
            </>
          )}
        </div>
      </div>
    );
  }
);

QMQuestion.displayName = 'QMQuestion';

export default QMQuestion;
