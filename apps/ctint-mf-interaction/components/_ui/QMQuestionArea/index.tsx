/* eslint-disable react-hooks/exhaustive-deps */
import { cn } from '@cdss-modules/design-system/lib/utils';
import { useQM } from '@cdss-modules/design-system';
import QMQuestion from '../QMQuestion';
import { DUMMY_CS_EVALUATION_FORM } from '../../../lib/dummy/qa';
import { useInView } from 'react-intersection-observer';
import { useEffect } from 'react';

const QMQuestionScrollTag = ({ id }: { id: string }) => {
  const { ref, inView } = useInView({
    /* Optional options */
    threshold: 1,
  });
  const {
    updateQMActiveSection,
    activeQMSection,
    scrollToScriptClickHandler,
    scrollingStage,
  } = useQM();
  useEffect(() => {
    if (inView && activeQMSection !== id && !scrollingStage) {
      updateQMActiveSection(id);
      scrollToScriptClickHandler(`script-${id}`);
    }
  }, [inView]);

  return (
    <div
      ref={ref}
      className="absolute top-[300px] left-0 w-full h-0"
    />
  );
};

const QMQuestionArea = ({ editable }: { editable: boolean }) => {
  const {
    qaFormAnswers,
    updateQMAnswer: updateAnswer,
    openedQA,
    getScrollToStageRef,
  } = useQM();
  const activeQA = openedQA || '';

  return (
    <>
      <div className="h-full overflow-auto">
        {DUMMY_CS_EVALUATION_FORM.questions?.map((stage, stageIndex) => {
          const stageId = `${stageIndex + 1}`;
          const stageKey = `stage-${stage.id}`;
          return (
            <div
              className="relative"
              id={stageKey}
              ref={getScrollToStageRef(stageKey)}
              key={stageKey}
            >
              <QMQuestionScrollTag id={stage.id} />
              {stage?.subSections?.map((sec, si) => {
                const sid = `${si + 1}`;
                const skey = `sec-${sid}`;
                return (
                  <div
                    key={skey}
                    className="pb-4 mb-4 border-b border-grey-200"
                  >
                    <div
                      className={cn(
                        'mb-2 font-bold text-body',
                        !editable && 'text-grey-500'
                      )}
                    >
                      {stageId}.{sid}. {sec.title}
                    </div>
                    <div>
                      {sec.questions.map((q: any, qi) => {
                        const qid = `${qi + 1}`;
                        const qkey = sid + `${qid}`;
                        const questionText = `${stageId}.${sid}.${qid}. ${q.question}`;
                        return (
                          <div
                            key={qkey}
                            className="mb-4 pl-8 pr-4 last:mb-0"
                          >
                            <QMQuestion
                              type={q.type}
                              questionType={q.questionType}
                              disabled={!editable}
                              qid={q.id}
                              question={questionText}
                              options={q.answers}
                              onAnswer={(ans) =>
                                updateAnswer(
                                  activeQA,
                                  stage.id,
                                  sec.id,
                                  q.id,
                                  ans
                                )
                              }
                              answer={
                                qaFormAnswers[activeQA]?.[stage.id]?.[sec.id]?.[
                                  q.id
                                ]
                              }
                            />
                          </div>
                        );
                      })}
                    </div>
                  </div>
                );
              })}
            </div>
          );
        })}
      </div>
    </>
  );
};

export default QMQuestionArea;
