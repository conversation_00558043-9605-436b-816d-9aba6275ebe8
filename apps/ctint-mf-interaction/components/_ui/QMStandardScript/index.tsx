/* eslint-disable react/no-unescaped-entities */
import React from 'react';
import { useQM } from '@cdss-modules/design-system';
import { cn } from '@cdss-modules/design-system/lib/utils';

const DUMMY_SCRIPT = [
  {
    tag: 's_1',
    fragments: [
      {
        tag: ['1.1.1', '1.1.2'],
        targetId: ['q_1_1_1', 'q_1_1_2'],
        script:
          "Hello, thank you for calling [Company Name]. My name is [Agent's Name], and I'll be assisting you today. May I have your name, please?",
      },
      {
        tag: ['1.1.2'],
        targetId: ['q_1_1_2'],
        script: "My name is [Agent's Name], and I'll be assisting you today.",
      },
      {
        tag: ['1.2.1'],
        targetId: ['q_1_2_1'],
        script: 'May I have your name, please?',
      },
    ],
  },
  {
    tag: 's_2',
    fragments: [
      {
        tag: ['2.1.1'],
        targetId: ['q_2_1_1'],
        script: "Could you please describe the issue you're experiencing?",
      },
      {
        tag: ['2.1.2'],
        targetId: ['q_2_1_2'],
        script: 'I’ll listen carefully to ensure I understand.',
      },
      {
        tag: ['2.2.1', '2.2.2'],
        targetId: ['q_2_2_1', 'q_2_2_2'],
        script:
          "Just to confirm, you're experiencing [restate problem]. Is that correct?",
      },
      {
        tag: ['2.3.1'],
        targetId: ['q_2_3_1'],
        script:
          "Based on what you've told me, here’s what we can do to resolve this: [provide solution].",
      },
    ],
  },
  {
    tag: 's_3',
    fragments: [
      {
        tag: ['3.1.1'],
        targetId: ['q_3_1_1'],
        script:
          'Is there anything else I can assist you with regarding this issue?',
      },
      {
        tag: ['3.2.1'],
        targetId: ['q_3_2_1'],
        script:
          'If there are any other problems or questions, I’m here to help.',
      },
      {
        tag: ['3.3.1'],
        targetId: ['q_3_3_1'],
        script:
          'If you have any additional comments or feedback, please feel free to share. ',
      },
      {
        tag: ['3.3.2'],
        targetId: ['q_3_3_2'],
        script:
          'Thank you for contacting [Company Name]. Have a great day, goodbye!',
      },
    ],
  },
];

export const QMStandardScript = () => {
  const {
    activeQMSection,
    getScrollToScriptRef,
    highlightQuestion,
    scrollToStageClickHandler,
    updateQMActiveSection,
  } = useQM();

  return (
    <div className="h-full flex flex-col py-4">
      <div className="flex-1 h-0 overflow-auto space-y-2">
        <h2 className="font-bold mb-2">Standard Script:</h2>
        {DUMMY_SCRIPT.map((section) => (
          <div
            className="leading-loose"
            ref={getScrollToScriptRef(`script-${section.tag}`)}
            key={section.tag}
          >
            <mark
              className={cn(
                'py-2',
                activeQMSection === section.tag
                  ? 'bg-primary-400'
                  : 'bg-transparent'
              )}
            >
              {section.fragments.map((fragment) => (
                <>
                  <mark
                    key={fragment?.tag?.join('') || ''}
                    className={cn(
                      'p-1 hover:bg-primary-200 first:pl-0 cursor-pointer',
                      activeQMSection === section.tag
                        ? 'bg-primary-100'
                        : 'bg-transparent'
                    )}
                    onClick={() => {
                      highlightQuestion(fragment?.targetId);
                      scrollToStageClickHandler(`stage-${section.tag}`);
                      updateQMActiveSection(section.tag);
                    }}
                  >
                    {fragment.script}{' '}
                    <sup className="bg-primary-900 px-1 py-[2px] rounded-md text-white">
                      {fragment?.tag?.join(', ')}
                    </sup>
                  </mark>
                  {` `}
                </>
              ))}
            </mark>
          </div>
        ))}
        {/* <p>
          <mark
            ref={getScrollToScriptRef('script-s_1')}
            className={cn(
              activeQMSection === 's_1' ? 'bg-primary-300' : 'bg-transparent'
            )}
          >
            Hello, thank you for calling [Company Name]. My name is [Agent's
            Name], and I'll be assisting you today. May I have your name,
            please?
          </mark>
        </p>
        <p>
          <mark
            ref={getScrollToScriptRef('script-s_2')}
            className={cn(
              activeQMSection === 's_2' ? 'bg-primary-300' : 'bg-transparent'
            )}
          >
            Could you please describe the issue you're experiencing? I’ll listen
            carefully to ensure I understand. Just to confirm, you're
            experiencing [restate problem]. Is that correct? Based on what
            you've told me, here’s what we can do to resolve this: [provide
            solution].
          </mark>
        </p>
        <p>
          <mark
            ref={getScrollToScriptRef('script-s_3')}
            className={cn(
              activeQMSection === 's_3' ? 'bg-primary-300' : 'bg-transparent'
            )}
          >
            Is there anything else I can assist you with regarding this issue?
            If there are any other problems or questions, I’m here to help.
            Thank you for contacting [Company Name]. Have a great day, goodbye!
          </mark>
        </p>
        <p>
          <mark
            className={cn(
              activeQMSection === 's_3' ? 'bg-primary-300' : 'bg-transparent'
            )}
          >
            If you have any additional comments or feedback, please feel free to
            share. Based on our conversation, the next steps may include [No
            Action/Training Required/etc.]. The result of this interaction has
            been overridden to [Passed/Failed] because [reason].
          </mark>
        </p> */}
      </div>
    </div>
  );
};

export default QMStandardScript;
