import { AppProps } from 'next/app';
import Head from 'next/head';
import Script from 'next/script';

import 'react-datepicker/dist/react-datepicker.css';

import './styles.css';
import { Roboto } from 'next/font/google';
import { cn } from '@cdss-modules/design-system/lib/utils';
import {
  initI18Next,
  addLocalesResources,
} from '@cdss-modules/design-system/i18n/client';
import { prepareLocales } from '../i18n/locales';

const roboto = Roboto({
  weight: ['100', '300', '400', '500', '700', '900'],
  style: ['normal', 'italic'],
  subsets: ['latin'],
  display: 'swap',
});

function CustomApp({ Component, pageProps }: AppProps) {
  initI18Next(
    (langauges: string[] = []) => {
      langauges.forEach((lang) => {
        prepareLocales((resultLocale: any) =>
          addLocalesResources(resultLocale[lang], lang)
        );
      });
    },
    undefined,
    pageProps?.globalConfig?.languages
  );
  return (
    <>
      <Head>
        <title>Conversation Playback Portal</title>
      </Head>
      <Script id="GLOBAL_CONFIG_VARS">
        {`window.GLOBAL_ENV_VARS = ${JSON.stringify(pageProps.publicEnvVars)};`}
      </Script>
      <main className={cn('app', roboto.className)}>
        <Component {...pageProps} />
      </main>
    </>
  );
}

export default CustomApp;
