import { Page<PERSON>enderer, TglobalConfig } from '@cdss-modules/design-system';
import PlaybackModule from '../components/_screen/Main';
import PlaybackModuleDetail from '../components/_screen/Detail';
import { basePath, mfName } from '../lib/appConfig';
import loadGlobalConfig from '@cdss-modules/design-system/lib/globalConfig';
import { CommonPermissionWrapper } from '@cdss-modules/design-system/components/_other/PermissionWrapper/CommonPermissionWrapper';
import { CommonPermission } from '@cdss-modules/design-system/@types/CommonPermission';
import { fireLogout } from '@cdss-modules/design-system/lib/api';
import { useEffect } from 'react';

export const Page = () => {
  useEffect(() => {
    const handleBeforeUnload = async () => {
      try {
        await fireLogout(basePath);
        localStorage.removeItem('cdss-auth-token');
        localStorage.removeItem('gc-access-token');
        localStorage.removeItem('permissions');
        localStorage.removeItem('userName');
      } catch (error) {
        console.error('Error logging out', error);
        // remove token in local storage even if logout api failed
        localStorage.removeItem('cdss-auth-token');
        localStorage.removeItem('gc-access-token');
        localStorage.removeItem('permissions');
        localStorage.removeItem('userName');
      }
    };
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  return (
    <div className="relative p-6 w-full h-screen">
      <PageRenderer
        routes={[
          {
            path: '/',
            group: 'playback',
            component: (
              // 這裡的permissionHandler是自定義的，用來控制是否顯示PlaybackModule
              <CommonPermissionWrapper
                customerPermissionHandler={(
                  globalConfig: TglobalConfig,
                  permissions: string[]
                ) => {
                  return new CommonPermission(
                    globalConfig,
                    permissions
                  ).isPermissionEnabled(
                    'ctint-mf-interaction',
                    'application',
                    'visit'
                  );
                }}
              >
                <PlaybackModule />
              </CommonPermissionWrapper>
            ),
            subroutes: [
              {
                path: '/detail',
                component: (
                  // 這裡的permissionHandler是自定義的，用來控制是否顯示PlaybackModuleDetail
                  <CommonPermissionWrapper
                    customerPermissionHandler={(
                      globalConfig: TglobalConfig,
                      permissions: string[]
                    ) => {
                      return new CommonPermission(
                        globalConfig,
                        permissions
                      ).isPermissionEnabled(
                        'ctint-mf-interaction',
                        'detail',
                        'visit'
                      );
                    }}
                  >
                    <PlaybackModuleDetail />
                  </CommonPermissionWrapper>
                ),
              },
            ],
          },
        ]}
        basePath={basePath}
      />
    </div>
  );
};

export const getServerSideProps = async () => {
  const globalConfig = loadGlobalConfig(mfName);
  const publicEnvVars = Object.keys(process.env).reduce(
    (publicVars: any, key) => {
      if (key.startsWith('CDSS_PUBLIC_')) {
        publicVars[key] = process.env[key];
      }
      return publicVars;
    },
    {}
  ) as any;
  return {
    props: {
      globalConfig,
      publicEnvVars,
    },
  };
};

export default Page;
