import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React, { useEffect, useRef, useState } from 'react';
import { WhitePanel } from '@cdss-modules/design-system/components/_ui/WhitePanel';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import SearchInput from '@cdss-modules/design-system/components/_ui/SearchInput';
import FilterComponent, {
  Condition,
} from '@cdss-modules/design-system/components/_ui/FilterComponent';
import dayjs from 'dayjs';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import {
  Auth<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Loader,
  THEME_FONT_COLORS,
  toast,
  Toaster,
  useRole,
  useRouteHandler,
  useToast,
} from '@cdss-modules/design-system';
import utc from 'dayjs/plugin/utc';
import Panel from '@cdss-modules/design-system/components/_ui/Panel';
import { ColumnDef, Row } from '@tanstack/react-table';
import {
  AssignReq,
  ManualQueueData,
  ManualQueueDataResp,
  ManualQueueTabName,
  microfrontends,
  UserSearchReq,
} from 'apps/ctint-mf-manual-queue/types/microfrontendsConfig';
import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';
import { Table as TableType } from '@tanstack/table-core/build/lib/types';
import {
  assignTo,
  FilterParams,
  getAudioUrl,
  getManualQueuePage,
  getQueueInfo,
  userSearch,
} from '../../../lib/api';
import Pagination from '@cdss-modules/design-system/components/_ui/Pagination';
import { AudioModal } from '../AudioModal/AudioModal';
import { createPortal } from 'react-dom';
import { UserSelector } from '../../_ui/UserSelector';
import { User } from '../../../types/microfrontendsConfig';
import { optionsMap } from '@cdss-modules/design-system/components/_ui/FilterComponent/config';
import AssignButton from '../../_ui/AssignButton';
import { RefreshCcw } from 'lucide-react';

dayjs.extend(utc);

export function CallBackPage() {
  const [queueList, setQueueList] = useState<{ id: string; name: string }[]>(
    []
  );
  const [userList, setUserList] = useState<{ id: string; name: string }[]>([]);
  const { basePath } = useRouteHandler();
  const [loading, setLoading] = useState(true);
  const [activePlayButton, setActivePlayButton] = useState<string | null>(null);

  const findQueueName = (queueId: string): string => {
    const queue = queueList.find((q) => q.id === queueId);
    return queue?.name || queueId;
  };
  const findAssignName = (userId: string): string => {
    const user = userList.find((u) => u.id === userId);
    return user?.name || userId;
  };

  useEffect(() => {
    const fetchQueueList = async () => {
      try {
        const queueListData = await getQueueInfo(basePath, 'callback');
        if (queueListData?.data?.data) {
          const queues = queueListData.data.data;
          setQueueList(queues);
          // 创建队列选项
          const queueOptions = [
            ...queues.map((queue: { id: any; name: any }) => ({
              value: queue.id, // 用于搜索的值（ID）
              labelEn: queue.name, // 显示用的英文名
              labelCh: queue.name, // 显示用的中文名
              name: queue.name, // 显示用的名称
            })),
          ];

          // 更新全局 optionsMap（这一步很重要，因为 SelectFilter 依赖它）
          optionsMap.queueName = queueOptions;
          // 更新 filterValue，确保包含所有必要属性
          setFilterValue((prevFilterValue) => ({
            ...prevFilterValue,
            queueName: {
              ...prevFilterValue.queueName,
              options: queueOptions,
            },
          }));

          const searchRequest: UserSearchReq = {
            keyword: '',
          };
          const response = await userSearch(`${basePath}`, searchRequest);
          const userList = await response.data.data;

          setUserList(userList);
          const userOptions = [
            ...userList.map((user: { id: any; name: any }) => ({
              value: user.id, // 用于搜索的值（ID）
              labelEn: user.name, // 显示用的英文名
              labelCh: user.name, // 显示用的中文名
              name: user.name, // 显示用的名称
            })),
          ];
          optionsMap.assign = userOptions;
          setFilterValue((prevFilterValue) => ({
            ...prevFilterValue,
            assign: {
              ...prevFilterValue.assign,
              options: userOptions,
            },
          }));

          // 触发初始加载
          await getManualQueueList(1, pagination.pageSize);
        }
      } catch (error) {
        console.error('Error fetching queue list:', error);
      }
    };
    fetchQueueList();
  }, [basePath]);

  const generateColumns = (
    columns: string[],
    showColumns: string[],
    columnOrdering: string[]
  ): ColumnDef<ManualQueueData>[] => {
    return [
      ...columns.map((col, index) => ({
        id: col,
        accessorKey: col,
        size:
          col === 'mediaType'
            ? 40
            : col === 'assign'
              ? 280
              : col === 'state'
                ? 120
                : undefined,
        header: () => (
          <div className="flex items-center">
            <span className="text-[14px]">{showColumns[index]}</span>
          </div>
        ),
        cell: ({ row }: { row: Row<ManualQueueData> }) => {
          if (col === 'mediaType') {
            return (
              <div className="flex justify-center items-center text-center w-full">
                <svg
                  width="25"
                  height="24"
                  viewBox="0 0 25 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M4.89756 5.89209C5.07149 5.6684 5.29386 5.48703 5.54796 5.36163C5.80206 5.23623 6.08127 5.17005 6.36462 5.16807C6.64797 5.16608 6.92809 5.22834 7.18391 5.35017C7.43974 5.472 7.66463 5.65023 7.84168 5.87146L10.1378 8.74209C10.3881 9.05501 10.5315 9.43988 10.5471 9.84028C10.5627 10.2407 10.4496 10.6355 10.2244 10.967L10.1701 11.0495C9.50031 12.0972 9.64581 13.1232 10.6801 14.3131C11.7751 15.572 12.8749 15.8157 14.1927 15.1227C14.5009 14.9606 14.8481 14.8875 15.1955 14.9116C15.5429 14.9357 15.8767 15.0561 16.1596 15.2592L19.2481 17.4781C19.4926 17.6537 19.6912 17.8857 19.8272 18.1544C19.9631 18.423 20.0324 18.7205 20.0291 19.0215C20.0258 19.3226 19.95 19.6185 19.8082 19.8841C19.6664 20.1497 19.4627 20.3772 19.2143 20.5475L16.7528 22.235C16.4246 22.4599 16.0497 22.6073 15.6562 22.666C15.2627 22.7248 14.861 22.6933 14.4814 22.574C11.3378 21.5855 8.79456 20.0847 6.86331 18.068C5.11168 16.2387 3.76956 13.9951 2.83731 11.3446C2.6291 10.7524 2.57287 10.1175 2.67376 9.49793C2.77466 8.87839 3.02946 8.29409 3.41481 7.79859L4.89756 5.89209ZM6.56893 6.72009C6.49034 6.67349 6.39744 6.65718 6.30767 6.67424C6.21791 6.69129 6.13746 6.74053 6.08143 6.81271L4.59868 8.71959C4.36758 9.0169 4.21479 9.36746 4.15431 9.73913C4.09384 10.1108 4.12762 10.4917 4.25256 10.847C5.11543 13.3013 6.34693 15.3601 7.94706 17.0307C9.69456 18.8555 12.0184 20.2272 14.9314 21.143C15.0941 21.194 15.2662 21.2074 15.4347 21.1822C15.6033 21.157 15.764 21.0939 15.9046 20.9975L18.3664 19.31C18.4161 19.2759 18.4568 19.2304 18.4852 19.1773C18.5136 19.1242 18.5287 19.065 18.5294 19.0048C18.53 18.9446 18.5162 18.8851 18.489 18.8313C18.4618 18.7776 18.4221 18.7312 18.3732 18.6961L15.2843 16.4776C15.2277 16.4369 15.161 16.4129 15.0915 16.408C15.022 16.4032 14.9526 16.4178 14.8909 16.4502C12.9387 17.4773 11.0869 17.0667 9.54831 15.2971C8.06218 13.5875 7.84768 11.7965 8.98393 10.124C9.02896 10.0576 9.05155 9.97861 9.04838 9.8985C9.04521 9.81839 9.01644 9.7414 8.96631 9.67884L6.67056 6.80859C6.65218 6.78558 6.63117 6.76481 6.60793 6.74671L6.56893 6.72009ZM15.6496 1.73821C15.7777 1.87543 15.8497 2.05572 15.8514 2.24346C15.8531 2.4312 15.7842 2.61274 15.6586 2.75221L15.6132 2.79834L13.8162 4.47609H17.3824C20.0119 4.47609 21.4774 5.87184 21.5232 8.42709L21.5243 8.55159C21.5243 11.1428 20.1192 12.7366 17.4964 13.1405C17.2998 13.1707 17.0992 13.1217 16.9388 13.0041C16.7783 12.8864 16.6712 12.7099 16.6409 12.5133C16.6106 12.3167 16.6596 12.116 16.7773 11.9556C16.8949 11.7952 17.0714 11.688 17.2681 11.6577C19.1678 11.3652 20.0243 10.394 20.0243 8.55159C20.0243 6.78759 19.2631 6.00721 17.4897 5.97721L17.3821 5.97609H11.9146C11.2534 5.97609 10.9246 5.18971 11.3622 4.71871L11.4027 4.67821L14.5894 1.70184C14.6614 1.63461 14.746 1.58222 14.8382 1.54767C14.9304 1.51311 15.0286 1.49706 15.127 1.50044C15.2255 1.50382 15.3223 1.52656 15.4119 1.56735C15.5016 1.60815 15.5823 1.66621 15.6496 1.73821Z"
                    fill="#1CC500"
                  />
                </svg>
              </div>
            );
          }
          const value = (row.original as Record<string, any>)[col];
          if (value === null || value === undefined) {
            return '-';
          }
          if (col === 'assign') {
            return (
              <div className="whitespace-pre-line">
                {String(value).split(',').join('\n')}
              </div>
            );
          }
          return String(value);
        },
      })),
      {
        id: 'assignButton',
        cell: ({ row }) => (
          <div className="flex justify-center items-center text-center w-full">
            <AssignButton
              row={row}
              onAssignmentSuccess={getManualQueueList}
              type={'callback'}
            />
          </div>
        ),
      },
    ];
  };

  // 在清除过滤条件后也需要刷新列表
  const clearFilter = () => {
    const clearedFilters = { ...initTableColsData };
    Object.keys(clearedFilters).forEach((key) => {
      if (!clearedFilters[key].require) {
        clearedFilters[key] = {
          ...clearedFilters[key],
          checked: false,
          data: undefined,
        };
      }
    });
    setFilterValue(clearedFilters);
    // 清除过滤条件后刷新列表
    getManualQueueList(1, pagination.pageSize);
  };

  const { globalConfig } = useRole();
  const microfrontendsConfig: microfrontends = globalConfig?.microfrontends;
  const manualQueueTabsCols: ManualQueueTabName[] =
    microfrontendsConfig['ctint-mf-manual-queue']['manual-tab-names'];

  const { t, i18n } = useTranslation();

  const initTableColsData = manualQueueTabsCols.reduce(
    (acc, filter) => {
      if (filter.value === 'createdAt') {
        acc[filter.value] = {
          ...filter,
          checked: true, // 强制设置为选中
          require: true, // 标记为必填
          data: {
            start: dayjs().subtract(30, 'day').format('YYYY-MM-DD HH:mm:ss'),
            end: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          },
        };
      } else if (
        filter.value === 'mediaType' ||
        filter.value === 'conversationDuration'
      ) {
        /* empty */
      } else if (filter.value === 'queueName') {
        acc[filter.value] = {
          ...filter,
          filterType: 'select',
          checked: false,
        };
      } else if (filter.value === 'ended') {
        acc[filter.value] = {
          ...filter,
          filterType: 'select',
          checked: true,
          require: true,
          data: 'No',
        };
      } else {
        acc[filter.value] = {
          ...filter,
          checked: filter.require ? true : false,
          data: filter.require ? '' : undefined,
        };
      }
      return acc;
    },
    {} as Record<string, Condition>
  );
  const [filterValue, setFilterValue] =
    useState<Record<string, Condition>>(initTableColsData);

  const conbineFiltersTagName = () => {
    const tags: string[] = [];
    const sortedFilters = [...Object.values(filterValue)];
    sortedFilters.map((item) => {
      const key = item.value;
      if (filterValue[key].checked && filterValue[key].data) {
        // 对于队列名特殊处理
        let displayValue = filterValue[key].data;
        if (key === 'queueName' && displayValue) {
          displayValue = findQueueName(displayValue as string);
        }
        if (key === 'assign' && displayValue) {
          displayValue = findAssignName(displayValue as string);
        }
        if (key === 'createdAt') {
          // 如果是对象格式，说明是时间范围
          if (typeof displayValue === 'object' && displayValue !== null) {
            displayValue = `${dayjs(displayValue.start).format('YYYY-MM-DD HH:mm:ss')} - ${dayjs(displayValue.end).format('YYYY-MM-DD HH:mm:ss')}`;
          } else {
            // 如果是单个时间点，直接显示
            displayValue = String(displayValue);
          }
        }

        tags.push(
          `${i18n.language == 'en' ? filterValue[key].labelEn : filterValue[key].labelCh}: ${displayValue}`
        );
      }
    });
    return tags;
  };

  const renderTagItems = () => {
    const sortedFilters = Object.values(filterValue);
    return sortedFilters.map((item, i) => {
      const key = item.value;
      if (!filterValue[key].checked || !filterValue[key].data) return null;

      // 对于队列名特殊处理
      let displayValue = filterValue[key].data;
      if (key === 'queueName' && displayValue) {
        displayValue = findQueueName(displayValue as string);
      }
      if (key === 'assign' && displayValue) {
        displayValue = findAssignName(displayValue as string);
      }
      if (key === 'createdAt') {
        // 如果是对象格式，说明是时间范围
        if (typeof displayValue === 'object' && displayValue !== null) {
          displayValue = `${dayjs(displayValue.start).format('YYYY-MM-DD HH:mm:ss')} - ${dayjs(displayValue.end).format('YYYY-MM-DD HH:mm:ss')}`;
        } else {
          // 如果是单个时间点，直接显示
          displayValue = String(displayValue);
        }
      }

      return (
        <div
          key={i}
          className="p-1 mr-1 mt-1 flex flex-row items-center border border-black rounded-[4px] text-[12px] text-black"
        >
          <span className="truncate">
            {(i18n.language === 'en'
              ? filterValue[key].labelEn
              : filterValue[key].labelCh) +
              ':' +
              displayValue}
          </span>
          {/* 对于非 createdAt 的字段才显示关闭按钮 */}
          {key !== 'createdAt' && (
            <span
              onClick={() => {
                setFilterValue((prev) => {
                  const newFilterValues = { ...prev };
                  delete newFilterValues[key].data;
                  newFilterValues[key].checked = false;
                  return newFilterValues;
                });
              }}
              className="ml-1 cursor-pointer"
            >
              <Icon name="cross" />
            </span>
          )}
        </div>
      );
    });
  };
  const [manualQueueList, setManualQueueList] = useState<ManualQueueData[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 50,
    total: 0,
    totalPage: 0,
  });

  // 分页改变的处理函数
  const getManualQueueList = (pageNumber = 1, pageSize = 50) => {
    // 构建过滤参数
    const filters: FilterParams = {};
    filters.type = 'callback';
    Object.entries(filterValue).forEach(([key, condition]) => {
      if (condition.checked && condition.data) {
        if (key === 'createdAt') {
          if (typeof condition.data === 'object' && condition.data !== null) {
            filters.createdAt = dayjs(condition.data.start).format(
              'YYYY-MM-DD HH:mm:ss'
            );
            filters.createdAtToEnd = dayjs(condition.data.end).format(
              'YYYY-MM-DD HH:mm:ss'
            );
          } else {
            filters.createdAt = dayjs(condition.data).format(
              'YYYY-MM-DD HH:mm:ss'
            );
            filters.createdAtToEnd = dayjs(condition.data).format(
              'YYYY-MM-DD HH:mm:ss'
            );
          }
        } else {
          filters[key] = condition.data;
        }
      }
    });

    setLoading(true);
    getManualQueuePage(basePath, pageNumber, pageSize, filters)
      .then((result) => {
        const respData: ManualQueueDataResp = result.data;
        if (respData.data) {
          setManualQueueList(respData.data.list || []);
          setPagination({
            current: respData.data.current_page || pageNumber,
            pageSize: respData.data.page_size || pageSize,
            total: respData.data.total || 0,
            totalPage: respData.data.total_pages || 0,
          });
          setLoading(false);
        } else {
          setManualQueueList([]);
          setPagination({
            current: pageNumber,
            pageSize: pageSize,
            total: 0,
            totalPage: 0,
          });
          setLoading(false);
        }
      })
      .catch((error) => {
        const errorMessage = error.response?.data?.error || '';
        toast({
          title: 'error',
          description: errorMessage,
          variant: 'error',
        });
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const showColumns: string[] = [];
  const showColumnsKey: string[] = [];
  const [table, setTable] = useState<TableType<ManualQueueData>>();
  const [rowSelection, setRowSelection] = useState({});

  Object.entries(manualQueueTabsCols).forEach(([key, item]) => {
    if (item.active && item.value !== 'ended') {
      showColumnsKey.push(item.value);
      showColumns.push(i18n.language == 'en' ? item.labelEn : item.labelCh);
    }
  });

  // 控制Audio
  const [isAudioModalOpen, setIsAudioModalOpen] = useState(false);
  const [currentAudioUrl, setCurrentAudioUrl] = useState('');
  const [currentConversationInfo, setCurrentConversationInfo] =
    useState<ManualQueueData>();
  const handlePlay = async (data: ManualQueueData) => {
    try {
      if (data.conversationId) {
        setActivePlayButton(data.conversationId);
      }
      const audioUrl = await getAudioUrl(basePath, data.conversationId);
      setCurrentAudioUrl(audioUrl.data.data);
      setIsAudioModalOpen(true);
      setCurrentConversationInfo(data);
    } catch (error) {
      console.error('Failed to play audio:', error);
      setActivePlayButton(null); // 添加这行
    }
  };
  // 添加搜索按钮点击事件处理函数
  const handleSearch = () => {
    // 重置到第一页并使用当前的过滤条件进行搜索
    getManualQueueList(1, pagination.pageSize);
  };

  return (
    <>
      {loading && (
        <div
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 999,
          }}
        >
          <Loader size={64} />
        </div>
      )}
      <div
        data-testid="cypress-panel-title-filter"
        id="panelContainer"
        className="relative flex flex-col h-full w-full gap-4"
      >
        <WhitePanel className="flex flex-row w-full overflow-visible">
          <div className="flex-1 flex flex-row items-center">
            <SearchInput tags={conbineFiltersTagName()}>
              <section>
                <section className="max-h-[409px] w-[700px] overflow-y-auto">
                  <section className="p-4">
                    {Object.keys(filterValue).filter(
                      (key) => filterValue[key].data !== undefined
                    ).length > 0 && (
                      <div className="flex flex-wrap flex-row">
                        {renderTagItems()}
                      </div>
                    )}
                  </section>
                  <section className="px-4">
                    <h2 className="text-[14px] mb-2">
                      {t('ctint-mf-manual-queue.filter.available')}:
                    </h2>
                    <div className="flex flex-col gap-y-2">
                      <FilterComponent
                        filterValues={filterValue}
                        setFilterValues={setFilterValue}
                      />
                    </div>
                  </section>
                </section>
              </section>
            </SearchInput>

            <Button
              className="self-center ml-2"
              bodyClassName="border border-black py-[0.375rem]"
              onClick={handleSearch}
              size="s"
            >
              {t('ctint-mf-manual-queue.filter.search')}
            </Button>

            <Button
              className="self-center ml-2"
              bodyClassName="border border-black py-[0.375rem]"
              onClick={() => clearFilter()}
              variant="blank"
              size="s"
            >
              {t('ctint-mf-manual-queue.filter.reset', 'Reset')}
            </Button>

            {/* 刷新按钮 */}
            <button
              onClick={handleSearch}
              className="ml-auto p-2 rounded-full hover:bg-primary-50 transition-colors"
              title={t('refresh', 'Refresh')}
            >
              <RefreshCcw
                // color={THEME_FONT_COLORS.primary[500]}
                size={20}
              />
            </button>
          </div>
        </WhitePanel>
        <Panel
          headerClassName="bg-white border-b border-grey-200"
          containerClassName="flex flex-col h-full min-h-0"
        >
          <div className="overflow-auto px-4 flex-1 min-h-0">
            <DataTable<ManualQueueData>
              data={manualQueueList}
              columns={generateColumns(showColumnsKey, showColumns, [])}
              loading={false}
              emptyMessage="No data found"
              rowSelection={rowSelection}
              setRowSelection={setRowSelection}
              onTableSetUp={(table) => setTable(table)}
              resize={true}
            />
          </div>
          {pagination.total > 0 && (
            <section className="flex-shrink-0 px-4 py-4 border-t">
              <div>
                <Pagination
                  total={pagination.totalPage}
                  current={pagination.current}
                  initialPage={1}
                  siblings={1}
                  totalCount={pagination.total}
                  perPage={pagination.pageSize}
                  onChange={(page) => {
                    getManualQueueList(page, pagination.pageSize);
                  }}
                  handleOnNext={() => {
                    getManualQueueList(
                      pagination.current + 1,
                      pagination.pageSize
                    );
                  }}
                  handleOnPrevious={() => {
                    getManualQueueList(
                      pagination.current - 1,
                      pagination.pageSize
                    );
                  }}
                  handlePerPageSetter={(newPageSize) => {
                    getManualQueueList(1, newPageSize);
                  }}
                />
              </div>
            </section>
          )}
        </Panel>
        <AudioModal
          isOpen={isAudioModalOpen}
          onClose={() => {
            setIsAudioModalOpen(false);
            setActivePlayButton(null);
          }}
          audioUrl={currentAudioUrl}
          callInfo={currentConversationInfo}
        />
        <Toaster />
      </div>
    </>
  );
}

export default function Main() {
  return <CallBackPage />;
}
