import React, { useEffect, useState } from 'react';
import {
    CallBack,
  toast,
  Toaster,
  useRole,
  useRouteHandler,
} from '@cdss-modules/design-system';
import {
  callbackNow,
  conversationActiveList,
  saveConversationRel,
} from '../../../lib/api';
import { VoiceMailInfo } from '../../../types/microfrontendsConfig';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { cn } from '@cdss-modules/design-system/lib/utils';
import IconCallBg from '@cdss-modules/design-system/components/_ui/Icon/IconCallBg';

interface CallBackProps {
  phoneNumber: string;
  conversation: VoiceMailInfo;
  isConnected: boolean;
}

const CallBackDetail: React.FC<CallBackProps> = ({
  phoneNumber,
  conversation,
  isConnected,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const { basePath } = useRouteHandler();
  const [hasActiveCall, setHasActiveCall] = useState(false);

  const handleCallBack = (conversationId: string) => {
    const callBackNowId = conversationId;
    const sourceConversationId = conversation.conversationId;
    const conversationRel = {
      referenceConversationId: callBackNowId,
      sourceConversationId: sourceConversationId,
    };
    saveConversationRel(basePath, conversationRel).catch((error) => {
      const errorMessage = error?.response?.data?.error || 'Operation Failed';
      toast({
        title: 'error',
        description: errorMessage,
        variant: 'error',
      });
    });
  };

  const { userConfig } = useRole();


  const formatUTCDate = (timestamp: string | undefined): string => {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    // 加上8小时转换到 UTC+8
    const utc8Date = new Date(date.getTime() + 8 * 60 * 60 * 1000);

    const year = utc8Date.getUTCFullYear();
    const month = String(utc8Date.getUTCMonth() + 1).padStart(2, '0');
    const day = String(utc8Date.getUTCDate()).padStart(2, '0');
    const hours = String(utc8Date.getUTCHours()).padStart(2, '0');
    const minutes = String(utc8Date.getUTCMinutes()).padStart(2, '0');

    return `${year}/${month}/${day} ${hours}:${minutes}`;
  };


  return (
    <div className="h-full flex flex-col bg-white">
      {/* 上部分 - 电话号码和通话时长 */}
      <div className="pt-16 pb-8">
        <h1 className="text-4xl font-medium tracking-wider mb-2 text-center">
          {phoneNumber}
        </h1>
        <span className="text-gray-400 text-center block">
          {formatUTCDate(conversation.selfParticipantStartTime)}
        </span>
      </div>

      {/* 中间部分 - 背景图标 */}
      <div className="flex items-center justify-center py-4">
        <IconCallBg size="45%" />
      </div>

      {/* 底部部分 - 按钮 */}
      <div className="flex-1 flex flex-col items-center justify-center px-8">
        {conversation.callbackNumber && (
          <div className="w-[50%] mb-2 bg-gray-100 p-3 rounded-md text-center">
            <div className="font-medium text-gray-700">
              Callback Number : +{conversation.callbackNumber}
            </div>
          </div>
        )}
        <CallBack
          className="w-full"
          triggerClassName="w-[50%]"
          number={conversation.callbackNumber || phoneNumber}
          onCallBack={(res) => {
            handleCallBack(res);
          }}
        />
      </div>
      <Toaster />
    </div>
  );
};

export default CallBackDetail;
