import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import {
  Loader,
  MiniWallboard,
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
  THEME_FONT_COLORS,
  useRouteHandler,
} from '@cdss-modules/design-system';
import CallDetailView from './callDetailView';
import { getConversationInfo } from '../../../lib/api';
import InfoPanel from './info';
import { VoiceMailInfo } from '../../../types/microfrontendsConfig';
import CallHeader from './callHeader';
import CallInfo from '@cdss-modules/design-system/components/_ui/CallInfo';
import { TbarProvider } from '@cdss-modules/design-system/context/TBarContext';
import Saa from '@cdss-modules/design-system/components/_ui/Saa';
import { useCustomerHistory } from '@cdss-modules/design-system/lib/hooks/useCustomerInfo';
import { useRole } from '@cdss-modules/design-system';

const queryClient = new QueryClient();
const MainContent = () => {
  const { searchParams, basePath, activePath } = useRouteHandler();
  const { globalConfig } = useRole();

  const [conversationId, setConversationId] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [data, setData] = useState<VoiceMailInfo>({});
  const [error, setError] = useState<boolean>(false);
  const { SAADetail, getSAADetail, SAADetailLoading } = useCustomerHistory();
  const [isFullResizablePanel, setIsFullResizablePanel] =
    useState<boolean>(false);

  const hiddenMiniWallboard =
    globalConfig?.microfrontends?.['ctint-mf-manual-queue']
      ?.hiddenMiniWallboard ?? false;

  console.log('MainContent => hiddenMiniWallboard', hiddenMiniWallboard);

  const fetch = async () => {
    if (!conversationId) {
      return;
    }
    try {
      setIsLoading(true);
      const response = await getConversationInfo(basePath, conversationId);
      setData(response.data.data);
      setError(false);
    } catch (err) {
      setError(true);
      console.error('Failed to fetch conversation:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // 监听 conversationId 变化来获取数据
  useEffect(() => {
    if (activePath?.includes('/callback')) {
      const urlConversationId = searchParams.get('conversationId');
      if (urlConversationId) {
        // 如果 conversationId 发生变化才设置新值
        if (urlConversationId !== conversationId) {
          setConversationId(urlConversationId);
        } else {
          // 如果 conversationId 没变，但是进入了这个页面，也需要获取最新数据
          fetch();
        }
      }
    }
  }, [activePath, searchParams]);

  useEffect(() => {
    if (conversationId) {
      fetch();
    }
  }, [conversationId]); // Separate effect to handle data fetching

  const onRefresh = async () => {
    try {
      await fetch();
    } catch (err) {
      console.error('Failed to refresh conversation data:', err);
    }
  };

  if (isLoading) {
    return (
      <div
        style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          zIndex: 999,
        }}
      >
        <Loader size={64} />
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ color: THEME_FONT_COLORS.status.success }}>
        {/*wrap up successfully*/}
      </div>
    );
  }

  return (
    <div className="relative flex flex-col h-full gap-4 justify-between">
      <ResizablePanelGroup
        autoSaveId="call-detail-panel"
        direction="horizontal"
        className="h-full flex-1 flex w-full gap-x-3 "
      >
        <ResizablePanel minSize={30}>
          <CallDetailView
            key={conversationId}
            voiceMailInfo={data}
            onRefresh={onRefresh}
          />
        </ResizablePanel>
        <ResizableHandle />
        <ResizablePanel minSize={30}>
          {/*<CallInfo*/}
          {/*  customerData={{*/}
          {/*    conversationId: conversationId,*/}
          {/*    phoneNumber: 'tel:+' + (data?.from || ''),*/}
          {/*  }}*/}
          {/*  headerClass="p-4"*/}
          {/*/>*/}
          <ResizablePanelGroup
            autoSaveId="SAA-detail-panel"
            direction="vertical"
            className="h-full flex-1 flex w-full h-0 gap-x-3 "
          >
            <ResizablePanel
              style={
                isFullResizablePanel
                  ? {
                      flexGrow: 2000,
                      flexShrink: 1,
                      flexBasis: '0px',
                    }
                  : undefined
              }
            >
              <CallInfo
                customerData={{
                  conversationId: conversationId,
                  phoneNumber: 'tel:+' + (data?.from || ''),
                }}
                headerClass="p-4"
              />
            </ResizablePanel>
            <ResizableHandle />
            <ResizablePanel minSize={50}>
              <Saa
                data={{
                  SAADetail: SAADetail,
                  getSAADetail: getSAADetail,
                  convId: conversationId,
                  SAADetailLoading: SAADetailLoading,
                }}
                useSAAv2={true}
                setIsFullResizablePanel={setIsFullResizablePanel}
                isShowCopy={true}
              />
            </ResizablePanel>
          </ResizablePanelGroup>
        </ResizablePanel>
      </ResizablePanelGroup>
      <TbarProvider>
        <MiniWallboard hidden={hiddenMiniWallboard} />
      </TbarProvider>
    </div>
  );
};

export default function Main() {
  return (
    <QueryClientProvider client={queryClient}>
      <MainContent />
    </QueryClientProvider>
  );
}
