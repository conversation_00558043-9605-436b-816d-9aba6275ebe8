import React, { useState, useEffect, useRef } from 'react';
import { X } from 'lucide-react';

interface RecipientFieldProps {
  /**
   * 字段标签（From, To, Cc, Bcc）
   */
  label: string;

  /**
   * 收件人邮箱地址列表
   */
  recipients: string[];

  /**
   * 添加新收件人的回调函数
   */
  onAdd?: (email: string) => void;

  /**
   * 移除收件人的回调函数
   */
  onRemove?: (index: number) => void;

  /**
   * 是否禁用添加/编辑功能
   */
  disabled?: boolean;
}

/**
 * 邮件收件人字段组件
 *
 * 用于显示和编辑邮件的收件人（发件人、收件人、抄送、密送）
 * 支持按回车添加邮箱和自动检测有效邮箱并添加
 */
const RecipientField: React.FC<RecipientFieldProps> = ({
  label,
  recipients,
  onAdd,
  onRemove,
  disabled = false,
}) => {
  // 保存当前输入框中的值
  const [inputValue, setInputValue] = useState('');
  // 保存定时器ID，用于防抖处理
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  // 输入框引用
  const inputRef = useRef<HTMLInputElement>(null);
  // 追踪用户是否正在连续输入
  const [isTyping, setIsTyping] = useState(false);

  // 邮箱验证正则表达式
  const EMAIL_REGEX = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

  // 检查邮箱是否有效
  const isValidEmail = (email: string): boolean => {
    return EMAIL_REGEX.test(email.trim());
  };

  // 添加邮箱地址的处理函数
  const addEmailAddress = (email: string) => {
    if (!email.trim() || !isValidEmail(email)) return;

    if (onAdd) {
      onAdd(email.trim());
    }

    // 添加成功后清空输入框
    setInputValue('');
  };

  // 处理输入变化的函数，带防抖功能
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
    setIsTyping(true);

    // 清除之前的定时器
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    // 如果输入为空，不进行处理
    if (!value.trim()) {
      setIsTyping(false);
      return;
    }

    // 设置新的定时器，500ms 后检查邮箱是否有效
    // 延长延迟时间为500ms，给用户更多反应时间
    timerRef.current = setTimeout(() => {
      setIsTyping(false);

      // 只处理包含分隔符的情况，让单个邮箱地址需要明确的提交动作
      if (value.includes(',') || value.includes(';')) {
        const emails = value.split(/[,;]+/).filter((email) => email.trim());

        // 检查并添加有效的邮箱
        emails.forEach((email) => {
          if (isValidEmail(email)) {
            addEmailAddress(email);
          }
        });
      }
      // 当输入有效邮箱且后面跟着空格时才自动添加
      else if (value.endsWith(' ') && isValidEmail(value.trim())) {
        addEmailAddress(value.trim());
      }
      // 不再自动添加单个邮箱，除非有明确的分隔符或空格
    }, 500);
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // 如果按下回车键，尝试添加当前输入的邮箱
    if (e.key === 'Enter') {
      e.preventDefault();

      if (inputValue.trim()) {
        addEmailAddress(inputValue);
      }
    }

    // 如果按下逗号、分号或者Tab键，也可以添加邮箱
    if (e.key === ',' || e.key === ';' || e.key === 'Tab') {
      if (inputValue.trim() && isValidEmail(inputValue)) {
        e.preventDefault();
        addEmailAddress(inputValue);
      }
    }

    // 处理删除键 - 当输入框为空且存在收件人时
    if (e.key === 'Backspace' && inputValue === '' && recipients.length > 0) {
      // 阻止默认行为，防止浏览器后退
      e.preventDefault();

      // 将最后一个收件人移回输入框以便编辑
      const lastRecipient = recipients[recipients.length - 1];
      if (onRemove) {
        onRemove(recipients.length - 1);
        // 稍微延迟设置输入值，确保状态更新后再设置
        setTimeout(() => {
          setInputValue(lastRecipient);
          // 确保焦点在输入框上并将光标移到末尾
          if (inputRef.current) {
            inputRef.current.focus();
          }
        }, 10);
      }
    }
  };

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  return (
    <div className="flex items-center mb-2">
      <span className="w-16 text-sm text-gray-600">{label}:</span>
      <div className="flex-1 flex flex-wrap gap-1 min-h-[32px] px-2 py-1 border rounded-md bg-white">
        {recipients.map((email, index) => (
          <div
            key={index}
            className="flex items-center gap-1 bg-gray-50 px-2 py-0.5 rounded"
          >
            <span className="text-sm text-gray-700">{email}</span>
            {!disabled && (
              <button
                onClick={() => {
                  if (onRemove) {
                    onRemove(index);
                  }
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-3 h-3" />
              </button>
            )}
          </div>
        ))}
        {!disabled && (
          <input
            ref={inputRef}
            type="email"
            className="flex-1 min-w-[120px] outline-none text-sm"
            placeholder="Enter email address"
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onBlur={() => {
              // 当输入框失去焦点且不是在输入过程中，如果是有效邮箱则添加
              if (!isTyping && inputValue.trim() && isValidEmail(inputValue)) {
                addEmailAddress(inputValue);
              }
              // 总是重置输入状态
              setIsTyping(false);
            }}
          />
        )}
      </div>
    </div>
  );
};

export default RecipientField;
