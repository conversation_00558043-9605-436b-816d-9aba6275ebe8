import React, { useEffect, useMemo, useState } from 'react';
import {
  Loader,
  Toaster,
  useRole,
  useRouteHandler,
  useToast,
} from '@cdss-modules/design-system';
import {
  SendEmailRequest,
  EmailAttachment,
  EmailTransferReq,
  TransferReq,
} from '../../../../types/microfrontendsConfig';
import {
  emailCancelTransfer,
  emailTransfer,
  saveDraft,
  sendEmail,
  wrapUp,
} from '../../../../lib/api';
import CallHeader from '../../Detail/callHeader';
import WrapUp from '@cdss-modules/design-system/components/_ui/WrapUp';
import {
  TWrapUpCodeQueueData,
  TWrapUpFormState,
  TWrapUpOption,
} from '@cdss-modules/design-system/@types/Interaction';
import { fireGetWrapupCategory } from '@cdss-modules/design-system/lib/api';
import EmailReplyEditor from './EmailReplyEditor';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import {
  emailEditorStyles,
  replaceEmailInlineAttachments,
  sanitizeEmailBody,
} from '@cdss-modules/design-system/lib/utils';

interface EmailDetailViewProps {
  emailInfo: EmailInfo;
  onRefresh: () => Promise<void>;
}

interface EmailInfo {
  conversationId?: string;
  from?: string;
  selfParticipantId?: string;
  selfParticipantState?: string;
  selfParticipantStartTime?: string;
  selfParticipantEndTime?: string;
  selfParticipantWrapupRequired?: boolean;
  selfParticipantWrapupPrompt?: string;
  selfQueueId?: string;
  type?: string;
  email?: EmailItem[];
  transferParticipantId?: string;
  transferAgentName?: string;
}

const EmailDetailView: React.FC<EmailDetailViewProps> = ({
  emailInfo,
  onRefresh,
}) => {
  const { basePath } = useRouteHandler();
  const [showWrapUp, setShowWrapUp] = useState(false);
  const { toast } = useToast();
  const [isTransferring, setIsTransferring] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { userConfig } = useRole();

  const [viewMode, setViewMode] = useState<
    'view' | 'reply' | 'forward' | 'new' | 'reply-disconnected'
  >('view');

  // 获取发件人信息，用于在CallHeader中显示
  const senderDisplayName = useMemo(() => {
    if (!emailInfo?.email) return emailInfo.from || '';

    // 查找入站邮件
    const inboundEmail = emailInfo.email.find(
      (email) => email.emailDirection === 'IN'
    );
    if (!inboundEmail || !inboundEmail.detail?.emailSender)
      return emailInfo.from || '';

    try {
      // 解析发件人信息
      const sender = JSON.parse(inboundEmail.detail.emailSender);
      if (sender?.emailAddress?.name && sender?.emailAddress?.address) {
        // 组合姓名和邮箱地址
        return `${sender.emailAddress.address} <${sender.emailAddress.name}>`;
      }
    } catch (error) {
      console.error('Error parsing email sender:', error);
    }

    return emailInfo.from || '';
  }, [emailInfo]);

  useEffect(() => {
    if (emailInfo) {
      setIsConnected(emailInfo.selfParticipantState === 'connected');
      setShowWrapUp(!(emailInfo.selfParticipantState === 'connected'));
    }
  }, []);

  useEffect(() => {
    if (emailInfo?.email) {
      // 只有当客服处于连接状态时才处理草稿邮件并设置编辑模式
      if (emailInfo.selfParticipantState === 'connected') {
        // 首先打印整个邮件数组
        console.log('All emails:', emailInfo.email);
        // 检查每封邮件的方向和草稿状态
        emailInfo.email.forEach((email, index) => {
          console.log(`Email ${index}:`, {
            id: email.id,
            direction: email.emailDirection,
            isDraft: email.detail.isDraft,
            detail: email.detail,
          });
        });
        // 添加详细的条件检查
        const draftEmail = emailInfo.email.find((email) => {
          const directionMatch = email.emailDirection === 'OUT';
          const isDraftMatch = Number(email.detail.isDraft) === 1;
          console.log('Checking email:', {
            id: email.id,
            directionMatch,
            isDraftMatch,
            actualDirection: email.emailDirection,
            actualIsDraft: email.detail.isDraft,
            isDraftType: typeof email.detail.isDraft,
          });
          return directionMatch && isDraftMatch;
        });
        console.log('Draft email found:', draftEmail);
        if (draftEmail?.detail) {
          const mode = draftEmail.detail.outboundType?.toLowerCase() as
            | 'reply'
            | 'forward'
            | 'new'
            | 'reply-disconnected';
          console.log('Mode detected:', mode);
          if (
            mode === 'reply' ||
            mode === 'forward' ||
            mode === 'new' ||
            mode === 'reply-disconnected'
          ) {
            console.log('Setting view mode to:', mode);
            setViewMode(mode);
          } else {
            console.log('Setting view mode to: view (no valid mode detected)');
            setViewMode('view');
          }
        }
      } else {
        // 如果未连接，强制设置为查看模式
        console.log('Agent not connected, forcing view mode');
        setViewMode('view');
      }
    } else {
      console.log('No email info available');
    }
  }, [emailInfo?.email, emailInfo?.selfParticipantState]);

  // 存储 WrapUp 表单状态
  const [wrapUpFormState, setWrapUpFormState] = useState<TWrapUpFormState>({
    selectedItems: [],
    notes: '',
    expandedItems: [],
    itemRemarks: [],
    activeRemarkItems: [],
  });
  const [wrapUpData, setWrapUpData] = useState<TWrapUpCodeQueueData[]>([]);
  const [wrapUpLoading, setWrapUpLoading] = useState(false);
  const [wrapUpError, setWrapUpError] = useState<string | null>(null);

  //转接
  const handleTransfer = async (id: string, type: string) => {
    if (isTransferring) return;
    setIsTransferring(true);

    toast({
      title: 'Transfer in progress',
      description: 'Please wait while we process your transfer request...',
      variant: 'loading',
    });

    const transferReq: EmailTransferReq = {
      conversationId: emailInfo.conversationId,
      agentParticipantId: emailInfo.selfParticipantId,
    };

    if (type === 'user') {
      // 转接给用户时，设置目标用户ID
      transferReq.destinationUserId = id;
      transferReq.transferType = 'Attended';
    } else if (type === 'workgroup') {
      // 转接给工作组时，设置目标队列ID
      transferReq.destinationQueueId = id;
    }
    const result = await emailTransfer(basePath, transferReq)
      .then((response) => {
        toast({
          title: 'Success',
          description: 'Transfer request has been successfully sent',
          variant: 'success',
        });
      })
      .catch((error) => {
        const errorMessage = error?.response?.data?.error || 'Operation Failed';
        toast({
          title: 'error',
          description: errorMessage,
          variant: 'error',
        });
      })
      .finally(async () => {
        //await onRefresh();
        setIsTransferring(false);
      });
  };

  //取消转接
  const cancelTransfer = async () => {
    const transferReq: TransferReq = {
      conversationId: emailInfo.conversationId,
      transferParticipantId: emailInfo.transferParticipantId,
    };
    const result = await emailCancelTransfer(basePath, transferReq)
      .then((response) => {
        toast({
          title: 'Success',
          description: 'Transfer request has been successfully sent',
          variant: 'success',
        });
        onRefresh();
      })
      .catch((error) => {
        const errorMessage = error?.response?.data?.error || 'Operation Failed';
        toast({
          title: 'error',
          description: errorMessage,
          variant: 'error',
        });
      });
  };

  //查WrapUpCode
  useEffect(() => {
    if ((!showWrapUp && isConnected) || !userConfig?.id) return;
    setWrapUpLoading(true);
    fireGetWrapupCategory(userConfig.id, basePath)
      .then((response) => {
        const data = response.data.data;
        const filteredData = data.find(
          (queue: { queueId: string }) =>
            queue.queueId === emailInfo?.selfQueueId
        );
        setWrapUpData(filteredData ? [filteredData] : []);
      })
      .catch((err) => {
        setWrapUpError('Failed to fetch wrap-up data');
        toast({
          title: 'Error',
          description: err?.response?.data?.error,
          variant: 'error',
        });
      })
      .finally(() => {
        setWrapUpLoading(false);
      });
  }, [emailInfo?.selfQueueId]);
  // }, [showWrapUp, userConfig?.id, basePath]);

  //头部按钮 控制 wrapup组件是否打开
  const handleWrapUpClick = () => {
    if (!showWrapUp) {
      setShowWrapUp(true);
    }
  };

  //wrapup组件 回调函数。关闭用
  const handleWrapUpClose = () => {
    setShowWrapUp(false);
  };

  //处理 WrapUp 表单状态更新
  const handleWrapUpStateChange = (newState: TWrapUpFormState) => {
    setWrapUpFormState(newState);
  };

  //提交wrapup表单
  const handleWrapUpSubmit = async () => {
    if (!emailInfo || !userConfig?.id) return;
    try {
      setLoading(true);
      setIsSubmitting(true);
      const findItemById = (
        items: TWrapUpOption[],
        targetId?: string
      ): TWrapUpOption | undefined => {
        if (!targetId) return undefined;
        for (const item of items) {
          if (item.id === targetId) return item;
          if (item.items) {
            const found = findItemById(item.items, targetId);
            if (found) return found;
          }
        }
        return undefined;
      };
      const wrapUpList = wrapUpFormState.selectedItems
        .map((itemId) => {
          const item = findItemById(wrapUpData[0]?.items || [], itemId);
          // 删除这里的 type 判断，所有被选中的项目都会被包含
          if (!item) return null;
          const itemRemark = wrapUpFormState.itemRemarks.find(
            (r) => r.itemId === itemId
          );
          return {
            wrapUpCode: item.code,
            wrapUpName: item.name,
            remark: itemRemark?.remark || '',
          };
        })
        .filter(Boolean);

      const payload = {
        wrapUpList,
        remark: wrapUpFormState.notes,
        participantId: emailInfo.selfParticipantId,
        userId: userConfig.id,
        conversationId: emailInfo.conversationId,
        queueId: emailInfo.selfQueueId,
        state: 'disconnected',
      };
      await wrapUp(basePath, payload);
      setShowWrapUp(false);
      onRefresh();
      toast({
        title: 'Success',
        description: 'Wrap-up submitted successfully',
        variant: 'success',
      });
    } catch (error) {
      const errorMessage = 'Operation failed';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'error',
      });
    } finally {
      setLoading(false);
      setIsSubmitting(false);
    }
  };

  const onEndCall = () => {
    setIsConnected(false);
    const payload = {
      participantId: emailInfo.selfParticipantId,
      state: 'disconnected',
      conversationId: emailInfo.conversationId,
    };
    wrapUp(basePath, payload)
      .then((response) => {
        setIsConnected(false);
        setShowWrapUp(true);
        if (
          emailInfo.selfParticipantWrapupPrompt === 'agentRequested' &&
          emailInfo.selfParticipantWrapupRequired === false
        ) {
          // onRefresh();
        }
      })
      .catch((err) => {
        setIsConnected(true);
        toast({
          title: 'Error',
          description: err?.response?.data?.error,
          variant: 'error',
        });
      });
  };

  if (!emailInfo?.conversationId) {
    return <div className="text-gray-500">No conversation data available</div>;
  }

  //email组件的处理函数

  // 处理回复和转发动作
  const handleAction = async (mode: 'reply' | 'forward'): Promise<void> => {
    const saveDraftReq: SendEmailRequest = {
      outboundType: mode,
      gcConversationId: emailInfo.conversationId,
    };
    const result = await saveDraft(basePath, saveDraftReq);
    await onRefresh();
  };

  // 处理保存草稿
  const handleSaveDraft = async (
    draftData: SendEmailRequest,
    isAutoSave: boolean
  ) => {
    try {
      // 只在非自动保存时设置loading状态
      if (!isAutoSave) {
        setLoading(true);
      }

      draftData.gcConversationId = emailInfo.conversationId;
      console.log('draftData:', draftData);
      const result = await saveDraft(basePath, draftData);

      if (!isAutoSave) {
        toast({
          title: 'Success',
          description: 'Draft saved successfully',
          variant: 'success',
        });
      }
    } catch (error) {
      // 错误提示也只在非自动保存时显示
      if (!isAutoSave) {
        toast({
          title: 'Error',
          description: 'Failed to save draft',
          variant: 'error',
        });
      } else {
        // 自动保存错误可以只在控制台记录，不打扰用户
        console.error('Auto-save draft failed:', error);
      }
    } finally {
      // 只在非自动保存时重置loading状态
      if (!isAutoSave) {
        setLoading(false);
      }
    }
  };

  // 处理发送邮件
  const handleSend = async (emailData: SendEmailRequest) => {
    try {
      setLoading(true);
      // 调用发送邮件的 API
      emailData.gcConversationId = emailInfo.conversationId;
      emailData.selfParticipantId = emailInfo.selfParticipantId;
      const result = await sendEmail(basePath, emailData);
      await onRefresh();
      toast({
        title: 'Success',
        description: 'Email sent successfully',
        variant: 'success',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to send',
        variant: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      {loading && (
        <div
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 999,
          }}
        >
          <Loader size={64} />
        </div>
      )}
      <div className="h-full flex flex-col bg-white border-r">
        <CallHeader
          phoneNumber={senderDisplayName}
          onTransfer={handleTransfer}
          onWrapUp={handleWrapUpClick}
          showWrapUp={showWrapUp}
          isConnected={isConnected}
          voiceMailInfo={emailInfo}
          onEndCall={onEndCall}
          cancelTransfer={cancelTransfer}
        />
        <div className="flex-1 overflow-auto">
          {showWrapUp ? (
            <WrapUp
              onRevert={handleWrapUpClose}
              onSubmit={handleWrapUpSubmit}
              formState={wrapUpFormState}
              onFormStateChange={handleWrapUpStateChange}
              wrapUpData={wrapUpData}
              loading={wrapUpLoading}
              error={wrapUpError}
              isSubmit={!isConnected}
              className="h-full"
              isSubmitting={isSubmitting}
            />
          ) : viewMode === 'view' ? (
            <EmailContent
              email={emailInfo.email}
              onSaveDraft={handleAction}
              isConnected={isConnected}
            />
          ) : (
            <EmailReplyEditor
              emailData={emailInfo.email || []}
              type={viewMode}
              onSaveDraft={handleSaveDraft}
              onSend={handleSend}
              onRefresh={onRefresh}
            />
          )}
        </div>
        <Toaster />
      </div>
    </>
  );
};

export default EmailDetailView;

export interface EmailAddress {
  emailAddress: {
    name: string;
    address: string;
  };
}

// 邮件详情接口
export interface EmailDetail {
  id: string;
  emailId: string;
  emailDirection: 'IN' | 'OUT';
  outboundType: string | null; //用于标识是回复还是转发
  emailSender: string; // 注意这是 JSON 字符串
  emailToRecipient: string; // 注意这是 JSON 字符串
  emailCcRecipient: string; // 注意这是 JSON 字符串
  emailBccRecipient: string | null; // 新增
  emailSubject: string;
  emailBodyType: string;
  emailBody: string;
  // 新增这些字段，在返回数据中存在
  importance: string;
  flagStatus: string;
  createTime: string;
  updateTime: string;
  createBy: string | null;
  updateBy: string | null;
  platform: string | null;
  tenant: string | null;
  hasAttachments: boolean;
  sentDateTime: string;
  isDraft: number;
}

// 邮件项接口
export interface EmailItem {
  id: string;
  microsoftEmailId: string;
  microsoftConversationId: string;
  emailDirection: 'IN' | 'OUT';
  // 新增这些字段，在返回数据中存在
  createTime: string;
  updateTime: string | null;
  createBy: string | null;
  updateBy: string | null;
  tenant: string | null;
  platform: string | null;
  emailSubject: string;
  mailBoxAddress: string;
  gcConversationId: string;
  gcQueueId: string;
  caseId: string;
  status: string;
  hashedMicrosoftEmailId: string | null;
  hashedMicrosoftConversationId: string | null;
  detail: EmailDetail;
  attachments: {
    items: EmailAttachment[];
  };
}

export interface EmailInfoProps {
  email?: EmailItem[];
  onSaveDraft?: (type: 'reply' | 'forward') => void;
  isConnected?: boolean;
}

export const EmailContent: React.FC<EmailInfoProps> = ({
  email,
  onSaveDraft,
  isConnected,
}) => {
  const selectEmail = () => {
    if (!email || email.length === 0) return null;

    // 首先检查是否有草稿邮件
    const draftEmail = email.find((e) => e.detail.isDraft === 1);
    if (draftEmail) return draftEmail;

    // 然后查找入站邮件
    const inboundEmail = email.find((e) => e.emailDirection === 'IN');
    if (inboundEmail) return inboundEmail;

    // 如果没有入站邮件，就返回第一个出站邮件
    const outboundEmail = email.find((e) => e.emailDirection === 'OUT');
    return outboundEmail; // 如果没有出站邮件，这里会返回 undefined
  };

  const selectedEmail = selectEmail();
  const emailDetail = selectedEmail?.detail;
  const attachments = selectedEmail?.attachments?.items || [];

  // 处理内联附件和邮件正文
  const processedEmailBody = useMemo(() => {
    if (!emailDetail?.emailBody) return '';
    return replaceEmailInlineAttachments(
      emailDetail.emailBody,
      attachments.map((attachment) => ({
        isInLine: attachment.isInLine,
        contentId: attachment.contentId,
        url: attachment.url,
      }))
    );
  }, [emailDetail?.emailBody, attachments]);

  if (!selectedEmail) {
    return <div className="p-4 text-gray-500">No email data available</div>;
  }

  // Parse recipients
  const toRecipients: EmailAddress[] = emailDetail?.emailToRecipient
    ? JSON.parse(emailDetail.emailToRecipient)
    : [];
  const ccRecipients: EmailAddress[] = emailDetail?.emailCcRecipient
    ? JSON.parse(emailDetail.emailCcRecipient)
    : [];
  const sender: EmailAddress = emailDetail?.emailSender
    ? JSON.parse(emailDetail.emailSender)
    : null;

  // 获取非内联附件
  const regularAttachments = attachments.filter((item) => !item.isInLine);

  // 处理回复和转发
  const handleAction = async (type: 'reply' | 'forward') => {
    if (onSaveDraft) {
      await onSaveDraft(type);
    }
  };

  // 格式化发送时间
  const formatSendTime = (sentDateTime?: string | null): string => {
    if (!sentDateTime) return ''; // Return a default message when empty, null or undefined

    try {
      const date = new Date(sentDateTime);

      // Check if the date is valid
      if (isNaN(date.getTime())) {
        console.warn('Invalid date format:', sentDateTime);
        return 'Invalid date';
      }

      return date.toLocaleString();
    } catch (error) {
      console.error('Error formatting send time:', error);
      return 'Date error';
    }
  };

  const handleDownload = async (url: string, filename: string) => {
    try {
      console.log('email file:', url);
      // 创建一个临时的 a 标签元素
      const tempLink = document.createElement('a');
      // 设置 a 标签的 href 和 download 属性
      tempLink.href = url;
      tempLink.download = filename;
      // 将 a 标签添加到 DOM 树中
      document.body.appendChild(tempLink);

      tempLink.click();
      // 移除 a 标签
      document.body.removeChild(tempLink);
    } catch (error) {
      console.error('下载失败:', error);
    }
  };

  return (
    <div className="h-full flex flex-col relative">
      {/* Email Header */}
      <div className="border-b p-4">
        <h2 className="text-lg font-medium">{emailDetail?.emailSubject}</h2>
        <h2>{formatSendTime(emailDetail?.sentDateTime)}</h2>
      </div>

      {/* Email Content - 使用 pb-16 为固定底部按钮腾出空间 */}
      <div className="flex-1 overflow-auto pb-16">
        <div className="p-4">
          {/* Email Info */}
          <div className="space-y-2 mb-4">
            <div className="grid grid-cols-[80px,1fr] gap-2">
              <span className="text-black font-semibold">From :</span>
              <span className="bg-amber-50 px-2 py-0.5">
                {sender?.emailAddress?.address}
              </span>

              <span className="text-black font-semibold">To :</span>
              <div className="space-y-1">
                {toRecipients.map((recipient, index) => (
                  <span
                    key={index}
                    className="block bg-amber-50 px-2 py-0.5"
                  >
                    {recipient.emailAddress.address}
                  </span>
                ))}
              </div>

              {ccRecipients.length > 0 && (
                <>
                  <span className="text-black font-semibold">cc :</span>
                  <div className="space-y-1">
                    {ccRecipients.map((recipient, index) => (
                      <span
                        key={index}
                        className="block bg-amber-50 px-2 py-0.5"
                      >
                        {recipient.emailAddress.address}
                      </span>
                    ))}
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Attachments */}
          {regularAttachments.length > 0 && (
            <div className="mb-4">
              <div className="flex flex-wrap gap-4">
                {regularAttachments.map((attachment) => (
                  <div
                    key={attachment.id}
                    className="flex items-center gap-2 border rounded px-3 py-2 bg-gray-50"
                  >
                    <svg
                      className="w-5 h-5 text-gray-500"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"
                      />
                    </svg>
                    <span className="text-gray-700">
                      {attachment.attachmentName}
                    </span>
                    <button
                      onClick={() =>
                        handleDownload(
                          attachment.url,
                          attachment.attachmentName
                        )
                      }
                      className="hover:bg-gray-200 p-1 rounded-md"
                      aria-label={`Download ${attachment.attachmentName}`}
                    >
                      <svg
                        className="w-4 h-4 text-gray-500"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                        />
                      </svg>
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Divider */}
          <div className="h-px bg-gray-200 mb-4" />
          <style>{emailEditorStyles}</style>
          {/* Email Body with processed inline attachments */}
          <div
            className="prose max-w-none mb-8  ProseMirror"
            dangerouslySetInnerHTML={{
              __html: sanitizeEmailBody(processedEmailBody),
            }}
          />
        </div>
      </div>

      {/* 固定在底部的按钮区域 */}
      <div className="absolute bottom-0 left-0 right-0 bg-white border-t">
        <div className="flex justify-end items-center p-4">
          <div className="flex gap-2">
            <Button
              variant="primary"
              onClick={() => handleAction('reply')}
              disabled={!isConnected}
            >
              Reply
            </Button>
            <Button
              variant="primary"
              onClick={() => handleAction('forward')}
              disabled={!isConnected}
            >
              Forward
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
