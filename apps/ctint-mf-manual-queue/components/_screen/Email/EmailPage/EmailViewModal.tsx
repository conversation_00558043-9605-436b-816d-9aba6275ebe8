import React, { useEffect, useState } from 'react';
import {
  Popup,
  PopupContent,
} from '@cdss-modules/design-system/components/_ui/Popup';
import { getEmailDetail } from '../../../../lib/api';
import { toast, useRoute<PERSON><PERSON><PERSON> } from '@cdss-modules/design-system';
import EmailContainer, { EmailData } from '../../../_ui/QuillEditor';
import { Loader } from 'lucide-react';
import { Row } from '@tanstack/react-table';
import { ManualQueueData } from '../../../../types/microfrontendsConfig';

interface EmailViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  gcConversationId: string;
  row: Row<ManualQueueData> | null;
  onRefreshList?: () => void;
}

interface EmailViewData {
  id: string;
  emailDirection: string;
  emailSubject: string;
  detail: {
    outboundType?: string;
    emailSender: string;
    emailToRecipient: string;
    emailBccRecipient: string;
    emailCcRecipient: string;
    sentDateTime: string;
    endTime: string;
    updateTime?: string;
    emailBody: string;
    isDraft?: number;
    containsPii?: boolean;
  };
  gcConversationId: string;
  caseId: string;
  status: string;
  attachments: {
    items: Array<{
      id: string;
      attachmentName: string;
      fileType: string;
      fileSize: number;
      isInLine: boolean;
      url: string;
      contentId: string;
    }>;
  };
  createTime: string;
  conversationStartTime: string;
  conversationEndTime: string;
  caseStatus: string;
  referenceId: string;
  referenceStatus: string;
}

export const EmailViewModal: React.FC<EmailViewModalProps> = ({
  isOpen,
  onClose,
  gcConversationId = '',
  row,
  onRefreshList,
}) => {
  const [loading, setLoading] = useState(true);
  const [emails, setEmails] = useState<EmailData[]>([]);
  const { basePath } = useRouteHandler();

  const formatRecipients = (recipientsStr: string | null): string => {
    if (!recipientsStr) return '-';

    try {
      const recipients = JSON.parse(recipientsStr);
      if (!Array.isArray(recipients)) return '-';

      return recipients
        .map((recipient) => recipient.emailAddress.address)
        .join(',\n');
    } catch (error) {
      console.error('Error parsing recipients:', error);
      return '-';
    }
  };

  useEffect(() => {
    if (isOpen && gcConversationId) {
      setLoading(true);
      getEmailDetail(basePath, gcConversationId)
        .then((response) => {
          const allEmails = response.data.data;

          // Filter out auto-replies and drafts
          const filteredEmails = allEmails.filter(
            (email: EmailViewData) =>
              !(
                (email.detail.outboundType === 'autoReply')
                //|| email.detail.isDraft === 1
              )
          );

          // Sort by sent date (newest first)
          const sortedEmails = filteredEmails.sort(
            (a: EmailViewData, b: EmailViewData) =>
              new Date(b.createTime).getTime() -
              new Date(a.createTime).getTime()
          );

          // Convert to EmailData format
          const formattedEmails = sortedEmails.map((email: EmailViewData) => ({
            id: email.id,
            direction: email.emailDirection,
            subject: email.emailSubject,
            from: JSON.parse(email.detail.emailSender).emailAddress.address,
            to: formatRecipients(email.detail.emailToRecipient),
            bcc: formatRecipients(email.detail.emailBccRecipient),
            cc: formatRecipients(email.detail.emailCcRecipient),
            conversationId: email.gcConversationId,
            caseId: email.caseId,
            status: email.status,
            startDate: email.detail.sentDateTime,
            endDate: email.detail.endTime || '-',
            body: email.detail.emailBody,
            attachments: email.attachments || { items: [] },
            containsPii: email.detail.containsPii,
            startTime: email.createTime,
            conversationStartTime: email.conversationStartTime,
            conversationEndTime: email.conversationEndTime,
            isDraft: email.detail.isDraft,
            caseStatus: email.caseStatus,
            referenceId: email.referenceId,
            referenceStatus: email.referenceStatus,
          }));

          setEmails(formattedEmails);
        })
        .catch((error) => {
          console.error('Error fetching email details:', error);
          toast({
            title: 'Error',
            description: 'Failed to load email details',
            variant: 'error',
          });
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [isOpen, gcConversationId, basePath]);

  return (
    <Popup
      open={isOpen}
      onOpenChange={onClose}
    >
      <PopupContent
        className="w-[1000px] max-w-[90vw] shadow-xl border border-gray-200"
        title="Email Conversation"
        onClick={(e) => e.stopPropagation()}
      >
        {loading ? (
          <div className="flex justify-center items-center p-8">
            <Loader size={48} />
          </div>
        ) : emails.length > 0 ? (
          <EmailContainer
            emailData={emails}
            row={row}
            onAssignmentSuccessCallback={() => {
              // 关闭模态窗口
              onClose();
              if (onRefreshList) {
                onRefreshList();
              }
            }}
          />
        ) : (
          <div className="p-4 text-center text-gray-500">
            No email data available
          </div>
        )}
      </PopupContent>
    </Popup>
  );
};

export default EmailViewModal;
