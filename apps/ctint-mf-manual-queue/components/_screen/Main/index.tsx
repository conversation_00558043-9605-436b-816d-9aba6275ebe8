import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React, { useEffect, useRef, useState } from 'react';
import { WhitePanel } from '@cdss-modules/design-system/components/_ui/WhitePanel';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import SearchInput from '@cdss-modules/design-system/components/_ui/SearchInput';
import FilterComponent, {
  Condition,
} from '@cdss-modules/design-system/components/_ui/FilterComponent';
import dayjs from 'dayjs';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import {
  Auth<PERSON><PERSON>in<PERSON>he<PERSON>,
  Loader,
  TglobalConfig,
  THEME_FONT_COLORS,
  toast,
  Toaster,
  useRole,
  useRouteHandler,
  useToast,
} from '@cdss-modules/design-system';
import utc from 'dayjs/plugin/utc';
import Panel from '@cdss-modules/design-system/components/_ui/Panel';
import { ColumnDef, Row } from '@tanstack/react-table';
import {
  AssignReq,
  ManualQueueData,
  ManualQueueDataResp,
  ManualQueueTabName,
  microfrontends,
  UserSearchReq,
} from 'apps/ctint-mf-manual-queue/types/microfrontendsConfig';
import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';
import { Table as TableType } from '@tanstack/table-core/build/lib/types';
import {
  assignTo,
  FilterParams,
  getAudioUrl,
  getManualQueuePage,
  getQueueInfo,
  userSearch,
} from '../../../lib/api';
import Pagination from '@cdss-modules/design-system/components/_ui/Pagination';
import { AudioModal } from '../AudioModal/AudioModal';
import { createPortal } from 'react-dom';
import { UserSelector } from '../../_ui/UserSelector';
import { User } from '../../../types/microfrontendsConfig';
import { optionsMap } from '@cdss-modules/design-system/components/_ui/FilterComponent/config';
import { CallBackPage } from '../CallBackPage';
import { RefreshCcw } from 'lucide-react';
import AssignButton from '../../_ui/AssignButton';
import { EmailPage } from '../Email/EmailPage';
import { CommonPermission } from '@cdss-modules/design-system/@types/CommonPermission';
import { CommonPermissionWrapper } from '@cdss-modules/design-system/components/_other/PermissionWrapper/CommonPermissionWrapper';

dayjs.extend(utc);

export function ManualQueueBody() {
  const [queueList, setQueueList] = useState<{ id: string; name: string }[]>(
    []
  );
  const [userList, setUserList] = useState<{ id: string; name: string }[]>([]);
  const { basePath } = useRouteHandler();
  const [loading, setLoading] = useState(true);
  const [activePlayButton, setActivePlayButton] = useState<string | null>(null);

  const findQueueName = (queueId: string): string => {
    const queue = queueList.find((q) => q.id === queueId);
    return queue?.name || queueId;
  };
  const findAssignName = (userId: string): string => {
    const user = userList.find((u) => u.id === userId);
    return user?.name || userId;
  };

  useEffect(() => {
    const fetchQueueList = async () => {
      try {
        const queueListData = await getQueueInfo(basePath, 'voicemail');
        if (queueListData?.data?.data) {
          const queues = queueListData.data.data;
          setQueueList(queues);
          // 创建队列选项
          const queueOptions = [
            ...queues.map((queue: { id: any; name: any }) => ({
              value: queue.id, // 用于搜索的值（ID）
              labelEn: queue.name, // 显示用的英文名
              labelCh: queue.name, // 显示用的中文名
              name: queue.name, // 显示用的名称
            })),
          ];

          // 更新全局 optionsMap（这一步很重要，因为 SelectFilter 依赖它）
          optionsMap.queueName = queueOptions;
          // 更新 filterValue，确保包含所有必要属性
          setFilterValue((prevFilterValue) => ({
            ...prevFilterValue,
            queueName: {
              ...prevFilterValue.queueName,
              options: queueOptions,
            },
          }));

          const searchRequest: UserSearchReq = {
            keyword: '',
          };
          const response = await userSearch(`${basePath}`, searchRequest);
          const userList = await response.data.data;

          setUserList(userList);
          const userOptions = [
            ...userList.map((user: { id: any; name: any }) => ({
              value: user.id, // 用于搜索的值（ID）
              labelEn: user.name, // 显示用的英文名
              labelCh: user.name, // 显示用的中文名
              name: user.name, // 显示用的名称
            })),
          ];
          optionsMap.assign = userOptions;
          setFilterValue((prevFilterValue) => ({
            ...prevFilterValue,
            assign: {
              ...prevFilterValue.assign,
              options: userOptions,
            },
          }));

          // 触发初始加载
          await getManualQueueList(1, pagination.pageSize);
        }
      } catch (error) {
        console.error('Error fetching queue list:', error);
      }
    };
    fetchQueueList();
  }, [basePath]);

  const generateColumns = (
    columns: string[],
    showColumns: string[],
    columnOrdering: string[]
  ): ColumnDef<ManualQueueData>[] => {
    return [
      ...columns.map((col, index) => ({
        id: col,
        accessorKey: col,
        size:
          col === 'mediaType'
            ? 40
            : col === 'assign'
              ? 280
              : col === 'state'
                ? 120
                : undefined,
        header: () => (
          <div className="flex items-center">
            <span className="text-[14px]">{showColumns[index]}</span>
          </div>
        ),
        cell: ({ row }: { row: Row<ManualQueueData> }) => {
          if (col === 'mediaType') {
            return (
              <div className="flex justify-center items-center text-center w-full">
                <svg
                  width="25"
                  height="24"
                  viewBox="0 0 25 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6.29102 16C8.50015 16 10.291 14.2091 10.291 12C10.291 9.79086 8.50015 8 6.29102 8C4.08188 8 2.29102 9.79086 2.29102 12C2.29102 14.2091 4.08188 16 6.29102 16Z"
                    stroke="#00A3FF"
                    strokeWidth="1.25"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M18.291 16C20.5002 16 22.291 14.2091 22.291 12C22.291 9.79086 20.5002 8 18.291 8C16.0819 8 14.291 9.79086 14.291 12C14.291 14.2091 16.0819 16 18.291 16Z"
                    stroke="#00A3FF"
                    strokeWidth="1.25"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M6.29102 16H18.291"
                    stroke="#00A3FF"
                    strokeWidth="1.25"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
            );
          }
          const value = (row.original as Record<string, any>)[col];
          if (value === null || value === undefined) {
            return '-';
          }
          //return String(value);

          if (col === 'assign') {
            return (
              <div className="whitespace-pre-line">
                {String(value).split(',').join('\n')}
              </div>
            );
          }
          return String(value);
        },
      })),
      {
        id: 'actions',
        cell: ({ row }: { row: Row<ManualQueueData> }) => {
          const isPlayable = row.original.state !== 'END';
          return (
            <div className="flex justify-center items-center text-center w-full">
              <button
                onClick={() => handlePlay(row.original)}
                className={`flex items-center hover:bg-transparent px-5 min-w-[40px] ${!isPlayable ? 'opacity-50 cursor-not-allowed' : ''}`}
                disabled={!isPlayable}
              >
                <svg
                  width="15"
                  height="18"
                  viewBox="0 0 15 18"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className={`mr-1 ${!isPlayable ? 'opacity-50' : ''}`}
                >
                  <path
                    d="M1.43616 1.5L13.4362 9L1.43616 16.5V1.5Z"
                    stroke={
                      activePlayButton === row.original.conversationId
                        ? THEME_FONT_COLORS.primary[500]
                        : 'black'
                    }
                    strokeWidth="1.25"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </button>
            </div>
          );
        },
      },
      {
        id: 'assignButton',
        cell: ({ row }) => (
          <div className="flex justify-center items-center text-center w-full">
            <AssignButton
              row={row}
              onAssignmentSuccess={getManualQueueList}
              type={'voicemail'}
            />
          </div>
        ),
      },
    ];
  };

  // 在清除过滤条件后也需要刷新列表
  const clearFilter = () => {
    const clearedFilters = { ...initTableColsData };
    Object.keys(clearedFilters).forEach((key) => {
      if (!clearedFilters[key].require) {
        clearedFilters[key] = {
          ...clearedFilters[key],
          checked: false,
          data: undefined,
        };
      }
    });
    setFilterValue(clearedFilters);
    // 清除过滤条件后刷新列表
    getManualQueueList(1, pagination.pageSize);
  };

  const { globalConfig } = useRole();
  const microfrontendsConfig: microfrontends = globalConfig?.microfrontends;
  const manualQueueTabsCols: ManualQueueTabName[] =
    microfrontendsConfig['ctint-mf-manual-queue']['manual-tab-names'];

  const { t, i18n } = useTranslation();

  const initTableColsData = manualQueueTabsCols.reduce(
    (acc, filter) => {
      if (filter.value === 'createdAt') {
        acc[filter.value] = {
          ...filter,
          checked: true, // 强制设置为选中
          require: true, // 标记为必填
          data: {
            start: dayjs().subtract(30, 'day').format('YYYY-MM-DD HH:mm:ss'),
            end: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          },
        };
      } else if (
        filter.value === 'mediaType' ||
        filter.value === 'conversationDuration'
      ) {
        /* empty */
      } else if (filter.value === 'queueName') {
        acc[filter.value] = {
          ...filter,
          filterType: 'select',
          checked: false,
        };
      } else if (filter.value === 'ended') {
        acc[filter.value] = {
          ...filter,
          filterType: 'select',
          checked: true,
          require: true,
          data: 'No',
        };
      } else {
        acc[filter.value] = {
          ...filter,
          checked: filter.require ? true : false,
          data: filter.require ? '' : undefined,
        };
      }
      return acc;
    },
    {} as Record<string, Condition>
  );
  const [filterValue, setFilterValue] =
    useState<Record<string, Condition>>(initTableColsData);

  const conbineFiltersTagName = () => {
    const tags: string[] = [];
    const sortedFilters = [...Object.values(filterValue)];
    sortedFilters.map((item) => {
      const key = item.value;
      if (filterValue[key].checked && filterValue[key].data) {
        // 对于队列名特殊处理
        let displayValue = filterValue[key].data;
        if (key === 'queueName' && displayValue) {
          displayValue = findQueueName(displayValue as string);
        }
        if (key === 'assign' && displayValue) {
          displayValue = findAssignName(displayValue as string);
        }
        if (key === 'createdAt') {
          // 如果是对象格式，说明是时间范围
          if (typeof displayValue === 'object' && displayValue !== null) {
            displayValue = `${dayjs(displayValue.start).format('YYYY-MM-DD HH:mm:ss')} - ${dayjs(displayValue.end).format('YYYY-MM-DD HH:mm:ss')}`;
          } else {
            // 如果是单个时间点，直接显示
            displayValue = String(displayValue);
          }
        }

        tags.push(
          `${i18n.language == 'en' ? filterValue[key].labelEn : filterValue[key].labelCh}: ${displayValue}`
        );
      }
    });
    return tags;
  };

  const renderTagItems = () => {
    const sortedFilters = Object.values(filterValue);
    return sortedFilters.map((item, i) => {
      const key = item.value;
      if (!filterValue[key].checked || !filterValue[key].data) return null;

      // 对于队列名特殊处理
      let displayValue = filterValue[key].data;
      if (key === 'queueName' && displayValue) {
        displayValue = findQueueName(displayValue as string);
      }
      if (key === 'assign' && displayValue) {
        displayValue = findAssignName(displayValue as string);
      }
      if (key === 'createdAt') {
        // 如果是对象格式，说明是时间范围
        if (typeof displayValue === 'object' && displayValue !== null) {
          displayValue = `${dayjs(displayValue.start).format('YYYY-MM-DD HH:mm:ss')} - ${dayjs(displayValue.end).format('YYYY-MM-DD HH:mm:ss')}`;
        } else {
          // 如果是单个时间点，直接显示
          displayValue = String(displayValue);
        }
      }

      return (
        <div
          key={i}
          className="p-1 mr-1 mt-1 flex flex-row items-center border border-black rounded-[4px] text-[12px] text-black"
        >
          <span className="truncate">
            {(i18n.language === 'en'
              ? filterValue[key].labelEn
              : filterValue[key].labelCh) +
              ':' +
              displayValue}
          </span>
          {/* 对于非 createdAt 的字段才显示关闭按钮 */}
          {key !== 'createdAt' && (
            <span
              onClick={() => {
                setFilterValue((prev) => {
                  const newFilterValues = { ...prev };
                  delete newFilterValues[key].data;
                  newFilterValues[key].checked = false;
                  return newFilterValues;
                });
              }}
              className="ml-1 cursor-pointer"
            >
              <Icon name="cross" />
            </span>
          )}
        </div>
      );
    });
  };
  const [manualQueueList, setManualQueueList] = useState<ManualQueueData[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 50,
    total: 0,
    totalPage: 0,
  });

  // 分页改变的处理函数
  const getManualQueueList = (pageNumber = 1, pageSize = 50) => {
    // 构建过滤参数
    const filters: FilterParams = {};
    filters.type = 'voicemail';
    Object.entries(filterValue).forEach(([key, condition]) => {
      if (condition.checked && condition.data) {
        if (key === 'createdAt') {
          if (typeof condition.data === 'object' && condition.data !== null) {
            filters.createdAt = dayjs(condition.data.start).format(
              'YYYY-MM-DD HH:mm:ss'
            );
            filters.createdAtToEnd = dayjs(condition.data.end).format(
              'YYYY-MM-DD HH:mm:ss'
            );
          } else {
            filters.createdAt = dayjs(condition.data).format(
              'YYYY-MM-DD HH:mm:ss'
            );
            filters.createdAtToEnd = dayjs(condition.data).format(
              'YYYY-MM-DD HH:mm:ss'
            );
          }
        } else {
          filters[key] = condition.data;
        }
      }
    });

    setLoading(true);
    getManualQueuePage(basePath, pageNumber, pageSize, filters)
      .then((result) => {
        const respData: ManualQueueDataResp = result.data;
        if (respData.data) {
          setManualQueueList(respData.data.list || []);
          setPagination({
            current: respData.data.current_page || pageNumber,
            pageSize: respData.data.page_size || pageSize,
            total: respData.data.total || 0,
            totalPage: respData.data.total_pages || 0,
          });
          setLoading(false);
        } else {
          setManualQueueList([]);
          setPagination({
            current: pageNumber,
            pageSize: pageSize,
            total: 0,
            totalPage: 0,
          });
          setLoading(false);
        }
      })
      .catch((error) => {
        const errorMessage = error.response?.data?.error || '';
        toast({
          title: 'error',
          description: errorMessage,
          variant: 'error',
        });
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const showColumns: string[] = [];
  const showColumnsKey: string[] = [];
  const [table, setTable] = useState<TableType<ManualQueueData>>();
  const [rowSelection, setRowSelection] = useState({});

  Object.entries(manualQueueTabsCols).forEach(([key, item]) => {
    if (item.active && item.value !== 'ended') {
      showColumnsKey.push(item.value);
      showColumns.push(i18n.language == 'en' ? item.labelEn : item.labelCh);
    }
  });

  // 控制Audio
  const [isAudioModalOpen, setIsAudioModalOpen] = useState(false);
  const [currentAudioUrl, setCurrentAudioUrl] = useState('');
  const [currentConversationInfo, setCurrentConversationInfo] =
    useState<ManualQueueData>();
  const handlePlay = async (data: ManualQueueData) => {
    try {
      if (data.conversationId) {
        setActivePlayButton(data.conversationId);
      }
      const audioUrl = await getAudioUrl(basePath, data.conversationId);
      setCurrentAudioUrl(audioUrl.data.data);
      setIsAudioModalOpen(true);
      setCurrentConversationInfo(data);
    } catch (error) {
      console.error('Failed to play audio:', error);
      setActivePlayButton(null);
    }
  };
  // 添加搜索按钮点击事件处理函数
  const handleSearch = () => {
    // 重置到第一页并使用当前的过滤条件进行搜索
    getManualQueueList(1, pagination.pageSize);
  };

  return (
    <>
      {loading && (
        <div
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 999,
          }}
        >
          <Loader size={64} />
        </div>
      )}
      <div
        data-testid="cypress-panel-title-filter"
        id="panelContainer"
        className="relative flex flex-col h-full w-full gap-4"
      >
        <WhitePanel className="flex flex-row w-full overflow-visible">
          <div className="flex-1 flex flex-row items-center">
            <SearchInput tags={conbineFiltersTagName()}>
              <section>
                <section className="max-h-[409px] overflow-y-auto">
                  <section className="p-4">
                    {Object.keys(filterValue).filter(
                      (key) => filterValue[key].data !== undefined
                    ).length > 0 && (
                      <div className="flex flex-wrap flex-row">
                        {renderTagItems()}
                      </div>
                    )}
                  </section>
                  <section className="px-4">
                    <h2 className="text-[14px] mb-2">
                      {t('ctint-mf-manual-queue.filter.available')}:
                    </h2>
                    <div className="flex flex-col gap-y-2">
                      <FilterComponent
                        filterValues={filterValue}
                        setFilterValues={setFilterValue}
                      />
                    </div>
                  </section>
                </section>
              </section>
            </SearchInput>

            <Button
              className="self-center ml-2"
              bodyClassName="border border-black py-[0.375rem]"
              onClick={handleSearch}
              size="s"
            >
              {t('ctint-mf-manual-queue.filter.search')}
            </Button>

            <Button
              className="self-center ml-2"
              bodyClassName="border border-black py-[0.375rem]"
              onClick={() => clearFilter()}
              variant="blank"
              size="s"
            >
              {t('ctint-mf-manual-queue.filter.reset', 'Reset')}
            </Button>

            {/* 刷新按钮 */}
            <button
              onClick={handleSearch}
              className="ml-auto p-2 rounded-full hover:bg-primary-50 transition-colors"
              title={t('refresh', 'Refresh')}
            >
              <RefreshCcw
                // color={THEME_FONT_COLORS.primary[500]}
                size={20}
              />
            </button>
          </div>
        </WhitePanel>
        <Panel
          headerClassName="bg-white border-b border-grey-200"
          containerClassName="flex flex-col h-full min-h-0"
        >
          <div className="overflow-auto px-4 flex-1 min-h-0">
            <DataTable<ManualQueueData>
              data={manualQueueList}
              columns={generateColumns(showColumnsKey, showColumns, [])}
              loading={false}
              emptyMessage="No data found"
              rowSelection={rowSelection}
              setRowSelection={setRowSelection}
              onTableSetUp={(table) => setTable(table)}
              resize={true}
            />
          </div>
          {pagination.total > 0 && (
            <section className="flex-shrink-0 px-4 py-4 border-t">
              <div>
                <Pagination
                  total={pagination.totalPage}
                  current={pagination.current}
                  initialPage={1}
                  siblings={1}
                  totalCount={pagination.total}
                  perPage={pagination.pageSize}
                  onChange={(page) => {
                    getManualQueueList(page, pagination.pageSize);
                  }}
                  handleOnNext={() => {
                    getManualQueueList(
                      pagination.current + 1,
                      pagination.pageSize
                    );
                  }}
                  handleOnPrevious={() => {
                    getManualQueueList(
                      pagination.current - 1,
                      pagination.pageSize
                    );
                  }}
                  handlePerPageSetter={(newPageSize) => {
                    getManualQueueList(1, newPageSize);
                  }}
                />
              </div>
            </section>
          )}
        </Panel>
        <AudioModal
          isOpen={isAudioModalOpen}
          onClose={() => {
            setIsAudioModalOpen(false);
            setActivePlayButton(null);
          }}
          audioUrl={currentAudioUrl}
          callInfo={currentConversationInfo}
        />
        <Toaster />
      </div>
    </>
  );
}

const queryClient = new QueryClient();

export default function Main() {
  const { searchParams } = useRouteHandler();
  const type = searchParams.get('type');

  // 根据 type 返回对应的组件
  const renderPage = () => {
    switch (type) {
      case 'voicemail':
        return (
          <CommonPermissionWrapper
            customerPermissionHandler={(
              globalConfig: TglobalConfig,
              permissions: string[]
            ) => {
              return new CommonPermission(
                globalConfig,
                permissions
              ).isPermissionEnabled('ctint-mf-cdss', 'voicemail', 'visit');
            }}
          >
            <ManualQueueBody />
          </CommonPermissionWrapper>
        );
      case 'callback':
        return (
          <CommonPermissionWrapper
            customerPermissionHandler={(
              globalConfig: TglobalConfig,
              permissions: string[]
            ) => {
              return new CommonPermission(
                globalConfig,
                permissions
              ).isPermissionEnabled('ctint-mf-cdss', 'callback', 'visit');
            }}
          >
            <CallBackPage />
          </CommonPermissionWrapper>
        );
      case 'email':
        return (
          <CommonPermissionWrapper
            customerPermissionHandler={(
              globalConfig: TglobalConfig,
              permissions: string[]
            ) => {
              return new CommonPermission(
                globalConfig,
                permissions
              ).isPermissionEnabled('ctint-mf-cdss', 'email', 'visit');
            }}
          >
            <EmailPage />
          </CommonPermissionWrapper>
        );
      default:
        return (
          <CommonPermissionWrapper
            customerPermissionHandler={(
              globalConfig: TglobalConfig,
              permissions: string[]
            ) => {
              return new CommonPermission(
                globalConfig,
                permissions
              ).isPermissionEnabled('ctint-mf-cdss', 'voicemail', 'visit');
            }}
          >
            <ManualQueueBody />
          </CommonPermissionWrapper>
        );
    }
  };
  return (
    <QueryClientProvider client={queryClient}>
      {renderPage()}
    </QueryClientProvider>
  );
}
