import React, { useEffect, useState } from 'react';
import { WhitePanel } from '@cdss-modules/design-system/components/_ui/WhitePanel';
import Panel from '@cdss-modules/design-system/components/_ui/Panel';

interface StyleIssue {
  type?: 'stylesheet' | 'style';
  element?: string;
  className?: string;
  property?: string;
  expected?: string;
  actual?: string;
  path?: string;
  href?: string;
  status?: string;
}

interface StyleDebuggerProps {
  targetSelector?: string;
  interval?: number;
}

const StyleDebugger: React.FC<StyleDebuggerProps> = ({
  targetSelector = '*',
  interval = 1000,
}) => {
  const [missingStyles, setMissingStyles] = useState<StyleIssue[]>([]);
  const [totalElements, setTotalElements] = useState<number>(0);

  useEffect(() => {
    const getElementPath = (element: Element): string => {
      const path: string[] = [];
      let currentElement: Element | null = element;

      while (currentElement && currentElement.tagName) {
        let selector = currentElement.tagName.toLowerCase();
        if (currentElement.id) {
          selector += `#${currentElement.id}`;
        } else if (
          currentElement.className &&
          typeof currentElement.className === 'string'
        ) {
          selector += `.${currentElement.className.split(' ').join('.')}`;
        }
        path.unshift(selector);
        currentElement = currentElement.parentElement;
      }
      return path.join(' > ');
    };

    const findMissingStyles = (): void => {
      const elements = document.querySelectorAll(targetSelector);
      setTotalElements(elements.length);

      const styleIssues: StyleIssue[] = [];

      elements.forEach((element: Element) => {
        const computedStyle = window.getComputedStyle(element);
        const expectedStyle = element.getAttribute('data-expected-style');

        if (expectedStyle) {
          const styles = expectedStyle.split(';');
          styles.forEach((style) => {
            const [property, value] = style.split(':').map((s) => s.trim());
            if (
              property &&
              value &&
              computedStyle.getPropertyValue(property) !== value
            ) {
              styleIssues.push({
                type: 'style',
                element: element.tagName.toLowerCase(),
                className:
                  element instanceof HTMLElement ? element.className : '',
                property,
                expected: value,
                actual: computedStyle.getPropertyValue(property),
                path: getElementPath(element),
              });
            }
          });
        }

        if (element.tagName === 'LINK') {
          const linkElement = element as HTMLLinkElement;
          if (linkElement.rel === 'stylesheet' && !linkElement.sheet) {
            styleIssues.push({
              type: 'stylesheet',
              href: linkElement.href,
              status: 'not loaded',
            });
          }
        }
      });

      setMissingStyles(styleIssues);
    };

    findMissingStyles();

    const intervalId = setInterval(findMissingStyles, interval);

    const observer = new MutationObserver(findMissingStyles);
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class', 'style'],
    });

    return () => {
      clearInterval(intervalId);
      observer.disconnect();
    };
  }, [targetSelector, interval]);

  return (
    <div className="p-4 space-y-4">
      <Panel>
        <h2 className="text-xl font-bold mb-4">样式加载检查器</h2>
        <div className="text-sm text-gray-600 mb-4">
          扫描元素总数: {totalElements}
        </div>

        {missingStyles.length > 0 ? (
          <div className="space-y-2">
            {missingStyles.map((issue, index) => (
              <WhitePanel
                key={index}
                className="border border-red-500"
              >
                <div className="p-4">
                  <div className="text-red-500 font-bold mb-2">
                    {issue.type === 'stylesheet'
                      ? '样式表加载失败'
                      : '样式不匹配'}
                  </div>
                  {issue.type === 'stylesheet' ? (
                    <div>
                      <p>未能加载样式表: {issue.href}</p>
                    </div>
                  ) : (
                    <div>
                      <p>元素: {issue.path}</p>
                      <p>属性: {issue.property}</p>
                      <p>期望值: {issue.expected}</p>
                      <p>实际值: {issue.actual}</p>
                    </div>
                  )}
                </div>
              </WhitePanel>
            ))}
          </div>
        ) : (
          <div className="text-green-600">未发现样式问题</div>
        )}
      </Panel>
    </div>
  );
};

export default StyleDebugger;
