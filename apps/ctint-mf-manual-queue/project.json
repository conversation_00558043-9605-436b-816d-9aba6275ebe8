{"name": "ctint-mf-manual-queue", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/ctint-mf-manual-queue", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/next:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"root": "apps/ctint-mf-manual-queue", "outputPath": "dist/apps/ctint-mf-manual-queue"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "ctint-mf-manual-queue:build", "dev": true, "port": 4301, "host": "0.0.0.0"}, "configurations": {"development": {"buildTarget": "ctint-mf-manual-queue:build:development", "dev": true}, "production": {"buildTarget": "ctint-mf-manual-queue:build:production", "dev": false}}}, "export": {"executor": "@nx/next:export", "options": {"buildTarget": "ctint-mf-manual-queue:build:production"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["coverage/apps/ctint-mf-manual-queue"], "options": {"jestConfig": "apps/ctint-mf-manual-queue/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/ctint-mf-manual-queue/**/*.{ts,tsx,js,jsx}"]}}}}