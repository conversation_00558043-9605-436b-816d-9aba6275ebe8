import { PrimaryContactInfo } from '@cdss-modules/design-system/@types/microfrontendsConfig';

export interface microfrontends {
  'ctint-mf-cdss': CtintMF;
  'ctint-mf-cpp': CtintMF;
  'ctint-mf-user-admin': CtintMFUserAdmin;
  'ctint-mf-tts': CtintMF;
  'ctint-mf-wap': CtintMF;
  'ctint-mf-info': CtintMF;
  'ctint-mf-message': CtintMFMessage;
}

export interface CtintMF {
  auditTabNames: never[];
  host: string;
  basepath: null | string;
}
export interface CtintMFUserAdmin {
  host: string;
  basepath: string;
  'user-tab-names': UserTabName[];
  'audit-tab-names': UserAuditTabName[];
}
export interface CtintMFMessage extends CtintMF {
  useSAAv2?: boolean;
  GCMessageSupportedId?: string;
  enableRightWindow?: boolean;
  enableSAA?: boolean;
}
export interface UserTabName {
  labelEn: string;
  labelCh: string;
  value: string;
  filterType: string;
  active: boolean;
  readOnly: boolean;
  require: boolean;
}
export interface UserAuditTabName {
  labelEn: string;
  labelCh: string;
  value: string;
  filterType: string;
  active: boolean;
  readOnly: boolean;
  sort: boolean;
}

export interface UserSearchReq {
  keyword?: string;
}
export type User = {
  id: string;
  name: string;
  phone?: string;
  presence?: {
    presenceDefinition?: {
      systemPresence: string;
    };
  };
  primaryContactInfo?: PrimaryContactInfo[];
};
export interface TransferReq {
  transferType?: string;
  userId?: string;
  queueId?: string;
}
