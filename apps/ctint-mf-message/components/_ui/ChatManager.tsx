/* eslint-disable @typescript-eslint/ban-ts-comment */
import React, { useCallback, useEffect, useState } from 'react';
import ChatWindow from './chatWindow/ChatWindow';
//@ts-ignore
import { useConversationStore } from 'cdss/store/conversation';
import { useSearchParams } from 'react-router-dom';

import CallInfo from '@cdss-modules/design-system/components/_ui/CallInfo';
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
  useRole,
  useRouteHandler,
} from '@cdss-modules/design-system';
import { useMessageSender } from '../../hooks/useMessageSender';
import { microfrontends } from '../../@types/microFrontendConfigs';
import Saa from '@cdss-modules/design-system/components/_ui/Saa';
import { useCustomerHistory } from '@cdss-modules/design-system/lib/hooks/useCustomerInfo';
import { SAASearchTerm } from '@cdss-modules/design-system/@types/SAA';

const ChatManager: React.FC = () => {
  const [isFullResizablePanel, setIsFullResizablePanel] =
    useState<boolean>(false);
  const { SAADetail, getSAADetail, SAADetailLoading } = useCustomerHistory();
  // const [searchParams] = useSearchParams();
  // const conversationIdByPath = searchParams.get('conversationId');
  const [conversationFromUrl, setConversationFromUrl] = useState('');
  const { conversations, setSAAContent, SAAResult, SAAManualSearchTerm } =
    useConversationStore();
  const [phoneNumber, setPhoneNumber] = useState<string>('');
  const [isPhoneNumberLoading, setIsPhoneNumberLoading] =
    useState<boolean>(false);
  const { basePath, activePath, searchParams } = useRouteHandler();
  const { sendTextMessage } = useMessageSender(basePath);
  const { globalConfig } = useRole();
  const microfrontendsConfig: microfrontends = globalConfig?.microfrontends;
  const useSAAv2: boolean | undefined =
    microfrontendsConfig['ctint-mf-message']['useSAAv2'];
  const enableSAA: boolean =
    microfrontendsConfig['ctint-mf-message']['enableSAA'] ?? false;
  const enableRightWindow: boolean =
    microfrontendsConfig['ctint-mf-message']['enableRightWindow'] ?? false;

  // useEffect(() => {
  //   if (conversationIdByPath) {
  //     setIsPhoneNumberLoading(true);
  //     setPhoneNumber(''); // 清空当前电话号码，等待新数据
  //   }
  //   console.log('useSAAv2', useSAAv2);
  // }, [conversationIdByPath]);

  useEffect(() => {
    if (activePath?.includes('/message')) {
      const urlConversationId = searchParams.get('conversationId');
      if (urlConversationId) {
        // 如果 conversationId 发生变化才设置新值
        if (urlConversationId !== conversationFromUrl) {
          setConversationFromUrl(urlConversationId);
          setPhoneNumber('');
        } else {
        }
      }
    }
  }, [activePath, searchParams]);

  // 处理从ChatWindow获取到的电话号码

  //处理复制内容 给SAA组件使用
  const handleSaaContentCopy = (contentToSet: string) => {
    // 设置内容到 store 中
    setSAAContent(contentToSet);
  };

  // 订阅SAAManualSearchTerm变化 给SAA使用
  useEffect(() => {
    const unsubscribe = useConversationStore.subscribe(
      (state: any) => state.SAAManualSearchTerm,
      (newSearchTerm: SAASearchTerm, prevSearchTerm: SAASearchTerm) => {
        console.log('SAAManualSearchTerm changed:', newSearchTerm);
        // 可以在这里执行额外的逻辑，但通常直接传递值给子组件即可
      }
    );
    return () => unsubscribe();
  }, []);
  const handleSaaContentSend = (contentToSet: string) => {
    if (conversationFromUrl && contentToSet) {
      sendTextMessage(conversationFromUrl, contentToSet)
        .then((success) => {
          if (success) {
            console.log('SAA content sent successfully:', contentToSet);
          } else {
            console.error('Failed to send SAA content');
          }
        })
        .catch((error) => {
          console.error('Error sending SAA content:', error);
        });
    } else {
      console.error('Cannot send: Missing conversation ID or content');
    }
  };

  // 检查 conversations 是否为空
  if (conversations.length === 0) {
    return <div>Loading...</div>;
  }

  return (
    <div className="flex flex-1 gap-2 xl:gap-4 2xl:gap-6 overflow-hidden">
      {/* Main Content */}
      <div className="flex flex-col flex-1 gap-2 lg:gap-3 xl:gap-4 min-w-0 overflow-hidden">
        <ResizablePanelGroup
          autoSaveId="chat-manager-panel"
          direction="horizontal"
          className="h-full flex-1 flex w-full gap-x-3"
        >
          <ResizablePanel minSize={40}>
            <section className="h-full bg-white shadow-lg rounded-lg transition-all duration-200 overflow-hidden">
              <ChatWindow
                conversationId={conversationFromUrl}
                phoneNumber={phoneNumber}
                setPhoneNumber={setPhoneNumber}
              />
            </section>
          </ResizablePanel>
          {enableRightWindow && (
            <>
              <ResizableHandle />
              <ResizablePanel minSize={30}>
                <ResizablePanelGroup
                  autoSaveId="SAA-detail-panel"
                  direction="vertical"
                  className="h-full flex-1 flex w-full h-0 gap-x-3 "
                >
                  <ResizablePanel
                    style={
                      isFullResizablePanel
                        ? {
                            flexGrow: 2000,
                            flexShrink: 1,
                            flexBasis: '0px',
                          }
                        : undefined
                    }
                  >
                    <CallInfo
                      customerData={{
                        conversationId: conversationFromUrl,
                        phoneNumber: phoneNumber,
                      }}
                    />
                  </ResizablePanel>
                  {enableSAA && (
                    <>
                      <ResizableHandle />
                      <ResizablePanel minSize={50}>
                        <Saa
                          data={{
                            SAADetail: SAADetail,
                            getSAADetail: getSAADetail,
                            convId: conversationFromUrl,
                            SAADetailLoading: SAADetailLoading,
                          }}
                          useSAAv2={useSAAv2}
                          setIsFullResizablePanel={setIsFullResizablePanel}
                          isShowCopy={false}
                          onSaaContentCopy={handleSaaContentCopy}
                          onSaaContentSend={handleSaaContentSend}
                          hasAutoMod={true}
                          autoSAAResult={SAAResult}
                          SAAManualSearchTerm={SAAManualSearchTerm}
                        />
                      </ResizablePanel>
                    </>
                  )}
                </ResizablePanelGroup>
                {/*<section className="h-full flex-col bg-white shadow-lg rounded-lg p-2 lg:p-3 xl:p-4 overflow-auto">
                <CallInfo
                  customerData={{
                    conversationId:
                      typeof conversationIdByPath === 'string'
                        ? conversationIdByPath
                        : '',
                    phoneNumber: phoneNumber,
                  }}
                  headerClass="p-3"
                  onSaaContentCopy={handleSaaContentCopy}
                  onSaaContentSend={handleSaaContentSend}
                  useSAAv2={useSAAv2}
                  hasAutoMod={true}
                  isShowCopy={false}
                />
              </section>*/}
              </ResizablePanel>
            </>
          )}
        </ResizablePanelGroup>
      </div>
    </div>
  );
};

export default ChatManager;
