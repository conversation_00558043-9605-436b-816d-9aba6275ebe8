import React, { useState, useRef, useEffect } from 'react';
import Picker from 'emoji-picker-react';
import IconBase from '../../../public/iconsConfig.json';

interface EmojiPickerProps {
  onSelect: (emoji: string) => void; // 选中表情后的回调
}

const EmojiPicker: React.FC<EmojiPickerProps> = ({ onSelect }) => {
  const [showPicker, setShowPicker] = useState(false); // 控制 Picker 的显示状态
  const pickerRef = useRef<HTMLDivElement>(null); // 用于获取 Picker 的 DOM 元素

  // 处理表情点击的逻辑
  const handleEmojiClick = (emojiData: { emoji: string }) => {
    onSelect(emojiData.emoji); // 将选中的表情传递给父组件
    setShowPicker(false); // 关闭 Picker
  };

  // 监听点击外部事件的逻辑
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        pickerRef.current &&
        !pickerRef.current.contains(event.target as Node)
      ) {
        setShowPicker(false); // 点击外部，关闭 Picker
      }
    };

    // 监听 mousedown 事件
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div
      className="relative"
      ref={pickerRef}
    >
      <button
        className="flex items-center justify-center hover:cursor-pointer"
        onClick={() => setShowPicker(!showPicker)}
      >
        <img
          src={IconBase.emoji} // 替换为你的表情图标路径
          alt="emoji"
          className="w-8 h-8"
        />
      </button>

      {showPicker && (
        <div className="absolute bottom-full right-0 z-50 mb-2">
          <Picker onEmojiClick={handleEmojiClick} />
        </div>
      )}
    </div>
  );
};

export default EmojiPicker;
