import React, { Suspense, lazy } from 'react';
import { createPortal } from 'react-dom';
import { Loader } from 'lucide-react';

// 懒加载 EmojiPicker
const EmojiPicker = lazy(() => import('emoji-picker-react'));

interface EmojiPickerWrapperProps {
  isOpen: boolean;
  onEmojiClick: (emojiData: { emoji: string }, event: any) => void;
  onClose: () => void;
}

const EmojiPickerWrapper: React.FC<EmojiPickerWrapperProps> = ({
  isOpen,
  onEmojiClick,
  onClose,
}) => {
  if (!isOpen) return null;

  // 使用 Portal 渲染到 body
  return createPortal(
    <div
      className="fixed inset-0 z-50"
      onClick={(e) => {
        if (e.target === e.currentTarget) onClose();
      }}
    >
      <div className="absolute bottom-24 right-24">
        <Suspense
          fallback={
            <div className="bg-white p-4 rounded-lg shadow-lg">
              <Loader className="w-6 h-6 animate-spin" />
            </div>
          }
        >
          <EmojiPicker onEmojiClick={onEmojiClick} />
        </Suspense>
      </div>
    </div>,
    document.body
  );
};

export default EmojiPickerWrapper;
