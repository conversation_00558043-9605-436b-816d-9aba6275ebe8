// import React, { useCallback } from 'react';
// import { useDropzone, FileRejection } from 'react-dropzone';
// import { MediaList } from '@cdss-modules/design-system/@types/Message';
// import FileIcon from '@cdss-modules/design-system/components/_ui/FileIcon';
// import { cn } from '@cdss-modules/design-system/lib/utils';
// import IconClose from '@cdss-modules/design-system/components/_ui/Icon/IconClose';
// /*
// // 只接受特定类型，每种类型有不同的大小限制
// <DropzoneAttachmentPage
//   maxSizeByType={{
//     'image/jpeg': 5 * 1024 * 1024,  // JPEG限制5MB
//     'image/png': 5 * 1024 * 1024,   // PNG限制5MB
//     'application/pdf': 10 * 1024 * 1024  // PDF限制10MB
//   }}
// />
//
// // 接受所有类型，统一限制25MB
// <DropzoneAttachmentPage
//   allowAllTypes={true}
//   defaultMaxSize={25 * 1024 * 1024}  // 25MB
// />
//
// // 混合使用：白名单内的类型有自己的限制，其他类型统一25MB限制
// <DropzoneAttachmentPage
//   maxSizeByType={{
//     'image/jpeg': 5 * 1024 * 1024,  // JPEG限制5MB
//     'image/png': 5 * 1024 * 1024    // PNG限制5MB
//   }}
//   allowAllTypes={true}
//   defaultMaxSize={25 * 1024 * 1024}  // 其他所有类型限制25MB
// />
//  */
// interface DropzoneAttachmentPageProps {
//   // File and operation related
//   attachmentList: MediaList[];
//   onRemoveFile: (id: string) => void;
//   onAddFiles: (files: File[]) => void;
//   onFileRejected?: (fileRejections: FileRejection[]) => void;
//
//   // Button operations
//   onSubmit: () => void;
//   onCancel: () => void;
//
//   // Control and customization
//   maxSizeByType?: Record<string, number>; // MIME type to max size mapping (also serves as whitelist)
//   defaultMaxSize?: number; // Default max size for all files if maxSizeByType is empty or undefined
//   allowAllTypes?: boolean; // Whether to allow all file types, regardless of maxSizeByType
//   clearOnCancel?: boolean;
//   submitLabel?: string;
//   cancelLabel?: string;
//   title?: string;
//   uploadAcceptText?: string;
//   uploadRejectText?: string;
//   uploadInstructionText?: string;
//   maxSizeText?: string;
//   showDropzone?: boolean;
//   emptyStateText?: string;
//   // 自定义样式相关
//   className?: string;
//   containerClassName?: string;
//   headerClassName?: string;
//   dropzoneClassName?: string;
//   fileListClassName?: string;
//   footerClassName?: string;
//   fileItemClassName?: string;
// }
//
// const formatFileSize = (bytes: number): string => {
//   if (bytes < 1024) return bytes + ' B';
//   if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB';
//   return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
// };
//
// const getMaxSizeMessage = (
//   maxSizeByType?: Record<string, number>,
//   defaultMaxSize?: number,
//   allowAllTypes?: boolean
// ): string => {
//   // If allowing all types with default size
//   if (allowAllTypes && defaultMaxSize) {
//     return `所有文件类型: 限制 ${formatFileSize(defaultMaxSize)}`;
//   }
//
//   if (!maxSizeByType) return '无文件大小限制';
//
//   const sizeMessages = Object.entries(maxSizeByType).map(([type, size]) => {
//     const typeName = type.split('/')[1] || type;
//     return `${typeName}: ${formatFileSize(size)}`;
//   });
//
//   return `限制: ${sizeMessages.join(', ')}`;
// };
//
// export const DropzoneAttachmentPage: React.FC<DropzoneAttachmentPageProps> = ({
//   attachmentList,
//   onRemoveFile,
//   onAddFiles,
//   onFileRejected,
//   onSubmit,
//   onCancel,
//   maxSizeByType,
//   defaultMaxSize = 25 * 1024 * 1024, // Default 25MB
//   allowAllTypes = false,
//   clearOnCancel = false,
//   submitLabel = 'Send',
//   cancelLabel = 'Cancel',
//   title = 'Attach file',
//   uploadAcceptText = 'Drop the files here...',
//   uploadRejectText = 'Some files will not be accepted...',
//   uploadInstructionText = 'Drop files here, or click to select files',
//   maxSizeText,
//   showDropzone = true,
//   emptyStateText = 'No files selected. Drop files here or click to add files.',
//   className,
//   containerClassName,
//   headerClassName,
//   dropzoneClassName,
//   fileListClassName,
//   footerClassName,
//   fileItemClassName,
// }) => {
//   // Generate the maxSizeText if not provided
//   const calculatedMaxSizeText =
//     maxSizeText ||
//     getMaxSizeMessage(maxSizeByType, defaultMaxSize, allowAllTypes);
//
//   // Create a validator function for dropzone
//   const fileValidator = useCallback(
//     (file: File) => {
//       // Check if file type is allowed
//       if (maxSizeByType && !allowAllTypes && !maxSizeByType[file.type]) {
//         return {
//           code: 'file-invalid-type',
//           message: `File "${file.name}" type (${file.type}) is not allowed.`,
//         };
//       }
//
//       // Check file size
//       const sizeLimit =
//         maxSizeByType && maxSizeByType[file.type]
//           ? maxSizeByType[file.type]
//           : defaultMaxSize;
//
//       if (sizeLimit && file.size > sizeLimit) {
//         return {
//           code: 'file-too-large',
//           message: `File "${file.name}" exceeds the maximum size of ${formatFileSize(sizeLimit)}`,
//         };
//       }
//
//       // No issues found
//       return null;
//     },
//     [maxSizeByType, defaultMaxSize, allowAllTypes]
//   );
//
//   const onDrop = useCallback(
//     (acceptedFiles: File[]) => {
//       if (acceptedFiles.length > 0) {
//         onAddFiles(acceptedFiles);
//       }
//     },
//     [onAddFiles]
//   );
//
//   // Handle rejected files
//   const onDropRejected = useCallback(
//     (fileRejections: FileRejection[]) => {
//       if (onFileRejected) {
//         onFileRejected(fileRejections);
//       }
//     },
//     [onFileRejected]
//   );
//
//   const {
//     getRootProps,
//     getInputProps,
//     isDragActive,
//     isDragAccept,
//     isDragReject,
//   } = useDropzone({
//     onDrop,
//     onDropRejected,
//     validator: fileValidator,
//     accept:
//       !allowAllTypes && maxSizeByType
//         ? Object.keys(maxSizeByType).reduce(
//             (acc, type) => {
//               acc[type] = [];
//               return acc;
//             },
//             {} as Record<string, string[]>
//           )
//         : undefined, // If allowAllTypes is true, don't specify accept (allows all types)
//     noClick: false,
//     noKeyboard: false,
//     multiple: true,
//   });
//
//   // Get dropzone styling based on drag state
//   const getDropzoneStyle = () => {
//     if (isDragActive) {
//       if (isDragAccept) {
//         return 'border-green-500 bg-green-50';
//       }
//       if (isDragReject) {
//         return 'border-red-500 bg-red-50';
//       }
//       return 'border-blue-500 bg-blue-50';
//     }
//     return 'border-dashed border-gray-300 bg-gray-50';
//   };
//
//   // Handle cancel button click
//   const handleCancel = () => {
//     onCancel();
//   };
//
//   const isEmpty =
//     attachmentList.length === 0 ||
//     attachmentList.every((list) => list.items.length === 0);
//
//   return (
//     <div
//       className={cn(
//         'w-96 bg-white rounded-2xl shadow-2xl',
//         containerClassName,
//         className
//       )}
//     >
//       <div
//         className={cn(
//           'flex items-center justify-between px-4 py-2 bg-[#F8A04A] rounded-t-2xl rounded-b-none',
//           headerClassName
//         )}
//       >
//         <h3 className="text-base font-medium text-gray-900">{title}</h3>
//         <button
//           onClick={handleCancel}
//           className="rounded-full p-0.5"
//         >
//           <div className="bg-black rounded-full p-1 flex items-center justify-center">
//             <IconClose
//               size={'12px'}
//               className={'text-white'}
//             />
//           </div>
//         </button>
//       </div>
//
//       {/* Dropzone Area - can be controlled with showDropzone */}
//       {showDropzone && (
//         <div
//           {...getRootProps({
//             className: `m-3 p-3 border-2 rounded-lg transition-colors ${getDropzoneStyle()}`,
//           })}
//         >
//           <input {...getInputProps()} />
//           <div className="flex flex-col items-center justify-center text-sm text-gray-500">
//             {isDragActive ? (
//               <p className="font-medium">
//                 {isDragAccept ? uploadAcceptText : uploadRejectText}
//               </p>
//             ) : (
//               <>
//                 <p>{uploadInstructionText}</p>
//                 {/*<p className="text-xs mt-1">{calculatedMaxSizeText}</p>*/}
//               </>
//             )}
//           </div>
//         </div>
//       )}
//
//       {/* File List */}
//       <div
//         className={cn(
//           'px-3 py-2 overflow-y-auto max-h-36 min-h-20',
//           fileListClassName
//         )}
//       >
//         {isEmpty ? (
//           <div className="flex items-center justify-center h-full text-sm text-gray-500">
//             {emptyStateText}
//           </div>
//         ) : (
//           <div className="grid grid-cols-2 gap-2">
//             {attachmentList.map((mediaList, index) => (
//               <div key={index}>
//                 {mediaList.items.map((item, itemIndex) => (
//                   <div
//                     key={itemIndex}
//                     className={cn(
//                       'relative bg-orange-50/50 rounded-lg p-2 mb-2',
//                       fileItemClassName
//                     )}
//                   >
//                     <button
//                       onClick={() => onRemoveFile(item.id)}
//                       className="absolute -right-1.5 -top-1.5 bg-white rounded-full p-0.5 shadow-sm"
//                     >
//                       <IconClose size={'8px'} />
//                     </button>
//                     <div className="flex items-start space-x-2">
//                       <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded flex items-center justify-center overflow-hidden">
//                         <FileIcon
//                           fileName={
//                             item.filename || `file.${item.mime.split('/')[1]}`
//                           }
//                           size="20"
//                           className="flex-shrink-0"
//                           iconClassName="text-orange-500"
//                         />
//                       </div>
//                       <div className="flex-1 min-w-0">
//                         <p className="text-sm text-gray-900 font-medium truncate leading-tight">
//                           {item.filename}
//                         </p>
//                         <p className="text-xs text-gray-500 mt-0.5">
//                           {formatFileSize(item.contentSizeBytes)}
//                         </p>
//                       </div>
//                     </div>
//                   </div>
//                 ))}
//               </div>
//             ))}
//           </div>
//         )}
//       </div>
//
//       <div
//         className={cn(
//           'flex justify-end space-x-2 p-3 border-t border-gray-100',
//           footerClassName
//         )}
//       >
//         <button
//           onClick={handleCancel}
//           className="px-4 h-8 text-sm text-gray-600 hover:bg-gray-50 rounded-lg border border-black"
//         >
//           {cancelLabel}
//         </button>
//         <button
//           onClick={onSubmit}
//           className="px-4 h-8 text-sm bg-black text-white rounded-lg hover:bg-gray-800 disabled:bg-gray-300 disabled:hover:bg-gray-300"
//           disabled={isEmpty}
//         >
//           {submitLabel}
//         </button>
//       </div>
//     </div>
//   );
// };
//
// export default DropzoneAttachmentPage;
