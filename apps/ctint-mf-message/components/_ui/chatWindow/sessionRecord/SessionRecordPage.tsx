/*
/!* eslint-disable jsx-a11y/img-redundant-alt *!/
/!* eslint-disable @next/next/no-img-element *!/
import React, { useState, useMemo } from 'react';
import { Search, Link2, Download } from 'lucide-react';
import {
  CDSSMessage,
  MediaItem,
} from '@cdss-modules/design-system/@types/Message';
import { useToast } from '@cdss-modules/design-system';
import FileIcon from '@cdss-modules/design-system/components/_ui/FileIcon';

interface SessionRecordPageProps {
  currentMessages: CDSSMessage[];
  className?: string;
  containerClassName?: string;
  searchContainerClassName?: string;
  tabsContainerClassName?: string;
  contentContainerClassName?: string;
  onItemClick?: (messageId: string) => void;
}

type TabType = 'all' | 'files' | 'images-videos' | 'links';

// Helper function to determine file type
const isImageOrVideo = (filename: string) => {
  if (!filename) return false;
  const ext = filename.split('.').pop()?.toLowerCase();
  const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
  const videoExts = ['mp4', 'webm', 'ogg', 'mov', 'avi', 'wmv', 'flv', 'mkv'];
  return imageExts.includes(ext as string) || videoExts.includes(ext as string);
};

const SessionRecordPage: React.FC<SessionRecordPageProps> = ({
  currentMessages,
  className = '',
  containerClassName = 'flex flex-col h-full overflow-hidden',
  searchContainerClassName = 'p-3 bg-gray-100',
  tabsContainerClassName = 'border-b border-gray-200',
  contentContainerClassName = 'flex-1 overflow-y-auto',
  onItemClick,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState<TabType>('all');
  const { toast } = useToast();

  // Handle text copy
  const handleCopy = (text: string, event: React.MouseEvent) => {
    navigator.clipboard.writeText(text).then(() => {
      toast({
        title: 'Success',
        description: 'Copied to clipboard',
        variant: 'success',
      });
    });
  };

  // Handle file download
  const handleDownload = async (url: string, filename: string) => {
    try {
      // Create a temporary anchor element
      const tempLink = document.createElement('a');

      // Set href and download attributes
      tempLink.href = url;
      tempLink.download = filename;

      // Add to DOM tree
      document.body.appendChild(tempLink);

      // Trigger click event
      tempLink.click();

      // Remove from DOM tree
      document.body.removeChild(tempLink);

      // Show success toast
      toast({
        title: 'Success',
        description: 'File download started',
        variant: 'success',
      });
    } catch (error) {
      console.error('Download failed:', error);
      toast({
        title: 'Error',
        description: 'Download failed',
        variant: 'error',
      });
    }
  };

  const handleLocateToChat = (messageId: string) => {
    if (onItemClick) {
      onItemClick(messageId);
    }
  };

  const filteredMessages = useMemo(() => {
    const messages: CDSSMessage[] = currentMessages;

    // First filter by search query
    const searchFiltered = messages.filter((msg) => {
      if (!searchQuery) return true;
      return msg.textBody.toLowerCase().includes(searchQuery.toLowerCase());
    });

    // Then filter by tab selection
    switch (activeTab) {
      case 'files':
        return searchFiltered.filter((msg) => {
          // Only show messages with media that are not images or videos
          if (!msg.medias || msg.medias.length === 0) return false;
          // Check if any media item is not an image or video
          return msg.medias.some(
            (item: MediaItem) => !isImageOrVideo(item.filename || '')
          );
        });

      case 'images-videos':
        return searchFiltered.filter((msg) => {
          // Only show messages with images or videos
          if (!msg.medias || msg.medias.length === 0) return false;
          // Check if any media item is an image or video
          return msg.medias.some((item: MediaItem) =>
            isImageOrVideo(item.filename || '')
          );
        });

      case 'links':
        return searchFiltered.filter((msg) => {
          // Exclude messages with only media files, but allow messages with both media and links
          if (!!msg.medias && msg.medias.length > 0 && !msg.textBody)
            return false;
          const urlRegex = /(https?:\/\/[^\s]+)/g;
          return urlRegex.test(msg.textBody);
        });

      default:
        return searchFiltered;
    }
  }, [currentMessages, searchQuery, activeTab]);

  const renderMessageItem = (message: CDSSMessage) => {
    const hasMedia = !!message.medias && message.medias.length > 0;
    const hasText = !!message.textBody;

    // Media only, no text
    const isMediaOnly = hasMedia && !hasText;
    // Both media and text
    const isMediaWithText = hasMedia && hasText;
    // Text with links
    const isLink = hasText && /(https?:\/\/[^\s]+)/g.test(message.textBody);

    // Handle media-only messages
    if (isMediaOnly) {
      const media = message.medias?.[0] as MediaItem;
      const isImgOrVideo = isImageOrVideo(media.filename || '');

      return (
        <div
          key={message.id}
          className="flex items-center p-3 hover:bg-gray-50"
        >
          <div className="flex-shrink-0 w-12 h-12 overflow-hidden">
            <FileIcon
              fileName={media.filename || 'file'}
              size="48"
              className="flex-shrink-0"
              iconClassName="text-gray-600"
            />
          </div>
          <div className="ml-3 flex-grow">
            <div className="text-sm font-medium truncate max-w-[200px]">
              {media.filename || 'Untitled'}
            </div>
            <div className="flex items-center text-sm text-gray-500">
              <span className="truncate max-w-[150px]">{message.userName}</span>
              <button
                onClick={() => handleDownload(media.url, media.filename)}
                className="flex items-center ml-2 p-1 hover:bg-gray-100 rounded-full"
                title="Download file"
              >
                <Download className="w-4 h-4 text-gray-500 hover:text-orange-400" />
              </button>
            </div>
          </div>
          <div className="text-sm text-gray-500 whitespace-nowrap ml-2">
            {message.timestamp.toLocaleDateString()}
          </div>
          <button
            className="ml-4 text-orange-400 text-sm whitespace-nowrap"
            onClick={() => handleLocateToChat(message.id)}
          >
            Locate to chat
          </button>
        </div>
      );
    }

    // Handle messages with both media and text
    if (isMediaWithText) {
      const media = message.medias?.[0] as MediaItem;

      return (
        <div
          key={message.id}
          className="flex items-center p-3 hover:bg-gray-50"
        >
          <div className="flex-shrink-0 w-12 h-12 overflow-hidden">
            <FileIcon
              fileName={media.filename || 'file'}
              size="48"
              className="flex-shrink-0"
              iconClassName="text-gray-600"
            />
          </div>
          <div className="ml-3 flex-grow relative group">
            <div className="text-sm font-medium truncate max-w-[200px]">
              {message.textBody}
            </div>
            <div className="flex items-center text-sm text-gray-500">
              <span className="truncate max-w-[150px]">{message.userName}</span>
              <button
                onClick={() => handleDownload(media.url, media.filename)}
                className="flex items-center ml-2 p-1 hover:bg-gray-100 rounded-full"
                title="Download file"
              >
                <Download className="w-4 h-4 text-gray-500 hover:text-orange-400" />
              </button>
            </div>
          </div>
          <div className="text-sm text-gray-500 whitespace-nowrap ml-2">
            {message.timestamp.toLocaleDateString()}
          </div>
          <button
            className="ml-4 text-orange-400 text-sm whitespace-nowrap"
            onClick={() => handleLocateToChat(message.id)}
          >
            Locate to chat
          </button>
        </div>
      );
    }

    // Handle link messages
    if (isLink) {
      return (
        <div
          key={message.id}
          className="flex items-center p-3 hover:bg-gray-50 relative"
        >
          <div className="flex-shrink-0 w-8 h-8">
            <Link2 className="w-6 h-6 text-gray-400" />
          </div>
          <div className="ml-3 flex-grow relative group">
            <div
              className="text-sm max-w-xs truncate overflow-hidden whitespace-normal break-words line-clamp-2"
              onClick={(e) => handleCopy(message.textBody, e)}
            >
              {message.textBody}
              <span className="absolute left-0 top-full mt-1 w-max max-w-xs p-2 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 z-10">
                {message.textBody}
              </span>
            </div>
            <div className="text-sm text-gray-500 truncate max-w-[150px]">
              {message.userName}
            </div>
          </div>
          <div className="text-sm text-gray-500 whitespace-nowrap ml-2">
            {message.timestamp.toLocaleDateString()}
          </div>
          <button
            className="ml-4 text-orange-400 text-sm whitespace-nowrap"
            onClick={() => handleLocateToChat(message.id)}
          >
            Locate to chat
          </button>
        </div>
      );
    }

    // Handle regular text messages
    return (
      <div
        key={message.id}
        className="p-3 hover:bg-gray-50"
      >
        <div className="flex justify-between items-start mb-1">
          <span className="text-sm text-gray-500 truncate max-w-[150px]">
            {message.userName}
          </span>
          <span className="text-sm text-gray-500">
            {message.timestamp.toLocaleDateString()}
          </span>
        </div>
        <div className="text-sm">{message.textBody}</div>
        <button
          className="mt-2 text-orange-400 text-sm"
          onClick={() => handleLocateToChat(message.id)}
        >
          Locate to chat
        </button>
      </div>
    );
  };

  return (
    <div className={`${containerClassName} ${className}`}>
      <div className={searchContainerClassName}>
        <div className="relative">
          <Search className="absolute left-3 top-2.5 w-5 h-5 text-gray-400" />
          <input
            type="text"
            className="w-full pl-10 pr-4 py-2 rounded-md border border-gray-200"
            placeholder="Search..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      <div className={tabsContainerClassName}>
        <nav className="flex">
          {[
            { id: 'all', label: 'All' },
            { id: 'files', label: 'Files' },
            { id: 'images-videos', label: 'Images and Videos' },
            { id: 'links', label: 'Links' },
          ].map((tab) => (
            <button
              key={tab.id}
              className={`px-4 py-2 text-sm ${
                activeTab === tab.id
                  ? 'text-orange-400 border-b-2 border-orange-400'
                  : 'text-gray-500'
              }`}
              onClick={() => setActiveTab(tab.id as TabType)}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      <div className={contentContainerClassName}>
        {filteredMessages.length > 0 ? (
          filteredMessages.map(renderMessageItem)
        ) : (
          <div className="p-4 text-center text-gray-500">No messages found</div>
        )}
      </div>
    </div>
  );
};

export default SessionRecordPage;
*/
