import { Button } from '@cdss-modules/design-system';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import { useEffect, useState } from 'react';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import { cn } from '@cdss-modules/design-system/lib/utils';
import TemplateSelector from '../../_ui/TemplateSelector';
import { CircleCheck } from 'lucide-react';
import { SocialIcon } from 'react-social-icons';

export const DUMMY_CHANNELS = [
  {
    id: 'whatsapp',
    label: (
      <div className="flex items-center gap-x-1">
        <SocialIcon
          network="whatsapp"
          style={{ width: 20, height: 20 }}
        />
        WhatsApp
      </div>
    ),
    value: 'whatsapp',
  },
  {
    id: 'wechat',
    label: (
      <div className="flex items-center gap-x-1">
        <SocialIcon
          network="wechat"
          style={{ width: 20, height: 20 }}
        />
        WeChat
      </div>
    ),
    value: 'wechat',
  },
];

const DUMMY_FROM_NUMBERS = [
  {
    id: '85244407755',
    label: '+85244407755',
    value: '85244407755',
  },
  {
    id: '85255415667',
    label: '+85255415667',
    value: '85255415667',
  },
];

const DUMMY_DIRECTORIES = [
  {
    id: 'john',
    label: 'John',
    value: 'john',
  },
  {
    id: 'jane',
    label: 'Jane',
    value: 'jane',
  },
  { id: 'mary', label: 'Mary', value: 'mary' },
  { id: 'peter', label: 'Peter', value: 'peter' },
  { id: 'paul', label: 'Paul', value: 'paul' },
  { id: 'tom', label: 'Tom', value: 'tom' },
];

const DUMMY_QUEUE_DATA = [
  {
    id: '1',
    label: 'Queue 1',
    value: '1',
  },
  {
    id: '2',
    label: 'Queue 2',
    value: '2',
  },
  {
    id: '3',
    label: 'Queue 3',
    value: '3',
  },
  {
    id: '4',
    label: 'Queue 4',
    value: '4',
  },
  {
    id: '5',
    label: 'Queue 5',
    value: '5',
  },
];
export function Open() {
  const [to, setTo] = useState<string>('');
  const [onBehalfOf, setOnBehalfOf] = useState<string>('');
  const [fromNo, setFromNo] = useState<string>('');
  const [channel, setChannel] = useState<string>(DUMMY_CHANNELS?.[0]?.value);
  const [errors, setErrors] = useState<any>({});
  const [step, setStep] = useState('start');
  const toDisplay = DUMMY_DIRECTORIES?.find((d) => d.id === to)?.label;
  const onBehalfOfDisplay = DUMMY_QUEUE_DATA?.find(
    (d) => d.id === onBehalfOf
  )?.label;

  useEffect(() => {
    setErrors({});
  }, [step]);

  const validate = () => {
    const errors: any = {};
    if (!to) {
      errors.to = 'Please enter a recipient';
    }
    if (!onBehalfOf) {
      errors.onBehalfOf = 'Please select a queue';
    }
    setErrors(errors);
    return Object.keys(errors).length === 0;
  };

  return (
    <div className="relative bg-white flex size-full overflow-hidden">
      <div
        className={cn(
          'bg-white relative flex flex-col gap-y-3 p-3 size-full flex-none overflow-auto z-10 transition-all duration-300',
          step !== 'start' && 'opacity-0'
        )}
      >
        <h1 className="text-t6 font-bold">Start a new message conversation</h1>
        <Field
          title={<span className="text-remark">Channel:</span>}
          icon={<Icon name="error" />}
          status={errors?.channel ? 'danger' : undefined}
          message={errors.channel}
        >
          <Select
            placeholder="Select a channel"
            mode="single"
            labelClassName="h-full text-remark"
            labelContainerClassName="h-8"
            options={DUMMY_CHANNELS}
            showSearch={true}
            value={channel}
            onChange={(v) => setChannel(v)}
          />
        </Field>
        <Field
          title={<span className="text-remark">On behalf of:</span>}
          icon={<Icon name="error" />}
          status={errors?.onBehalfOf ? 'danger' : undefined}
          message={errors.onBehalfOf}
        >
          <Select
            placeholder="Select a queue"
            mode="single"
            labelClassName="h-full text-remark"
            labelContainerClassName="h-8"
            options={DUMMY_QUEUE_DATA}
            showSearch={true}
            value={onBehalfOf}
            onChange={(v) => setOnBehalfOf(v)}
          />
        </Field>
        <Field
          title={<span className="text-remark">From No.:</span>}
          icon={<Icon name="error" />}
          status={errors?.from ? 'danger' : undefined}
          message={errors.from}
        >
          <Select
            placeholder="Select a number to send on behalf of"
            mode="single"
            labelClassName="h-full text-remark"
            labelContainerClassName="h-8"
            options={DUMMY_FROM_NUMBERS}
            showSearch={true}
            value={fromNo}
            onChange={(v) => setFromNo(v)}
          />
        </Field>
        <Field
          title={<span className="text-remark">To:</span>}
          icon={<Icon name="error" />}
          status={errors?.to ? 'danger' : undefined}
          message={errors.to}
        >
          <Input
            placeholder="Enter the recipient's name or a number"
            autoCompleteOptions={DUMMY_DIRECTORIES}
            value={to}
            size="s"
            onChange={(v: any) => {
              setTo(v);
            }}
          />
        </Field>
        <div className="mt-2 flex items-center gap-x-3">
          <Button
            fullWidth
            size="s"
            type="button"
            onClick={() => {
              if (validate()) {
                setStep('final');
              }
            }}
          >
            Start a conversation now
          </Button>
          {/* <Button
            fullWidth
            size="s"
            type="button"
            onClick={() => {
              if (validate()) {
                setStep('template');
              }
            }}
          >
            Start with a template message
          </Button> */}
        </div>
      </div>
      <div
        className={cn(
          'bg-white relative flex flex-col gap-y-3 p-3 size-full flex-none z-20 opacity-0 transition-all duration-300',
          step === 'template' && '-translate-x-full opacity-100',
          (step === 'final' || step === 'final-template') &&
            '-translate-x-full opacity-0'
        )}
      >
        <Button
          type="button"
          onClick={() => setStep('start')}
          variant="back"
          beforeIcon={<Icon name="back" />}
          bodyClassName="p-1 min-w-0"
        >
          <span className="font-bold">&nbsp;Back</span>
        </Button>
        <h1 className="text-t6 font-bold">
          Message to <span>{toDisplay}</span> on behalf of{' '}
          <span>{onBehalfOfDisplay}</span>
        </h1>
        <TemplateSelector />
        <Button
          fullWidth
          onClick={() => setStep('final-template')}
          size="s"
        >
          Start and send
        </Button>
      </div>
      <div
        className={cn(
          'bg-white absolute left-full top-0 flex flex-col items-center justify-center gap-y-3 p-3 size-full flex-none z-30 opacity-0 transition-all duration-300',
          step === 'final' && '-translate-x-full opacity-100'
        )}
      >
        <div className="flex flex-col items-center px-8">
          <CircleCheck
            className="text-status-success"
            size={60}
          />
          <h1 className="text-t6 font-bold mt-4 mb-2">Conversation Started!</h1>
          <p className="text-center text-remark mb-4">
            Conversation has been started with <span>{toDisplay}</span> on
            behalf of <span>{onBehalfOfDisplay}</span>. Please go to
            interactions to continue the conversation.
          </p>
          <Button
            onClick={() => setStep('start')}
            size="s"
          >
            Create another conversation
          </Button>
        </div>
      </div>
      <div
        className={cn(
          'bg-white absolute left-full top-0 flex flex-col items-center justify-center gap-y-3 p-3 size-full flex-none z-30 opacity-0 transition-all duration-300',
          step === 'final-template' && '-translate-x-full opacity-100'
        )}
      >
        <div className="flex flex-col items-center px-8">
          <CircleCheck
            className="text-status-success"
            size={60}
          />
          <h1 className="text-t6 font-bold mt-4 mb-2">
            Template message sent!
          </h1>
          <p className="text-center text-remark mb-4">
            The message has been sent to <span>{toDisplay}</span> on behalf of{' '}
            <span>{onBehalfOfDisplay}</span>. Please go to interactions to
            continue the conversation.
          </p>
          <Button
            onClick={() => setStep('start')}
            size="s"
          >
            Send another message
          </Button>
        </div>
      </div>
    </div>
  );
}

export default Open;
