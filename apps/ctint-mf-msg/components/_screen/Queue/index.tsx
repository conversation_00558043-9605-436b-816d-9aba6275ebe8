import { But<PERSON>, Panel, useCDSS } from '@cdss-modules/design-system';
import QueueList from '../../_ui/QueueList';
import SearchInput from '@cdss-modules/design-system/components/_ui/SearchInput';
import PopoverMenu from '@cdss-modules/design-system/components/_ui/PopoverMenu';
import { cn } from '@cdss-modules/design-system/lib/utils';
import Checkbox from '@cdss-modules/design-system/components/_ui/Checkbox';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { useState } from 'react';
import _ from 'lodash';
export function Queue() {
  const { activeInteraction, openQueueItem } = useCDSS();
  const applyFilter = () => null;
  const clearFilter = () => null;
  const conbineFiltersTagName = () => [];
  const AVAILABLE_FILTERS = [] as any[];
  const allAvailableColumns = [] as any[];
  const [filterFields, setFilterFields] = useState<any[]>([]);
  const [filterInput, setFilterInput] = useState<any>({});
  const clearPreference = (type: string) => null;
  const savePreference = (type: string) => null;
  return (
    <div className="flex flex-col gap-2 h-full">
      <div
        className={cn(
          `w-full py-2 px-3 bg-white rounded-xl inline-flex flex-row overflow-x-auto`,
          openQueueItem && 'hidden'
        )}
      >
        <div className="flex-1 flex flex-row">
          {/* Search Filter Input Component */}
          <SearchInput tags={conbineFiltersTagName()}>
            {/* Filter Operation Popover Content */}
            <section>
              {/* Filter Operation Scroll Block */}
              <section className="max-h-[409px] min-w-[600px] max-w-[800px] overflow-y-auto">
                {/* Popover selected filters items */}
                {/* <section className="p-4">
                {filterFields.length > 0 && (
                  <div className="flex flex-wrap flex-row">
                    {renderTagItems(conbineFiltersTagName())}
                  </div>
                )}
              </section> */}
                {/* Popover filter input form */}
                <section className="px-4">
                  <h2 className="text-remark font-bold my-2">
                    Some filters here
                  </h2>
                  <div className="flex flex-col gap-y-2">
                    {AVAILABLE_FILTERS.map((item) => {
                      const filterName = item?.name;
                      const isChecked = filterFields.includes(filterName); //default is false
                      const filterValue = filterInput?.[filterName];

                      return (
                        <div
                          key={`filter-${filterName}-key`}
                          className={cn(
                            ` items-center gap-x-4 `,
                            filterFields.includes('id') && filterName !== 'id'
                              ? 'hidden'
                              : 'flex'
                          )}
                        >
                          <div className="w-[280px]">
                            <Checkbox
                              id={`filter-${filterName}`}
                              label={filterName}
                              value={filterName}
                              checked={isChecked}
                              onChange={(e: any) => {
                                const isSelected = e?.target.checked;
                                const value = e?.target.value;

                                if (
                                  filterName === 'isInbound' ||
                                  filterName === 'isOutbound'
                                ) {
                                  isSelected
                                    ? setFilterInput({
                                        ...filterInput,
                                        [value]: isSelected,
                                      })
                                    : delete filterInput[value];
                                }

                                setFilterFields((prev) => {
                                  const newColumns = isSelected
                                    ? [...prev, value]
                                    : prev.filter((pre) => pre !== value);
                                  return _.sortBy(newColumns, (column) =>
                                    allAvailableColumns.indexOf(column)
                                  );
                                });
                              }}
                            />
                          </div>
                          {/* <FilterInput
                            type={item.type}
                            filterName={filterName}
                            filterValue={filterValue}
                            isChecked={isChecked}
                            operatorValue={operatorValue}
                            setFilterInput={setFilterInput}
                            handleOperatorValue={handleOperatorValue}
                          /> */}
                        </div>
                      );
                    })}
                  </div>
                </section>
              </section>
              {/* Filter Items Operation Button */}
              <section className="max-h-[45px] px-4 py-1 flex flex-row-reverse w-full ">
                <Button
                  className="mx-1 z-0"
                  bodyClassName="py-[0.375rem]"
                  variant={'orange'}
                  onClick={() => clearPreference('filters')}
                  size="s"
                >
                  Clear Filter
                </Button>
                <Button
                  className="mx-1 z-0"
                  bodyClassName="py-[0.375rem]"
                  variant={'orange'}
                  onClick={() => savePreference('filters')}
                  size="s"
                >
                  Save Filter
                </Button>
              </section>
            </section>
          </SearchInput>
          {/* Search Button */}
          <Button
            className="self-center ml-2"
            bodyClassName="border border-black py-[0.375rem]"
            onClick={() => applyFilter()}
            size="s"
          >
            Search
          </Button>
          {/* Clear Tags Button */}
          <Button
            className="self-center ml-2"
            bodyClassName="border border-black py-[0.375rem]"
            onClick={() => clearFilter()}
            variant="blank"
            size="s"
          >
            Clear
          </Button>
        </div>
        {/* Table Operation Menu */}
        <PopoverMenu
          icon={
            <Icon
              name="verticalDots"
              className="self-center justify-end cursor-pointer mx-1 flex-shrink-0"
              size={23}
            />
          }
        >
          <div className="flex flex-col bg-white shadow-[0px_2px_10px_0px_rgba(0,0,0,0.18)]">
            <button
              className="m-2 flex gap-2 items-center w-full"
              onClick={() => null}
            >
              <Icon name="eye" />
              <span>Add Columns</span>
            </button>
            <div className="w-full h-[1px] bg-black"></div>
            <button
              onClick={() => null}
              className="mx-2 mt-2 flex gap-2 items-center w-full"
            >
              <Icon name="save" />
              <span>Save Columns</span>
            </button>
            <button
              onClick={() => null}
              className="m-2 flex gap-2 items-center w-full"
            >
              <Icon name="cross" />
              <span>Clear Columns</span>
            </button>
          </div>
        </PopoverMenu>
      </div>
      <Panel
        className="p-0"
        containerClassName="h-full"
      >
        <QueueList />
      </Panel>
    </div>
  );
}

export default Queue;
