import Icon from '@cdss-modules/design-system/components/_ui/Icon';

const DUMMY_DETAIL = {
  conversationId: '-',
  startTime: '2024-03-22T02:57:28.000+0000',
  endTime: '2024-03-22T02:58:08.000+0000',
  mediaUri:
    '/recordings/E0DBMNLAM52MJ0GUEKVQ8I0ODO00000T/play/a5d659f4-c5c7-472e-9a7a-f9dcdafc44cf.mp3',
  duration: 40.187,
  users: null,
  direction: 'Internal',
  mediaType: 'call',
  username: 'MOCK USER',
  dialedNumber: '22001',
  callerNumber: '71002',
} as any;

const DUMMY_INFO_DATA = [
  {
    label: 'Queue',
    value: 'Complaint',
    icon: 'user',
  },
  {
    label: 'Type',
    value: 'Call',
    icon: 'phone',
  },
  {
    label: 'Date',
    value: '2024-03-22T02:57:28.000+0000',
    icon: 'calendar',
  },
  {
    label: 'Duration',
    value: '40.187',
    icon: 'prev-10s',
  },
  {
    label: 'Media Type',
    value: 'Call',
    icon: 'file',
  },
  {
    label: 'Direction',
    value: 'Internal',
    icon: 'inbound',
  },
  {
    label: 'Media Source',
    value: 'N/A',
    icon: 'file',
  },
];

export const BasicInfo = ({ data = DUMMY_INFO_DATA }: { data: any[] }) => {
  return (
    <div className="overflow-y-auto h-0 flex-1 flex flex-col gap-6">
      {data?.map((item, index) => (
        <div
          key={`info-item-${index}`}
          className="flex gap-2"
        >
          <Icon
            name={item.icon}
            size={24}
          />
          <strong>{item.label}:</strong>
          <p>{item.value}</p>
        </div>
      ))}
    </div>
  );
};

export default BasicInfo;
