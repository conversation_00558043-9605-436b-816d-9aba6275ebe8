import {
  Panel,
  useCDSS,
  useScrollToElement,
} from '@cdss-modules/design-system';
import Avatar from '@cdss-modules/design-system/components/_ui/Avatar';
import CDSSImage from '@cdss-modules/design-system/components/_ui/CDSSImage';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import { cn } from '@cdss-modules/design-system/lib/utils';
import dayjs from 'dayjs';
import {
  Check,
  CheckCheck,
  Paperclip,
  SendHorizonal,
  Smile,
} from 'lucide-react';
import { useEffect, useState } from 'react';
// TODO: consider move to global helper
const getFilteredTranscripts = (transcripts?: any[]) => {
  if (!Array.isArray(transcripts)) return null;
  return transcripts?.filter((transcript) => {
    return transcript?.speaker !== 'speaker';
  });
};

const getLettersFromUsername = (username: string, length = 2) => {
  let output = '';
  for (let i = 0; i < length; i++) {
    const letter = username?.[i];
    if (letter) {
      output += letter.toUpperCase();
    }
  }
  return output || 'N/A';
};

export type TMsg = {
  time: string;
  speaker: string;
  text: string;
  status?: string;
};
const Chatroom = () => {
  const [msg, setMsg] = useState<string>('');
  const [demoNewMessage, setDemoNewMessage] = useState<TMsg[]>([]);
  const { activeInteraction, updateActiveSAA } = useCDSS();
  const demoMsgs = [...(activeInteraction?.content || []), ...demoNewMessage];

  const { getScrollToElementRef, scrollToElementClickHandler } =
    useScrollToElement(
      ['msg-bottom'], //array of strings
      {
        behavior: 'instant',
      } //this is optional - behavior: smooth is used by default
    );

  useEffect(() => {
    if (demoNewMessage?.some((msg) => msg.status === 'sending')) {
      setTimeout(() => {
        const newDemoNewMessage = demoNewMessage.map((msg) => {
          if (msg.status === 'sending') {
            return {
              ...msg,
              status: 'sent',
            };
          }
          return msg;
        });
        setDemoNewMessage(newDemoNewMessage);
      }, 2000);
    }
  }, [demoNewMessage]);

  const pos = 0;

  useEffect(() => {
    // add click event for .saa-keyword to get the data-saa-id and run updateActiveSAA
    const saaKeywords = document.querySelectorAll('.saa-keyword');
    saaKeywords.forEach((keyword) => {
      keyword.addEventListener('click', (e: any) => {
        const saaId = e.target.getAttribute('data-saa-id');
        if (saaId) {
          updateActiveSAA(saaId);
        }
      });
    });
    return () => {
      saaKeywords.forEach((keyword) => {
        keyword.removeEventListener('click', (e: any) => {
          const saaId = e.target.getAttribute('data-saa-id');
          if (saaId) {
            updateActiveSAA(saaId);
          }
        });
      });
    };
  }, [updateActiveSAA]);

  // Onmi Channel Selection demo
  const [activeChannel, setActiveChannel] = useState('wa');

  return (
    <Panel
      className="p-4"
      containerClassName="h-full"
    >
      <div className="flex justify-between items-center border-b border-grey-200 pb-4 mb-4">
        <div className="flex items-center gap-x-2">
          <Avatar
            text={activeInteraction?.customerShortName || 'N/A'}
            textClassName="text-body text-black"
            className={cn('size-10 flex-none')}
          />
          <h2 className="font-bold text-t6">
            {activeInteraction?.customerName || 'Unknown Customer'}
          </h2>
        </div>
        <div className="flex gap-x-4">
          <Select
            placeholder="placeholder"
            mode="single"
            options={[
              { label: 'WhatsApp', value: 'wa', id: 'wa' },
              { label: 'Facebook', value: 'fb', id: 'fb' },
              { label: 'Webchat', value: 'webchat', id: 'webchat' },
              { label: 'SMS', value: 'sms', id: 'sms' },
              { label: 'Email', value: 'email', id: 'email' },
              { label: 'Others', value: 'others', id: 'others' },
            ]}
            value={activeChannel}
            onChange={(v) => {
              setActiveChannel(v);
            }}
            labelClassName="h-full text-remark"
            labelContainerClassName="h-8"
          />
          <button>
            <Icon
              name="search"
              size={24}
            />
          </button>
        </div>
      </div>
      <div className="w-full flex-1 h-0">
        <div className="relative h-full overflow-auto">
          <div className={cn('flex flex-col gap-4 pb-4')}>
            {getFilteredTranscripts(demoMsgs)?.map((item, i) => {
              const key = `${item?.start}-${item?.end}-${item?.speaker}-${item?.text}`;
              const start = parseFloat(item?.start);
              const end = parseFloat(item?.end);
              const isPlaying = pos > start && pos < end;
              const filteredTranscript = getFilteredTranscripts(demoMsgs);

              const theSameSpeaker =
                item.speaker ===
                (filteredTranscript !== null &&
                  filteredTranscript[i - 1]?.speaker);
              const notTheSameSpeaker =
                item.speaker !==
                (filteredTranscript !== null &&
                  filteredTranscript[i - 1]?.speaker);
              const isSameAsFirstSpeaker =
                item.speaker ===
                (filteredTranscript !== null && filteredTranscript[0]?.speaker);

              const displayRight = item?.speaker === 'Agent';

              return (
                <div
                  key={key}
                  className={cn(
                    'flex gap-x-2',
                    i !== 0 && theSameSpeaker && '-mt-3',
                    displayRight && 'pr-2 flex-row-reverse'
                  )}
                >
                  {notTheSameSpeaker ? (
                    <div
                      className={cn(
                        'size-8 rounded-full flex justify-center items-center flex-none',
                        displayRight && 'flex-row-reverse',
                        item?.speaker === 'Agent'
                          ? 'bg-tertiary-100'
                          : 'bg-primary-100'
                      )}
                    >
                      {item?.speaker === 'Agent'
                        ? getLettersFromUsername(item?.speaker)
                        : activeInteraction?.customerShortName}
                    </div>
                  ) : (
                    <div className="w-8 h-8 flex-none" />
                  )}

                  <div
                    className={cn(
                      'flex items-end gap-x-3',
                      displayRight && 'flex-row-reverse'
                    )}
                  >
                    <div className={cn('flex flex-col')}>
                      {notTheSameSpeaker && (
                        <div
                          className={cn(
                            'text-footnote text-grey-500',
                            displayRight ? 'text-right' : 'text-left'
                          )}
                        >
                          {item?.speaker}
                        </div>
                      )}
                      <div
                        className={cn(
                          'relative transcript px-4 pt-2 pb-3 rounded-lg',
                          isSameAsFirstSpeaker
                            ? 'bg-primary-100'
                            : 'bg-common-bg',
                          isPlaying && 'bg-primary-300',
                          displayRight ? 'ml-10' : 'mr-10'
                        )}
                      >
                        <span
                          dangerouslySetInnerHTML={{
                            __html: item?.text,
                          }}
                        />
                        <span
                          className={cn(
                            'inline-block h-1',
                            displayRight ? 'w-14' : 'w-8'
                          )}
                        />
                        {item?.attachedImages && (
                          <div className="w-full aspect-video rounded-md overflow-hidden mt-2 mb-4 max-w-[300px]">
                            <CDSSImage
                              className="size-full object-cover"
                              src={item?.attachedImages?.[0] || ''}
                              alt=""
                            />
                          </div>
                        )}
                        <div className="text-grey-500 absolute bottom-1 right-3 inline-flex text-left text-footnote italic group">
                          {dayjs(item?.time).format('HH:mm')}
                          {displayRight && (
                            <>
                              {item?.status === 'sending' ? (
                                <Check className="size-4 ml-1 mt-[2px]" />
                              ) : (
                                <CheckCheck className="size-4 ml-1 mt-[2px]" />
                              )}
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
          <div
            className="relative w-px h-px left-0 bottom-0 pointer-events-none"
            ref={getScrollToElementRef('msg-bottom') as any}
          />
        </div>
      </div>
      <form
        className="relative w-full border-t-4 border-primary pt-4"
        onSubmit={(e) => {
          e.preventDefault();
          if (msg) {
            setDemoNewMessage([
              ...demoNewMessage,
              {
                time: dayjs().toISOString(),
                speaker: 'Agent',
                text: msg,
                status: 'sending',
              },
            ]);
            setMsg('');
            setTimeout(() => {
              scrollToElementClickHandler('msg-bottom');
            }, 250);
          }
        }}
      >
        <Input
          value={msg}
          onChange={(v) => setMsg(`${v}`)}
          placeholder="Type some message"
        />
        <div className="absolute h-full right-0 top-0 pt-4 pr-4 flex gap-x-4">
          <button
            type="button"
            className=" hover:text-primary-400"
          >
            <Paperclip />
          </button>
          <button
            type="button"
            className=" hover:text-primary-400"
          >
            <Smile />
          </button>
          <div className="relative w-px h-full py-2">
            <div className="size-full bg-grey-200" />
          </div>
          <button
            type="submit"
            className="hover:text-primary-400"
          >
            <SendHorizonal />
          </button>
        </div>
      </form>
    </Panel>
  );
};

export default Chatroom;
