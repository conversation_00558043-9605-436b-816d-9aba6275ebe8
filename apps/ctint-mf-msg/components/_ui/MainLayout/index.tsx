'use client';
import { Tooltip, useCDSS, useRouteHandler } from '@cdss-modules/design-system';
import AppLayout from '@cdss-modules/design-system/components/_ui/AppLayout';
import { Toaster } from '@cdss-modules/design-system/components/_ui/Toast/toaster';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import SideBar from '../SideBar';
import { SquareStack } from 'lucide-react';
import { cn } from '@cdss-modules/design-system/lib/utils';
import QueuePreview from '../QueuePreview';
type TMainLayoutProps = {
  children: React.ReactNode;
};

export const MainLayout = ({ children }: TMainLayoutProps) => {
  const { activePath, toPath } = useRouteHandler();
  const { t, i18n } = useTranslation();
  const { openQueueData, updateOpenQueueData } = useCDSS();
  const targetLng = i18n.language === 'en' ? 'zh-HK' : 'en';
  return (
    <AppLayout
      noPadding
      headerPanel={
        <Tooltip
          content="Interaction Queues"
          trigger={
            <button
              className={cn(
                'flex items-center justify-center aspect-square h-10 bg-grey-300 hover:bg-primary-500 rounded-sm',
                activePath &&
                  activePath?.indexOf('queue') > -1 &&
                  'bg-primary-500'
              )}
              type="button"
              onClick={() => toPath('/queue')}
            >
              <SquareStack
                size={28}
                className="text-white"
              />
            </button>
          }
        />
      }
      hasSideNav={false}
    >
      <div className="flex flex-row size-full items-start overflow-hidden">
        <SideBar />
        <QueuePreview
          data={openQueueData}
          onOpenChange={(open: boolean) => {
            if (!open) updateOpenQueueData(null);
          }}
        />
        <div className="relative w-full h-full p-4">{children}</div>
      </div>
      <Toaster />
    </AppLayout>
  );
};

export default MainLayout;
