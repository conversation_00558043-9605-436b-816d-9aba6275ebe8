import { cn, secondsToFormat } from '@cdss-modules/design-system/lib/utils';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';

type TProgressCircleProps = {
  status?: string;
  time?: string;
  progressPercent?: number;
  showTime?: boolean;
};
export const ProgressCircle = ({
  time,
  showTime,
  status,
}: TProgressCircleProps) => {
  const stroke = '5';
  const [count, setCount] = useState<number>(0);

  // Count the seconds from time to now using dayjs

  useEffect(() => {
    if (!time) return;

    // Function to update the time
    const updateTimer = () => {
      const secToNow = dayjs().diff(dayjs(time), 'second');
      setCount(secToNow);
    };
    updateTimer();
    // Set interval to update time every second
    const intervalId = setInterval(updateTimer, 1000);

    return () => clearInterval(intervalId);
  }, [time]);

  const countProgress = Math.min(100, (count / 360) * 100);
  return (
    <div className="relative size-full">
      <svg
        className="w-full h-full scale-110"
        viewBox="0 0 100 100"
      >
        <circle
          className="text-gray-200 stroke-current"
          strokeWidth={stroke}
          cx="50"
          cy="50"
          r="40"
          fill="transparent"
        />
        <circle
          className={cn(
            'stroke-current origin-center -rotate-90 fill-transparent',
            status && status === 'history' && '!text-grey-400',
            countProgress >= 70 && 'text-status-danger',
            countProgress >= 50 && countProgress < 70 && 'text-primary-600',
            countProgress < 50 && 'text-status-success'
          )}
          strokeWidth={stroke}
          strokeLinecap="round"
          cx="50"
          cy="50"
          r="40"
          strokeDasharray="251.2"
          strokeDashoffset={`calc(251.2px - (251.2px * ${countProgress}) / 100)`}
        />
      </svg>

      {showTime && (
        <div className="absolute top-0 left-0 size-full p-1">
          <div className="size-full flex items-center justify-center text-mini text-black bg-primary-100 rounded-full">
            {secondsToFormat(count)}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProgressCircle;
