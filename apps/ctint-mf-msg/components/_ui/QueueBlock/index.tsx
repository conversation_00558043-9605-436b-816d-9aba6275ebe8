import Avatar from '@cdss-modules/design-system/components/_ui/Avatar';
import { useCDSS } from '@cdss-modules/design-system/context/CDSSContext';
import { cn, get2LettersFromName } from '@cdss-modules/design-system/lib/utils';

export type TQueueBlockProps = {
  data: any;
  index: number;
};

const QueueBlock = ({ data }: TQueueBlockProps) => {
  const {
    sideBarExpanded,
    queuePanelExpanded,
    activeInteraction,
    toggleQueuePanel,
  } = useCDSS();
  const fullName = data?.name;
  const shortName = get2LettersFromName(fullName);
  const isActive = queuePanelExpanded?.id === data.id;
  const queueItems = data?.items || [];
  const queueItemsCount = queueItems.length;
  if(!queueItemsCount) return null;
  return (
    <button
      type="button"
      onClick={() => {
        toggleQueuePanel(data);
      }}
      className={cn(
        'relative flex w-full px-1 items-center group/msg-side-bar gap-x-1 text-footnote',
        sideBarExpanded
          ? 'px-4 justify-start border-b border-grey-200 hover:bg-primary-200'
          : 'justify-center',
        isActive && sideBarExpanded && 'bg-primary-200'
      )}
    >
      <div
        className={cn(
          'relative w-full p-[10px] flex-none rounded-lg aspect-square max-w-16',
          sideBarExpanded ? '' : 'group-hover/msg-side-bar:bg-primary-200',
          isActive && !sideBarExpanded && 'bg-primary-200'
        )}
      >
        <div
          className={cn(
            'relative size-full rounded-lg border-2 border-gray-200 bg-white flex-none'
          )}
        >
          <div className="z-20 absolute top-[2px] right-[2px] -translate-y-1/2 translate-x-1/2 bg-primary-500 text-white text-[12px] rounded-full size-5 flex justify-center items-center">
            {queueItemsCount}
          </div>
          <Avatar
            text={shortName}
            textClassName="text-body text-black"
            className={cn('relative size-full flex-none z-10 bg-white')}
          />
        </div>
      </div>
    </button>
  );
};

export default QueueBlock;
