import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';
import { useMemo, useState } from 'react';
import { ColumnDef, Table as TableType } from '@tanstack/react-table';
import {
  SortingButton,
  toast,
  Tooltip,
  useCDSS,
} from '@cdss-modules/design-system';
import { GLOBAL_DATETIME_FORMAT } from '@cdss-modules/design-system/lib/constants';
import dayjs from 'dayjs';
import Checkbox from '@cdss-modules/design-system/components/_ui/Checkbox';
import { Files, Mail, Pause, Play, Voicemail } from 'lucide-react';
import QueueListActionMenu from '../QueueListActionMenu';
import VoiceMailViewer from '../VoiceMailViewer';
import EmailViewer from '../EmailViewer';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import Breadcrumb from '@cdss-modules/design-system/components/_ui/Breadcrumb';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { cn } from '@cdss-modules/design-system/lib/utils';

// Types
export type TQueueData = {
  id: string;
  status: string;
  customerName: string;
  from: string;
  type: string;
  queueName?: string;
  createdAt: string;
};

type TQueueDataColumn = keyof TQueueData;

const QUEUE_COL_LABEL: any = {
  status: 'Status',
  customerName: 'Customer Name',
  from: 'From',
  displayFrom: 'From',
  type: 'Type',
  detail: 'Content',
  queueName: 'Queue Name',
  createdAt: 'Created At',
};

const PlayBtn = () => {
  const [isPlayRecording, setIsPlayRecording] = useState(false);

  return (
    <button
      type="button"
      className={cn('text-grey-500 hover:text-primary-500')}
      onClick={(e) => {
        e.stopPropagation();
        setIsPlayRecording(!isPlayRecording);
      }}
    >
      {isPlayRecording ? (
        <Pause className="size-5" />
      ) : (
        <Play className="size-5" />
      )}
    </button>
  );
};

const generateColumns = (
  columns: TQueueDataColumn[],
  columnOrdering: string[],
  sortOrder: any,
  setSortOrder: (input: any) => void
) => {
  const selectionCol = {
    id: 'select',
    header: ({ table }: any) => (
      <div
        className="bg-white z-30 inline-flex items-center my-1"
        onClick={(e) => e.stopPropagation()}
      >
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onChange={(e: any) => {
            const isSelected = e?.target.checked;
            table.toggleAllPageRowsSelected(isSelected);
          }}
        />
      </div>
    ),
    cell: ({ row }: any) => (
      <div
        className="z-0 inline-flex items-center my-1"
        onClick={(e) => e.stopPropagation()}
      >
        <Checkbox
          checked={row.getIsSelected()}
          onChange={(e: any) => {
            const isSelected = e?.target.checked;
            row.toggleSelected(isSelected);
          }}
        />
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  };

  const actionCol = {
    id: 'action',
    header: () => <div></div>,
    cell: ({ row }: any) => (
      <div className="flex justify-end gap-x-2 z-0">
        {row?.original?.type === 'voicemail' && <PlayBtn />}
        <QueueListActionMenu data={{}} />
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  };

  const orderedColumns = columnOrdering
    ? columns.sort((firstEl, secondEl) => {
        const firstIndex = columnOrdering?.findIndex(
          (item: any) => item.name === firstEl
        );
        const secondIndex = columnOrdering?.findIndex(
          (item: any) => item.name === secondEl
        );
        return firstIndex - secondIndex;
      })
    : columns;

  const stripHtml = (html: string) => {
    const tmp = document.createElement('DIV');
    tmp.innerHTML = html;
    return tmp.textContent || tmp.innerText || '';
  };

  const formattedColumns = orderedColumns.map((column: string) => {
    const isDate = column === 'createdAt' || column === 'updatedAt';
    const isType = column === 'type';

    return {
      id: column,
      accessorKey: column,
      header: () => {
        return (
          <SortingButton
            sorting={
              sortOrder?.[column]
                ? sortOrder?.[column] === 'ASC'
                  ? 'asc'
                  : 'desc'
                : false
            }
            onClick={async () => {
              const targetSortOrder =
                sortOrder?.[column] === 'ASC' ? 'DESC' : 'ASC';
              setSortOrder({
                [column]: targetSortOrder,
              });
            }}
          >
            {QUEUE_COL_LABEL?.[column] || column}
          </SortingButton>
        );
      },
      cell: ({ row }) => {
        let val = row.getValue(column) as any;
        if (isDate) val = dayjs(val).format(GLOBAL_DATETIME_FORMAT);
        if (isType) {
          return (
            <div className="flex items-center gap-x-2">
              <Tooltip
                content={val === 'email' ? 'Email' : 'Voicemail'}
                side="top"
                trigger={
                  val === 'email' ? (
                    <Mail className="fill-primary-500 text-white" />
                  ) : (
                    <Voicemail className="text-tertiary-400" />
                  )
                }
              />
            </div>
          );
        }
        if (column === 'values') val = val?.map((v: any) => v.value).join(', ');
        if (column === 'key') {
          return (
            <button
              onClick={(e) => {
                e.stopPropagation();
                toast({
                  variant: 'success',
                  title: 'Text Copied',
                  description: `"${val}" copied to clipboard`,
                });
              }}
              className="text-primary-900 font-bold flex items-center gap-x-2 group/table-copy"
            >
              {val}
              <Files
                size={16}
                className="opacity-0 group-hover/table-copy:opacity-100"
              />
            </button>
          );
        }
        if (column === 'name')
          return (
            <div className="text-primary-900 font-bold flex items-center gap-x-2">
              {val}
            </div>
          );
        if (column === 'detail') {
          return (
            <div className="max-w-[500px] whitespace-nowrap truncate">
              {val?.subject && (
                <>
                  <span className="font-bold">{val.subject}</span>
                  <span>{` - `}</span>
                  <span
                    dangerouslySetInnerHTML={{ __html: stripHtml(val.content) }}
                  />
                </>
              )}
              {val?.transcript && (
                <span className="font-bold">{val.transcript}</span>
              )}
            </div>
          );
        }
        if (column === 'displayFrom') {
          return (
            <div className="max-w-[300px] whitespace-nowrap truncate">
              <strong>{val?.customerName}</strong>
              <span className="text-footnote">{` <${val?.from}>`}</span>
            </div>
          );
        }
        return <div>{val}</div>;
      },
    } as ColumnDef<TQueueData>;
  });

  return [selectionCol, ...formattedColumns, actionCol];
};
// queue data sample:
// const queueData = [
//   {
//     id: 'dummy-queue-1',
//     name: 'Customer Service Queue',
//     items: [
//       {
//         id: 'dummy-int-1',
//         customerName: 'Tigger Wong',
//         from: '+852 6123 4567',
//         type: 'voicemail',
//       },
//       {
//         id: 'dummy-int-2',
//         customerName: 'Peter Parker',
//         from: '<EMAIL>',
//         type: 'email',
//       },
//       {
//         id: 'dummy-int-3',
//         customerName: 'Mary Ng',
//         from: '<EMAIL>',
//         type: 'email',
//       },
//     ],
//   },
//   {
//     id: 'dummy-queue-2',
//     name: 'Technical Support Queue',
//     items: [
//       {
//         id: 'dummy-int-4',
//         customerName: 'Mobile Number, Hong Kong',
//         from: '+852 5331 3557',
//         type: 'voicemail',
//       },
//     ],
//   },
//   {
//     id: 'dummy-queue-3',
//     name: 'Sales Queue',
//     items: [],
//   },
// ];
export const QueueList = () => {
  const {
    queueData,
    openQueueItem,
    updateOpenQueueData: setOpenedEntity,
  } = useCDSS();
  const dummyQueueList = useMemo(() => {
    //Flat the queueData into an array
    if (!queueData) return [];
    return queueData.reduce((acc: any[], queue: any) => {
      return [
        ...acc,
        ...queue.items.map((item: any) => {
          return {
            ...item,
            displayFrom: {
              customerName: item.customerName,
              from: item.from,
            },
            queueName: queue.name,
          };
        }),
      ];
    }, []);
  }, [queueData]);
  const [rowSelection, setRowSelection] = useState({});
  const [table, setTable] = useState<TableType<TQueueData>>();
  const [sortOrder, setSortOrder] = useState<any>();
  // const [openedEntity, setOpenedEntity] = useState<TQueueData | null>(null);
  const shownColumns = [
    'type',
    'status',
    'displayFrom',
    'detail',
    'queueName',
    'createdAt',
  ] as TQueueDataColumn[];

  return (
    <div className="px-3 pt-1 pb-6 flex flex-col h-full gap-y-4 overflow-auto">
      <div className="flex-1 h-0">
        {openQueueItem ? (
          <div className="py-2">
            <div className="mb-4 flex items-center gap-2">
              <Button
                asSquare
                variant="back"
                onClick={() => setOpenedEntity(null)}
                beforeIcon={<Icon name="back" />}
              />
              <Breadcrumb
                items={[
                  {
                    label: 'Manual Queue',
                    onClick: () => setOpenedEntity(null),
                  },
                  {
                    label: openQueueItem?.id,
                    link: '',
                  },
                ]}
              />
            </div>
            {openQueueItem?.type === 'email' ? (
              <EmailViewer email={openQueueItem as any} />
            ) : (
              <VoiceMailViewer data={openQueueItem} />
            )}
          </div>
        ) : (
          <DataTable<TQueueData>
            data={dummyQueueList}
            columns={
              generateColumns(shownColumns, [], sortOrder, (input) => {
                setSortOrder(input);
              }) as any
            }
            loading={false}
            // error={error?.message}
            rowSelection={rowSelection}
            setRowSelection={setRowSelection}
            onClickRow={(row) => {
              setOpenedEntity(dummyQueueList?.[row?.index || 0] || {});
            }}
            onTableSetUp={(table) => setTable(table)}
          />
        )}
      </div>
    </div>
  );
};

export default QueueList;
