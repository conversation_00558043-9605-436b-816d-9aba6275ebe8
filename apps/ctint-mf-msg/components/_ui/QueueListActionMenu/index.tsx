import { useRouteHand<PERSON> } from '@cdss-modules/design-system';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@cdss-modules/design-system/components/_ui/DropdownMenu';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { useCDSS } from '@cdss-modules/design-system/context/CDSSContext';
import React from 'react';

export type TQueueListActionMenuProps = {
  data: any;
  icon?: React.ReactNode;
  onClickCancel?: () => void;
};

export const QueueListActionMenu = ({
  data,
  icon,
  onClickCancel,
}: TQueueListActionMenuProps) => {
  const { addInteractionFromQueue, updateOpenQueueData } = useCDSS();
  const { toPath } = useRouteHandler();
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="flex items-center gap-x-2 hover:text-primary-600">
          {icon || (
            <Icon
              name="verticalDots"
              size={23}
            />
          )}
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <>
          <DropdownMenuGroup>
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                addInteractionFromQueue(data?.id);
                updateOpenQueueData(null);
                toPath('/');
              }}
              className="text-remark"
            >
              Assign To Me
            </DropdownMenuItem>

            <DropdownMenuSub>
              <DropdownMenuSubTrigger className="text-remark">
                Assign to...
              </DropdownMenuSubTrigger>
              <DropdownMenuPortal>
                <DropdownMenuSubContent>
                  <div className="px-3">
                    <Input
                      placeholder="Search..."
                      size="s"
                    />
                  </div>
                  <DropdownMenuItem className="text-remark">
                    <button
                      className="w-full text-left"
                      onClick={() => console.log('Template 2')}
                    >
                      Agent 1
                    </button>
                  </DropdownMenuItem>
                  <DropdownMenuItem className="text-remark">
                    <button
                      className="w-full text-left"
                      onClick={() => console.log('Template 3')}
                    >
                      Agent 2
                    </button>
                  </DropdownMenuItem>
                </DropdownMenuSubContent>
              </DropdownMenuPortal>
            </DropdownMenuSub>
            <DropdownMenuSub>
              <DropdownMenuSubTrigger className="text-remark">
                Transfer to...
              </DropdownMenuSubTrigger>
              <DropdownMenuPortal>
                <DropdownMenuSubContent>
                  <div className="px-3">
                    <Input
                      placeholder="Search..."
                      size="s"
                    />
                  </div>
                  <DropdownMenuItem className="text-remark">
                    <button
                      className="w-full text-left"
                      onClick={() => console.log('Template 1')}
                    >
                      Customer Service Queue
                    </button>
                  </DropdownMenuItem>
                  <DropdownMenuItem className="text-remark">
                    <button
                      className="w-full text-left"
                      onClick={() => console.log('Template 2')}
                    >
                      Technical Support Queue
                    </button>
                  </DropdownMenuItem>
                  <DropdownMenuItem className="text-remark">
                    <button
                      className="w-full text-left"
                      onClick={() => console.log('Template 3')}
                    >
                      Sales Queue
                    </button>
                  </DropdownMenuItem>
                </DropdownMenuSubContent>
              </DropdownMenuPortal>
            </DropdownMenuSub>
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                // addInteractionFromQueue(data?.id);
                // updateOpenQueueData(null);
                // toPath('/');
              }}
              className="text-remark"
            >
              Close
            </DropdownMenuItem>
            {onClickCancel && (
              <DropdownMenuItem
                onClick={onClickCancel}
                className="text-remark"
              >
                Cancel
              </DropdownMenuItem>
            )}
          </DropdownMenuGroup>
        </>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default QueueListActionMenu;
