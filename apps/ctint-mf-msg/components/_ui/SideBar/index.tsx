import { useCDSS } from '@cdss-modules/design-system/context/CDSSContext';
import { cn } from '@cdss-modules/design-system/lib/utils';
import { Table2 } from 'lucide-react';
import { Fragment, useMemo, useState } from 'react';
import CustomerBlock from '../CustomerBlock';
import QueuePanelBody from '../QueuePanelBody';
import QueueBlock from '../QueueBlock';

export type SideBarProps = {
  testId?: string;
  titleI18n: string;
  descI18n: string;
  btnLabelI18n: string;
  onClickButton: () => void;
};
const SideBar = () => {
  const [filter, setFilter] = useState<string>('active');
  const {
    sideBarExpanded,
    queuePanelExpanded,
    toggleSideBar,
    toggleQueuePanel,
    interactions,
    interactionHistory,
    queueData,
  } = useCDSS();

  const filteredData = useMemo(() => {
    // Filter out items that are not WAITING for status
    return queueData.map((item) => ({
      ...item,
      items: item?.items?.filter((i: any) => i.status === 'WAITING') || [],
    }));
  }, [queueData]);

  const filteredInteractions = useMemo(() => {
    if (!sideBarExpanded) return interactions;
    if (filter === 'active') return interactions;
    if (filter === 'closed') return interactionHistory;
    return [...interactions, ...interactionHistory];
  }, [filter, interactionHistory, interactions, sideBarExpanded]);
  const isExpanded = sideBarExpanded;
  return (
    <>
      <div
        className={cn(
          'flex-none relative h-full w-20 bg-common-white flex flex-col justify-between items-start border-t border-grey-200 shadow-contact-window transition-all z-30',
          isExpanded ? 'w-[400px]' : 'w-20 py-2'
        )}
      >
        <div className={cn('relative w-full flex flex-col')}>
          <div
            className={cn(
              'flex w-full justify-between items-center border-b border-grey-200',
              sideBarExpanded ? 'flex' : 'hidden'
            )}
          >
            <div className={cn('w-full flex gap-6 shrink-0 px-4')}>
              {[
                { value: 'active', label: 'Current' },
                { value: 'closed', label: 'History' },
                { value: 'all', label: 'All' },
              ].map((trigger) => {
                const isActive = filter === trigger.value;
                return (
                  <button
                    key={`trigger-${trigger.value}`}
                    onClick={() => setFilter(trigger.value)}
                    className={cn(
                      'p-2 text-remark font-bold w-1/3',
                      isActive
                        ? 'text-black shadow-b shadow-tab-selected'
                        : 'text-grey-500'
                    )}
                  >
                    {trigger.label}
                  </button>
                );
              })}
            </div>
          </div>
          <div
            className={cn(
              'relative w-full flex flex-col',
              !sideBarExpanded && 'gap-y-1'
            )}
          >
            {filteredInteractions.map((int, i) => (
              <Fragment key={`customer-block-demo-${i}`}>
                <CustomerBlock
                  data={int}
                  index={i + 1}
                />
              </Fragment>
            ))}
          </div>
          {!sideBarExpanded && (
            <>
              <div className="flex flex-col w-full h-px bg-gray-200 my-2" />
              <div
                className={cn(
                  'relative w-full flex flex-col',
                  !sideBarExpanded && 'gap-y-1'
                )}
              >
                {filteredData.map((queue, i) => (
                  <Fragment key={`customer-block-demo-${i}`}>
                    <QueueBlock
                      data={queue}
                      index={i + 1}
                    />
                  </Fragment>
                ))}
              </div>
            </>
          )}
        </div>
        <div
          className={cn(
            'flex flex-col items-center gap-3 w-full p-2',
            isExpanded && 'flex-row justify-end'
          )}
        >
          {/* <button
            onClick={() => toggleQueuePanel(!queuePanelExpanded)}
            className={cn(
              'relative inline-flex p-1 justify-center items-center group/sidebar-expand size-10 rounded-lg hover:bg-primary-200',
              queuePanelExpanded && 'bg-primary-200'
            )}
          >
            <SquareStack />
            <div className="absolute top-[2px] right-[2px] bg-primary-500 text-white text-[10px] rounded-full size-4 flex justify-center items-center">
              {filteredData.length}
            </div>
          </button> */}
          <button
            onClick={() => toggleSideBar(!sideBarExpanded)}
            className={cn(
              'inline-flex p-1 justify-center items-center group/sidebar-expand size-10 rounded-lg hover:bg-primary-200',
              sideBarExpanded && 'bg-primary-200'
            )}
          >
            <Table2 />
          </button>
        </div>
      </div>
      <div
        className={cn(
          'flex-none relative h-full flex flex-col bg-white z-20 overflow-hidden transition-all',
          queuePanelExpanded
            ? 'w-[400px] border-t border-grey-200 shadow-contact-window'
            : 'w-0'
        )}
      >
        <QueuePanelBody />
      </div>
    </>
  );
};

export default SideBar;
