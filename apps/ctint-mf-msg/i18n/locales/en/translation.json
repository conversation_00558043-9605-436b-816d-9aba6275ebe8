{"ctint-mf-cpp": {"filter": {"title": "Filter", "add": "Add Filter", "search": "Search", "clear": "Clear All", "available": "Available Filters", "save": "Save filters", "clearSave": "Clear saved filters", "result": "Result", "columns": "columns", "prepare": "Preparing", "download": "Download", "downloadList": "Download list", "selectedList": "Selected interactions", "reload": "Reload", "addColumns": "Add / Remove Columns", "saveColumns": "Save columns", "clearColumns": "Clear saved columns", "apply": "Apply", "view": "View", "recording": "Recording", "recordingError": "There is a error loading the audio file. ", "onlyId": "If checked, only search by Interaction ID.", "greaterOrLess": "Greater or Less than", "greater": "Greater than", "less": "Less than", "allMediaSource": "All Media Source", "mediaSource": "Media Source", "evaluation": "Evaluation", "all": "All", "filter": "filter", "filters": "filters", "applied": " applied", "noResult": "No result(s)."}, "audio": {"jumpToTime": "Jump to time", "go": "Go", "speed": "Speed", "invalidTime": "Invalid time"}, "transcript": {"generate": "Generate Transcript", "processing": "Transcript is processing..."}, "columns": {"id": "Interaction ID", "conversationId": "Conversation ID", "startTime": "Start Time", "endTime": "End Time", "duration": "Duration", "direction": "Initiator", "users": "Users Info", "mediaType": "Media Type", "username": "Users", "dialedNumber": "DNIS", "mediaUri": "Media URI", "callerNumber": "ANI", "mediaSource": "Media Source", "inbound": "Inbound", "outbound": "Outbound", "evaluation": "QM"}}, "ctint-mf-msg": {"test": "test:en", "msgHome": {"title": "CDSS 3.0 Microfrontend Template", "desc": "This is a msg project for microfrontend development in CDSS 3.0. Click below button to go to example detail page.", "btnLabel": "Go to Detail"}, "msgDetail": {"title": "CDSS 3.0 Microfrontend Template - Detail Page", "desc": "This is the detail page of msg project for microfrontend development in CDSS 3.0.", "btnLabel": "Go back to Main Page"}, "langDemo": {"languageDemo": "Language Demo", "currentLanguage": "Current Language", "changeLanguage": "Change Language", "changeTo": "Change to"}}}