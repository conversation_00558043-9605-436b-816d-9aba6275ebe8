import Button from '@cdss-modules/design-system/components/_ui/Button';
import SearchInput from '@cdss-modules/design-system/components/_ui/SearchInput';
import { useTranslation } from 'react-i18next';
import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';
import { Table as TableType } from '@tanstack/react-table';
import { useEffect, useState } from 'react';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import PopoverMenu from '@cdss-modules/design-system/components/_ui/PopoverMenu';
import FilterComponent, {
  Condition,
} from '@cdss-modules/design-system/components/_ui/FilterComponent';
import {
  SortingButton,
  toast,
  Toaster,
  useRole,
  useRouteHandler,
} from '@cdss-modules/design-system';
import { ICriteria } from '@cdss-modules/design-system/@types/config';
import { downloadRecord, searchRecords } from '../../../lib/api';
import Pagination from '@cdss-modules/design-system/components/_ui/Pagination';
import { Download } from 'lucide-react';
const GenerateRecordPanelBody = () => {
  const { t, i18n } = useTranslation();
  const { globalConfig } = useRole();
  const { basePath } = useRouteHandler();
  // const [table, setTable] = useState<TableType<{ [key: string]: string }>>();

  const [criteria, setCriteria] = useState<ICriteria[]>([]);
  const [defaultColumns, setDefaultColumns] = useState<ICriteria[]>([]);
  const [filterValues, setFilterValues] = useState<Record<string, any>>({});
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [orderBy, setOrderBy] = useState('createTime');
  const [order, setOrder] = useState('desc');
  const [sorting, setSorting] = useState<{ [key: string]: string }>({});

  const [rowSelection, setRowSelection] = useState({});
  const [records, setRecords] = useState<Record<string, any>[]>([]);

  // 默认条件
  const defaultSearchCondition = {
    page: currentPage,
    pageSize: perPage,
    orderBy: orderBy,
    order: order,
  };

  const [searchCondition, setSearchCondition] = useState<Record<string, any>>(
    defaultSearchCondition
  );
  const [autoSearchCondition, setAutoSearchCondition] = useState<
    Record<string, any>
  >(defaultSearchCondition);

  const handleNext = () => {
    let tar = currentPage + 1;
    if (tar > totalPages) tar = totalPages;
    setCurrentPage(tar);
  };

  const handlePrevious = () => {
    let tar = currentPage - 1;
    if (tar < 1) tar = 1;
    setCurrentPage(tar);
  };

  useEffect(() => {
    const criteria: ICriteria[] =
      globalConfig?.microfrontends['ctint-mf-report']?.['record-filter'] || [];
    // 设置标准config
    setCriteria(criteria);
  }, [globalConfig]);

  useEffect(() => {
    const activeCriteria = criteria.filter((item) => item.active);
    // 如果 criteria 有值，則初始化 defaultColumns
    if (activeCriteria.length > 0) {
      setDefaultColumns(activeCriteria);
    }
    // 如果 defaultColumns 有值，則初始化 filterValues
    if (activeCriteria.length > 0) {
      const initialFilterValues = activeCriteria.reduce(
        (acc, filter) => {
          acc[filter.value] = {
            ...filter,
            labelEn: filter.labelEn ?? '',
            labelCh: filter.labelCh ?? '',
            data: undefined,
            checked: false,
          }; // Initialize all filter values to empty
          return acc;
        },
        {} as Record<string, Condition>
      );
      setFilterValues(initialFilterValues);
    }
  }, [criteria]);

  useEffect(() => {
    const totalPages = Math.ceil(totalCount / perPage);
    setTotalPages(totalPages);
  }, [totalCount, perPage]);

  useEffect(() => {
    console.log('GenerateRecordPanel ==> Sorting: ', sorting);
    setOrderBy(Object.keys(sorting)[0]);
    setOrder(sorting[Object.keys(sorting)[0]]);
  }, [sorting]);

  useEffect(() => {
    console.log('GenerateRecordPanel ==> Filter Values: ', filterValues);
    // 组装查询条件的标签
    const selectedTags = Object.values(filterValues)
      .filter((item) => item.checked && item.data !== undefined)
      .map(
        (item) =>
          `${i18n.language === 'en' ? item.labelEn : item.labelCh} : ${item.data}`
      );
    setSelectedTags(selectedTags);
    // 找到 filterValues 中 checked 為 true 且 data不为空的 filter, 返回只需要 value 和 data, 最终得到一个对象
    const activeFilterValues = Object.values(filterValues).filter((item) => {
      if (item.checked && item.data) {
        return {
          value: item.value,
          data: item.data,
        };
      }
    });
    // 将 activeFilterValues 中的每个对象的 value 和 data 转换为 key 和 value，最后得到一个object
    const activeFilterValuesObject = activeFilterValues.reduce((acc, item) => {
      acc[item.value] = item.data;
      return acc;
    }, {});
    // order by 条件
    const orderByCondition = {
      orderBy: orderBy,
      order: order,
    };
    // page 条件
    const pageCondition = {
      page: currentPage,
      pageSize: perPage,
    };
    // 组装查询条件
    const searchCondition = {
      ...orderByCondition,
      ...pageCondition,
      ...activeFilterValuesObject,
    };
    setSearchCondition(searchCondition);
    console.log('GenerateRecordPanel ==> Search Condition: ', searchCondition);
  }, [filterValues, i18n.language]);

  useEffect(() => {
    const orderByCondition = {
      orderBy: orderBy,
      order: order,
    };
    // page 条件
    const pageCondition = {
      page: currentPage,
      pageSize: perPage,
    };
    const activeFilterValues = Object.values(filterValues).filter((item) => {
      if (item.checked && item.data) {
        return {
          value: item.value,
          data: item.data,
        };
      }
    });
    // 将 activeFilterValues 中的每个对象的 value 和 data 转换为 key 和 value，最后得到一个object
    const activeFilterValuesObject = activeFilterValues.reduce((acc, item) => {
      acc[item.value] = item.data;
      return acc;
    }, {});
    // 组装查询条件
    const autoSearchCondition = {
      ...orderByCondition,
      ...pageCondition,
      ...activeFilterValuesObject,
    };
    setAutoSearchCondition(autoSearchCondition);
  }, [orderBy, order, currentPage, perPage]);

  useEffect(() => {
    console.log(
      'GenerateRecordPanel ==> Auto Search Condition: ',
      autoSearchCondition
    );
    searchRecords(basePath, autoSearchCondition).then((res) => {
      console.log('GenerateRecordPanel ==> Search Records: ', res.data.data);
      setRecords(res.data.data.reportRecordList ?? []);
      setTotalCount(res.data.data.totalCount ?? 0);
    });
  }, [autoSearchCondition, basePath]);

  return (
    <section className={`w-full p-2 rounded-md `}>
      <div className="flex">
        <div className="flex-1 flex flex-row overflow-x-auto py-2">
          <SearchInput tags={selectedTags}>
            <section className="max-h-[409px] w-[900px] overflow-y-auto">
              <section className="p-4">
                {/* 显示选中的选项 */}
                <div className="flex flex-wrap flex-row">
                  {selectedTags.map((tag, i) => (
                    <div
                      key={i}
                      className="p-1 mr-1 mt-1 flex flex-row items-center border border-black rounded-[4px] text-[12px] text-black"
                    >
                      <span className="truncate">{tag}</span>
                      <span
                        className="ml-1 cursor-pointer"
                        onClick={() => {
                          setFilterValues((prev) => {
                            const newFilterValues = { ...prev };
                            // 找到tag对应的key
                            const targetKey = Object.keys(newFilterValues).find(
                              (key) => {
                                const item = newFilterValues[key];
                                const itemLabel =
                                  i18n.language === 'en'
                                    ? item.labelEn
                                    : item.labelCh;
                                return tag.startsWith(`${itemLabel} :`);
                              }
                            );

                            if (targetKey) {
                              // 重置对应项的data和checked状态
                              newFilterValues[targetKey].data = undefined;
                              newFilterValues[targetKey].checked = false;
                            }

                            return newFilterValues;
                          });
                        }}
                      >
                        <Icon name="cross" />
                      </span>
                    </div>
                  ))}
                </div>
                {/* 显示可选择的选项 */}
                <section className={`${selectedTags.length > 0 ? 'mt-4' : ''}`}>
                  <h2 className="text-[14px] mb-2">
                    {t('ctint-mf-report.main.record.filter.available')}:
                  </h2>
                  <div className="flex flex-col gap-y-2">
                    <FilterComponent
                      filterValues={filterValues}
                      setFilterValues={setFilterValues}
                    />
                  </div>
                </section>
              </section>
            </section>
          </SearchInput>
          <Button
            className="self-center ml-2"
            bodyClassName="border border-black py-[0.375rem]"
            onClick={() => {
              // call api
              searchRecords(basePath, searchCondition).then((res) => {
                console.log(
                  'GenerateRecordPanel ==> Search Records: ',
                  res.data.data
                );
                setRecords(res.data.data.reportRecordList ?? []);
                setCurrentPage(1);
                setTotalCount(res.data.data.totalCount ?? 0);
              });
            }}
            size="s"
          >
            {t('ctint-mf-report.main.record.filter.search')}
          </Button>
          {/* Clear Tags Button */}
          <Button
            className="self-center ml-2"
            bodyClassName="border border-black py-[0.375rem]"
            onClick={() => {
              setFilterValues(
                Object.keys(filterValues).reduce(
                  (acc: Record<string, Condition>, key: string) => {
                    acc[key] = {
                      ...filterValues[key],
                      data: undefined,
                      checked: false,
                    };
                    return acc;
                  },
                  {}
                )
              );
              setSorting({});
            }}
            variant="blank"
            size="s"
          >
            {t('ctint-mf-report.main.record.filter.clear')}
          </Button>
        </div>
        {/* <PopoverMenu
          icon={
            <Icon
              name="verticalDots"
              className="self-center justify-end cursor-pointer mx-1 flex-shrink-0"
              size={23}
            />
          }
        >
          <div className="flex flex-col bg-white shadow-[0px_2px_10px_0px_rgba(0,0,0,0.18)]">
            <button
              className="m-2 flex gap-2 items-center w-full"
              onClick={() => null}
            >
              <Icon name="eye" />
              <span>{t('ctint-mf-report.main.record.filter.addColumns')}</span>
            </button>
            <div className="w-full h-[1px] bg-black"></div>
            <button
              onClick={() => null}
              className="mx-2 mt-2 flex gap-2 items-center w-full"
            >
              <Icon name="save" />
              <span>{t('ctint-mf-report.main.record.filter.saveColumns')}</span>
            </button>
            <button
              onClick={() => null}
              className="m-2 flex gap-2 items-center w-full"
            >
              <Icon name="cross" />
              <span>
                {t('ctint-mf-report.main.record.filter.clearColumns')}
              </span>
            </button>
          </div>
        </PopoverMenu> */}
      </div>
      <div className="">
        <DataTable<{ [key: string]: string }>
          data={records}
          columns={criteria.map((item) => ({
            id: item.value,
            accessorKey: item.value,
            header: () => {
              return (
                <SortingButton
                  sorting={
                    sorting[item.value]
                      ? sorting[item.value] === 'asc'
                        ? 'asc'
                        : 'desc'
                      : false
                  }
                  onClick={() => {
                    setSorting({
                      [item.value]:
                        sorting[item.value] === 'asc' ? 'desc' : 'asc',
                    });
                  }}
                >
                  <div>
                    {i18n.language === 'en' ? item.labelEn : item.labelCh}
                  </div>
                </SortingButton>
              );
            },
            cell: ({ row }) => {
              if (
                row.getValue(item.value) === '' ||
                row.getValue(item.value) === undefined
              ) {
                return <div>--</div>;
              }

              if (item.value === 'state') {
                if (row.getValue(item.value) === 'completed') {
                  return (
                    <Download
                      className="hover:text-primary-700 text-primary-500"
                      onClick={(e) => {
                        e.stopPropagation(); // 阻止事件冒泡
                        console.log(
                          'GenerateRecordPanel ==> Cell Clicked: ',
                          row.getValue(item.value)
                        );
                        // 下载文件
                        const fileUrl = row.original.fileUrl;
                        const reportRecordId = row.original.reportRecordId;
                        console.log(
                          'GenerateRecordPanel ==> Download Record Request Body: ',
                          {
                            fileUrl,
                            reportRecordId,
                          }
                        );
                        downloadRecord(basePath, [
                          {
                            fileUrl,
                            reportRecordId,
                          },
                        ])
                          .then((res) => {
                            toast({
                              title: 'Success',
                              description: 'Report downloaded successfully',
                            });
                          })
                          .catch((err) => {
                            console.log(
                              'GenerateRecordPanel ==> Download Record Error: ',
                              err
                            );
                          })
                          .finally(() => {
                            console.log(
                              'GenerateRecordPanel ==> Download Record Finally: '
                            );
                          });
                      }}
                    />
                  );
                } else {
                  return (
                    <div className="text-primary-500">
                      {row.getValue(item.value)}
                    </div>
                  );
                }
              }

              return <div>{row.getValue(item.value)}</div>;
            },
          }))}
          loading={false}
          emptyMessage="No data found"
          rowSelection={rowSelection}
          setRowSelection={setRowSelection}
          onClickRow={(row) => {
            console.log('GenerateRecordPanel ==> Row Clicked: ', row);
          }}
          onTableSetUp={(table) => {
            // setTable(table);
          }}
          resize={true}
        />
        {totalPages > 0 && (
          <section className="flex-1 flex-row">
            <div>
              <Pagination
                current={currentPage}
                perPage={perPage}
                total={totalPages}
                totalCount={totalCount}
                onChange={(v) => setCurrentPage(v)}
                handleOnPrevious={() => handlePrevious()}
                handleOnNext={() => handleNext()}
                handlePerPageSetter={(p: number) => {
                  setPerPage(p);
                  setCurrentPage(1);
                }}
              />
            </div>
          </section>
        )}
      </div>
    </section>
  );
};

// const queryClient = new QueryClient();

const GenerateReportPanel = () => {
  return (
    <div>
      <GenerateRecordPanelBody />
      <Toaster />
    </div>
  );
};

export default GenerateReportPanel;
