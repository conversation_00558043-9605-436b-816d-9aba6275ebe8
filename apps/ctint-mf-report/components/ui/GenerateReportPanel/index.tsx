import { Button, Toaster, use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@cdss-modules/design-system';
import DynamicInputComponent from '@cdss-modules/design-system/components/_ui/ReportInputField';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import { toast } from '@cdss-modules/design-system/components/_ui/Toast/use-toast';
import { useEffect, useState } from 'react';
import {
  generateReport,
  getReportTemplateFields,
  getReportTemplateList,
} from '../../../lib/api';
import {
  QueryClient,
  QueryClientProvider,
  useQuery,
} from '@tanstack/react-query';
import { IReportTemplate, ReportDetailElement } from '../../../types/report';
import { InputType } from '@cdss-modules/design-system/components/_ui/ReportInputField/types';

const GenerateReportPanelBody = () => {
  const [showFields, setShowFields] = useState(true);
  const { i18n } = useTranslation();

  const [selectedReportType, setSelectedReportType] = useState('0');
  const [selectedReportMasterId, setSelectedReportMasterId] = useState('0');
  const [implementUrl, setImplementUrl] = useState('');
  const [reportTypeList, setReportTypeList] = useState<
    Array<{ value: string; label: string }>
  >([]);
  const [reportNameList, setReportNameList] = useState<
    Array<{ value: string; label: string }>
  >([]);
  const [reportFields, setReportFields] = useState<ReportDetailElement[]>([]);
  const [reportDetails, setReportDetails] = useState<ReportDetailElement[]>([]);
  const [fieldErrors, setFieldErrors] = useState<Record<string, boolean>>({});

  const { t } = useTranslation();
  const { basePath } = useRouteHandler();

  // get report template list
  const { data: reportTemplateList } = useQuery({
    queryKey: ['reportTemplateList'],
    queryFn: async () => {
      const res = await getReportTemplateList(basePath, '');
      return res?.data?.data || [];
    },
    enabled: !!basePath,
    refetchOnWindowFocus: false,
  });

  // init report type list
  useEffect(() => {
    const reportTypeList =
      reportTemplateList?.reduce(
        (
          acc: Array<{ value: string; label: string }>,
          item: IReportTemplate
        ) => {
          if (!acc.some((type) => type.value === item.reportType)) {
            acc.push({
              value: item.reportType || '',
              label: item.reportType || '',
            });
          }
          return acc;
        },
        [] as Array<{ value: string; label: string }>
      ) || [];
    setReportTypeList(reportTypeList);
  }, [reportTemplateList]);

  // init report name list
  useEffect(() => {
    const reportNameList =
      reportTemplateList
        ?.filter(
          (item: IReportTemplate) =>
            selectedReportType === '0' || item.reportType === selectedReportType
        )
        .map((item: IReportTemplate) => ({
          value: item.reportMasterId || '',
          label: item.reportName || '',
        })) || [];
    setReportNameList(reportNameList || []);
    setSelectedReportMasterId('0');
  }, [selectedReportType]);

  // get report name list
  useEffect(() => {
    console.log('selectedReportMasterId: ', selectedReportMasterId);
    const fetchFields = async () => {
      if (selectedReportMasterId && selectedReportMasterId != '0') {
        const reportTemplateFields = await getReportTemplateFields(
          basePath,
          selectedReportMasterId
        );
        const filteredDetails =
          reportTemplateFields?.data?.data?.[0]?.reportDetail.filter(
            (detail: ReportDetailElement) =>
              detail.enable && detail.detailType === 'parameter'
          ) || [];
        setImplementUrl(reportTemplateFields?.data?.data?.[0]?.implementUrl);
        setReportDetails(filteredDetails as ReportDetailElement[]);
        setShowFields(true);
      } else {
        setShowFields(false);
        setReportDetails([]);
        setReportFields([]);
      }
    };

    fetchFields();
  }, [selectedReportMasterId]);

  // monitor report fields change
  useEffect(() => {
    console.log('reportFields: ', reportFields);
  }, [reportFields]);

  // monitor report details
  useEffect(() => {
    console.log('reportDetails: ', reportDetails);
  }, [reportDetails]);

  const validateFields = () => {
    const newErrors: Record<string, boolean> = {};
    reportDetails.forEach((detail) => {
      if (detail.isRequired) {
        const field = reportFields.find(
          (f) => f.reportDetailId === detail.reportDetailId
        );

        // 检查必填字段是否有值
        let hasError = !field?.detailValue;

        // 对datetime类型进行格式验证
        if (
          !hasError &&
          detail.detailSubType === 'datetime' &&
          field?.detailValue
        ) {
          const dateTimeRegex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/;
          hasError = !dateTimeRegex.test(field.detailValue);
        }

        newErrors[detail.reportDetailId || ''] = hasError;
      }
    });
    setFieldErrors(newErrors);
    return Object.values(newErrors).every((error) => !error);
  };

  /**
   * Get report detail label
   * @param detail - report detail
   * @returns report detail label
   */
  const getReportDetailLabel = (detail: ReportDetailElement) => {
    const label = JSON.parse(detail.detailRemark || '{}') || '';
    return label[`label_${i18n.language}`] || '';
  };

  return (
    <section className={`w-full p-2 rounded-md`}>
      <div className="grid grid-cols-2 gap-6">
        <div className="max-w-[600px]">
          <DynamicInputComponent
            label={t('ctint-mf-report.main.report.fields.reportType')}
            type="select"
            value={selectedReportType}
            options={reportTypeList}
            onChange={(value) => {
              console.log('type input onChange: ', value);
              setSelectedReportType(value);
            }}
          />
        </div>
        <div className="max-w-[600px]">
          <DynamicInputComponent
            label={t('ctint-mf-report.main.report.fields.reportName')}
            type="select"
            value={selectedReportMasterId}
            options={reportNameList}
            onChange={(value) => {
              console.log('name input onChange: ', value);
              setSelectedReportMasterId(value);
            }}
          />
        </div>
      </div>
      {showFields && (
        <div>
          <div className="grid grid-cols-2 gap-x-6 gap-y-2 bg-gray-200 p-2 my-6">
            {reportDetails.map((detail: ReportDetailElement, index: number) => {
              return (
                <div
                  key={detail.reportDetailId || index}
                  className="max-w-[600px]"
                >
                  <DynamicInputComponent
                    label={getReportDetailLabel(detail)}
                    type={detail.detailSubType as InputType}
                    isRequired={detail.isRequired}
                    error={
                      fieldErrors[detail.reportDetailId || '']
                        ? t('ctint-mf-report.main.report.fields.required')
                        : ''
                    }
                    onChange={(value) => {
                      // 清除当前字段的错误
                      if (value) {
                        setFieldErrors((prev) => ({
                          ...prev,
                          [detail.reportDetailId || '']: false,
                        }));
                      }

                      setReportFields((pre: ReportDetailElement[]) => {
                        const existingIndex = pre.findIndex(
                          (field: ReportDetailElement) =>
                            field.reportDetailId === detail.reportDetailId
                        );

                        // 如果 value 为空，且找到了对应字段，则删除该字段
                        if (!value && existingIndex !== -1) {
                          const newFields = [...pre];
                          newFields.splice(existingIndex, 1);
                          return newFields;
                        }

                        const newField = {
                          reportDetailId: detail.reportDetailId,
                          reportMasterId: detail.reportMasterId,
                          detailType: detail.detailType,
                          detailSubType: detail.detailSubType,
                          detailKey: detail.detailKey,
                          detailValue: value,
                          detailRemark: detail.detailRemark,
                          enable: detail.enable,
                          isRequired: detail.isRequired,
                          tenant: detail.tenant,
                          platform: detail.platform,
                        };

                        if (existingIndex !== -1) {
                          // 如果找到相同id的字段，则更新它
                          const newFields = [...pre];
                          newFields[existingIndex] = newField;
                          return newFields;
                        } else {
                          // 如果没有找到相同id的字段，则添加新字段
                          return [...pre, newField];
                        }
                      });
                    }}
                  />
                </div>
              );
            })}
          </div>
          <div className="flex justify-end">
            <Button
              className="self-center ml-2"
              bodyClassName="border border-black py-[0.375rem]"
              onClick={() => {
                if (validateFields()) {
                  console.log('generate report');
                  generateReport(
                    basePath,
                    reportFields,
                    selectedReportMasterId,
                    implementUrl
                  )
                    .then((res) => {
                      console.log('generate report res: ', res);
                      toast({
                        title: 'Success',
                        description: 'Report generated successfully',
                      });
                    })
                    .catch((err) => {
                      console.log('generate report err: ', err);
                      toast({
                        variant: 'error',
                        title: 'Error',
                        description: err.response.data.error,
                      });
                    })
                    .finally(() => {
                      console.log('generate report finally');
                    });
                } else {
                  toast({
                    variant: 'error',
                    title: 'Error',
                    description:
                      'Please fill in all required fields in the report',
                  });
                }
              }}
              size="s"
            >
              {t('ctint-mf-report.main.report.search')}
            </Button>
          </div>
        </div>
      )}
    </section>
  );
};

const queryClient = new QueryClient();

const GenerateReportPanel = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <GenerateReportPanelBody />
      <Toaster />
    </QueryClientProvider>
  );
};

export default GenerateReportPanel;
