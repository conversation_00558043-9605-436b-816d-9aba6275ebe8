import { addLoggingToAxios } from '@cdss-modules/design-system/lib/logging';
import { apiConfig } from './config';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { ReportDetailElement } from '../../types/report';

type UserConfigItems = {
  name: string;
  value: string;
};

type UserConfigProps = {
  filters?: UserConfigItems[];
  columns?: UserConfigItems[];
};

enum FilterCondition {
  mediaType = 'mediaType',
  users = 'users',
  conversationId = 'conversationId',
  ani = 'ani',
  dnis = 'dnis',
  queues = 'queues',
  conversationDuration = 'conversationDuration',
  recordingMediaSource = 'recordingMediaSource',
  customerRemote = 'customerRemote',
  wrapups = 'wrapups',
  provider = 'provider',
  recording = 'recording',
  direction = 'direction',
}

export const axiosInstance = axios.create({
  timeout: 20000,
  headers: {
    'Content-Type': 'application/json',
    traceId: uuidv4(),
    tenant: 'ctint',
    sourceId: 'ctint-mf-report',
    previousId: 'ctint-bff-cdss',
  },
});

export const axiosDownloadInstance = axios.create({
  timeout: 20000,
  headers: {
    traceId: uuidv4(),
    tenant: 'ctint',
    sourceId: 'ctint-mf-report',
    previousId: 'ctint-bff-cdss',
  },
});

axiosDownloadInstance.interceptors.request.use(
  (config) => {
    // TODO: should call the auth api and set the token when program initialize
    if (
      process.env.NODE_ENV === 'development' &&
      localStorage.getItem('cdss-auth-token') === null
    ) {
      localStorage.setItem('deviceId', uuidv4());
      localStorage.setItem(
        'gc-access-token',
        '4ne850UXVwj7J1oqS0yP05IqNLxnE_i81-js3tXdf5uCkuOQHE737vRCO8wHvXLC-VNZKOUnUctkT7k8RyvYbg'
      );
      localStorage.setItem(
        'cdss-auth-token',
        'EdOImMlG47o8McBVtqY2QKHtIoT7OPznIInDv7cOXG+0DnbPZAcG6Ra+604VzhdT+fX3I2pbhPKWfQSrGpd+hZ5OuZOcptbvJ01GpJkpzCbiiT6TpuU='
      );
    }
    const cdssAuthToken = localStorage.getItem('cdss-auth-token') || '';
    const gcAccessToken = localStorage.getItem('gc-access-token') || '';
    const deviceId = localStorage.getItem('deviceId') || '';
    config.headers['tenant'] = localStorage.getItem('tenant') || 'ctint';
    if (cdssAuthToken) {
      config.headers['cdss-authorization'] = 'Bearer ' + cdssAuthToken;
    }
    if (deviceId) {
      config.headers['deviceId'] = deviceId;
    }
    return config;
  },
  (error) => {
    Promise.reject(error);
  }
);

axiosInstance.interceptors.request.use(
  (config) => {
    // TODO: should call the auth api and set the token when program initialize
    if (
      process.env.NODE_ENV === 'development' &&
      localStorage.getItem('cdss-auth-token') === null
    ) {
      localStorage.setItem('deviceId', uuidv4());
      localStorage.setItem(
        'gc-access-token',
        'ofROc0Va1i74x3CKxPJ-et8g9jwo08wFkRgCzCcIYDwdAEmJMCIh-HDn9Sgw_U17WxTvtwnngeOksDWN8Sbatw'
      );
      localStorage.setItem(
        'cdss-auth-token',
        'EdOImMlG47o8McBVtqY2QKHtIoT7OPznIInDv7cOXG+0DnbPZAcG6Ra+604VzhdT+fX3I2pbhPKWfQSrGpd+hZ5OuZOcptbvJ01GpJkpzCbiiT6TpuU='
      );
    }
    const cdssAuthToken = localStorage.getItem('cdss-auth-token') || '';
    const gcAccessToken = localStorage.getItem('gc-access-token') || '';
    const deviceId = localStorage.getItem('deviceId') || '';
    config.headers['tenant'] = localStorage.getItem('tenant') || 'ctint';
    if (cdssAuthToken) {
      config.headers['cdss-authorization'] = 'Bearer ' + cdssAuthToken;
    }
    if (deviceId) {
      config.headers['deviceId'] = deviceId;
    }

    if (config.url?.includes('/interaction/recordings/transcript/')) {
      config.headers['requester'] = 'agent1';
    }

    console.log('axiosInstance.interceptors.request', config);
    return config;
  },
  (error) => {
    Promise.reject(error);
  }
);

addLoggingToAxios(axiosInstance);

export const getReportTemplateList = (
  basePath: string | undefined,
  controlType?: string
) => {
  return axiosInstance.get(`${basePath}${apiConfig.paths.report.template}`);
  // return {
  //   data: {
  //     data: [
  //       {
  //         reportMasterId: '01a241c2-3f88-460a-8035-b12dea95dca8',
  //         reportType: 'monthly',
  //         reportName: 'Monthly Test Report 2',
  //         description: '',
  //         tenant: 'ctint',
  //         platform: '',
  //         createTime: '2025-01-23T03:57:01.343Z',
  //         updateTime: '2025-01-23T03:57:01.343Z',
  //         createBy: '',
  //         updateBy: '',
  //       },
  //       {
  //         reportMasterId: '14d14532-26d3-4696-b6e3-0395041b8dd5',
  //         reportType: 'monthly',
  //         reportName: 'Monthly Test Report 1',
  //         description: '',
  //         tenant: 'ctint',
  //         platform: '',
  //         createTime: '2025-01-23T03:56:20.539Z',
  //         updateTime: '2025-01-23T03:56:20.539Z',
  //         createBy: '',
  //         updateBy: '',
  //       },
  //       {
  //         reportMasterId: '244998c7-e690-4ea8-9a8b-4d55e13b950c',
  //         reportType: 'daily',
  //         reportName: 'Daily Test Report 1',
  //         description: '',
  //         tenant: 'ctint',
  //         platform: '',
  //         createTime: '2025-01-23T03:56:03.752Z',
  //         updateTime: '2025-01-23T03:56:03.752Z',
  //         createBy: '',
  //         updateBy: '',
  //       },
  //     ],
  //     error: '',
  //     isSuccess: true,
  //   },
  // };
};

export const getReportTemplateFields = (
  basePath: string | undefined,
  reportMasterId: string
) => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.report.form}?reportMasterId=${reportMasterId}`
  );
  // return {
  //   data: {
  //     data: [
  //       {
  //         reportMasterId: 'c8f53a30-4dad-48c8-8109-7dca162a6017',
  //         reportType: 'AutoQM',
  //         reportName: 'Analysis Report - SOP Level',
  //         description: '',
  //         tenant: 'ctint',
  //         platform: '',
  //         createTime: '2025-01-22T03:50:22.661Z',
  //         updateTime: '2025-01-22T03:50:22.661Z',
  //         createBy: '',
  //         updateBy: '',
  //         reportDetail: [
  //           {
  //             reportDetailId: '0ff2b2a2-7246-448f-8319-74fbd3f0e415',
  //             reportMasterId: 'c8f53a30-4dad-48c8-8109-7dca162a6017',
  //             detailType: 'parameter',
  //             detailSubType: 'datetime',
  //             detailKey: 'startDate',
  //             detailValue: '',
  //             detailRemark: 'Execution Date Range',
  //             enable: true,
  //             isRequired: true,
  //             tenant: 'ctint',
  //             platform: '',
  //             createTime: '2025-01-22T03:52:30.578Z',
  //             updateTime: '2025-01-22T03:52:30.578Z',
  //             createBy: '',
  //             updateBy: '',
  //           },
  //           {
  //             reportDetailId: 'c917e8f4-247f-42a4-b093-d70c59db4a4d',
  //             reportMasterId: 'c8f53a30-4dad-48c8-8109-7dca162a6017',
  //             detailType: 'parameter',
  //             detailSubType: 'text',
  //             detailKey: 'formVersion',
  //             detailValue: '',
  //             detailRemark: 'SOP Version',
  //             enable: true,
  //             isRequired: true,
  //             tenant: 'ctint',
  //             platform: '',
  //             createTime: '2025-01-22T03:53:38.078Z',
  //             updateTime: '2025-01-22T03:53:38.078Z',
  //             createBy: '',
  //             updateBy: '',
  //           },
  //           {
  //             reportDetailId: 'de02f834-0b13-4083-ba24-65efb721f812',
  //             reportMasterId: 'c8f53a30-4dad-48c8-8109-7dca162a6017',
  //             detailType: 'parameter',
  //             detailSubType: 'text',
  //             detailKey: 'formName',
  //             detailValue: '',
  //             detailRemark: 'SOP',
  //             enable: true,
  //             isRequired: true,
  //             tenant: 'ctint',
  //             platform: '',
  //             createTime: '2025-01-22T03:53:16.241Z',
  //             updateTime: '2025-01-22T03:53:16.241Z',
  //             createBy: '',
  //             updateBy: '',
  //           },
  //           {
  //             reportDetailId: 'f1c3778e-b85d-46bd-bc2a-ca31caf78776',
  //             reportMasterId: 'c8f53a30-4dad-48c8-8109-7dca162a6017',
  //             detailType: 'parameter',
  //             detailSubType: 'datetime',
  //             detailKey: 'endDate',
  //             detailValue: '',
  //             detailRemark: 'Execution Date Range',
  //             enable: true,
  //             isRequired: true,
  //             tenant: 'ctint',
  //             platform: '',
  //             createTime: '2025-01-22T03:52:55.600Z',
  //             updateTime: '2025-01-22T03:52:55.600Z',
  //             createBy: '',
  //             updateBy: '',
  //           },
  //         ],
  //         reportPermission: [
  //           {
  //             reportPermissionId: 'f11dbe97-3fc1-4c9d-9c9d-19084cda035d',
  //             reportMasterId: 'c8f53a30-4dad-48c8-8109-7dca162a6017',
  //             subjectType: 'role',
  //             subjectValue: 'CDSS_SUPERVISOR',
  //             permissionLevel: 'admin',
  //             enable: true,
  //             tenant: 'ctint',
  //             platform: '',
  //             createTime: '2025-01-22T08:50:33.557Z',
  //             updateTime: '2025-01-22T08:50:33.557Z',
  //             createBy: '',
  //             updateBy: '',
  //           },
  //         ],
  //       },
  //     ],
  //     error: '',
  //     isSuccess: true,
  //   },
  // };
};

export const generateReport = (
  basePath: string | undefined,
  reportFields: ReportDetailElement[],
  selectedReportMasterId: string,
  implementUrl: string
) => {
  return axiosInstance.post(`${basePath}${apiConfig.paths.report.generate}`, {
    reportDetail: reportFields,
    reportMasterId: selectedReportMasterId,
    implementUrl: implementUrl,
  });
};

export const searchRecords = (
  basePath: string | undefined,
  searchCondition: Record<string, any>
) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.record.list}`,
    searchCondition
  );
};

export const downloadRecord = (
  basePath: string | undefined,
  data: Record<string, any>[]
) => {
  return axiosInstance({
    method: 'POST',
    url: basePath + apiConfig.paths.record.download,
    responseType: 'blob',
    data: data,
  })
    .then((response) => {
      // 从响应头中提取 Content-Disposition
      const contentDisposition = response.headers['content-disposition'];
      let fileName = 'default-filename.zip'; // 默认文件名

      // 使用正则表达式提取文件名
      if (contentDisposition) {
        const matches = contentDisposition.match(/filename=([^;]+)/);
        if (matches && matches[1]) {
          fileName = matches[1].replace(/['"]/g, '').trim(); // 确保去除多余的空格
        }
      }
      // 创建一个 Blob URL 并触发下载
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName); // 使用从响应头中提取的文件名
      document.body.appendChild(link);
      link.click();
      link.remove(); // 下载完成后移除 link 元素
      console.info('文件下载成功！文件名为：', fileName);
    })
    .catch((error) => {
      console.error('文件下载失败：', error);
    })
    .finally(() => {
      // callback();
    });
};

export default axiosInstance;
