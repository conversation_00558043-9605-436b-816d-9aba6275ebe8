auth:
  headers:
  - name: traceId
  - name: tenant
  - name: sourceId
  - name: previousId
  - name: cdss_authorization
logger:
  headers:
  - name: traceId
  - name: tenant
  - name: sourceId
  - name: previousId
pureengage:
  host: http://*************:8090
  recordsLimit: 200
genesys-cloud:
  environment: mypurecloud.com.au
  apiHost: https://api.mypurecloud.com.au
  authHost: https://login.mypurecloud.com.au
  clientId: ${genesysCloudClientId}
  clientSecret: ${genesysCloudClientSecret}
ctint-dab:
  auth:
    host: http://ctint-dab-auth-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  config:
    host: http://ctint-dab-config-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  conv:
    host: http://ctint-dab-conv-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
ctint-state:
  auth:
    host: http://ctint-state-auth-service.cdss-data-ctint.svc.cluster.local:35000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /v1.0/state/ctint-state-auth
    active: true
  cdssmf:
    host: http://ctint-state-cdssmf-service.cdss-data-ctint.svc.cluster.local:35010
    basepath: /v1.0/state/ctint-state-cdssmf
    active: true
portals:
- ctint-mf-interaction
- ctint-mf-cpp
- ctint-mf-tts
- ctint-mf-wap
- ctint-mf-template
microfrontends:
  ctint-mf-cdss:
    host: http://localhost:4400
    basepath: /__CDSS_BASE_PATH_TBM__
  ctint-mf-cpp:
    host: http://localhost:4500
    basepath: /__CPP_BASE_PATH_TBM__
  ctint-mf-template:
    host: http://localhost:4000
    basepath: /ctint/mf-template
  ctint-mf-tts:
    host: http://localhost:4600
    basepath: 
  ctint-mf-wap:
    host: http://localhost:4700
    basepath: 
  ctint-mf-info:
    host: http://localhost:4800
    basepath: 
  ctint-mf-interaction:
    host: http://localhost:4900
    basepath: /__INTERACTION_BASE_PATH_TBM__
  testUrl: prod
languages:
  supportedLanguages:
  - en
  - zh-HK
  defaultLanguage: zh-HK
services:
  ctint-stt:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/stt
    active: true
    provider: 
    - astri
    healthcheck: /healthcheck
  ctint-auth:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint-demo/auth
    active: true
    provider: # pure-engage / genesys-cloud / ctint-dab-auth / ctint-state-auth
    - pure-engage
    - ctint-dab-auth
    - ctint-state-auth
    healthcheck: /healthcheck
  ctint-conv:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint-demo/conv
    active: true
    provider: 
    - pureengage
    - ctint-dab-conv
    healthcheck: /healthcheck
  ctint-qm:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/qm
    active: true
    healthcheck: /healthcheck
  ctint-config:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/config
    active: true
    provider: 
    - pureengage
    - ctint-dab-config
    healthcheck: /healthcheck
  ctint-session:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/session
    active: true
    provider: 
    - ctint-state-cdssmf
    healthcheck: /healthcheck