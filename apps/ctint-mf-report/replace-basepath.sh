#!/bin/sh

# Path to the YAML configuration file
CONFIG_FILE="/app/public/config/ctint-global-config-uat.yaml"

# Restore the original .next directory from the backup
cp -r /app/.next_backup/* /app/.next/

# Extract the basepaths and hosts using yq
CDSS_HOST=$(yq e '.microfrontends."ctint-mf-cdss".host' $CONFIG_FILE)
CDSS_BASE_PATH=$(yq e '.microfrontends."ctint-mf-cdss".basepath' $CONFIG_FILE)
CPP_HOST=$(yq e '.microfrontends."ctint-mf-report".host' $CONFIG_FILE)
CPP_BASE_PATH=$(yq e '.microfrontends."ctint-mf-report".basepath' $CONFIG_FILE)

# Add slashes around the paths if they are not empty
if [ -n "$CDSS_HOST" ]; then
  TARGET_PATH_CDSS_HOST="${CDSS_HOST}"
else
  TARGET_PATH_CDSS_HOST=""
fi
if [ -n "$CDSS_BASE_PATH" ]; then
  TARGET_PATH_CDSS_BASE_PATH="${CDSS_BASE_PATH}"
else
  TARGET_PATH_CDSS_BASE_PATH=""
fi
if [ -n "$CPP_HOST" ]; then
  TARGET_PATH_CPP_HOST="${CPP_HOST}"
else
  TARGET_PATH_CPP_HOST=""
fi
if [ -n "$CPP_BASE_PATH" ]; then
  TARGET_PATH_CPP_BASE_PATH="${CPP_BASE_PATH}"
else
  TARGET_PATH_CPP_BASE_PATH=""
fi

# Replace __HOST_TBM__ and __BASE_PATH_TBM__ with the actual values in all files
find /app/.next -type f -exec sed -i "s|http://localhost:4400|$TARGET_PATH_CDSS_HOST|g" {} +
find /app/.next -type f -exec sed -i "s|/__CDSS_BASE_PATH_TBM__|$TARGET_PATH_CDSS_BASE_PATH|g" {} +
find /app/.next -type f -exec sed -i "s|http://localhost:4500|$TARGET_PATH_CPP_HOST|g" {} +
find /app/.next -type f -exec sed -i "s|/__CPP_BASE_PATH_TBM__|$TARGET_PATH_CPP_BASE_PATH|g" {} +
