export interface IReportTemplate {
  reportMasterId?: string;
  reportType?: string;
  reportName?: string;
  description?: string;
  tenant?: string;
  platform?: string;
  createTime?: string;
  updateTime?: string;
  createBy?: string;
  updateBy?: string;
}

export interface IReportDetail {
  reportMasterId?: string;
  reportType?: string;
  reportName?: string;
  description?: string;
  tenant?: string;
  platform?: string;
  createTime?: string;
  updateTime?: string;
  createBy?: string;
  updateBy?: string;
  reportDetail?: ReportDetailElement[];
  reportPermission?: ReportPermission[];
}

export interface ReportDetailElement {
  reportDetailId?: string;
  reportMasterId?: string;
  detailType?: string;
  detailSubType?: string;
  detailKey?: string;
  detailValue?: string;
  detailRemark?: string;
  enable?: boolean;
  isRequired?: boolean;
  tenant?: string;
  platform?: string;
  createTime?: string;
  updateTime?: string;
  createBy?: string;
  updateBy?: string;
}

export interface ReportPermission {
  reportPermissionId?: string;
  reportMasterId?: string;
  subjectType?: string;
  subjectValue?: string;
  permissionLevel?: string;
  enable?: boolean;
  tenant?: string;
  platform?: string;
  createTime?: string;
  updateTime?: string;
  createBy?: string;
  updateBy?: string;
}
