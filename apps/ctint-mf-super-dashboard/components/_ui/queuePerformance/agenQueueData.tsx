export interface QueueStatistics {
  svl: string;
  callsDone: number;
  avgAnsSpd: string;
  callAnsRate: string;
  priority: string;
  callsOffered: number;
  avgHdlSpd: string;
  callAbdRate: string;
  waitingCalls: number;
  longestWT: number;
}

export interface QueueMetrics {
  id: string;
  name: string;
  data: QueueStatistics;
  queueName: string;
}

export const queueStatistics: QueueStatistics = {
  svl: '94%',
  callsDone: 153,
  avgAnsSpd: '00:08',
  callAnsRate: '99%(154)',
  priority: '30',
  callsOffered: 156,
  avgHdlSpd: '07:08',
  callAbdRate: '1%(2)',
  waitingCalls: 1,
  longestWT: 12384,
};

export const QueueStatisticsInitData: QueueMetrics[] = [
  {
    id: 'abc',
    queueName: 'Customer Engagement',
    data: queueStatistics,
    name: '',
  },
];

// export const randomUpdateItem = (data: string, index: number) => {
//   // 随机选择一个索引
//   const randomIndex = index;
//   // 创建新的数据对象
//   // const randomIndex = Math.floor(Math.random() * data.length);
//   const newData = {
//     svl: `${Math.floor(Math.random() * (99 - 85) + 85)}%`,
//     callsDone: `${Math.floor(Math.random() * (300 - 100) + 100)}`,
//     avgAnsSpd: `00:${Math.floor(Math.random() * 59)
//       .toString()
//       .padStart(2, '0')}`,
//     callAnsRate: `${Math.floor(Math.random() * (99 - 90) + 90)}%(${Math.floor(Math.random() * 200)})`,
//     priority: `${Math.floor(Math.random() * 40)}`,
//     callsOffered: `${Math.floor(Math.random() * (350 - 150) + 150)}`,
//     avgHdlSpd: `${Math.floor(Math.random() * 12)}:${Math.floor(
//       Math.random() * 59
//     )
//       .toString()
//       .padStart(2, '0')}`,
//     callAbdRate: `${Math.floor(Math.random() * 10)}%(${Math.floor(Math.random() * 30)})`,
//     waitingCalls: `${Math.floor(Math.random() * 10)}`,
//     longestWT:
//       Math.random() < 0.3
//         ? 'N/A'
//         : `${Math.floor(Math.random() * 5)}:${Math.floor(Math.random() * 59)
//             .toString()
//             .padStart(2, '0')}`,
//   };

//   // 更新数组
//   const newDashboardItems = [...data];
//   newDashboardItems[randomIndex] = {
//     ...newDashboardItems[randomIndex],
//     data: newData,
//   };
//   return [newDashboardItems, randomIndex];
//   //setDashboardItems(newDashboardItems);
// };
