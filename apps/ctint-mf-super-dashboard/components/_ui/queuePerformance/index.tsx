import { FC, memo } from 'react';
import QueueStateCard from './queueStateCard';
import { QueueMetrics } from './agenQueueData';
import { TAgentQueueItem } from '../../../types/index';
interface QueuePerformanceProps {
  page: number;
  dashboardItems: TAgentQueueItem[];
  actIndex: number;
}
const QueuePerformance: FC<QueuePerformanceProps> = memo(
  (props) => {
    const { page, dashboardItems, actIndex } = props;

    //console.log(page + " is render");

    return (
      <div className="flex-1 overflow-auto p-2 bg-gray-50">
        {/* <div>{page}</div> */}
        <div className="mx-auto h-full">
          <div className="h-full grid grid-cols-2 gap-4 auto-rows-fr">
            {dashboardItems.map((item, index) => (
              <QueueStateCard
                key={index}
                index={index}
                title={item.name}
                data={item.data}
                actIndex={actIndex}
              />
            ))}
          </div>
        </div>
      </div>
    );
  },
  (prevProps, nextProps) => {
    // 自定义比较函数，只有当数据真正变化时才重渲染
    return (
      JSON.stringify(prevProps.dashboardItems) ===
      JSON.stringify(nextProps.dashboardItems)
    );
  }
);
QueuePerformance.displayName = 'QueuePerformance'; // 添加这行

export default QueuePerformance;
