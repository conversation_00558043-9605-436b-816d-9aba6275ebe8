import { QueueStatistics } from '../components/_ui/queuePerformance/agenQueueData';

export interface TAgentQueueItem {
  createBy: string;
  createTime: string;
  data: QueueStatistics;
  divisionId: string;
  divisionName: string;
  externalPhoneNums: any;
  id: string;
  name: string;
  platform: string;
  state: string;
  tenant: string;
  updateBy: string;
  updateTime: string;
}

export interface TAgentStatus {
  divisionId: string;
  id: string;
  languageLabelEnUs: string;
  systemPresence: string;
  type: string;
}

export interface TQueueInitData {
  call: {
    abandonRate: string;
    answeredRate: string;
    avgAnswered: number;
    avgHandle: number;
    maxWait: number;
    nHandle: number;
    nOffered: number;
    nTalkComplete: number;
    nWait: number;
    oServiceLevel: number;
  };
  interval: string;
  name: string;
  queueId: string;
}
