import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Tabs,
  TabsContent,
} from '@cdss-modules/design-system';
import TdcCss from '../../_ui/TdcCss';
import TdcTd from '../../_ui/TdcTd';
import { useEffect, useState } from 'react';
import { useInteractionContext } from '@cdss-modules/design-system/context/InteractionContext';
import { extractIdandStatus } from '@cdss-modules/design-system/lib/utils';
import { toHandleInteractionData } from '../../../util/toHandleInteractionData';

export function Main() {
  const [translate, setTranslate] = useState(false);

  // const { selectedInteraction } = useTbarContext();
  // const { conversationId, phoneNumber, customerParticipantData } =
  //   useHandleInteractionData(selectedInteraction);

  const [conversationId, setConversationId] = useState('');
  const [customerParticipantId, setCustomerParticipantId] = useState('');
  const [customerPhoneNumber, setCustomerPhoneNumber] = useState('');

  const handleClick = () => {
    setTranslate(!translate);
  };

  const { lastMessage } = useInteractionContext();

  // For Debug
  // setInterval(() => {
  //   console.log('TDC Last Message', lastMessage);
  // }, 1000);

  useEffect(() => {
    // For Debug
    // console.log('TDC Last Message 2', lastMessage);
    if (!lastMessage) return;

    const data = JSON.parse(lastMessage.data);
    const result = extractIdandStatus(data.event);

    if (result?.eventType === 'conversation' && result.status === 'connected') {
      handleClick();
      const interaction = toHandleInteractionData(data);
      setConversationId(interaction?.conversationId);
      setCustomerParticipantId(interaction?.customerData?.id);
      setCustomerPhoneNumber(interaction?.phoneNumber);
    }
  }, [lastMessage]);

  return (
    <AuthChecker
      // requiredPemissions={{
      //   global: {
      //     portals: ['ctint-mf-qhms'],
      //   },
      //   user: {
      //     permissions: ['ctint-mf-qhms.application.visit'],
      //   },
      // }}
      unAuthorizedComponent={
        <Panel>
          <h2 className="p-6 font-bold text-t6">
            You are unauthorized to use this feature.
          </h2>
        </Panel>
      }
    >
      <Button
        onClick={handleClick}
        className="fixed bottom-4 right-4 px-4 py-2 rounded z-50"
      >
        Pop
      </Button>
      <Panel
        className={`absolute top-0 right-0 w-full h-full transform transition-transform duration-500 z-40 ${
          translate ? 'translate-x-0' : 'translate-x-full'
        }`}
      >
        <Tabs
          triggers={[
            { label: 'Customer Search', value: 'tdc-css' },
            { label: 'Telephony Directory', value: 'tdc-td' },
          ]}
          defaultTab="tdc-css"
        >
          <TabsContent value="tdc-css">
            <TdcCss phoneNumber={customerPhoneNumber} />
          </TabsContent>
          <TabsContent value="tdc-td">
            <TdcTd
              conversationId={conversationId}
              customerParticipantId={customerParticipantId}
            />
          </TabsContent>
        </Tabs>
      </Panel>
    </AuthChecker>
  );
}

export default Main;
