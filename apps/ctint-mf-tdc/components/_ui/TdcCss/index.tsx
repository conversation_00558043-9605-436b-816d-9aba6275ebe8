import { useEffect, useState } from 'react';

export type Props = {
  phoneNumber?: string;
};

const Component = (context: Props) => {
  const [iframeSrc, setiframeSrc] = useState('http://css.hktdc.org:8080/css');
  const { phoneNumber } = context;

  useEffect(() => {
    if (phoneNumber) {
      setiframeSrc(
        `http://css.hktdc.org:8080/css/customer/phone/${phoneNumber}`
      );
    } else {
      setiframeSrc('http://css.hktdc.org:8080/css');
    }
  }, [phoneNumber]);

  return (
    <div>
      {/* {iframeSrc}
      <p />
      {phoneNumber} */}

      <iframe
        src={iframeSrc}
        className="h-screen w-screen"
      />
    </div>
  );
};

export default Component;
