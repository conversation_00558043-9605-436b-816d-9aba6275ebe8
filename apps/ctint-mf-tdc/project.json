{"name": "ctint-mf-tdc", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/ctint-mf-tdc", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/next:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"root": "apps/ctint-mf-tdc", "outputPath": "dist/apps/ctint-mf-tdc"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "ctint-mf-tdc:build", "dev": true, "port": 5100, "host": "0.0.0.0"}, "configurations": {"development": {"buildTarget": "ctint-mf-tdc:build:development", "dev": true}, "production": {"buildTarget": "ctint-mf-tdc:build:production", "dev": false}}}, "export": {"executor": "@nx/next:export", "options": {"buildTarget": "ctint-mf-tdc:build:production"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["coverage/apps/ctint-mf-tdc"], "options": {"jestConfig": "apps/ctint-mf-tdc/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/ctint-mf-tdc/**/*.{ts,tsx,js,jsx}"]}}}}