import { useTbarContext } from '@cdss-modules/design-system/context/TBarContext';
import {
  formatInteractionTiming,
  extractIdandStatus,
} from '@cdss-modules/design-system/lib/utils';

import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
dayjs.extend(utc);

export const toHandleInteractionData = (interaction: any) => {
  const isHistory = interaction?.isHistory;
  const selectedInteraction = interaction;
  let tBarStatus, conversationId;
  if (isHistory) {
    conversationId = interaction.conversationId;
    tBarStatus = '';
  } else {
    conversationId = extractIdandStatus(
      interaction?.event || ''
    )?.conversationId;
    tBarStatus = extractIdandStatus(interaction?.event || '')?.status;
  }
  const isActive =
    conversationId ==
    extractIdandStatus(selectedInteraction?.event || '')?.conversationId;

  // agent only one
  const agents = interaction?.eventData?.data?.agent;
  const agent = agents?.[agents.length - 1];

  const voicemails = interaction?.eventData?.data?.voicemail;
  const lastvoicemail = voicemails?.[voicemails.length - 1];
  const voicemail =
    lastvoicemail?.state !== 'terminated' &&
    lastvoicemail?.state !== 'disconnected'
      ? lastvoicemail
      : null;

  const allCustomers = interaction?.eventData?.data?.customer;
  const customers = allCustomers?.filter(
    (v: any) => v?.state !== 'terminated' && v?.state !== 'disconnected'
  );

  const customer = customers?.[0];
  const allconsults = interaction?.eventData?.data?.consult;
  const consults = allconsults?.filter(
    (v: any) => v?.state !== 'terminated' && v?.state !== 'disconnected'
  );
  const consult = consults?.[consults.length - 1];
  const voice = interaction?.eventData?.data?.voice?.[0];
  const workflow = interaction?.eventData?.data?.workflow?.[0];
  const acd = interaction?.eventData?.data?.acd?.[0];

  const isHaveCustomerOrConsult = Boolean(customer) || Boolean(consult);

  const consultInitiator =
    Boolean(customer) &&
    Boolean(consult) &&
    consult?.attributes?.consultInitiator
      ? 'consult'
      : 'agent';
  // may transfer,consult now
  const isConference =
    Boolean(customer) && (Boolean(consult) || Boolean(voicemail));

  //  only confenrence
  const fullConference =
    Boolean(customer) &&
    Boolean(consult) &&
    !customer?.confined &&
    !agent?.confined &&
    !consult?.confined &&
    !customer?.held &&
    !agent?.held &&
    !consult?.held;

  const historyCallBackRole = customer ? 'customer' : 'consult';
  const customerParticipantData = customer || consult;

  const getRowData = (rowData: any, role: string) => {
    let participantName, phoneNumber;
    if (role === 'customer') {
      const isOutBound = rowData.direction == 'outbound';
      phoneNumber = isOutBound
        ? rowData?.address || rowData?.dnis
        : rowData?.address || rowData.ani;
      participantName = rowData?.name || 'N/A';
    }
    if (role === 'acd') {
      participantName = rowData?.name || 'N/A';
      phoneNumber = 'N/A';
    }
    return {
      ...rowData,
      role,
      participantName,
      phoneNumber: phoneNumber?.replace(/^(tel:\+|\+)/, ''),
      telPhonneNumber: phoneNumber,
    };
  };
  // const customerData = customer ? getRowData(customer, 'customer') : null;
  // const consultData = consult ? getRowData(consult, 'consult') : null;
  const agentData = agent ? getRowData(agent, 'agent') : null;
  const acdData = acd ? getRowData(acd, 'acd') : null;

  const customerDatas = customers?.map((customer: any) => {
    return getRowData(customer, 'customer');
  });
  const customerData = customerDatas?.[0];
  const consultDatas = consults?.map((customer: any) => {
    return getRowData(customer, 'consult');
  });
  const consultData = consultDatas?.[0];
  const name =
    customerData?.participantName ||
    consultData?.participantName ||
    acdData?.participantName;
  const phoneNumber =
    customerData?.phoneNumber ||
    consultData?.phoneNumber ||
    acdData?.phoneNumber;
  const telPhonneNumber =
    customerData?.telPhonneNumber ||
    consultData?.telPhonneNumber ||
    acdData?.telPhonneNumber;

  return {
    connectedTime: customer?.connectedTime || consult?.connectedTime,
    conversationId,
    isHistory,
    historyCallBackRole,
    isActive,
    tBarStatus,
    voice,
    workflow,
    isConference,
    fullConference,
    consultInitiator,
    customerDatas,
    customerData,
    consultDatas,
    consultData,
    agentData,
    acdData,
    name,
    phoneNumber,
    telPhonneNumber,
    customerParticipantData,
    isHaveCustomerOrConsult,
    talkTime: formatInteractionTiming(
      interaction?.conversationStart,
      interaction?.conversationEnd
    ),
  };
};
