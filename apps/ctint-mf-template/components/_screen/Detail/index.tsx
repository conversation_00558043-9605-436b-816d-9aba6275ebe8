import {
  <PERSON>th<PERSON><PERSON><PERSON>,
  <PERSON>,
  useRouteHand<PERSON>,
} from '@cdss-modules/design-system';
import TemplateDemo from '../../_ui/TemplateDemo';

export function Detail() {
  const { toPath } = useRouteHandler();
  return (
    <AuthChecker
      unAuthorizedComponent={
        <Panel>
          <h2 className="p-6 font-bold text-t6">
            You are unauthorized to use this feature.
          </h2>
        </Panel>
      }
    >
      <TemplateDemo
        testId="detail"
        titleI18n="ctint-mf-template.templateDetail.title"
        descI18n="ctint-mf-template.templateDetail.desc"
        btnLabelI18n="ctint-mf-template.templateDetail.btnLabel"
        onClickButton={() => toPath('/ctint-mf-template/')}
      />
    </AuthChecker>
  );
}

export default Detail;
