/* eslint-disable @nx/enforce-module-boundaries */

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import AdminAudit from '../../ui/AdminAudit';
import { CDSSAdminProvider, useRole } from '@cdss-modules/design-system';
import {
  microfrontends,
  UserAuditTabName,
} from 'apps/ctint-mf-user-admin/types/microfrontendsConfig';
import { AuditCondition } from 'apps/ctint-mf-user-admin/types/adminAudit';

export const AdminAuditBody = () => {
  const { globalConfig } = useRole();
  const microfrontendsConfig: microfrontends = globalConfig?.microfrontends;
  const userAuditTabsCols: UserAuditTabName[] =
    microfrontendsConfig['ctint-mf-user-admin']['audit-tab-names'];
  // const tableCols: Record<string, Condition> = {};
  // console.log(userTabsCols);

  const initTableColsData = userAuditTabsCols.reduce(
    (acc, filter) => {
      acc[filter.value] = {
        ...filter,
        checked: false,
        require: false, // Adding the missing required property
      }; // Initialize all filter values to empty
      return acc;
    },
    {} as Record<string, AuditCondition>
  );
  return <AdminAudit cols={initTableColsData} />;
};

// Create a client
const queryClient = new QueryClient();

const AdminAuditScreen = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <CDSSAdminProvider>
        <AdminAuditBody />
      </CDSSAdminProvider>
    </QueryClientProvider>
  );
};

export default AdminAuditScreen;
