import { Button, Panel, useCDSS } from '@cdss-modules/design-system';
import SearchInput from '@cdss-modules/design-system/components/_ui/SearchInput';
import PopoverMenu from '@cdss-modules/design-system/components/_ui/PopoverMenu';
import { cn } from '@cdss-modules/design-system/lib/utils';
import Checkbox from '@cdss-modules/design-system/components/_ui/Checkbox';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { useEffect, useState } from 'react';
import _, { filter } from 'lodash';
import { AdminAuditList } from '../AdminAuditList';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { AuditCondition } from 'apps/ctint-mf-user-admin/types/adminAudit';
import FilterCompoment, {
  Condition,
} from '@cdss-modules/design-system/components/_ui/FilterComponent';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';

export type AdminUserAuditProps = {
  cols: Record<string, AuditCondition>;
};
export type AdminUserAuditFilterProps = {
  cols: Record<string, AuditCondition>;
  onApplyFilter: (filterValue: Record<string, AuditCondition>) => void;
  onClearFilter: () => void;
};

export function AdminAudit({ cols }: AdminUserAuditProps) {
  // 当前页面对应生效filter
  const [currentFilter, setCurrentFilter] =
    useState<Record<string, AuditCondition>>(cols);

  // filter 点击 search
  const onApplyFilter = (filterValue: Record<string, AuditCondition>) => {
    // setCurrentFilter((prev) => {
    //   // 检查是否需要更新 checked 或 data 都需要prev和filter对应
    //   const hasCheckedOrData = Object.keys(filterValue).every(key => {
    //     return prev[key].checked === filterValue[key].checked && prev[key].data === filterValue[key].data
    //   })
    //   if(hasCheckedOrData){
    //     return prev
    //   }
    //   return filterValue
    // })

    // 凡是按了search，都刷新currentFilter
    setCurrentFilter(filterValue);
  };
  // filter 点击 clear
  const onClearFilter = () => {
    setCurrentFilter((prev) => {
      // 检查是否有需要清除的值
      const hasCheckedOrData = Object.values(prev).some(
        (filter) => filter.checked || filter.data
      );
      // 如果没有需要清除的值，直接返回原对象
      if (!hasCheckedOrData) {
        return prev;
      }
      // 只在有需要清除的值时创建新对象
      const newFilterValues = { ...prev };
      Object.keys(newFilterValues).forEach((key) => {
        delete newFilterValues[key].data;
        newFilterValues[key].checked = false;
      });
      return newFilterValues;
    });
  };

  return (
    <div className="flex flex-col gap-2 h-full">
      <Panel
        className="p-0"
        containerClassName="h-full"
      >
        <AdminAuditFilter
          cols={cols}
          onApplyFilter={onApplyFilter}
          onClearFilter={onClearFilter}
        />
        <AdminAuditList cols={currentFilter} />
      </Panel>
    </div>
  );
}

export function AdminAuditFilter({
  cols,
  onApplyFilter,
  onClearFilter,
}: AdminUserAuditFilterProps) {
  interface DateRange {
    start: string;
    end: string;
  }

  // 類型守衛函數
  const isDateRange = (data: any): data is DateRange => {
    return data && typeof data === 'object' && 'start' in data && 'end' in data;
  };
  const { t, i18n } = useTranslation();
  // 已经修改的filter
  const [filterValue, setFilterValue] =
    useState<Record<string, AuditCondition>>(cols);

  // 生成 tagnames 数组, 用于filter input中的display
  const conbineFiltersTagName = () => {
    const tags: string[] = [];

    const filters = [...Object.values(filterValue)];

    filters.map((item) => {
      const key = item.value;
      if (filterValue[key].checked && filterValue[key].data) {
        if (
          key === 'eventTimestamp' &&
          typeof filterValue[key].data === 'object'
        ) {
          const data = filterValue[key].data as {
            start?: string;
            end?: string;
          };
          //将时间转成当前时区再显示12333
          const start = data.start
            ? dayjs(data.start).format('YYYY-MM-DD HH:mm:ss')
            : null;
          const end = data.end
            ? dayjs(data.end).format('YYYY-MM-DD HH:mm:ss')
            : null;
          if (start && end) {
            tags.push(
              `${i18n.language == 'en' ? filterValue[key].labelEn : filterValue[key].labelCh}: ${start} - ${end}`
            );
          } else if (start && !end) {
            tags.push(
              `${i18n.language == 'en' ? filterValue[key].labelEn : filterValue[key].labelCh}: ${start}`
            );
          } else if (!start && end) {
            tags.push(
              `${i18n.language == 'en' ? filterValue[key].labelEn : filterValue[key].labelCh}: ${end}`
            );
          }
        } else {
          tags.push(
            `${i18n.language == 'en' ? filterValue[key].labelEn : filterValue[key].labelCh}: ${filterValue[key].data}`
          );
        }
      }
    });
    return tags;
  };

  // 渲染已经选择了的条件, 允许点击删除
  const renderTagItems = () => {
    const filters = [...Object.values(filterValue)];

    return filters.map((item, i) => {
      const key = item.value;
      if (!filterValue[key].checked || !filterValue[key].data) return null;

      let valueText = '';
      if (
        key === 'eventTimestamp' &&
        typeof filterValue[key].data === 'object'
      ) {
        const data = filterValue[key].data as { start?: string; end?: string };
        //将时间转成当前时区再显示
        const start = data.start
          ? dayjs(data.start).format('YYYY-MM-DD HH:mm:ss')
          : null;
        const end = data.end
          ? dayjs(data.end).format('YYYY-MM-DD HH:mm:ss')
          : null;
        if (start && end) {
          valueText = `${i18n.language == 'en' ? filterValue[key].labelEn : filterValue[key].labelCh}: ${start} - ${end}`;
        } else if (start && !end) {
          valueText = `${i18n.language == 'en' ? filterValue[key].labelEn : filterValue[key].labelCh}: ${start}`;
        } else if (!start && end) {
          valueText = `${i18n.language == 'en' ? filterValue[key].labelEn : filterValue[key].labelCh}: ${end}`;
        }
      } else {
        valueText = `${i18n.language == 'en' ? filterValue[key].labelEn : filterValue[key].labelCh}: ${filterValue[key].data}`;
      }

      return (
        <div
          key={i}
          className="p-1 mr-1 mt-1 flex flex-row items-center border border-black rounded-[4px] text-[12px] text-black"
        >
          <span className="truncate">{valueText}</span>
          <span
            onClick={() => {
              setFilterValue((prev) => {
                const newFilterValues = { ...prev };
                delete newFilterValues[key].data;
                newFilterValues[key].checked = false;
                return newFilterValues;
              });
            }}
            className="ml-1 cursor-pointer"
          >
            <Icon name="cross" />
          </span>
        </div>
      );
    });
  };

  // useEffect(() => {
  //   console.log('filterValue', filterValue);
  // }, [filterValue]);

  return (
    <div
      className={cn(
        `w-full py-2 px-3 bg-white rounded-xl inline-flex flex-row overflow-x-auto`
      )}
    >
      <div className="flex-1 flex flex-row">
        {/* Search Filter Input Component */}
        <SearchInput
          tags={conbineFiltersTagName()}
          placeholder={t('ctint-mf-user-admin.filter.name')}
        >
          {/* Filter Operation Popover Content */}
          <section>
            {/* Filter Operation Scroll Block */}
            <section className="max-h-[409px] min-w-[600px] max-w-[800px] overflow-y-auto">
              {/* Popover selected filters items */}
              {Object.values(filterValue).some((filter) => filter.checked) && (
                <section className="p-4">
                  {Object.keys(filterValue).filter(
                    (key) => filterValue[key].data !== undefined
                  ).length > 0 && (
                    <div className="flex flex-wrap flex-row">
                      {renderTagItems()}
                    </div>
                  )}
                </section>
              )}
              {/* Popover filter input form */}
              <section className="px-4 py-4">
                <h2 className="text-remark font-bold my-2">
                  {t('ctint-mf-user-admin.filter.name')}
                </h2>
                <div className="flex flex-col gap-y-2">
                  <FilterCompoment
                    filterValues={filterValue}
                    setFilterValues={setFilterValue}
                  />
                </div>
              </section>
            </section>
          </section>
        </SearchInput>
        {/* Search Button */}
        <Button
          className="self-center ml-2"
          bodyClassName="border border-black py-[0.375rem]"
          onClick={() => onApplyFilter(cloneDeep(filterValue))}
          size="s"
        >
          {t('ctint-mf-user-admin.filter.search')}
        </Button>
        {/* Clear Tags Button */}
        <Button
          className="self-center ml-2"
          bodyClassName="border border-black py-[0.375rem]"
          onClick={() => {
            setFilterValue((prev) => {
              // 检查是否有需要清除的值
              const hasCheckedOrData = Object.values(prev).some(
                (filter) => filter.checked || filter.data
              );
              // 如果没有需要清除的值，直接返回原对象
              if (!hasCheckedOrData) {
                return prev;
              }
              // 只在有需要清除的值时创建新对象
              const newFilterValues = { ...prev };
              Object.keys(newFilterValues).forEach((key) => {
                delete newFilterValues[key].data;
                newFilterValues[key].checked = false;
              });
              return newFilterValues;
            });
            // 调用父组件的 onClearFilter 方法
            onClearFilter();
          }}
          variant="blank"
          size="s"
        >
          {t('ctint-mf-user-admin.filter.clear')}
        </Button>
      </div>
    </div>
  );
}

export default AdminAudit;
