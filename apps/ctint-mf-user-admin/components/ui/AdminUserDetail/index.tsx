import { toast, useCDSSAdmin } from '@cdss-modules/design-system';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { get2LettersFromName } from '@cdss-modules/design-system/lib/utils';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { PenSquare, Save } from 'lucide-react';
import { Controller, FormProvider, useForm } from 'react-hook-form';
import EditableField from '../EditableField';
import { Fragment, useEffect, useState } from 'react';
import _ from 'lodash';
import { formatDateTime, TcustmCol } from '../AdminUserList';
import {
  CreateUser,
  GetRolesList,
  GetUserGroupsList,
  UpdateUser,
} from '../../../lib/api';
import { basePath } from '../../../lib/appConfig';
import {
  CombinedRoleNGroupData,
  TAdminRole,
  TAdminRolesDataResp,
  TAdminUserData,
  TAdminUserGroup,
  TAdminUserGroupDataResp,
  TformComponent,
} from '../../../types/microfrontendsConfig';
import { useTranslation } from 'react-i18next';
import { UserInitData } from './newUserInit';

type TadminUserDetail = {
  showCols: TcustmCol[];
  setOpenedEntity: (data: any) => void;
};

export const AdminUserDetail = (props: TadminUserDetail) => {
  const { t } = useTranslation();
  const { updateOpenedEntity, openedEntity } = useCDSSAdmin();
  const [isEditing, setIsEditing] = useState(false);
  // const [roleData, setRoleData] = useState([]);
  const [formLayout, setFormLayout] = useState<TformComponent[]>([]);
  const [userRoles, setUserRoles] = useState([]);
  const [userGroup, setUserGroups] = useState([]);
  const [adminRolesList, setAdminRolesList] = useState<TAdminRole[]>([]);
  const [adminUserGroups, setAdminUserGroups] = useState<TAdminUserGroup[]>([]);
  const [combinedData, setCombinedData] = useState<CombinedRoleNGroupData>({
    roleNames: [],
    groupNames: [],
  });
  const [selectedTab, setSelectedTab] = useState('user-roles');
  const methods = useForm({
    //   resolver: yupResolver(sopSchema),
  });
  const { showCols, setOpenedEntity } = props;

  const {
    handleSubmit,
    control,
    watch,
    setValue,
    formState: { errors },
  } = methods;
  const isNew = _.isEmpty(openedEntity?.entity);

  useEffect(() => {
    fetchRoleNGroupData();
  }, []);

  const fetchRoleNGroupData = async () => {
    try {
      const [rolesResult, groupsResult] = await Promise.all([
        GetRolesList(basePath),
        GetUserGroupsList(basePath),
      ]);
      const rolesData: TAdminRolesDataResp = rolesResult.data;
      const groupsData: TAdminUserGroupDataResp = groupsResult.data;

      const tempCombinedData = {
        roleNames: rolesData.data || [],
        groupNames: groupsData.data || [],
      };
      setCombinedData(tempCombinedData);
    } catch (error) {
      console.error('Get Role and Group data fail:', error);
    }
  };

  const getRolesList = async () => {
    const result = await GetRolesList(basePath);
    const respData: TAdminRolesDataResp = result.data;
    const rolesList = respData.data;
    console.log(rolesList);
    setAdminRolesList(rolesList);
  };

  const getUserGroup = async () => {
    const result = await GetUserGroupsList(basePath);
    const respData: TAdminUserGroupDataResp = result.data;
    const userGroups = respData.data;
    console.log(userGroups);
    setAdminUserGroups(userGroups);
  };

  useEffect(() => {
    // console.log(isEditing);
  }, [isEditing]);

  const onSubmit = async (data: any) => {
    // console.log(data);
    const rs = matchRoleNGroupValues(data, combinedData);
    data = { ...data, ...rs } as TAdminUserData;
    // console.log(data);
    data.up = Buffer.from(data.up).toString('base64');
    console.log(data);
    // let resp = null;
    // if (isNew) {
    //   resp = await CreateUser(basePath, data);
    // } else {
    //   resp = await UpdateUser(basePath, data);
    // }

    // if (resp.status == 200 || resp.data.isSuccess) {
    //   toast({
    //     variant: 'success',
    //     title: 'User Saved',
    //     description: `New user Information was saved successfully!`,
    //   });
    //   if (!isEditing) {
    //     setIsEditing(true);
    //   } else {
    //     setIsEditing(false);
    //   }

    //   setTimeout(() => {
    //     // setOpenedEntity(null);
    //     window.location.reload();
    //   }, 1500);
    // }
  };

  useEffect(() => {}, [userRoles]);

  useEffect(() => {
    // console.log('openedEntity.entity');
    // console.log(openedEntity.entity);
    const tempFormLayout: TformComponent[] = [];
    showCols.map((col, index) => {
      const formComponent: TformComponent = {
        title: col.name,
        name: col.key,
        value: openedEntity.entity[col.key],
        readOnly: col.readOnly,
        filterType: col.filterType,
        require: col.require,
      };
      tempFormLayout.push(formComponent);
    });
    //methods.reset({ state: 'inactive' });
    if (!openedEntity?.entity || isNew) {
      setIsEditing(true);
    }
    setFormLayout(tempFormLayout);
  }, [openedEntity, isNew]);

  useEffect(() => {
    const sourceData = !isNew ? openedEntity?.entity : UserInitData;

    if (sourceData) {
      // 处理表单数据
      const inputVals = Object.fromEntries(
        formLayout.map((item) => [
          item.name,
          item.filterType === 'dateRange'
            ? formatDateTime(sourceData[item.name])
            : item.filterType === 'multipleSelect'
              ? handleMultipleSelectValue(sourceData[item.name])
              : sourceData[item.name] || '',
        ])
      );

      // 重置表单
      methods.reset(inputVals);
    }
  }, [formLayout]);

  const handleMultipleSelectValue = (value: string): string[] => {
    // console.log(value);
    if (value === '' || value === undefined) return [];
    return value.split(',');
  };

  const handleInvalid = (e: React.InvalidEvent<HTMLInputElement>) => {
    e.target.setCustomValidity(t('ctint-mf-user-admin.form.invaildRequired'));
  };

  const handleInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    // 清空錯誤信息
    e.target.setCustomValidity('');
  };

  const entity = openedEntity?.entity;
  const avatarLetters = (
    entity?.name ? get2LettersFromName(entity?.name || '') : 'NA'
  ) as string;
  return (
    <FormProvider {...methods}>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="flex flex-col w-full h-full px-2 pt-0 pb-6"
      >
        <div className="flex-1 h-0 flex flex-col gap-y-4 overflow-auto">
          <div className="flex flex-col gap-x-4 w-full h-0 flex-1">
            <div className="w-full relative flex items-center gap-x-2 justify-between py-2 z-30">
              <div className="w-full flex items-center gap-x-8">
                <div className="flex items-center gap-x-2">
                  <button
                    type="button"
                    onClick={() => updateOpenedEntity(null)}
                    className="inline-flex gap-x-2 items-center group/sop-back hover:text-primary"
                  >
                    <Icon
                      name="back"
                      //   className="hidden group-hover/sop-back:inline-flex"
                      className="inline-flex"
                    />
                    <div className="w-8">
                      {t('ctint-mf-user-admin.tab.user')}
                    </div>
                  </button>
                  <span className="text-black">/</span>
                  <div className="w-full">
                    <span className="text-black">
                      {entity?.name || t('ctint-mf-user-admin.tab.new')}
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-x-2">
                {isEditing ? (
                  <>
                    <Button
                      size="s"
                      //type="submit"
                      variant="secondary"
                      onClick={() => {
                        if (isNew) {
                          updateOpenedEntity(null);
                        } else {
                          setIsEditing(false);
                        }
                      }}
                    >
                      <span className="whitespace-nowrap">
                        {t('ctint-mf-user-admin.formAction.discard')}
                      </span>
                    </Button>
                    <Button
                      beforeIcon={<Save />}
                      size="s"
                      type="submit"
                    >
                      <span className="whitespace-nowrap">
                        {t('ctint-mf-user-admin.formAction.save')}
                      </span>
                    </Button>
                  </>
                ) : (
                  <Button
                    beforeIcon={<PenSquare />}
                    size="s"
                    type="button"
                    onClick={() => setIsEditing(true)}
                    disabled={!entity.isAllowEdit}
                  >
                    <span className="whitespace-nowrap">
                      {entity.isAllowEdit
                        ? t('ctint-mf-user-admin.formAction.edit')
                        : t('ctint-mf-user-admin.formAction.notAllowEdit')}
                    </span>
                  </Button>
                )}
              </div>
            </div>
            <div className="flex items-start py-2 gap-x-8 justify-center">
              {/* <div className="flex items-center gap-x-6">
                <Avatar
                  className="size-24"
                  text={avatarLetters}
                  textClassName="text-t4"
                />
                <div
                  className={cn(
                    'flex flex-col',
                    isEditing ? 'gap-y-6' : 'gap-y-1'
                  )}
                >
                  <Controller
                    name={'name'}
                    control={control}
                    rules={{ required: true }}
                    render={({ field }) => (
                      <>
                        {isEditing ? (
                          <div>
                            <div className="text-body font-bold">Name</div>
                            <Input
                              size="s"
                              defaultValue={entity?.name}
                              className="w-full"
                              {...field}
                            />
                          </div>
                        ) : (
                          <div className="text-t5 -mt-1 font-bold">
                            {entity?.name}
                          </div>
                        )}
                      </>
                    )}
                  />
                  <Controller
                    name={'username'}
                    control={control}
                    rules={{ required: true }}
                    render={({ field }) => (
                      <>
                        {isEditing ? (
                          <div>
                            <div className="text-body font-bold">Username</div>
                            <Input
                              size="s"
                              defaultValue={entity?.username}
                              className="w-full"
                              {...field}
                            />
                          </div>
                        ) : (
                          <div className="text-remark">
                            <i>{entity?.username}</i>
                          </div>
                        )}
                      </>
                    )}
                  />
                </div>
              </div> */}
              {/* <div className="flex h-full w-px bg-gray-200" /> */}
              <div className="flex">
                <div className="flex flex-wrap gap-6 max-w-[1400px]">
                  {formLayout.map((v: any) => (
                    <Fragment key={v.name}>
                      <EditableField
                        isEditing={isEditing}
                        combinedData={combinedData}
                        {...v}
                      />
                    </Fragment>
                  ))}
                  {isEditing && (
                    <Field
                      title={t('ctint-mf-user-admin.form.password')}
                      icon={<Icon name="error" />}
                      placeholder="Please Enter Password"
                    >
                      <Controller
                        name="up"
                        control={control}
                        // rules={{ required: true }}
                        defaultValue=""
                        render={({ field }) => (
                          <Input
                            type="text"
                            size="s"
                            {...field}
                            placeholder={t(
                              'ctint-mf-user-admin.form.passworPlacehoder'
                            )}
                            required={true}
                            onInvalid={handleInvalid}
                            onInput={handleInput}
                          />
                        )}
                      />
                    </Field>
                  )}

                  <Controller
                    name="id"
                    control={control}
                    // rules={{ required: true }}
                    defaultValue={entity?.id}
                    render={({ field }) => (
                      <>
                        <input
                          type="hidden"
                          {...field}
                        />
                      </>
                    )}
                  />
                </div>
              </div>
            </div>
            {/* <div
              className={cn(
                'mt-4 flex flex-col w-full h-full rounded-2xl bg-white'
              )}
            >
              <div className="flex w-full justify-between items-center border-b border-grey-200">
                <div className="flex gap-6 shrink-0 px-0">
                  {[
                    {
                      value: 'user-roles',
                      label: 'Roles',
                    },
                    {
                      value: 'user-user-groups',
                      label: 'User Groups',
                    },
                  ].map((trigger) => (
                    <button
                      key={`trigger-${trigger.value}`}
                      type="button"
                      onClick={() => {
                        //console.log(isEditing);
                        setSelectedTab(trigger.value);
                      }}
                      className={cn(
                        'p-2 text-remark font-bold',
                        selectedTab === trigger.value
                          ? 'text-black shadow-b shadow-tab-selected'
                          : 'text-grey-500',
                        'py-2 px-2 text-body'
                      )}
                    >
                      {trigger.label}
                    </button>
                  ))}
                </div>
              </div>
              <div
                className={cn(
                  'p-4 h-0 flex-1 flex flex-col',
                  selectedTab === 'user-user-groups' ? 'flex' : 'hidden'
                )}
              >
                <AdminUserGroupList
                  inDetail
                  isEditing
                  setUserGroups={setUserGroups}
                />
              </div>
              <div
                className={cn(
                  'p-4 h-0 flex-1 flex flex-col',
                  selectedTab === 'user-roles' ? 'flex' : 'hidden'
                )}
              >
                <AdminUserRoleList
                  inDetail
                  isEditing
                  setUserRoles={setUserRoles}
                />
              </div>
            </div> */}
          </div>
          <div className="flex justify-between gap-y-4 pt-4 h-10 overflow-auto hidden">
            {avatarLetters}
            {JSON.stringify(entity)}
          </div>
        </div>
      </form>
    </FormProvider>
  );

  function matchRoleNGroupValues(firstJson, secondJson) {
    const result = {
      roles: [],
      groups: [],
    };

    // 處理roleNames
    if (firstJson.roleNames && Array.isArray(firstJson.roleNames)) {
      firstJson.roleNames.forEach((roleName) => {
        // 在第二個json的roleNames中尋找匹配的對象
        const matchedRole = secondJson.roleNames.find(
          (role) => role.name === roleName
        );
        if (matchedRole) {
          result.roles.push(matchedRole);
        }
      });
    }

    // 處理groupNames
    if (firstJson.groupNames && Array.isArray(firstJson.groupNames)) {
      firstJson.groupNames.forEach((groups) => {
        // 在第二個json的groupNames中尋找匹配的對象
        const matchedGroup = secondJson.groupNames.find(
          (group) => group.name === groups
        );
        if (matchedGroup) {
          result.groups.push(matchedGroup);
        }
      });
    }

    return result;
  }
};
