import { addLoggingToAxios } from '@cdss-modules/design-system/lib/logging';
import { apiConfig } from './config';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { TAdminUserData } from 'apps/ctint-mf-user-admin/types/microfrontendsConfig';
import { TAuditLogQueryParams } from 'apps/ctint-mf-user-admin/types/adminAudit';
export const axiosInstance = axios.create({
  timeout: 20000,
  headers: {
    'Content-Type': 'application/json',
    traceId: uuidv4(),
    tenant: 'ctint',
    sourceId: 'ctint-mf-interaction',
    previousId: 'ctint-bff-cdss',
  },
});

axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', {
      url: error.config?.url,
      status: error.response?.status,
      message: error.message,
    });
    return Promise.reject(error);
  }
);

axiosInstance.interceptors.request.use(
  (config) => {
    // TODO: should call the auth api and set the token when program initialize
    if (
      process.env.NODE_ENV === 'development' &&
      localStorage.getItem('cdss-auth-token') === null
    ) {
      localStorage.setItem('deviceId', uuidv4());
      // localStorage.setItem(
      //   'gc-access-token',
      //   'ofROc0Va1i74x3CKxPJ-et8g9jwo08wFkRgCzCcIYDwdAEmJMCIh-HDn9Sgw_U17WxTvtwnngeOksDWN8Sbatw'
      // );
      localStorage.setItem(
        'cdss-auth-token',
        // 'EdOImMlG47o8McBVtqYyQbz7d4nte/7lO8HE+qEKFHupHznCcQMNoFOo+gYGxBtX8viFDj0Zgb6upeTMWiHvnNr09Q=='
        // 'EdOImMlG47o8McBVtqY2QKHtIoT7OPznIInDv7cOXG+0DnbPZAcG6Ra+604VzhdT+fX3I2pbhPKWfQSrGpd+hZ5OuZOcptbvJ01GpJkpzCbiiT6TpuU='
        'EdOImMlG47o8McBVtqY2QKHtIoT7OPznIInDv7cOXG+0DnbPZAcG6Ra+604VzhdT+fXTFANkosKzSjoQDgB5B937RFamgWt3o8D8'
      );
    }
    const cdssAuthToken = localStorage.getItem('cdss-auth-token') || '';
    const gcAccessToken = localStorage.getItem('gc-access-token') || '';
    const deviceId = localStorage.getItem('deviceId') || '';
    config.headers['tenant'] = localStorage.getItem('tenant') || 'ctint';
    if (cdssAuthToken) {
      config.headers['cdss-authorization'] = 'Bearer ' + cdssAuthToken;
    }
    if (deviceId) {
      config.headers['deviceId'] = deviceId;
    }

    if (config.url?.includes('/interaction/recordings/transcript/')) {
      config.headers['requester'] = 'agent1';
    }

    // console.log('axiosInstance.interceptors.request', config);
    return config;
  },
  (error) => {
    Promise.reject(error);
  }
);

addLoggingToAxios(axiosInstance);

export const GetUserList = (basePath = '') =>
  axiosInstance.get(`${basePath}${apiConfig.paths.userList}`);

export const GetRolesList = (basePath = '') =>
  axiosInstance.get(`${basePath}${apiConfig.paths.getRoles}`);

export const GetUserGroupsList = (basePath = '') =>
  axiosInstance.get(`${basePath}${apiConfig.paths.getUserGroups}`);

export const CreateUser = (basePath = '', data: TAdminUserData) =>
  axiosInstance.post(`${basePath}${apiConfig.paths.userCreate}`, data);

export const UpdateUser = (basePath = '', data: TAdminUserData) =>
  axiosInstance.put(`${basePath}${apiConfig.paths.userUpdate}`, data);
export const GetUserAuditLogs = (basePath = '', data: TAuditLogQueryParams) =>
  axiosInstance.post(`${basePath}${apiConfig.paths.userAuditLogs}`, data);
//  axiosInstance.get(`${basePath}${apiConfig.paths.userList}?${queryParams}`);

export default axiosInstance;
