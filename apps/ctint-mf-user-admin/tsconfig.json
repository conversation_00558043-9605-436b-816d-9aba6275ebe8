{
  "extends": "../../tsconfig.base.json",
  "compilerOptions": {
    "noImplicitAny": false,
    "strictNullChecks": false, 
    "skipLibCheck": true,          // 跳過聲明文件的類型檢查
    "noUnusedLocals": false,       // 允許未使用的本地變量
    "noUnusedParameters": false,    // 允許未使用的參數
    "jsx": "preserve",
    "allowJs": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": false,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "types": ["jest", "node"]
  },
  "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "next-env.d.ts"],
  "exclude": ["node_modules", "jest.config.ts", "**/*.spec.ts", "**/*.test.ts"]
}
