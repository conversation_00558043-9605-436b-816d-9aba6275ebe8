import { Condition } from '@cdss-modules/design-system/components/_ui/FilterComponent';

export interface AuditCondition extends Condition {
  sort?: boolean;
}

export type TSortDirection = 'ASC' | 'DESC';

export interface TEventTimestampRange {
  start: string;
  end: string;
}

export interface TSortConfig {
  eventTimestamp?: TSortDirection;
  // 如果还有其他可排序字段，可以在这里添加
}

export interface TAuditLogQueryParams {
  eventTimestamp?: TEventTimestampRange;
  ipAddress?: string;
  sort?: TSortConfig;
  page: number;
  pageSize: number;
}

export interface TAuditLogData {
  total: number;
  logs: object[];
}

export interface TAuditLogResponse {
  data: TAuditLogData;
  error: string;
  isSuccess: boolean;
}
