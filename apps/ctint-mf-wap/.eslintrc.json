{"extends": ["plugin:@nx/react-typescript", "next", "next/core-web-vitals", "../../.eslintrc.json"], "ignorePatterns": ["!**/*", ".next/**/*"], "overrides": [{"files": ["*.ts", "*.tsx", "*.js", "*.jsx"], "rules": {"@next/next/no-html-link-for-pages": ["error", "apps/ctint-mf-wap/pages"]}}, {"files": ["*.ts", "*.tsx"], "rules": {}}, {"files": ["*.js", "*.jsx"], "rules": {}}, {"files": ["*.spec.ts", "*.spec.tsx", "*.spec.js", "*.spec.jsx"], "env": {"jest": true}}]}