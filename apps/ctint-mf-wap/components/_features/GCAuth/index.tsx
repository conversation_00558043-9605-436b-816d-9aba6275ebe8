// Obtain a reference to the platformClient object
import { useEffect, useState } from 'react';
import { authenticate, getUserMe } from '../../../utils/genesysCloudUtils';
import Panel from '@cdss-modules/design-system/components/_ui/Panel';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import MsgForm from '../MsgForm';

type TAuthProps = {
  gcClientId?: string;
  gcRedirect?: string;
};

export const GCAuth = ({ gcClientId, gcRedirect }: TAuthProps) => {
  const [meInfo, setMeInfo] = useState<any>();
  const [isInIframe, setIsInIframe] = useState(window !== window?.top);
  const [infoRequested, setInfoRequested] = useState(false);
  const [isAuthorized, setIsAuthorized] = useState(
    !!new URLSearchParams(window?.location?.hash?.substring(1))?.get(
      'access_token'
    )
  );
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const checkedIsIframe = window !== window.top;
    const hasAccessToken = !!new URLSearchParams(
      window.location.hash.substring(1)
    ).get('access_token');
    const code = new URLSearchParams(window.location.search).get('code');

    setIsInIframe(checkedIsIframe);
    setIsAuthorized(hasAccessToken);
    if (checkedIsIframe || hasAccessToken) {
      // The app is running inside an iframe
      getPlatformClientData();
    } else if (code) {
      // If we has url Params "code" then we need to get the access token
      console.log('code', code);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getPlatformClientData = async () => {
    setInfoRequested(true);
    setLoading(true);
    await authenticate(gcClientId, gcRedirect)
      .then((data: any) => {
        return getUserMe();
      })
      .then((data: any) => {
        setMeInfo(data);
      })
      .finally(() => {
        setLoading(false);
      });
  };
  const handleButtonClick = () => {
    getPlatformClientData();
  };

  if (loading) {
    return (
      <Panel
        containerClassName="h-full"
        loading
      />
    );
  }

  return (
    <>
      {!isInIframe && !infoRequested && !isAuthorized && (
        <Panel containerClassName="h-full">
          <div className="p-6">
            <h2 className="font-bold text-t6 mb-6">
              You need to login to GC to see the information.
            </h2>
            {/* <div>
                            client ID: {gcClientId} | redirect: {gcRedirect} | redirectUri: {redirectUri}
                        </div> */}
            <Button onClick={() => handleButtonClick()}>Login</Button>
          </div>
        </Panel>
      )}
      {isAuthorized && infoRequested && (
        <MsgForm
          infoRequested={infoRequested}
          meInfo={meInfo}
          loading={loading}
        />
      )}
    </>
  );
};

export default GCAuth;
