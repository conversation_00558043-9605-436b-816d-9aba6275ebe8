// Obtain a reference to the platformClient object
import { useMemo, useState } from 'react';
import PHONE_CODES from '@cdss-modules/design-system/lib/constants/phoneCodes';
import Loader from '@cdss-modules/design-system/components/_ui/Loader';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import Avatar from '@cdss-modules/design-system/components/_ui/Avatar';
import Panel from '@cdss-modules/design-system/components/_ui/Panel';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import {
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle,
} from '@cdss-modules/design-system/components/_ui/Resizable';

import { Reply } from 'lucide-react';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { DevBlock, toast } from '@cdss-modules/design-system';
import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import dayjs from 'dayjs';

// Helper functions
// TODO: move to helper
function extractVariableIndices(htmlText: string): number[] {
  if (!htmlText) return [];
  // Regular expression to find occurrences of {{number}}
  const regex = /{{(\d+)}}/g;
  let match: RegExpExecArray | null;
  const indices: number[] = [];

  // Continue to apply the regex until no more matches are found
  while ((match = regex.exec(htmlText)) !== null) {
    // Convert the captured group to a number and add to the array
    const index = parseInt(match[1], 10);
    indices.push(index);
  }

  return indices;
}
function displayTemplateText(
  templateText: string,
  watch: (variableName: string) => string
): string {
  // Regular expression to match placeholders like {{0}} to {{9}}
  const regex = /{{(\d+)}}/g;

  // Replace each match in the templateText
  const resultText = templateText.replace(regex, (match, index) => {
    // Call the watch function with 'v' followed by the index found
    const dv = watch(`v${index}`);
    return dv ? dv : match;
  });

  return resultText;
}

const schema = yup
  .object({
    fromNo: yup.string().required('This field is required'),
    toCustomerPhoneCode: yup.string().required('This field is required'),
    toCustomer: yup
      .number()
      .typeError('Invalid number')
      .required('This field is required'),
    template: yup.string().required('This field is required'),
    v0: yup.string(),
    v1: yup.string(),
    v2: yup.string(),
    v3: yup.string(),
    v4: yup.string(),
    v5: yup.string(),
    v6: yup.string(),
    v7: yup.string(),
    v8: yup.string(),
    v9: yup.string(),
  })
  .required();

const USER_SESSION_ID = '7af15803ecc8a5c9ee808d8a643d9171edc37e0f';
const ENDPOINT = 'https://whatsappuat.sunmobile.com.hk/api';
const FROM_NO_LIST = [
  {
    id: 'from-no-15550909758',
    label: '+852 55415667',
    value: '85255415667',
  },
];
const DEFAULT_FROM_NO = FROM_NO_LIST?.[0]?.value;

export type TMsgFormProps = {
  infoRequested?: boolean;
  meInfo?: any;
  loading?: boolean;
};

export type TVarNumber = 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9;

export const MsgForm = ({ infoRequested, meInfo, loading }: TMsgFormProps) => {
  const [isSending, setIsSending] = useState(false);
  const [varsError, setVarsError] = useState('');

  // Form
  const {
    handleSubmit,
    control,
    formState: { errors },
    watch,
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      fromNo: DEFAULT_FROM_NO,
      toCustomerPhoneCode: '852',
      template: '',
    },
  });

  const template = watch('template');
  const fromNo = watch('fromNo');
  const toCustomerPhoneCode = watch('toCustomerPhoneCode');
  const isMock = false;

  // Fetching Template
  const {
    data: templateData,
    isLoading: loadingTemplate,
    refetch,
  } = useQuery({
    queryKey: ['template', USER_SESSION_ID, fromNo, isMock],
    queryFn: async () => {
      const result = axios
        .get(
          isMock
            ? `/api/mock/msg/template`
            : `${ENDPOINT}/cpws/chatProxies/messagetemplatelist`,
          {
            // const result = axios.get(`/api/mock/msg/template`, {
            headers: {
              userSessionId: USER_SESSION_ID,
              Authorization: `Basic testingapikey12345`,
              templatePermission: 'AllowAgent',
              searchDict: `{"isActive":true,"isOffline":true,"hasRealTemplateId":true,"channelType":"whatsapp","channelAccountNumber":"${fromNo ?? DEFAULT_FROM_NO}"}`,
            },
          }
        )
        .then((res) => res.data?.data?.[0]);
      return result;
    },
  });

  const activeTemplate = useMemo(() => {
    return {
      ...JSON.parse(
        templateData?.find((item: any) => item.pkey === template)
          ?.messageJsonHtml ?? '{}'
      ),
      // ...JSON.parse("[{\"body\":{\"text\":\"<p>test {{0}}, {{1}} test {{2}} {{3}} {{4}} test</p>\\n\"},\"option\":{\"type\":\"BUTTON\",\"list\":[{\"id\":0,\"type\":\"QUICK_REPLY\",\"text\":\"想了解更多呢個優惠詳情\",\"content\":\"想了解更多呢個優惠詳情\"},{\"id\":1,\"type\":\"QUICK_REPLY\",\"text\":\"想了解其他優惠\",\"content\":\"想了解其他優惠\"},{\"id\":2,\"type\":\"QUICK_REPLY\",\"text\":\"不想再收到此通知訊息\",\"content\":\"不想再收到此通知訊息\"}]},\"header\":{\"text\":null},\"image\":{\"fileName\":null},\"language\":\"en\"}]"),
    };
  }, [template, templateData]);
  const vars = extractVariableIndices(
    activeTemplate?.[0]?.body?.text
  ) as TVarNumber[];

  // On submit
  const onSubmit = (data: any) => {
    setVarsError('');
    let hasVarsError = false;
    vars.forEach((v) => {
      if (!data[`v${v}`]) {
        setVarsError('All variables are required');
        hasVarsError = true;
      }
    });
    if (hasVarsError) {
      toast({
        variant: 'error',
        title: 'All variables are required',
        description: `Please fill in all the variables.`,
      });
      return;
    }
    setIsSending(true);

    const parameters = vars.map((v) => data[`v${v}`]);

    const dataJSON = {
      customerList: [
        {
          phoneNumber: `${toCustomerPhoneCode}${data?.toCustomer}`,
          parameters,
        },
      ],
      templateId: template,
      senderDetails: {
        senderName: meInfo?.name ?? 'unknown',
        senderId: meInfo?.id ?? 'unknown',
        senderEmail: meInfo?.email ?? 'unknown',
        isUseSenderAsPreferredAgent: true,
      },
    };
    axios
      .post(
        `${ENDPOINT}/cpws/channel/${data?.fromNo}/sendTemplatesToCustomerList`,
        dataJSON,
        {
          headers: {
            Authorization: `Basic testingapikey12345`,
          },
        }
      )
      .then(() => {
        toast({
          title: 'Success',
          description: `Single message to +${toCustomerPhoneCode} ${data?.toCustomer} request has been submitted`,
        });
      })
      .catch((e) => {
        toast({
          title: 'Success',
          description: `Single message request failed with error: ${e?.message}`,
        });
      })
      .finally(() => {
        setIsSending(false);
      });
  };
  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="h-full"
    >
      <ResizablePanelGroup
        autoSaveId="example"
        direction="horizontal"
        className="flex-1 flex w-full h-0 gap-x-3 overflow-auto"
      >
        <ResizablePanel minSize={30}>
          <Panel
            loading={loadingTemplate}
            containerClassName="h-full"
          >
            <div className="h-full px-3 py-6 flex flex-col gap-y-6">
              <h2 className="font-bold text-t6 px-3">Send single message</h2>
              <div className="flex-1 h-0 overflow-auto space-y-4 px-3">
                <div>
                  <Field
                    title="From Whatsapp No.* "
                    icon={<Icon name="error" />}
                    status={errors?.fromNo?.message ? 'danger' : undefined}
                    message={errors?.fromNo?.message}
                  >
                    <Controller
                      name="fromNo"
                      control={control}
                      rules={{ required: true }}
                      render={({ field }) => (
                        <Select
                          mode="single"
                          options={FROM_NO_LIST}
                          showSearch={true}
                          status={
                            errors?.fromNo?.message ? 'danger' : undefined
                          }
                          {...field}
                          value={fromNo}
                        />
                      )}
                    />
                  </Field>
                </div>
                <div className="@container">
                  <Field
                    title="To Customer Mobile No.*"
                    icon={<Icon name="error" />}
                    status={
                      errors?.toCustomerPhoneCode?.message ??
                      errors?.toCustomer?.message
                        ? 'danger'
                        : undefined
                    }
                    message={
                      errors?.toCustomerPhoneCode?.message ??
                      errors?.toCustomer?.message
                    }
                  >
                    <div className="flex gap-2 flex-col @[450px]:flex-row">
                      <div className="flex-none w-full @[450px]:w-[300px]">
                        <Controller
                          name="fromNo"
                          control={control}
                          rules={{ required: true }}
                          render={({ field }) => (
                            <Select
                              placeholder="Country Code"
                              mode="single"
                              options={PHONE_CODES.map((item) => ({
                                id: `from-no-${item.code}`,
                                label: `${item.emoji} ${item.dial_code} (${item.name})`,
                                value: item?.dial_code?.replace('+', '') ?? '',
                              }))}
                              showSearch={true}
                              status={
                                errors?.fromNo?.message ? 'danger' : undefined
                              }
                              {...field}
                              value={toCustomerPhoneCode}
                            />
                          )}
                        />
                      </div>
                      <div className="flex-1">
                        <Controller
                          name="toCustomer"
                          control={control}
                          rules={{ required: true }}
                          render={({ field }) => (
                            <Input
                              type="number"
                              isSearch={true}
                              placeholder="Input a Customer's number"
                              allowClear
                              {...field}
                            />
                          )}
                        />
                      </div>
                    </div>
                  </Field>
                  <div className="mt-4">
                    <Field
                      title="Template"
                      icon={<Icon name="error" />}
                      status={errors?.template?.message ? 'danger' : undefined}
                      message={errors?.template?.message}
                    >
                      <Controller
                        name="template"
                        control={control}
                        rules={{ required: true }}
                        render={({ field }) => (
                          <Select
                            placeholder="Select a template"
                            mode="single"
                            options={
                              templateData
                                ?.filter(
                                  (item: any) => item?.status === 'ACTIVE'
                                )
                                ?.map((item: any) => ({
                                  id: item.pkey,
                                  label: item.name,
                                  value: item.pkey,
                                })) ?? []
                            }
                            showSearch={true}
                            status={
                              errors?.template?.message ? 'danger' : undefined
                            }
                            {...field}
                          />
                        )}
                      />
                    </Field>
                  </div>
                </div>
                {vars && vars?.length > 0 && (
                  <Field
                    title="Variables"
                    icon={<Icon name="error" />}
                    status={varsError ? 'danger' : undefined}
                    message={varsError}
                  >
                    <div className="space-y-2">
                      {vars?.map((v) => (
                        <div key={`vars-${v}`}>
                          <Controller
                            name={`v${v}`}
                            control={control}
                            rules={{ required: true }}
                            render={({ field }) => (
                              <Input
                                status={'danger'}
                                type="text"
                                placeholder={`Variable {{${v}}}`}
                                allowClear
                                {...field}
                              />
                            )}
                          />
                        </div>
                      ))}
                    </div>
                  </Field>
                )}
                {infoRequested && (
                  <DevBlock show={false}>
                    {loading ? (
                      <div className="w-full h-full flex items-center justify-center py-12">
                        <Loader size={64} />
                      </div>
                    ) : (
                      <>
                        <Button
                          type="button"
                          onClick={() => refetch()}
                        >
                          Test
                        </Button>
                        {JSON.stringify(templateData)}
                        {/* <h2 className="font-bold text-t6 mb-6">
                                                    Agent Info
                                                </h2>
                                                <div className="mb-4 flex gap-4">
                                                    <div className='font-bold'>Agent ID: </div>
                                                    <div>{meInfo?.id ?? ''}</div>
                                                </div>
                                                <div className="mb-4 flex gap-4">
                                                    <div className='font-bold'>Agent Email: </div>
                                                    <div>{meInfo?.email ?? ''}</div>
                                                </div>
                                                <div className="mb-4 flex gap-4">
                                                    <div className='font-bold'>Agent Name: </div>
                                                    <div>{meInfo?.name ?? ''}</div>
                                                </div> */}
                      </>
                    )}
                  </DevBlock>
                )}
              </div>
            </div>
          </Panel>
        </ResizablePanel>
        <ResizableHandle />
        <ResizablePanel minSize={30}>
          <Panel containerClassName="h-full">
            <div className="p-6 h-full flex flex-col">
              <h2 className="font-bold text-t6 mb-6">Preview Template</h2>
              <div className="flex-1 bg-[#ECE5DD] rounded-lg overflow-hidden">
                <div className="px-4 py-3 flex items-center bg-[#075E54] text-white">
                  <div className="flex items-center gap-x-4">
                    <Avatar
                      text={'S'}
                      className="bg-white"
                    />
                    <h3 className="font-bold">Sun Mobile</h3>
                  </div>
                </div>
                <div className="p-4 flex flex-col gap-y-4">
                  <div className="relative flex justify-center">
                    <div className="flex items-end p-2 bg-white rounded-lg">
                      Today
                    </div>
                  </div>
                  <div className="relative flex items-start">
                    <Icon name="whatsapp-cornner" />
                    <div className="relative flex flex-col bg-white rounded-b-lg rounded-tr-lg max-w-[70%]">
                      <div className="relative flex flex-col p-2">
                        <div className="flex-1">
                          {activeTemplate?.[0]?.header?.text && (
                            <h3 className="text-t6 mb-2 font-bold">
                              {activeTemplate?.[0]?.header?.text}
                            </h3>
                          )}
                          {template ? (
                            <div
                              dangerouslySetInnerHTML={{
                                __html: displayTemplateText(
                                  activeTemplate?.[0]?.body?.text ?? '',
                                  watch
                                ),
                              }}
                            />
                          ) : (
                            '(Please select a template to see the preview)'
                          )}
                          <div className="inline-flex h-2 w-16" />
                        </div>
                        <div className="absolute right-2 bottom-2 text-footnote text-grey-400 -mb-1">
                          {dayjs().format('hh:mm A')}
                        </div>
                      </div>
                      {activeTemplate?.[0]?.option &&
                        activeTemplate?.[0]?.option?.type === 'BUTTON' && (
                          <div className="relative w-full text-center">
                            {activeTemplate?.[0]?.option?.list?.map(
                              (item: any) => (
                                <div
                                  key={`msg-preview-button-${item?.text}`}
                                  className="flex items-center gap-x-2 py-2 px-6 relative w-full text-blue-400 border-t border-grey-400"
                                >
                                  <Reply className="size-4 inline-block" />
                                  {item?.text}
                                </div>
                              )
                            )}
                          </div>
                        )}
                    </div>
                  </div>
                </div>
              </div>
              <div className="mt-6 flex justify-end">
                <Button
                  fullWidth
                  type="submit"
                  disabled={isSending}
                >
                  {isSending ? 'Sending...' : 'Submit Message'}
                </Button>
              </div>
            </div>
          </Panel>
        </ResizablePanel>
      </ResizablePanelGroup>
    </form>
  );
};

export default MsgForm;
