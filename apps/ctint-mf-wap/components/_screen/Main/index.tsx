// import Image from "next/image";
import { <PERSON>th<PERSON><PERSON><PERSON> } from '@cdss-modules/design-system/components/_ui/AuthChecker';
import { Panel } from '@cdss-modules/design-system/components/_ui/Panel';
import GCAuth from '../../_features/GCAuth';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from '@cdss-modules/design-system';

// Create a client
const queryClient = new QueryClient();

export function Main({ roleContext, gcClientId, gcRedirect }: { roleContext?: any, gcClientId?: string, gcRedirect?: string }) {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthChecker
        // requiredPemissions={{
        //   global: {
        //     portals: ['ctint-mf-wap'],
        //   },
        //   user: {
        //     permissions: ['ctint-mf-wap.application.visit']
        //   }
        // }}
        unAuthorizedComponent={
          <Panel>
            <h2 className="p-6 font-bold text-t6">
              You are unauthorized to use this feature.
            </h2>
          </Panel>
        }
      >
        <GCAuth gcClientId={gcClientId} gcRedirect={gcRedirect} />
        <Toaster />
      </AuthChecker>
    </QueryClientProvider>
  );
}

export default Main;
