
// eslint-disable-next-line @typescript-eslint/no-var-requires
const { composePlugins, withNx } = require('@nx/next');
const { NextFederationPlugin } = require('@module-federation/nextjs-mf');
const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');

// Config
const mfName = 'ctint-mf-info';
const mfEnv = process?.env?.CDSS_PUBLIC_ENVIRONMENT || 'dev';
const yamlEnv = mfEnv === 'local' ? 'dev' : mfEnv;
let yamlPath = path.join(
  process.cwd(),
  `apps/${mfName}/public/config/ctint-global-config-${yamlEnv}.yaml`
);
if (!fs.existsSync(yamlPath)) {
  yamlPath = path.join(
    process.cwd(),
    `public/config/ctint-global-config-${yamlEnv}.yaml`
  );
  if (!fs.existsSync(yamlPath)) {
    throw new Error(`Configuration file not found: ${yamlPath}`);
  }
}
const fileContents = fs.readFileSync(yamlPath, 'utf8');
const configData = yaml.load(fileContents);
// Get MF paths (Host, Base Path or Full URL) from config
const getMFPath = (targetMFName) => {
  const mfHost = configData?.microfrontends?.[targetMFName]?.host || '';
  const mfBasePath = configData?.microfrontends?.[targetMFName]?.basepath || '';
  return {
    host: mfHost,
    basePath: mfBasePath,
    url: mfHost + mfBasePath,
  };
}
/**
 * @type {import('@nx/next/plugins/with-nx').WithNxOptions}
 **/
const nextConfig = {
  basePath: getMFPath(mfName).basePath,
  env: {
    basePath: getMFPath(mfName).basePath,
  },
  swcMinify: false,
  nx: {
    // Set this to true if you would like to use SVGR
    // See: https://github.com/gregberge/svgr
    svgr: false,
  },
  experimental: { esmExternals: 'loose' },
  /**
   *
   * @param {import('webpack').Configuration} config
   * @returns {import('webpack').Configuration}
   */
  webpack(config) {
    config.output.publicPath = 'auto';
    config.plugins.push(
      new NextFederationPlugin({
        name: 'wap',
        filename: 'static/chunks/remoteEntry.js',
        remotes: {},
        extraOptions: {
          automaticAsyncBoundary: true,
        },
        exposes: {
          './module': './components/_screen/Main/index.tsx',
        },
        shared: {
          'react-singleton-context': { singleton: true, eager: true },
          'react-router-dom': { singleton: true, eager: true },
          'react-i18next': { singleton: true, eager: true },
          'i18next': { singleton: true, eager: true },
        },
      })
    );

    return config;
  },
  // Add rewrites
  async rewrites() {
    const services = configData?.services ?? {};
    const servicesRewrites = Object.keys(services).map((key) => {
      const serviceHost = services?.[key]?.host ?? '';
      const serviceBasePath = services?.[key]?.basepath ?? '';
      return {
        source: `/process-api${serviceBasePath}/:path*`,
        destination: `${serviceHost}${serviceBasePath}/:path*`,
      };
    });
    return [
      {
        source: '/api/session/:path*',
        destination: `/api/mock/:path*`,
      },
      // Process API Rewrites
      ...servicesRewrites,
      // Redirect all paths to root for SPA handling
      {
        source: `/:path((?!api|process-api).*)`,
        destination: '/'
      }
    ];
  },
};

const plugins = [
  // Add more Next.js plugins to this list if needed.
  withNx,
];

module.exports = composePlugins(...plugins)(nextConfig);
