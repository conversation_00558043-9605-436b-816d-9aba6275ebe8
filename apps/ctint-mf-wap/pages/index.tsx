

import { <PERSON><PERSON>enderer } from '@cdss-modules/design-system';
import Main from '../components/_screen/Main';
import { basePath } from '../lib/appConfig';

export const Page = () => {
  return (
    <div className="p-6 w-full h-screen">
      <PageRenderer routes={[
        {
          path: '/',
          group: 'playback',
          component: <Main gcClientId={process.env.NEXT_PUBLIC_MSG_GC_CLIENT_ID} gcRedirect={`${process.env.NEXT_PUBLIC_MSG_URL}`} />,
        }
      ]} basePath={basePath} />
    </div>
  );
}

export const getServerSideProps = (async () => {
  const publicEnvVars = Object.keys(process.env).reduce((publicVars: any, key) => {
    if (key.startsWith('CDSS_PUBLIC_')) {
      publicVars[key] = process.env[key];
    }
    return publicVars;
  }, {}) as any;
  return {
    props: {
      publicEnvVars
    }
  }
});

export default Page;
