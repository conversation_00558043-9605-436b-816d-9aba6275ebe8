apiVersion: apps/v1
kind: Deployment
metadata:
  name: ctint-mf-content-creation-deployment
  namespace: cdss-frontend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ctint-mf-content-creation
  template:
    metadata:
      labels:
        app: ctint-mf-content-creation
    spec:
      containers:
      - name: ctint-mf-content-creation
        image: cdss3uatacr.azurecr.io/ctint-mf-content-creation:1
        ports:
          - name: http
            containerPort: 3000
            protocol: TCP
        env:
        - name: CDSS_PUBLIC_ENVIRONMENT
          value: uat
        - name: GLOBAL_CONFIG_FILE
          value: ctint-global-config-uat.yaml
        volumeMounts:
        - name: ctint-global-config
          mountPath: /app/orign_dist/apps/ctint-mf-interaction/public/config
      volumes:  
      - name: ctint-global-config
        azureFile:
          secretName: ctintcdss3uatakssa-share
          shareName: ctint-cdss-globalconfig
          readOnly: false