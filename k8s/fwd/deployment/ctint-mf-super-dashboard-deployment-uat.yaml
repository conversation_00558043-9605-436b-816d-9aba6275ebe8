apiVersion: apps/v1
kind: Deployment
metadata:
  name: ctint-mf-super-dashboard-deployment
  namespace: ctint-fwd
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ctint-mf-super-dashboard
  template:
    metadata:
      labels:
        app: ctint-mf-super-dashboard
    spec:
      containers:
      - name: ctint-mf-super-dashboard
        image: cdss3projectdevacr.azurecr.io/ctint-mf-super-dashboard:1
        ports:
          - name: http
            containerPort: 3000
            protocol: TCP
        env:
        - name: CDSS_PUBLIC_ENVIRONMENT
          value: uat
        - name: GLOBAL_CONFIG_FILE
          value: fwd-global-config-uat.yaml
        volumeMounts:
        - name: ctint-global-config
          mountPath: /app/orign_dist/apps/ctint-mf-super-dashboard/public/config
      volumes:  
      - name: ctint-global-config
        azureFile:
          secretName: ctintcdss3projectakssa-share
          shareName: fwd/ctint-cdss-globalconfig
          readOnly: false