apiVersion: apps/v1
kind: Deployment
metadata:
  name: ctint-mf-cdss-deployment
  namespace: ctint-qhms
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ctint-mf-cdss
  template:
    metadata:
      labels:
        app: ctint-mf-cdss
    spec:
      containers:
      - name: ctint-mf-cdss
        image: cdss3projectdevacr.azurecr.io/ctint-mf-cdss:1.0.4206
        ports:
          - name: http
            containerPort: 3000
            protocol: TCP
        env:
        - name: CDSS_PUBLIC_ENVIRONMENT
          value: uat
        - name: GLOBAL_CONFIG_FILE
          value: qhms-global-config-uat.yaml
        volumeMounts:
        - name: ctint-global-config
          mountPath: /app/orign_dist/apps/ctint-mf-cdss/public/config
      volumes:
      - name: ctint-global-config
        azureFile:
          secretName: ctintcdss3projectakssa-share
          shareName: qhms/ctint-cdss-globalconfig
          readOnly: false
