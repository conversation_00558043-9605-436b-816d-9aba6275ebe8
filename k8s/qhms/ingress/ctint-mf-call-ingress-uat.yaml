apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ctint-mf-call-ingress
  namespace: ctint-qhms
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  rules:
    - http:
        paths:
          - path: /qhms/mf-call(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: ctint-mf-call-service
                port:
                  number: 4000
