apiVersion: apps/v1
kind: Deployment
metadata:
  name: ctint-mf-call-deployment
  namespace: ctint-qhms-phase2
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ctint-mf-call
  template:
    metadata:
      labels:
        app: ctint-mf-call
    spec:
      containers:
      - name: ctint-mf-call
        image: cdss3projectdevacr.azurecr.io/ctint-mf-call:1
        ports:
          - name: http
            containerPort: 3000
            protocol: TCP
        env:
        - name: CDSS_PUBLIC_ENVIRONMENT
          value: uat
        - name: GLOBAL_CONFIG_FILE
          value: qhms-global-config-uat.yaml
        volumeMounts:
        - name: ctint-global-config
          mountPath: /app/orign_dist/apps/ctint-mf-call/public/config
      volumes:
      - name: ctint-global-config
        azureFile:
          secretName: ctintcdss3projectakssa-share
          shareName: qhms2/ctint-cdss-globalconfig
          readOnly: false
