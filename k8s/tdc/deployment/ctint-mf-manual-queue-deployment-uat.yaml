apiVersion: apps/v1
kind: Deployment
metadata:
  name: ctint-mf-manual-queue-deployment
  namespace: ctint-tdc
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ctint-mf-manual-queue
  template:
    metadata:
      labels:
        app: ctint-mf-manual-queue
    spec:
      containers:
      - name: ctint-mf-manual-queue
        image: cdss3projectdevacr.azurecr.io/tdc-mf-manual-queue:1
        ports:
          - name: http
            containerPort: 3000
            protocol: TCP
        env:
        - name: CDSS_PUBLIC_ENVIRONMENT
          value: uat
        - name: GLOBAL_CONFIG_FILE
          value: tdc-global-config-uat.yaml
        volumeMounts:
        - name: ctint-global-config
          mountPath: /app/orign_dist/apps/ctint-mf-manual-queue/public/config
      volumes:
      - name: ctint-global-config
        azureFile:
          secretName: ctintcdss3projectakssa-share
          shareName: tdc/ctint-cdss-globalconfig
          readOnly: false
