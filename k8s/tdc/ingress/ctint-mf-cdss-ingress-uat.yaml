apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ctint-mf-cdss-ingress
  namespace: ctint-tdc
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  rules:
    - http:
        paths:
          - path: /tdc/mf-cdss(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: ctint-mf-cdss-service
                port:
                  number: 4000
