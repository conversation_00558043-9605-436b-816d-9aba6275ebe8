{"name": "cdss-ui", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/cdss-ui/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/rollup:rollup", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/cdss-ui", "tsConfig": "libs/cdss-ui/tsconfig.lib.json", "project": "libs/cdss-ui/package.json", "entryFile": "libs/cdss-ui/src/index.ts", "external": ["react", "react-dom", "react/jsx-runtime"], "rollupConfig": "@nx/react/plugins/bundle-rollup", "compiler": "swc", "assets": [{"glob": "libs/cdss-ui/README.md", "input": ".", "output": "."}]}}, "nx-release-publish": {"options": {"packageRoot": "dist/libs/cdss-ui"}}}}