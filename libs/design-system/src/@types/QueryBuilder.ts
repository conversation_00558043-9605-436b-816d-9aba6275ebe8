type FilterCondition = {
  type: string; // 过滤类型，例如 "and"、"or"
  name: string; // 过滤字段名
  value: string; // 过滤字段的值
  rule?: string; // 过滤规则，例如 "equal"、"notEqual"
};

type PaginationOptions = {
  pageSize: number;
  page: number;
};

type OrderingOptions = {
  order: 'asc' | 'desc';
  orderBy: string;
};

class QueryBuilder {
  private queryData: {
    conversationStart: string;
    conversationEnd?: string;
    conversationId?: string;
    pageSize: number;
    page: number;
    order: 'asc' | 'desc';
    orderBy?: string;
    conversationFilter: FilterCondition[];
  };

  constructor(conversationStart: string, pageSize = 10, page = 1) {
    this.queryData = {
      conversationStart: conversationStart,
      conversationEnd: '',
      conversationId: '',
      pageSize: pageSize,
      page: page,
      order: 'asc',
      conversationFilter: [],
    };
  }

  // 设置过滤条件
  setFilters(
    id: string,
    start: string,
    end: string,
    filters: FilterCondition[]
  ): this {
    this.queryData.conversationStart = start;
    this.queryData.conversationEnd = end;
    this.queryData.conversationId = id;
    this.queryData.conversationFilter = filters;
    return this;
  }

  // 设置分页
  setPagination({ pageSize, page }: PaginationOptions): this {
    this.queryData.pageSize = pageSize;
    this.queryData.page = page;
    return this;
  }

  // 设置排序
  setOrdering({ order, orderBy }: OrderingOptions): this {
    this.queryData.order = order;
    this.queryData.orderBy = orderBy;
    return this;
  }

  // 构建最终查询对象
  build(): typeof this.queryData {
    return this.queryData;
  }
}

export default QueryBuilder;
