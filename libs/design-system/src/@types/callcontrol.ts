export type TCallControlType =
  | 'hold'
  | 'unhold'
  | 'mute'
  | 'unmute'
  | 'pickup'
  | 'disconnect';

export interface MakeCallRequestBody {
  phoneNumber?: string;
  callUserId?: string;
  callQueueId?: string;
}

export interface CallControlRequestBody {
  type: TCallControlType;
  conversationId: string;
  agentParticipantId: string;
}
export interface CallBlockingResponse {
  conversationId: string;
  isBlocking: boolean;
  blockingRules: {
    needCheckFlag: string;
    approvalLogId: string;
    blockedPPSList: string;
    callSummary: {
      equalToZero: string;
      greaterThan15Sec: string;
      lessThan15Sec: string;
      numOfTimes: string;
    };
    callSummaryShortCodes: string;
    isDisplayCallBlockingResultOnly: string;
    isShowCallSummary: string;
    hitCallBlockingRuleIDList: string;
    displayRuleList: DisplayRuleItem[];
    superList: string[];
  };
}
export interface DisplayRuleItem {
  ruleId: string;
  userDisplay: string;
  checked?: boolean;
}
