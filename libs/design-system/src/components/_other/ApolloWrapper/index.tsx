'use client';
import React from 'react';
// ^ this file needs the "use client" pragma

import { ApolloClient, InMemoryCache, ApolloProvider } from '@apollo/client';
import { Toaster } from '../../_ui/Toast/toaster';

const client = new ApolloClient({
  uri: `/proxy/graphql`,
  cache: new InMemoryCache(),
});

// you need to create a component to wrap your app in
export function ApolloWrapper({ children }: React.PropsWithChildren) {
  return (
    <ApolloProvider client={client}>
      {children}
      <Toaster />
    </ApolloProvider>
  );
}

export default ApolloWrapper;
