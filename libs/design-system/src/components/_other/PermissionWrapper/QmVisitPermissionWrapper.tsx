import { QmVisitPermission } from '../../../@types/QmVisitPermission';
import { TglobalConfig } from '../../../context/RoleContext';
import { BasicPermissionWrapper } from './BasicPermissionWrapper';

interface QmVisitPermissionWrapperProps {
  customerPermissionHandler?: (
    globalConfig: TglobalConfig,
    permissions: string[]
  ) => boolean;
  children: React.ReactNode;
}

export const QmVisitPermissionWrapper: React.FC<
  QmVisitPermissionWrapperProps
> = ({ customerPermissionHandler, children }) => {
  const effectivePermissionHandler =
    customerPermissionHandler ||
    ((globalConfig, permissions) => {
      return new QmVisitPermission(
        globalConfig,
        permissions
      ).isPermissionEnabled('ctint-mf-interaction', 'qm', 'visit');
    });

  return (
    <BasicPermissionWrapper permissionValidation={effectivePermissionHandler}>
      {children}
    </BasicPermissionWrapper>
  );
};
