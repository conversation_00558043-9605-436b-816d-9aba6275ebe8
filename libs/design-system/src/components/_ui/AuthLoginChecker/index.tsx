'use client';
import { useEffect, useState } from 'react';
import Loader from '../Loader';
import { useRouter } from 'next/router';

export type TAuthLoginCheckerProps = {
  children: React.ReactNode;
};

export const AuthLoginChecker = ({ children }: TAuthLoginCheckerProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const checkAuth = async () => {
      const result = localStorage.getItem('cdss-auth-token');
      setIsAuthorized(!!result);
      setIsLoading(false);
    };
    checkAuth();
  }, []);

  if (isLoading) {
    return (
      <div className="w-full h-screen flex items-center justify-center py-12">
        <Loader size={90} />
      </div>
    );
  }

  if (!isAuthorized) {
    router.push(`/login?redirect=${encodeURIComponent(router.asPath)}`);
    return null;
  }

  return <>{children}</>;
};

export default AuthLoginChecker;
