import { cn } from '../../../lib/utils';
import CDSSImage from '../CDSSImage';

export type TAvatarProps = {
  src?: string;
  text?: string;
  textClassName?: string;
  icon?: React.ReactNode;
  children?: React.ReactNode;
  selected?: boolean;
};

const Avatar: React.FC<TAvatarProps & React.HTMLAttributes<HTMLDivElement>> = ({
  src,
  text,
  icon,
  children,
  selected,
  textClassName,
  ...props
}) => {
  return (
    <div
      className={cn(
        `aspect-square w-12 bg-primary-100 rounded-full flex items-center justify-center overflow-hidden`,
        selected && `bg-primary-500 shadow-contact-item`,
        props?.className
      )}
    >
      {(src && (
        <CDSSImage
          alt=""
          src={src}
          fill
        />
      )) ||
        icon ||
        (text && (
          <span
            className={cn(
              'uppercase text-t5 text-status-success',
              textClassName
            )}
          >
            {text}
          </span>
        )) ||
        children}
    </div>
  );
};

export default Avatar;
