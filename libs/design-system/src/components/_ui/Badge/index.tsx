import { cn } from '../../../lib/utils';

type BadgeProps = {
  count?: number;
};

const Badge: React.FC<BadgeProps & React.HTMLAttributes<HTMLDivElement>> = ({
  count,
  children,
  ...props
}: any) => {
  return (
    <div className="relative">
      {(count && (
        <span
          className={cn(
            `absolute w-5 h-5 flex justify-center items-center rounded-full -right-1 top-0 bg-status-danger !text-common-white text-mini shadow-dot`,
            props?.className
          )}
        >
          {count}
        </span>
      )) || (
        <span
          className={cn(
            `absolute w-2.5 h-2.5 rounded-full right-0 top-0 bg-status-danger shadow-dot`,
            props?.className
          )}
        />
      )}
      {children}
    </div>
  );
};

export default Badge;
