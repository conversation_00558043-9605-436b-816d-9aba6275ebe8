import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, toast, use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@cdss-modules/design-system"
import { useOpenBlockingContext } from "@cdss-modules/design-system/context/BlockingContext"
import { extractErrorMessage } from "@cdss-modules/design-system/lib/utils"
import { fireBlocking } from "@cdss/lib/api"
import React from "react"

type TPopup = {
    openPopup: boolean,
    onChang: (b: boolean) => void
    blockingList: any[]
}
export const PopupModel = React.memo((props: TPopup) => {
    const { onChang, openPopup, blockingList } = props
    return (
        <Popup
            open={openPopup}
            onOpenChange={onChang}
        >
            <PopupContent
            >
                <PopupTitle />
                <div className="p-4  overflow-y-auto">
                    <p>Contact is prohibited because the number is on the DNC list. If you believe this is an error, please contact a supervisor.</p>
                    <div className="flex">
                        <span className="text-[#ccc] pr-1">Invalid:</span>
                        {
                            blockingList?.map((b: any) => {
                                return b?.isBlocked ? <span className="pr-2 text-[#ccc]" key={b?.destination}>{b?.destination}</span> : <></>
                            })
                        }
                    </div>
                </div>
                <PopupFooter>
                    <Button
                        className="py-4"
                        onClick={() => {
                            onChang(!openPopup);
                        }}
                    >
                        Okay
                    </Button>
                </PopupFooter>
            </PopupContent>
        </Popup>
    )
})

export const handleBlocking = async (ContactValue: string, contactType: string, basePath: any) => {
    try {
        let body: any = {};
        if (ContactValue) {
            body = { contactValue: ContactValue, contactType: contactType }
        }
        const res = await fireBlocking(body, basePath);
        return res
    } catch (error) {
        toast({
            variant: 'error',
            title: 'Error refreshing the web',
            description: `These is an error: ${extractErrorMessage(error)}. If problem persists, please re-login.`,
        });
        return null
    }
}
export const useBlocking = () => {
    const { basePath } = useRouteHandler();
    const { isBlocked, open, setBlockingOpen, setBlockingList } = useOpenBlockingContext()
    const handleTriggerBlocking = (ContactValue: any, contactType: string) => {
        return handleBlocking(ContactValue, contactType, basePath)?.then((res) => {
            if (res?.data?.isSuccess && res?.data?.data?.length > 0) {
                setBlockingList(res?.data?.data?.filter((b: any) => b?.isBlocked) || [])
                // 使用 some() 找到第一个符合条件的项后立即返回
                const hasBlocked = res?.data?.data?.some((item: any) => {
                    if (item?.direction === 'outbound' && item?.isBlocked) {
                        isBlocked.current = true;
                        setBlockingOpen(!open)
                        return true // 找到即停止遍历
                    }
                    return false
                })
                return hasBlocked
            } else {
                return isBlocked.current
            }
        })
    }
    return {
        handleTriggerBlocking
    }
}