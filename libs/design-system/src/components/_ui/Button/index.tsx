// import React from 'react';
// import Link, { LinkProps } from 'next/link';
// import { cn } from '../../../lib/utils';
// import { VariantProps, cva } from 'class-variance-authority';

// export const buttonVariants = cva(
//   'relative z-20 flex items-center justify-center rounded-[4px] transition',
//   {
//     variants: {
//       variant: {
//         primary:
//           'text-white bg-black group-hover/btn:shadow-button-primary border-black border',
//         secondary:
//           'border border-black bg-white group-hover/btn:shadow-button-secondary',
//         blank:
//           'text-black bg-transparent group-hover/btn:bg-white group-hover/btn:shadow-button-blank',
//         back: 'text-black bg-white hover:bg-gray-100 active:bg-gray-200',
//         orange: 'text-white bg-primary group-hover/btn:shadow-button-black',
//         green: 'text-white bg-green-500 group-hover/btn:bg-green-600',
//       },
//       size: {
//         xs: 'py-1 px-2 text-mini min-w-[60px]',
//         s: 'py-1 px-2 text-sm min-w-[75px]',
//         m: 'py-2 px-2 text-base min-w-[80px]',
//         l: 'py-3 px-4 text-lg min-w-[100px]',
//         mini: 'p-2',
//       },
//     },
//     defaultVariants: {
//       variant: 'primary',
//     },
//   }
// );

// export type TButtonVariants = VariantProps<typeof buttonVariants>;

// type TCommonProps = {
//   asLink?: boolean;
//   testId?: string;
//   asSquare?: boolean;
//   beforeIcon?: React.ReactNode;
//   afterIcon?: React.ReactNode;
//   children?: React.ReactNode;
//   bodyClassName?: string;
//   className?: string;
//   fullWidth?: boolean;
//   hidden?: boolean;
// } & TButtonVariants;

// export type TButtonAsButtonProps = TCommonProps &
//   React.ButtonHTMLAttributes<HTMLButtonElement>;
// export type TButtonAsLinkProps = TCommonProps & LinkProps;

// export type TButtonProps = TButtonAsButtonProps | TButtonAsLinkProps;

// export const Button = ({
//   testId,
//   size = 'm',
//   asSquare,
//   variant = 'primary',
//   beforeIcon,
//   afterIcon,
//   children,
//   asLink = false,
//   bodyClassName,
//   className,
//   fullWidth,
//   hidden,
//   ...props
// }: TButtonProps) => {
//   const classes = cn(
//     'relative inline-flex group/btn disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none',
//     fullWidth && 'w-full'
//   );

//   const buttonBody = (
//     <div className={cn('relative', fullWidth && 'w-full')}>
//       <div
//         className={cn(
//           buttonVariants({ variant, size }),
//           'space-x-2',
//           variant !== 'back' &&
//             'group-hover/btn:-translate-x-1 group-hover/btn:-translate-y-1',
//           asSquare && 'aspect-square size-9 min-w-0',
//           asSquare && size === 's' && 'size-7 p-1',
//           bodyClassName,
//           fullWidth && 'w-full'
//         )}
//       >
//         {beforeIcon && <span>{beforeIcon}</span>}
//         {(props as any)?.ubLabel || children}
//         {afterIcon && <span>{afterIcon}</span>}
//       </div>
//       <div
//         className={cn(
//           'z-10 absolute block w-full h-full left-0 top-0 rounded-[4px]',
//           variant === 'primary' && 'bg-primary',
//           variant === 'secondary' && 'bg-tertiary',
//           variant === 'blank' &&
//             'bg-transparent transition-none group-hover/btn:transition-all group-hover/btn:bg-black delay-75',
//           variant === 'orange' &&
//             'bg-transparent transition-none group-hover/btn:transition-all group-hover/btn:bg-black delay-75',
//           variant === 'back' && 'hidden'
//         )}
//       />
//     </div>
//   );

//   if (asLink) {
//     return (
//       <Link
//         data-testid={testId}
//         className={cn(classes, className, hidden && 'hidden')}
//         {...(props as TButtonAsLinkProps)}
//       >
//         {buttonBody}
//       </Link>
//     );
//   }

//   return (
//     <>
//       <button
//         data-testid={testId}
//         className={cn(classes, className, hidden && 'hidden')}
//         {...(props as TButtonAsButtonProps)}
//         type={(props as TButtonAsButtonProps)?.type || 'button'}
//       >
//         {buttonBody}
//       </button>
//     </>
//   );
// };

// export default Button;

import React from 'react';
import Link, { LinkProps } from 'next/link';
import { cn } from '../../../lib/utils';
import { VariantProps, cva } from 'class-variance-authority';

export const buttonVariants = cva(
  'relative z-20 flex items-center justify-center rounded-[4px] transition',
  {
    variants: {
      variant: {
        primary:
          'border border-black text-white bg-black group-hover/btn:shadow-button-primary',
        secondary:
          'border border-black bg-white group-hover/btn:shadow-button-secondary',
        blank: 'text-black bg-white group-hover/btn:shadow-button-blank',
        back: 'text-black bg-white hover:bg-gray-100 active:bg-gray-200',
        orange: 'text-white bg-primary group-hover/btn:shadow-button-black',
        green: 'text-white bg-green-500 group-hover/btn:bg-green-600',
      },
      size: {
        xs: 'py-1 px-2 text-mini min-w-[60px]',
        s: 'py-1 px-2 text-sm min-w-[75px]',
        m: 'py-2 px-2 text-base min-w-[80px]',
        l: 'py-3 px-4 text-lg min-w-[100px]',
        mini: 'p-2',
      },
    },
    defaultVariants: {
      variant: 'primary',
    },
  }
);

export type TButtonVariants = VariantProps<typeof buttonVariants>;

type TCommonProps = {
  asLink?: boolean;
  testId?: string;
  asSquare?: boolean;
  beforeIcon?: React.ReactNode;
  afterIcon?: React.ReactNode;
  children?: React.ReactNode;
  bodyClassName?: string;
  className?: string;
  fullWidth?: boolean;
  hidden?: boolean;
} & TButtonVariants;

export type TButtonAsButtonProps = TCommonProps &
  React.ButtonHTMLAttributes<HTMLButtonElement>;
export type TButtonAsLinkProps = TCommonProps & LinkProps;

export type TButtonProps = TButtonAsButtonProps | TButtonAsLinkProps;

export const Button = ({
  testId,
  size = 'm',
  asSquare,
  variant = 'primary',
  beforeIcon,
  afterIcon,
  children,
  asLink = false,
  bodyClassName,
  className,
  fullWidth,
  hidden,
  ...props
}: TButtonProps) => {
  const classes = cn(
    'relative inline-flex group/btn disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none',
    fullWidth && 'w-full'
  );

  const buttonBody = (
    <div className={cn('relative', fullWidth && 'w-full')}>
      <div
        className={cn(
          buttonVariants({ variant, size }),
          'space-x-2',
          variant !== 'back' &&
            'group-hover/btn:-translate-x-1 group-hover/btn:-translate-y-1',
          asSquare && 'aspect-square size-9 min-w-0',
          asSquare && size === 's' && 'size-7 p-1',
          bodyClassName,
          fullWidth && 'w-full'
        )}
      >
        {beforeIcon && <span>{beforeIcon}</span>}
        {(props as any)?.ubLabel || children}
        {afterIcon && <span>{afterIcon}</span>}
      </div>
      <div
        className={cn(
          'z-10 absolute block w-full h-full inset-0 rounded-[6px] group-hover/btn:rounded-[4px] scale-[0.95] group-hover/btn:scale-[1] transition-transform',
          variant === 'primary' && 'bg-primary',
          variant === 'secondary' && 'bg-tertiary',
          variant === 'blank' && 'bg-black',
          variant === 'orange' && 'bg-black',
          variant === 'back' && 'hidden'
        )}
      />
    </div>
  );

  if (asLink) {
    return (
      <Link
        data-testid={testId}
        className={cn(classes, className, hidden && 'hidden')}
        {...(props as TButtonAsLinkProps)}
      >
        {buttonBody}
      </Link>
    );
  }

  return (
    <>
      <button
        data-testid={testId}
        className={cn(classes, className, hidden && 'hidden')}
        {...(props as TButtonAsButtonProps)}
        type={(props as TButtonAsButtonProps)?.type || 'button'}
      >
        {buttonBody}
      </button>
    </>
  );
};

export default Button;
