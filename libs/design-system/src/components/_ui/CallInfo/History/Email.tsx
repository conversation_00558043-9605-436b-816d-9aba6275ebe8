import {
  QueryClient,
  QueryClientProvider,
  useQuery,
} from '@tanstack/react-query';
import { useRouteHandler } from '../../../../context/RouteContext';
import { getEmailDetails } from '../../../../lib/api';
import { useState } from 'react';
import { useEffect } from 'react';
import EmailItem from '../../EmailPanel/EmailItem';
import { Loader } from 'lucide-react';
import IconEmptyRecords from '../../Icon/IconEmptyRecords';
import { useTranslation } from 'react-i18next';

const EmailBody = ({ data }: { data: any }) => {
  const { t } = useTranslation();
  const [emailList, setEmailList] = useState<any[]>([]);

  useEffect(() => {
    console.log('EmailBody ===> data?.data?.data', data?.data?.data);

    if (data?.data?.data) {
      setEmailList(data?.data?.data);
    }
  }, [data]);

  return (
    <div>
      <div className="flex flex-col w-full h-full bg-white rounded-lg relative">
        {/* Email History List */}
        <div className="flex flex-col w-full h-full overflow-y-auto p-4 space-y-4 hide-scrollbar">
          <div className="flex-1">
            {emailList && emailList?.length > 0 ? (
              emailList?.map((email: any) => {
                return (
                  <EmailItem
                    key={email.id}
                    email={email}
                  />
                );
              })
            ) : (
              <div className="w-full h-full flex flex-col items-center justify-center">
                <IconEmptyRecords size="78" />
                <div className="text-grey-500">
                  {t('ctint-mf-interaction.whatsapp.noSelectedInteraction')}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

interface EmailProps {
  conversationId: string;
}

const Email = ({ conversationId }: EmailProps) => {
  const { basePath } = useRouteHandler();
  const queryClient = new QueryClient();
  const { data, isLoading } = useQuery({
    queryKey: ['email'],
    queryFn: () => getEmailDetails(conversationId, basePath),
  });

  if (isLoading) {
    return (
      <div className="flex justify-center items-center w-full h-full">
        <Loader size={64} />
      </div>
    );
  }

  return (
    <QueryClientProvider client={queryClient}>
      <EmailBody data={data} />
    </QueryClientProvider>
  );
};

export default Email;
