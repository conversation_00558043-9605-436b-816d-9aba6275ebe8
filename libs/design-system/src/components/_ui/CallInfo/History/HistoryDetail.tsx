import {
    cn
} from '@cdss-modules/design-system/lib/utils';
import {
    <PERSON><PERSON>,
    Panel,
} from '@cdss-modules/design-system';
import { FC, memo, ReactNode, useEffect, useState } from "react";
import { Messages } from './Messages';
import dayjs from 'dayjs';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { MultipleAudioRender } from '@cdss-modules/design-system/components/_ui/AudioPlayer';
import { useCustomerHistory } from '@cdss-modules/design-system/lib/hooks/useCustomerInfo';
import Email from './Email';
import WhatsApp from './WhatsApp';

export type ThistoryDetailProps = {
    isOpen: boolean;
    onClose: () => void;
    convId: string
};

type TdetailItem = {
    name: string,
    content: ReactNode
    isFlex?: boolean
    lastChil?: boolean
}
const HistoryDetail: FC<ThistoryDetailProps> = memo(({ isOpen, onClose, convId }) => {
    const {
        getInteractionDetail,
        detailLoading,
        interactionsHistoryDetail,
        getInteractionRecording,
        interactionsRecording,
        recordingLoading,
        contactDetailLoading,
        contactDetail,
        getContactDetail
    } = useCustomerHistory()
    const [recordings, setRecordings] = useState<any[]>([])
    // const { load, isReady, seek, error: loadMp3Error } = useGlobalAudioPlayer();
    useEffect(() => {
        setRecordings(interactionsRecording)
    }, [interactionsRecording]);

    const [pos, setPos] = useState(0);
    const detailItem = ({ name, content, isFlex, lastChil }: TdetailItem) => {
        return (
            <div className="flex flex-col" >
                <div className="z-10">
                    <div className={cn("flex items-center gap-4 py-2", !lastChil && "border-b-2 border-grey-200")}>
                        <div className="w-full flex flex-col gap-2 justify-between">

                            <p className="text-lg font-black ">
                                {name}
                            </p>

                            <p className={cn('w-full  gap-4 overflow-x-auto', isFlex ? 'flex flex-col' : '')}>
                                {content}
                            </p>

                        </div>
                    </div>
                </div>
            </div>
        )
    }
    useEffect(() => {
        if (convId) {
            getInteractionDetail(convId)
            getContactDetail(convId)
        }
    }, [convId])
    const iconRender = (history: any) => {
        if (history?.conversationType === "voice" || history?.conversationType === "call" || history?.conversationType === "callback") {
            return <Icon name="phone" />
        }
        if (history?.conversationType === "email") {
            return <Icon name="email2" />
        }
        if (history?.conversationType === "message") {
            return <Icon name="msg" />
        }
        if (history?.conversationType === "whatsapp") {
            return <Icon name="msg" />
        }
        return (
            <>-
            </>
        )
    }
    return (
        <>
            {isOpen && (<Panel containerClassName="h-full" loading={detailLoading || contactDetailLoading || recordingLoading}>
                <><div><div className="relative flex flex-col border-b-2">
                    <div className='w-full flex justify-between items-center'>
                        <p className="flex items-center  gap-2 ">
                            <Button
                                asSquare
                                variant="back"
                                onClick={() => { onClose() }}
                                beforeIcon={<Icon name="back" />}
                            />
                            {iconRender(contactDetail)}
                            <span>{interactionsHistoryDetail?.queue}</span>
                        </p>
                        <p >
                            {interactionsHistoryDetail?.conversationStart && dayjs(
                                interactionsHistoryDetail?.conversationStart
                            ).format('YYYY-MM-DD HH:mm') || "-"}
                        </p>

                    </div>

                </div>
                </div>

                    <div className='flex-1 overflow-y-auto pr-1'>
                        {
                            contactDetail?.wrapups && Object.entries(contactDetail?.wrapups)?.length > 0 && detailItem({
                                name: "Wrap Up", content: <> {

                                    Object.entries(contactDetail?.wrapups)?.map(([key, value], i) => {

                                        return <div className="flex gap-2 items-center" key={key || i}>{String(key)}:
                                            {(value as any)?.wrapList?.map((item: any, i: number) => {
                                                return <p className="flex gap-2" key={i}>{item?.map((item: any, ii: number) => {
                                                    return <span className="bg-[#FFF5DA] w-fit p-2 rounded-md" key={ii}>{item?.wrapUpName || "-"}</span>
                                                })}</p>
                                            })}
                                        </div>
                                    })

                                }</>
                                , isFlex: true
                            })
                        }
                        {
                            contactDetail?.transactions && contactDetail?.transactions?.length > 0 && detailItem({
                                name: "IVR", content: <>{contactDetail?.transactions?.map((item: any, i: number) => {
                                    return <span key={i}>{item}</span>
                                })} </>
                                , isFlex: true
                            })
                        }
                        {
                            recordings?.length > 0 && (contactDetail?.conversationType === "voice" || contactDetail?.conversationType === "call" || contactDetail?.conversationType === "callback") && detailItem({
                                name: "Recording",
                                content: (
                                    <div className={cn('px-4 py-4 flex flex-col gap-2')}>
                                        {recordings?.map((item: any, index: number) => {
                                            return (
                                                <MultipleAudioRender key={item || index} audioSrc={item} />
                                            )
                                        })}
                                    </div>
                                ),
                                isFlex: false,
                            })

                        }
                        {
                            contactDetail?.conversationType==="whatsapp"&&detailItem({
                                name: "Messages", content: <><WhatsApp conversationId={convId} /> </>
                                , isFlex: false,lastChil:true
                            })
                        }
                        {
                            contactDetail?.conversationType==="email"&&detailItem({
                                name: "Email", content: <Email conversationId={convId} />
                                , isFlex: false,lastChil:true
                            })
                        }

                    </div></>
            </Panel>)}
        </>
    )
})
HistoryDetail.displayName = 'HistoryDetail';
export default HistoryDetail;