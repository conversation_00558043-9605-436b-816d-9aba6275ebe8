
const m = [
    {
        type: "customer",
        time: "2024-1-1 11:30",
        msg: "hello agent"
    },
    {
        type: "agent",
        time: "2024-1-1 11:30",
        msg: "hello customer"
    }
]
export function Messages() {
    const msgItem = () => {
        return m ? <div>{m.map((item, index) => (
            item.type != 'agent' ? (<div key={index}>
                <span className="text-[12px] text-[#ACACAC]">{item.time}</span>
                <p className="bg-[#F2F2F2] w-fit max-w-[473px] p-2 rounded-md">{item.msg}</p>
            </div>) : (
                <div key={index} className="flex flex-col items-end">
                    <span className="text-[12px] text-[#ACACAC]">{item.time}</span>
                    <p className="bg-[#FFF5DA] w-fit max-w-[473px] p-2 rounded-md">{item.msg}</p>
                </div>
            )
        ))} </div> : <></>
    }
    return (<>
        {msgItem()}
    </>)
}