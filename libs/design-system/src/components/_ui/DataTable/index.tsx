/* eslint-disable react-hooks/exhaustive-deps */
'use client';

import * as React from 'react';
import {
  ColumnDef,
  ColumnFilter,
  ColumnFiltersState,
  OnChangeFn,
  PaginationState,
  Row,
  RowSelectionState,
  SortingState,
  TableOptions,
  Table as TableType,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../_ui/Table/index';
import {
  DUMMAY_COLUMNS,
  DUMMY_TABLE_DATA,
} from '../../../lib/dummy/dummyTableData';
import { useEffect } from 'react';
import { cn } from '../../../lib/utils';
import IconEmptyRecords from '../Icon/IconEmptyRecords';
// eslint-disable-next-line @nx/enforce-module-boundaries
import Pagination from '@cdss-modules/design-system/components/_ui/Pagination';

export type TDataRecord = {
  [key: string]: any;
};

export type PaginationConfig = {
  enable: boolean;
  pageIndex: number;
  pageSize: number;
};

export type TColumnInfo = {
  label: string;
  accessor: string;
};

export type TDataTableProps<T> = {
  data?: T[];
  columns?: ColumnDef<T>[];
  columnLabel?: { [key: string]: string }[];
  emptyMessage?: string;
  loading?: boolean;
  error?: string;
  tableOptions?: Omit<TableOptions<T>, 'data' | 'columns' | 'getCoreRowModel'>;
  // TODO: think of a more generic to handle table options and state like row selection, etc.
  rowSelection?: RowSelectionState;
  setRowSelection?: OnChangeFn<RowSelectionState>;
  onClickRow?: (row: Row<T>) => void;
  onTableSetUp?: (table: TableType<T>) => void;
  paginationConfig?: PaginationConfig;
  onSelectedDataTrigger?: (data: any) => void;
  customFilterValue?: ColumnFilter[];
  resize?: boolean;
  pageSizeOnChange?: (num: number) => void;
};

export function DataTable<T>({
  data = DUMMY_TABLE_DATA as T[],
  columns = DUMMAY_COLUMNS as ColumnDef<T>[],
  loading,
  error,
  emptyMessage,
  onClickRow,
  tableOptions,
  rowSelection: externalRowSelection,
  setRowSelection: setExternalRowSelection,
  onTableSetUp,
  paginationConfig,
  onSelectedDataTrigger,
  customFilterValue,
  resize,
  pageSizeOnChange,
}: TDataTableProps<T>) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([
    // { id: 'name', value: 'email' },
  ]);
  // console.log('paginationConfig?.enable');
  // console.log(
  //   paginationConfig?.enable === true ? paginationConfig?.pageSize : data.length
  // );
  const [pagination, setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});

  const noOfColumns = columns?.length ?? 1;
  // TODO: think of a more generic to handle table options and state like row selection, etc.
  const [internalRowSelection, setInternalRowSelection] = React.useState({});
  const rowSelection = externalRowSelection ?? internalRowSelection;
  const onRowSelectionChange =
    setExternalRowSelection ?? setInternalRowSelection;

  useEffect(() => {
    const rows = table.getSelectedRowModel().rows;
    // console.log(rows);
    const tempOrigRowData: any[] = [];
    rows.map((item, index) => {
      const orginalData = item.original;
      tempOrigRowData.push(orginalData);
    });
    // console.log(tempOrigRowData);
    onSelectedDataTrigger?.(tempOrigRowData);
  }, [internalRowSelection]);

  const table = useReactTable({
    data,
    columns: columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onPaginationChange: setPagination,
    enableColumnResizing: true,
    columnResizeMode: 'onChange',
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      pagination,
      // pagination: {
      //   pageIndex: 0, //custom initial page index
      //   pageSize: 10, //custom default page size
      // },
    },
    onRowSelectionChange,
    ...tableOptions,
  });

  useEffect(() => {
    if (onTableSetUp) {
      onTableSetUp(table);
    }
    const pageSize =
      paginationConfig?.enable === true
        ? paginationConfig?.pageSize
        : data.length;
    setPagination({
      pageIndex: 0,
      pageSize: pageSize,
    });

    // console.log(customFilterValue);
    customFilterValue
      ? setColumnFilters(customFilterValue)
      : setColumnFilters([]);
  }, [table, onTableSetUp]);

  // useEffect(() => {
  //   console.log(3343);

  // }, pagination);

  if (loading) {
    return (
      <div className="pointer-events-none">
        <Table
          {...{
            style: {
              // width: table.getTotalSize(),
            },
          }}
        >
          <TableHeader className="sticky top-0 bg-white shadow-[inset_0_-1px_0_#DEDEDE] z-30">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow
                inThead={true}
                key={headerGroup.id}
              >
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      key={header.id}
                      className={cn(header.id === 'select' && 'w-[21px]')}
                      style={{
                        position: 'relative',
                        width: header.getSize(),
                      }}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header?.column?.columnDef?.header,
                            header?.getContext()
                          )}
                      <div
                        onMouseDown={header.getResizeHandler()}
                        onTouchStart={header.getResizeHandler()}
                        className={`absolute right-0 top-0 h-full w-1 cursor-col-resize select-none touch-none bg-gray-300 ${
                          header.column.getIsResizing() ? 'bg-blue-500' : ''
                        }`}
                      />
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {new Array(5)?.fill(0)?.map((_, rid) => (
              <TableRow key={`table-loader-th-${rid}`}>
                {new Array(noOfColumns)?.fill(0)?.map((_, cid) => (
                  <TableCell
                    key={`table-loader-td-${rid}-${cid}`}
                    className={cn(cid === 0 && 'w-[21px]')}
                  >
                    <div className={cn('h-6 bg-grey-200 animate-pulse')} />
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <div className="overflow-y-auto flex-1">
        <Table
          className="min-w-[991px] h-full"
          {...{
            style: {
              width: table.getTotalSize(),
              // width: "100%",
              // maxWidth:"100%"
              minWidth: '100%',
            },
          }}
        >
          <TableHeader className="sticky top-0 bg-white shadow-[inset_0_-1px_0_#DEDEDE] z-30">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow
                inThead={true}
                key={headerGroup.id}
              >
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      key={header.id}
                      className={cn(header.id === 'select' && 'w-[21px]')}
                      style={{
                        position: 'relative',
                        width: header.getSize(),
                        overflow: 'hidden', // 防止内容溢出
                      }}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header?.column?.columnDef?.header,
                            header?.getContext()
                          )}
                      {resize && header.id != 'action' && (
                        <div
                          onMouseDown={header.getResizeHandler()}
                          onTouchStart={header.getResizeHandler()}
                          className={`absolute right-0 top-0 h-full w-3 cursor-col-resize select-none touch-none flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 ${
                            header.column.getIsResizing() ? 'opacity-100' : 'opacity-50'
                          }`}
                        >
                          <div
                            className={`w-[3px] h-3 rounded-full bg-gray-300 group-hover:bg-blue-400 ${
                              header.column.getIsResizing() ? 'bg-blue-500' : ''
                            }`}
                          />
                        </div>
                      )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length > 0 ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  onClick={() => onClickRow && onClickRow(row)}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      key={cell.id}
                      className={cn(
                        'break-words',
                        cell.id === 'select' && 'w-[21px]'
                      )}
                      style={{
                        width: cell.column.getSize(),
                        overflow: 'hidden', // 防止内容溢出
                      }}
                    >
                      <div style={{
                        width: cell.column.getSize(),
                        overflow: 'hidden', // 防止内容溢出
                      }}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                      </div>
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow></TableRow>
            )}
          </TableBody>
        </Table>
        {table.getRowModel().rows?.length <= 0 && (
          <div className="w-full h-full flex flex-col gap-y-4 items-center justify-center">
            <IconEmptyRecords size="78" />
            <div className="text-grey-500">
              {emptyMessage || 'No selected interaction.'}
            </div>
          </div>
        )}
      </div>
      {paginationConfig?.enable === true && (
        <div className="pt-2 pb-6 flex-none">
          <Pagination
            current={table.getState().pagination.pageIndex + 1}
            perPage={table.getState().pagination.pageSize}
            total={Math.ceil(
              table.getRowCount() / table.getState().pagination.pageSize
            )}
            totalCount={table.getRowCount()}
            onChange={(v) => {
              table.setPageIndex(v - 1);
            }}
            handleOnPrevious={() => {
              table.setPageIndex(table.getState().pagination.pageIndex - 1);
            }}
            handleOnNext={() => {
              table.setPageIndex(table.getState().pagination.pageIndex + 1);
            }}
            handlePerPageSetter={(p: number) => {
              console.log(p);
              setPagination({
                pageIndex: 0,
                pageSize: p,
              });
              table.setPageSize(p);
              pageSizeOnChange && pageSizeOnChange(p);
              // table.setPageSize(p);
              //setPerPage(p);
              // setCurrentPage(1);
            }}
          />
        </div>
      )}
    </div>
  );
}
