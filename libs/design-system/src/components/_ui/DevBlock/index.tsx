
export type TDevBlockProps = {
    show?: boolean;
    children: React.ReactNode;
}

export const DevBlock = ({ children, show = window?.location?.hostname?.indexOf('localhost') > -1 }: TDevBlockProps) => {
    if (!show) return null;
    return (
        <div className="mt-4 border border-grey-300 border-dashed p-6">
            <h2 className="mb-2 font-bold">For development only:</h2>
            <div>
                {children}
            </div>
        </div>
    );
}

export default DevBlock;