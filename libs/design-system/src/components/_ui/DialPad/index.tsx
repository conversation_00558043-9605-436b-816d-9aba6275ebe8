'use client';

import { useState } from 'react';
import Input from '../Input';
import Icon from '../Icon';
import { cn } from '../../../lib/utils';

type TDialPadProps = {
  value: string[];
  buttonClassName?: string;
};

const keys = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '*', '0', '#'];

const DialPad: React.FC<TDialPadProps> = ({ value, buttonClassName }) => {
  const [inputValue, setInputValue] = useState<any>(value);
  const [pickup, setPickup] = useState(false);

  const handleInputValue = (e: any) => {
    setInputValue([...inputValue, e]);
  };

  const formattedValue = inputValue?.length > 0 && inputValue?.join('');

  const removeValue = () => {
    if (inputValue.length > 0) {
      const newValue = inputValue?.slice(0, -1);

      setInputValue(newValue);
    }
  };

  return (
    <div className="flex flex-col justify-center items-center gap-4">
      <Input
        value={inputValue?.length > 0 ? formattedValue : ''}
        onFocus={() => {
          return null;
        }}
        readOnly
        containerClassName="bg-grey-100 border-none rounded-lg"
        className="focus:shadow-none cursor-not-allowed"
      />
      <div className="flex flex-col gap-y-4 max-w-[465px]">
        <div className="grid grid-cols-3 gap-x-10 gap-y-4">
          {keys.map((key) => (
            <button
              key={`key-${key}`}
              className={cn(
                'w-16 h-16 flex justify-center items-center rounded-full bg-grey-200 hover:bg-grey-300 active:bg-grey-400 font-bold text-t4',
                key === '*' && 'pt-3',
                buttonClassName
              )}
              onClick={() => handleInputValue(key)}
            >
              {key}
            </button>
          ))}
        </div>
        <div className="flex flex-row-reverse gap-x-10 gap-y-4">
          <button
            className={cn(
              'w-16 h-16 flex justify-center items-center rounded-full hover:border-black hover:border active:bg-primary-500 active:shadow-button-primary active:border-none'
            )}
            onClick={removeValue}
          >
            <Icon
              name="backspace"
              size={32}
            />
          </button>
          <button
            className={cn(
              'w-16 h-16 flex justify-center items-center rounded-full ',
              pickup ? 'bg-status-danger' : 'bg-status-success'
            )}
            onClick={() => setPickup(!pickup)}
          >
            {pickup ? (
              <Icon
                name="pickup"
                size={32}
              />
            ) : (
              <Icon
                name="phone-end"
                size={32}
              />
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default DialPad;
