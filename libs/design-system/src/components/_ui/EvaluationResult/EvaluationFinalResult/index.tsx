import * as Accordion from '@radix-ui/react-accordion';
import EvaluationUpdator from './EvaluationUpdator';
import { useState } from 'react';
import IconArrowFullDown from '../../Icon/IconArrowFullDown';
import { ManualDetailList } from './types/evaluation';
import EvaluationResultCommentItem from '../EvaluationResultItem/EvaluationResultComment/EvaluationResultCommentItem';
import { useTranslation } from 'react-i18next';
import { getTranslatedResult } from '../../../../lib/utils';

interface EvaluationFinalResultProps {
  evaluationId: string;
  result: string;
  resultComment?: string;
  manualDetailList?: ManualDetailList[];
  onSubmit: (comment: string, manualResult: string) => void;
}

const EvaluationFinalResult: React.FC<EvaluationFinalResultProps> = ({
  evaluationId,
  result,
  manualDetailList,
  onSubmit,
}) => {
  const [openItem, setOpenItem] = useState<string | null>(null); // 追踪当前打开的 item
  const [opened, setOpened] = useState<boolean>(false); // 是否展开comment

  const { t } = useTranslation();

  const handleValueChange = (value: string | null) => {
    setOpenItem(value);
  };

  const isEdited = manualDetailList && manualDetailList.length > 0;

  const displayResult = result;
  const accordionId = `final-result-${evaluationId}`;
  return (
    <Accordion.Root
      type="single"
      collapsible
      className="my-1"
      value={openItem || ''}
      onValueChange={handleValueChange}
    >
      <Accordion.Item value={accordionId}>
        <Accordion.Header>
          <Accordion.Trigger className="w-full font-bold text-body">
            <div className="flex items-center gap-x-2">
              {/* result description */}
              <span className="flex-1 text-left">
                {t('evaluation.finalResult')}:
              </span>
              {/* result evaluation */}
              <div className="flex flex-wrap items-center">
                <span
                  className={`flex-wrap ${
                    displayResult === 'Passed'
                      ? 'text-status-success'
                      : displayResult === 'Failed'
                        ? 'text-status-danger'
                        : 'text-primary-500'
                  }`}
                >
                  {t(
                    getTranslatedResult(
                      displayResult.toLowerCase(),
                      'evaluation'
                    )
                  )}
                </span>
                <span
                  className={`m-1 transition-transform duration-300 ease-in-out ${
                    openItem === accordionId ? 'rotate-180' : 'rotate-0'
                  }`}
                >
                  <IconArrowFullDown
                    color={openItem === accordionId ? '#FFAC4A' : '#000000'}
                    size={'10'}
                  />
                </span>
              </div>
            </div>
          </Accordion.Trigger>
          {/* TODO: handle edit status later */}
          {/* item edited status */}
          {isEdited &&
            manualDetailList.map((item, index) => {
              if (index === 0) {
                return (
                  <EvaluationResultCommentItem
                    key={item.manualDetailId}
                    data={item}
                    showMore={
                      manualDetailList !== undefined &&
                      manualDetailList?.length > 1
                    }
                    opened={opened}
                    setOpened={setOpened}
                  />
                );
              } else {
                return (
                  opened && (
                    <div
                      className={`transition-opacity duration-300 ${opened ? 'opacity-100' : 'opacity-0'}`}
                    >
                      <EvaluationResultCommentItem
                        key={item.manualDetailId}
                        data={item}
                        showMore={false}
                      />
                    </div>
                  )
                );
              }
            })}
        </Accordion.Header>
        <Accordion.Content className="mt-1 mb-4">
          <EvaluationUpdator
            evaluationId={evaluationId}
            result={result}
            onSubmit={onSubmit}
          />
        </Accordion.Content>
      </Accordion.Item>
    </Accordion.Root>
  );
};

export default EvaluationFinalResult;
