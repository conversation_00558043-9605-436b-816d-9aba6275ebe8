import { useState } from 'react';
import { EvaluationResultSubStep, SimilarityResult } from '../types/evaluation';
import * as ToggleGroup from '@radix-ui/react-toggle-group';
import * as Accordion from '@radix-ui/react-accordion';
import { useGlobalAudioPlayer } from 'react-use-audio-player';
import {
  getTranslatedResult,
  timeStringToSeconds,
} from '../../../../../lib/utils';
import { CommonPermission } from '../../../../../@types/CommonPermission';
import { useRole } from '../../../../../context/RoleContext';
import { usePermission } from '../../../../../context/PremissionContext';
import { useTranslation } from 'react-i18next';
import { X, Check } from 'lucide-react';
import IconArrowFullDown from '../../../Icon/IconArrowFullDown';

interface EvaluationUpdatorV3Props {
  subStep: EvaluationResultSubStep;
  evaluationId: string;
  onSubmit: (comment: string, manualResult: string) => void;
}

const EvaluationUpdatorV3: React.FC<EvaluationUpdatorV3Props> = ({
  subStep,
  evaluationId,
  onSubmit,
}) => {
  const { seek } = useGlobalAudioPlayer();
  const [comment, setComment] = useState<string>('');
  const [manualResult, setManualResult] = useState<string | undefined>();
  const { globalConfig } = useRole();
  const { permissions } = usePermission();
  const { t } = useTranslation();

  const [openItem, setOpenItem] = useState<string | null>(null); // 追踪当前打开的 item

  const displayResult =
    subStep.manualResult && subStep.manualResult.length > 0
      ? subStep.manualResult
      : subStep.autoResult;

  return (
    <section className="w-full overflow-x-auto">
      <div className="w-full flex flex-col px-6 py-4 border border-[#EEEFF1] bg-[#FCFCFD] rounded-md gap-2">
        {/* Time & Notes */}
        <div className="flex flex-col gap-2">
          <div className="flex flex-col gap-1">
            {/* Similarity */}
            <span className="flex gap-x-1">
              <div className="font-bold underline text-grey">
                {t('evaluation.similarity')}:
              </div>
              <label
                className={`${
                  subStep.similarityResult?.allMatch
                    ? 'text-green-500'
                    : 'text-red-500'
                } text-xs self-end p-1`}
              >
                {subStep.similarityResult?.allMatch
                  ? t('evaluation.passed')
                  : t('evaluation.failed')}
              </label>
            </span>
            <span>
              {subStep.similarityResult?.similarityResultDetailList &&
                subStep.similarityResult?.similarityResultDetailList.length >
                  0 &&
                subStep.similarityResult?.similarityResultDetailList.map(
                  (item, index) => {
                    const resultPercentage = item.similarityScore;
                    const displayResultPercentage =
                      resultPercentage || resultPercentage === 0
                        ? (resultPercentage * 100).toString().split('.')[1]
                            ?.length > 3
                          ? (
                              Math.round(resultPercentage * 100 * 100) / 100
                            ).toFixed(2)
                          : (resultPercentage * 100).toString()
                        : '--';
                    return (
                      <div
                        key={index + '-similarity_' + subStep.subStepName}
                        className="flex gap-2"
                      >
                        <span
                          key={'similarity-' + index}
                          dangerouslySetInnerHTML={{
                            __html: t('similarity.similarityScore', {
                              score: displayResultPercentage,
                            }),
                          }}
                        />
                        <span
                          className="underline cursor-pointer font-bold"
                          onClick={() => {
                            if (item.startTime) {
                              console.log(
                                'clicked',
                                timeStringToSeconds(item.startTime)
                              );
                              seek(timeStringToSeconds(item.startTime));
                            }
                          }}
                        >
                          {item.startTime + ' - ' + item.endTime}
                        </span>
                      </div>
                    );
                  }
                )}
            </span>
            {/* Meta Data Verification */}
            <span className="flex gap-x-1">
              <div className="font-bold underline text-grey">
                {t('evaluation.metaDataVerification')}:
              </div>
              <label
                className={`${
                  subStep.metaDataResult?.allMatch
                    ? 'text-green-500'
                    : 'text-red-500'
                } text-xs self-end p-1`}
              >
                {subStep.metaDataResult?.allMatch
                  ? t('evaluation.passed')
                  : t('evaluation.failed')}
              </label>
            </span>
            <span>
              {subStep.metaDataResult?.entityResults &&
                subStep.metaDataResult?.entityResults.length > 0 &&
                subStep.metaDataResult?.entityResults.map((item, index) => {
                  return (
                    <div
                      key={
                        index +
                        '-meta-data-verification-' +
                        item.entityType +
                        '_' +
                        item.entityValue
                      }
                      className="flex gap-2"
                    >
                      <span className="text-grey">
                        {item.entityValue && item.entityValue.length > 0
                          ? t('metaDataVerification.expect', {
                              entityType: item.entityType,
                              entityValue: item.entityValue,
                            })
                          : item.entityType}
                      </span>
                      <span>
                        {item.entityValueMatch ? (
                          <Check className="text-green-500" />
                        ) : (
                          <X className="text-red-500" />
                        )}
                      </span>
                    </div>
                  );
                })}
            </span>
            {/* Important response */}
            <span className="flex gap-x-1">
              <div className="font-bold underline text-grey">
                {t('evaluation.importantResponse')}:
              </div>
              <label
                className={`${
                  subStep.importantWordResult?.allMatch
                    ? 'text-green-500'
                    : 'text-red-500'
                } text-xs self-end p-1`}
              >
                {subStep.importantWordResult?.allMatch
                  ? t('evaluation.passed')
                  : t('evaluation.failed')}
              </label>
            </span>
            <span>
              {subStep.importantWordResult?.importantWordDetailList &&
                subStep.importantWordResult?.importantWordDetailList.length >
                  0 &&
                subStep.importantWordResult?.importantWordDetailList.map(
                  (item, index) => {
                    return (
                      <div
                        key={
                          index +
                          '-important-word-' +
                          item.entityValue +
                          item.existed
                        }
                        className="flex gap-2"
                      >
                        <span className="text-grey">{item.entityValue}</span>
                        {item.existed ? (
                          <Check className="text-green-500" />
                        ) : (
                          <X className="text-red-500" />
                        )}
                      </div>
                    );
                  }
                )}
            </span>
          </div>
          {/* Other Results */}
          <div className="flex flex-col gap-1">
            <span className="font-bold underline text-grey">
              Other Results:{' '}
            </span>
            {subStep.othersResult &&
              subStep.othersResult instanceof Array &&
              subStep.othersResult.length > 0 &&
              subStep.othersResult.map((item, index) => {
                const resultPercentage = item.finalRate;
                const displayResultPercentage =
                  resultPercentage || resultPercentage === 0
                    ? (resultPercentage * 100).toString().split('.')[1]
                        ?.length > 3
                      ? (
                          Math.round(resultPercentage * 100 * 100) / 100
                        ).toFixed(2)
                      : (resultPercentage * 100).toString()
                    : '--';

                return (
                  <Accordion.Root
                    key={`${subStep.nlpResultId}-others-result-${index}`}
                    type="single"
                    collapsible
                    className="my-1"
                    onValueChange={(value) => {
                      setOpenItem(value);
                    }}
                  >
                    <Accordion.Item
                      value={`${subStep.nlpResultId}-others-result-${index}`}
                    >
                      <Accordion.Header>
                        <Accordion.Trigger className="flex w-full text-left items-center text-grey">
                          <>
                            {t('similarity.othersResult.title', {
                              index: index + 1,
                              timerStart: item.timerStart,
                              timerEnd: item.timerEnd,
                            })}
                            <span
                              className={`m-1 transition-transform duration-300 ease-in-out ${
                                openItem ===
                                `${subStep.nlpResultId}-others-result-${index}`
                                  ? 'rotate-180'
                                  : 'rotate-0'
                              }`}
                            >
                              <IconArrowFullDown
                                color={
                                  openItem ===
                                  `${subStep.nlpResultId}-others-result-${index}`
                                    ? '#FFAC4A'
                                    : '#000000'
                                }
                                size={'10'}
                              />
                            </span>
                          </>
                        </Accordion.Trigger>
                      </Accordion.Header>
                      <Accordion.Content className="border border-primary rounded-[0.25rem] p-2">
                        {/* Similarity */}
                        <span className="flex gap-x-1">
                          <div className="font-bold underline text-grey">
                            Similarity:
                          </div>
                          <label
                            className={`${
                              item.similarityResult?.allMatch
                                ? 'text-green-500'
                                : 'text-red-500'
                            } text-xs self-end p-1`}
                          >
                            {item.similarityResult?.allMatch
                              ? 'Passed'
                              : 'Failed'}
                          </label>
                        </span>
                        <span>
                          <div className="flex gap-2">
                            <span
                              dangerouslySetInnerHTML={{
                                __html: t(
                                  'similarity.othersResult.similarityScore',
                                  {
                                    score: displayResultPercentage,
                                  }
                                ),
                              }}
                            />
                            <span
                              className="underline cursor-pointer font-bold"
                              onClick={() => {
                                if (item.timerStart) {
                                  console.log(
                                    'clicked',
                                    timeStringToSeconds(item.timerEnd)
                                  );
                                  seek(timeStringToSeconds(item.timerStart));
                                }
                              }}
                            >
                              {item.timerStart + ' - ' + item.timerEnd}
                            </span>
                          </div>
                        </span>
                        {/* Meta Data Verification */}
                        <span className="flex gap-x-1">
                          <div className="font-bold underline text-grey">
                            Meta Data Verification:
                          </div>
                          <label
                            className={`${
                              item.metaDataResult?.allMatch
                                ? 'text-green-500'
                                : 'text-red-500'
                            } text-xs self-end p-1`}
                          >
                            {item.metaDataResult?.allMatch
                              ? 'Passed'
                              : 'Failed'}
                          </label>
                        </span>
                        <span>
                          {item.metaDataResult?.entityResults &&
                            item.metaDataResult?.entityResults.length > 0 &&
                            item.metaDataResult?.entityResults.map(
                              (entityResultItem, entityResultIndex) => {
                                return (
                                  <div
                                    key={
                                      entityResultIndex +
                                      '-meta-data-verification-' +
                                      entityResultItem.entityType +
                                      '_' +
                                      entityResultItem.entityValue
                                    }
                                    className="flex gap-2"
                                  >
                                    <span className="text-grey">
                                      {entityResultItem.entityValue &&
                                      entityResultItem.entityValue.length > 0
                                        ? t('metaDataVerification.expect', {
                                            entityType:
                                              entityResultItem.entityType,
                                            entityValue:
                                              entityResultItem.entityValue,
                                          })
                                        : entityResultItem.entityType}
                                    </span>
                                    <span>
                                      {entityResultItem.entityValueMatch ? (
                                        <Check className="text-green-500" />
                                      ) : (
                                        <X className="text-red-500" />
                                      )}
                                    </span>
                                  </div>
                                );
                              }
                            )}
                        </span>
                        {/* Important response */}
                        <span className="flex gap-x-1">
                          <div className="font-bold underline text-grey">
                            Important Response:
                          </div>
                          <label
                            className={`${
                              item.importantWordResult?.allMatch
                                ? 'text-green-500'
                                : 'text-red-500'
                            } text-xs self-end p-1`}
                          >
                            {item.importantWordResult?.allMatch
                              ? 'Passed'
                              : 'Failed'}
                          </label>
                        </span>
                        <span>
                          {item.importantWordResult?.importantWordDetailList &&
                            item.importantWordResult?.importantWordDetailList
                              .length > 0 &&
                            item.importantWordResult?.importantWordDetailList.map(
                              (
                                importantWordDetailItem,
                                importantWordDetailIndex
                              ) => {
                                return (
                                  <div
                                    key={
                                      importantWordDetailIndex +
                                      '-important-word-' +
                                      importantWordDetailItem.entityValue +
                                      importantWordDetailItem.existed
                                    }
                                    className="flex gap-2"
                                  >
                                    <span className="text-grey">
                                      {importantWordDetailItem.entityValue}
                                    </span>
                                    {importantWordDetailItem.existed ? (
                                      <Check className="text-green-500" />
                                    ) : (
                                      <X className="text-red-500" />
                                    )}
                                  </div>
                                );
                              }
                            )}
                        </span>
                      </Accordion.Content>
                    </Accordion.Item>
                  </Accordion.Root>
                );
              })}
          </div>
          {/* Notes */}
          <div className="flex flex-col gap-1">
            <span className="font-bold underline text-grey">
              {t('evaluation.notes')}:{' '}
            </span>
            <span>{subStep.notes ? subStep.notes : ''}</span>
          </div>
        </div>
        {/* Override to */}
        {new CommonPermission(globalConfig, permissions).isPermissionEnabled(
          'ctint-mf-interaction',
          'qm',
          'edit'
        ) ? (
          displayResult?.toLowerCase() === 'to be reviewed' ? (
            <div className="flex items-center">
              <span className="flex-wrap text-[#949494] mr-2">
                {t('evaluation.overrideTo')}:{' '}
              </span>
              <span className="flex flex-1 gap-2 items-center">
                {/* manual result */}
                <div>
                  <ToggleGroup.Root
                    type="single"
                    value={manualResult}
                    onValueChange={(value) => {
                      if (value) setManualResult(value);
                    }}
                    className="flex-wrap border-[1px] border-[#E0E0E0] rounded-[0.25rem]"
                  >
                    <ToggleGroup.Item
                      value="Passed"
                      className="data-[state=on]:bg-black data-[state=on]:text-white font-light px-2 py-[2px] rounded-l-[0.25rem] border-r-[1px] border-[#E0E0E0]"
                    >
                      {t('evaluation.passed')}
                    </ToggleGroup.Item>
                    <ToggleGroup.Item
                      value="Failed"
                      className="data-[state=on]:bg-black data-[state=on]:text-white font-light px-2 py-[2px] rounded-r-[0.25rem]"
                    >
                      {t('evaluation.failed')}
                    </ToggleGroup.Item>
                  </ToggleGroup.Root>
                </div>
                {/* manual comment and submit button */}
                <div className="flex flex-1 border-[1px] border-[#E0E0E0] rounded-[0.25rem]">
                  <input
                    type="text"
                    alt="comment"
                    disabled={!manualResult}
                    placeholder={t('evaluation.because')}
                    value={comment}
                    className="flex-1 px-2 py-[2px] rounded-l-[0.25rem] focus:outline-none font-light"
                    onChange={(v) => setComment(v.target.value)}
                  />
                  <button
                    className="px-2 py-[2px] bg-black text-white rounded-r-[0.25rem] font-light"
                    disabled={
                      comment === subStep.manualComment &&
                      manualResult === subStep.manualResult
                    } // 未修改时不允许提交
                    onClick={() => onSubmit(comment, manualResult || '')}
                  >
                    {t('evaluation.submit')}
                  </button>
                </div>
              </span>
            </div>
          ) : (
            <div className="flex items-center">
              <span className="flex flex-col flex-1 gap-2 items-left">
                {/* manual result */}
                <div>
                  <ToggleGroup.Root
                    type="single"
                    onValueChange={(value) => {
                      console.log('value', value);

                      if (value && value?.length > 0) setManualResult(value);
                      else setManualResult(undefined);
                    }}
                    className="flex-wrap"
                  >
                    <ToggleGroup.Item
                      value={
                        subStep.autoResult === 'Passed'
                          ? t('evaluation.failed')
                          : t('evaluation.passed')
                      }
                      className="data-[state=on]:bg-black data-[state=on]:text-white font-light px-2 py-[2px] rounded-[0.25rem] border-[1px] border-[#E0E0E0]"
                    >
                      {t('evaluation.overrideTo')}:{' '}
                      {subStep.autoResult === 'Passed'
                        ? t('evaluation.failed')
                        : t('evaluation.passed')}
                    </ToggleGroup.Item>
                  </ToggleGroup.Root>
                </div>
                {/* manual comment and submit button */}
                <div className="flex flex-1 border-[1px] border-[#E0E0E0] rounded-[0.25rem]">
                  <input
                    type="text"
                    alt="comment"
                    placeholder={
                      manualResult
                        ? `${t('evaluation.overrideTo')} ${getTranslatedResult(
                            manualResult.toLowerCase(),
                            'evaluation'
                          )}, ${t('evaluation.because')}`
                        : `${t('evaluation.leaveComment')}`
                    }
                    value={comment}
                    className="flex-1 px-2 py-[2px] rounded-l-[0.25rem] focus:outline-none font-light"
                    onChange={(v) => setComment(v.target.value)}
                  />
                  <button
                    className="px-2 py-[2px] bg-black text-white rounded-r-[0.25rem] font-light"
                    onClick={() => onSubmit(comment, manualResult || '')}
                  >
                    {t('evaluation.submit')}
                  </button>
                </div>
              </span>
            </div>
          )
        ) : null}
      </div>
    </section>
  );
};

export default EvaluationUpdatorV3;
