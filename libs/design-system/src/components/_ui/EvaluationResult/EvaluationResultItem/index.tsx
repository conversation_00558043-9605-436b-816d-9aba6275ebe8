import { EvaluationResultSubStep } from './types/evaluation';
import * as Accordion from '@radix-ui/react-accordion';
import EvaluationUpdator from './EvaluationUpdator';
import { useState } from 'react';
import IconArrowFullDown from '../../Icon/IconArrowFullDown';
import dayjs from 'dayjs';
import EvaluationUpdatorV2 from './EvaluationUpdatorv2';
import EvaluationResultComment from './EvaluationResultComment';
import EvaluationUpdatorV3 from './EvaluationUpdatorV3';
import { useTranslation } from 'react-i18next';
import { getTranslatedResult } from '../../../../lib/utils';

interface EvaluationResultItemProps {
  subStep: EvaluationResultSubStep;
  isFocus: boolean;
  evaluationId: string;
  onSubmit: (comment: string, manualResult: string) => void;
}

const EvaluationResultItem: React.FC<EvaluationResultItemProps> = ({
  subStep,
  isFocus,
  evaluationId,
  onSubmit,
}) => {
  const [openItem, setOpenItem] = useState<string | null>(null); // 追踪当前打开的 item

  const handleValueChange = (value: string | null) => {
    setOpenItem(value);
  };

  const { t } = useTranslation();

  const isEdited =
    subStep.manualDetailList && subStep.manualDetailList.length > 0;

  const displayResult =
    subStep.manualResult && subStep.manualResult.length > 0
      ? subStep.manualResult
      : subStep.autoResult;

  // display result percentage to 2 decimal places
  const resultPercentage = subStep?.autoRate;
  const displayResultPercentage =
    resultPercentage || resultPercentage === 0
      ? (resultPercentage * 100).toString().split('.')[1]?.length > 3
        ? (Math.round(resultPercentage * 100 * 100) / 100).toFixed(2)
        : (resultPercentage * 100).toString()
      : '--';

  return (
    <Accordion.Root
      type="single"
      collapsible
      className="my-1"
      value={openItem || ''}
      onValueChange={handleValueChange}
    >
      <Accordion.Item
        value={`${subStep.subStepId}-${subStep.subStepName}`}
        className={`transition-all ${isFocus ? 'border border-[#FFAC4A] p-2 rounded-md' : 'm-2'}`}
      >
        <Accordion.Header>
          <Accordion.Trigger className="w-full">
            <div className="flex">
              {/* result description */}
              <span className="flex-1 text-left">
                {subStep.subStepId}
                {` `}
                {subStep.subStepName}
              </span>
              {/* result evaluation */}
              <div className="flex flex-wrap items-center">
                <span
                  className={`flex-wrap ${
                    displayResult.toLowerCase() === 'passed'
                      ? 'text-[#57B62D]'
                      : displayResult.toLowerCase() === 'failed'
                        ? 'text-[#FF271C]'
                        : 'text-primary-500'
                  }`}
                >
                  {t(
                    getTranslatedResult(
                      displayResult.toLowerCase(),
                      'evaluation'
                    )
                  )}
                </span>
                <span>&nbsp;{'-'}&nbsp;</span>
                <span>{displayResultPercentage}</span>
                <span>{`% ${t('evaluation.matched')}`}</span>
                <span
                  className={`m-1 transition-transform duration-300 ease-in-out ${
                    openItem === `${subStep.subStepId}-${subStep.subStepName}`
                      ? 'rotate-180'
                      : 'rotate-0'
                  }`}
                >
                  <IconArrowFullDown
                    color={
                      openItem === `${subStep.subStepId}-${subStep.subStepName}`
                        ? '#FFAC4A'
                        : '#000000'
                    }
                    size={'10'}
                  />
                </span>
              </div>
            </div>
          </Accordion.Trigger>
          {/* item edited status */}
          {isEdited && <EvaluationResultComment subStep={subStep} />}
        </Accordion.Header>
        <Accordion.Content className="mt-1 mb-4">
          <EvaluationUpdatorV3
            subStep={subStep}
            evaluationId={evaluationId}
            onSubmit={onSubmit}
          />
        </Accordion.Content>
      </Accordion.Item>
    </Accordion.Root>
  );
};

export default EvaluationResultItem;
