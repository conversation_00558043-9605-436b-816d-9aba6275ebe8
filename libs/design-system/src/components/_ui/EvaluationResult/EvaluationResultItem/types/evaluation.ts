// 评估结果，用于display的结构
export interface EvaluationResult {
  stepId: string;
  stepName: string;
  subStep: EvaluationResultSubStep[];
}

export interface EvaluationResultSubStep {
  subStepId: string;
  subStepName: string;
  nlpResultId: string;
  autoRate: number;
  autoResult: string;
  manualResult: string;
  manualComment: string;
  manualUpdateTime?: string;
  notes?: string;
  recordingTimeLocation: string;
  timerStart?: string;
  timerEnd?: string;
  manualDetailList?: ManualDetailItem[];
  importantWordResult?: ImportantWordResult;
  metaDataResult?: MetaDataResult;
  similarityResult?: SimilarityResult;
  othersResult?: QmResultItem[];
}

export interface ManualDetailItem {
  manualDetailId: string;
  type: string;
  referenceId: string;
  tenant: string;
  platform: string;
  createTime: string; // 创建时间
  updateTime: string;
  createBy: string; // 创建人
  updateBy: string;
  manualResult: string; // 人工结果 Passed | Failed
  comment: string; // 备注
}

export interface MetaDataResult {
  entityResults?: EntityResult[];
  matchMinCorrect?: boolean;
  allMatch?: boolean;
}

export interface EntityResult {
  entityType?: string;
  entityValue?: string;
  speaker?: string;
  entityValueMatch?: boolean;
}

export interface SimilarityResult {
  similarityResultDetailList?: SimilarityResultDetailList[];
  allMatch?: boolean;
}

export interface ImportantWordResult {
  importantWordDetailList?: ImportantWordDetailList[];
  allMatch?: boolean;
}

export interface ImportantWordDetailList {
  entityType?: string;
  entityValue?: string;
  speaker?: string;
  existed?: boolean;
  transcripts?: string[];
}

export interface QmResultItem {
  metaDataResult: MetaDataResult;
  finalRate: number;
  finalResult: string;
  systemResult: string;
  comment: string;
  createTime: Date;
  updateTime: null;
  createBy: string;
  updateBy: string;
  platform: string;
  tenant: string;
  evaluationId: string;
  extractionResult: string;
  similarityResult: SimilarityResult;
  standardScriptId: string;
  transcriptMasterId: string;
  nplResultId: string;
  timerStart: string;
  timerEnd: string;
  systemNote: string;
  displayContent: string;
  standardScript: StandardScript;
  manualDetailList: null;
  importantWordResult: ImportantWordResult;
  othersResult?: QmResultItem[];
}

export interface SimilarityResultDetailList {
  similarityScore: number;
  similarityResult: boolean;
  startTime: string;
  endTime: string;
  speaker: string;
  text_segment_with_higheset_similarity: TextSegmentWithHighesetSimilarity;
  best_transcript: string;
  full_transcripts: null;
  extractionResults: null;
  metaResults: MetaResults;
  importantWordResults: null;
}

export interface MetaResults {
  entityResults: null;
  matchMinCorrect: boolean;
}

export interface TextSegmentWithHighesetSimilarity {
  transcript: string;
  standard_script: string;
}

export interface StandardScript {
  standardScriptId: string;
  formId: string;
  stepId: number;
  scenarioId: string;
  scriptOrder: number;
  content: string;
  helpContent: string;
  participant: string;
  doSimilarity: boolean;
  doExtraction: boolean;
  createTime: Date;
  updateTime: null;
  createBy: string;
  updateBy: string;
  platform: string;
  tenant: string;
  enable: string;
  similarityRequired: number;
  standardScriptRules: null;
  useDictionary: boolean;
}
