import Message, { TMessageStatus } from '../Message';
import { cn } from '../../../lib/utils';

type TFieldProps = {
  title?: string | React.ReactNode;
  placeholder?: string;
  value?: string | number;
  disabled?: boolean;
  defaultValue?: string | number;
  icon?: React.ReactNode;
  status?: TMessageStatus;
  message?: string;
  className?: string;
  titleClassName?: string;
  messageClassName?: string;
  children: React.ReactNode;
  childrenContainerClassName?: string;
  direction?: 'vertical' | 'horizontal';
  isRequired?: boolean;
};

const Field: React.FC<TFieldProps> = ({
  title,
  icon,
  status,
  message,
  className,
  childrenContainerClassName,
  children,
  direction = 'vertical',
  isRequired,
}) => {
  const messageClasses = {
    danger: 'text-status-danger',
    info: 'text-status-info',
    success: 'text-status-success',
    warning: 'text-status-warning',
  };

  return (
    <div
      className={cn(
        'flex flex-col gap-1',
        direction === 'horizontal' && 'flex-row gap-2 w-full items-center',
        className
      )}
    >
      {title && (
        <div
          className={cn(
            `leading-[25.2px]`,
            direction === 'vertical' && 'text-body font-bold',
            direction === 'horizontal' &&
              'text-remark leading-[25.2px] whitespace-nowrap min-w-[60px] w-1/3',
            status && message && messageClasses[status]
          )}
        >
          {isRequired && (
            <span className="text-red-500 text-[14px] leading-none translate-y-[-2px] mr-0.5">
              *
            </span>
          )}
          {title}
        </div>
      )}
      <div className={cn('w-full', childrenContainerClassName)}>
        {children}

        {message && (
          <Message
            icon={icon}
            status={status}
            message={message}
          />
        )}
      </div>
    </div>
  );
};

export default Field;
