import React from 'react';
import { FilterComponentProps } from './config';
import Icon from '../Icon';
import { useTranslation } from 'react-i18next';
import ReactDatePicker from 'react-datepicker';
import dayjs from 'dayjs';
const DateRangeFilter: React.FC<FilterComponentProps> = ({
  labelCh,
  labelEn,
  value,
  onChange,
}) => {
  const { i18n } = useTranslation();

  const isContersationTimeInput =
    value.value === 'conversationEnd' ||
    value.value === 'conversationStart' ||
    value.value === 'createdAt';

  return (
    <div className="flex flex-row items-center gap-2">
      <div className="flex-none inline-flex relative">
        <input
          className="appearance-none peer cursor-pointer w-[21px] h-[21px] border-2 rounded-md border-primary-500 checked:bg-primary-500 disabled:bg-grey-200 disabled:border-grey-200 checked:after:text-white"
          type="checkbox"
          checked={isContersationTimeInput ? true : value.checked}
          disabled={isContersationTimeInput}
          onChange={() => onChange({ ...value, checked: !value.checked })}
        />
        <Icon
          size={11}
          name="check"
          className={
            'absolute left-[5px] top-[5px] hidden peer-checked:block text-white pointer-events-none'
          }
        />
      </div>

      <label
        className="basis-1/5 pr-6"
        style={{ textAlignLast: 'justify' }}
      >
        {i18n.language === 'en' ? labelEn : labelCh}
      </label>
      <div className="basis-2/5 *:w-full">
        <ReactDatePicker
          dateFormat={'yyyy-MM-dd HH:mm:ss'}
          disabled={!value.checked}
          className="block w-full h-full outline-none ring-0 p-2 border border-grey-300 rounded-md disabled:bg-grey-200 disabled:text-neutral-500 focus:border-primary"
          maxDate={
            value.data && typeof value.data === 'object'
              ? dayjs(value.data['end']).add(-1, 'day').toDate()
              : null
          }
          selected={
            value.data &&
            typeof value.data === 'object' &&
            value.data['start'] !== '' &&
            value.data['start'] !== undefined
              ? dayjs(value.data['start']).toDate()
              : null
          }
          onChange={(date) =>
            onChange({
              ...value,
              data: {
                ...(typeof value.data === 'object' ? value.data : {}),
                start: date ? dayjs(date).toISOString() : '',
              },
            })
          }
          onKeyDown={(e) => {
            e.preventDefault();
          }}
          showTimeSelect
        />
      </div>
      <div>-</div>
      <div className="basis-2/5 *:w-full">
        <ReactDatePicker
          dateFormat={'yyyy-MM-dd HH:mm:ss'}
          disabled={!value.checked}
          className="block w-full h-full outline-none ring-0 p-2 border border-grey-300 rounded-md disabled:bg-grey-200 disabled:text-neutral-500 focus:border-primary"
          selected={
            value.data &&
            typeof value.data === 'object' &&
            value.data['end'] !== '' &&
            value.data['end'] !== undefined
              ? dayjs(value.data['end']).toDate()
              : null
          }
          minDate={
            value.data && typeof value.data === 'object'
              ? dayjs(value.data['start']).add(1, 'day').toDate()
              : null
          }
          onChange={(date) =>
            onChange({
              ...value,
              data: {
                ...(typeof value.data === 'object' ? value.data : {}),
                end: date ? dayjs(date).toISOString() : '',
              },
            })
          }
          onKeyDown={(e) => {
            e.preventDefault();
          }}
          showTimeSelect
        />
      </div>
    </div>
  );
};

export default DateRangeFilter;
