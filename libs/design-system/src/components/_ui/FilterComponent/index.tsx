import React from 'react';
import filterTypeToComponent from './config';

interface FilterComponentProps {
  filterValues: Record<string, Condition>;
  setFilterValues: (filterValues: Record<string, Condition>) => void;
  onRequest?: (
    value: Condition,
    setOptions: (options: unknown) => void
  ) => void;
}

export interface Condition {
  labelCh: string;
  labelEn: string;
  data?: string | Record<string, string>;
  rule?: string;
  value: string;
  filterType: string;
  active: boolean;
  checked: boolean;
  readOnly?: boolean;
  require?: boolean;
}

const FilterComponent: React.FC<FilterComponentProps> = ({
  filterValues,
  setFilterValues,
  onRequest,
}) => {
  const handleFilterChange = (filterValue: Condition) => {
    const newFilterValues = {
      ...filterValues,
      [filterValue.value]: filterValue,
    };
    setFilterValues(newFilterValues);
  };

  // Sort filters to ensure conversationStart and conversationEnd are in the correct order
  const sortedFilters = [...Object.values(filterValues)].sort((a, b) => {
    if (a.value === 'conversationStart') return -1;
    if (b.value === 'conversationStart') return 1;
    if (a.value === 'conversationEnd') return -1;
    if (b.value === 'conversationEnd') return 1;
    return 0;
  });

  return (
    <div className="flex flex-col gap-2 w-full">
      {sortedFilters.map((filter, index) => {
        if (!filter.active) return null; // Skip inactive filters

        const FilterComponent = filterTypeToComponent[filter.filterType];
        if (!FilterComponent) return null; // Skip if no component is mapped

        const componentProps = onRequest
          ? {
              labelCh: filter.labelCh,
              labelEn: filter.labelEn,
              value: filterValues[filter.value],
              onChange: handleFilterChange,
              onRequest: (
                value: Condition,
                setOptions: (options: unknown) => void
              ) => {
                onRequest(value, setOptions);
              },
            }
          : {
              labelCh: filter.labelCh,
              labelEn: filter.labelEn,
              value: filterValues[filter.value],
              onChange: handleFilterChange,
            };

        return (
          <FilterComponent
            key={index}
            {...componentProps}
          />
        );
      })}
    </div>
  );
};

export default FilterComponent;
