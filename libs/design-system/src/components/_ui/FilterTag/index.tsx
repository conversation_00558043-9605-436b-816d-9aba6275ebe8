import IconTagClose from '../Icon/IconTagClose';

interface IFilterTagProps {
  id: string;
  tagName: string | null;
  showCloseIcon?: boolean;
  onDelete: () => void;
}

const FilterTag = ({
  id,
  tagName,
  showCloseIcon = true,
  onDelete,
}: IFilterTagProps) => {
  return (
    <div
      id={id}
      className="bg-[#f0f0f0] p-1 m-1 flex flex-row text-[12px] rounded-sm justify-center self-center"
    >
      <div className="self-center truncate">{tagName ?? ''}</div>
      {showCloseIcon && (
        <IconTagClose
          size="10px"
          className="self-center m-1"
          onClick={onDelete}
        />
      )}
    </div>
  );
};

export default FilterTag;
