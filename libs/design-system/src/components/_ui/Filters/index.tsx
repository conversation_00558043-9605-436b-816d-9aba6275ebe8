import Icon from '../Icon';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '../DropdownMenu';

type TFiltersProps = {
  children?: React.ReactNode;
  addBtnLabel?: string;
};

const Filters = ({ children, addBtnLabel }: TFiltersProps) => {
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger className="relative inline-flex items-center gap-2">
        <Icon
          name="plus"
        />
        {addBtnLabel ?? 'Add filter'}
      </DropdownMenuTrigger>
      <DropdownMenuContent className="bg-white p-4 max-h-[400px] overflow-auto">
        {children}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default Filters;
