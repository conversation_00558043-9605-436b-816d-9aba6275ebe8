import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconArrowLeft: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 16 17"
    >
      <g clipPath="url(#clip0_2140_6656)">
        <path
          d="M8 16.5C10.1217 16.5 12.1566 15.6571 13.6569 14.1569C15.1571 12.6566 16 10.6217 16 8.5C16 6.37827 15.1571 4.34344 13.6569 2.84315C12.1566 1.34285 10.1217 0.5 8 0.5C5.87827 0.5 3.84344 1.34285 2.34315 2.84315C0.842855 4.34344 0 6.37827 0 8.5C0 10.6217 0.842855 12.6566 2.34315 14.1569C3.84344 15.6571 5.87827 16.5 8 16.5Z"
          fill="white"
        />
        <path
          d="M16 8.5C16 6.37827 15.1571 4.34344 13.6569 2.84315C12.1566 1.34285 10.1217 0.5 8 0.5C5.87827 0.5 3.84344 1.34285 2.34315 2.84315C0.842854 4.34344 0 6.37827 0 8.5C0 10.6217 0.842854 12.6566 2.34315 14.1569C3.84344 15.6571 5.87827 16.5 8 16.5C10.1217 16.5 12.1566 15.6571 13.6569 14.1569C15.1571 12.6566 16 10.6217 16 8.5ZM7.21875 4.46875C7.5125 4.175 7.9875 4.175 8.27812 4.46875C8.56875 4.7625 8.57187 5.2375 8.27812 5.52812L6.05937 7.74687L11.75 7.75C12.1656 7.75 12.5 8.08438 12.5 8.5C12.5 8.91563 12.1656 9.25 11.75 9.25H6.05937L8.27812 11.4688C8.57187 11.7625 8.57187 12.2375 8.27812 12.5281C7.98438 12.8188 7.50937 12.8219 7.21875 12.5281L3.71875 9.03125C3.425 8.7375 3.425 8.2625 3.71875 7.97188L7.21875 4.46875Z"
          // fill="#C6C6C6"
        />
      </g>
      <defs>
        <clipPath id="clip0_2140_6656">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(0 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconArrowLeft;
