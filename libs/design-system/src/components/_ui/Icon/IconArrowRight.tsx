import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconArrowRight: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 16 17"
    >
      <g clipPath="url(#clip0_2140_6827)">
        <path
          d="M8 16C10.1217 16 12.1566 15.1571 13.6569 13.6569C15.1571 12.1566 16 10.1217 16 8C16 5.87827 15.1571 3.84344 13.6569 2.34315C12.1566 0.842855 10.1217 0 8 0C5.87827 0 3.84344 0.842855 2.34315 2.34315C0.842855 3.84344 0 5.87827 0 8C0 10.1217 0.842855 12.1566 2.34315 13.6569C3.84344 15.1571 5.87827 16 8 16Z"
          fill="white"
        />
        <path
          d="M0 8C0 10.1217 0.842855 12.1566 2.34315 13.6569C3.84344 15.1571 5.87827 16 8 16C10.1217 16 12.1566 15.1571 13.6569 13.6569C15.1571 12.1566 16 10.1217 16 8C16 5.87827 15.1571 3.84344 13.6569 2.34315C12.1566 0.842855 10.1217 0 8 0C5.87827 0 3.84344 0.842855 2.34315 2.34315C0.842855 3.84344 0 5.87827 0 8ZM8.78125 12.0312C8.4875 12.325 8.0125 12.325 7.72188 12.0312C7.43125 11.7375 7.42813 11.2625 7.72188 10.9719L9.94063 8.75313L4.25 8.75C3.83437 8.75 3.5 8.41562 3.5 8C3.5 7.58437 3.83437 7.25 4.25 7.25H9.94063L7.72188 5.03125C7.42813 4.7375 7.42813 4.2625 7.72188 3.97187C8.01562 3.68125 8.49063 3.67812 8.78125 3.97187L12.2812 7.46875C12.575 7.7625 12.575 8.2375 12.2812 8.52812L8.78125 12.0312Z"
          //  fill="#C6C6C6"
        />
      </g>
      <defs>
        <clipPath id="clip0_2140_6827">
          <rect
            width="16"
            height="16"
            fill="white"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconArrowRight;
