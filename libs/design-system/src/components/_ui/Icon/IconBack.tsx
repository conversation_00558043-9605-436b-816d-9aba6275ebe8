import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconBack: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 18 18"
      aria-label={alt}
    >
      <g clipPath="url(#clip0_1688_668)">
        <path
          d="M4.27692 8.59227C3.77748 9.09433 3.77748 9.90968 4.27692 10.4117L11.9483 18.1235C12.4477 18.6255 13.2588 18.6255 13.7582 18.1235C14.2577 17.6214 14.2577 16.806 13.7582 16.304L6.98986 9.5L13.7542 2.69603C14.2537 2.19396 14.2537 1.37861 13.7542 0.876548C13.2548 0.374484 12.4437 0.374484 11.9443 0.876548L4.27293 8.58825L4.27692 8.59227Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_1688_668">
          <rect
            width="18"
            height="18"
            fill="white"
            transform="translate(0 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconBack;
