interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconBehalf: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 12 12"
      aria-label={alt}
    >
      <rect
        width="12"
        height="12"
        fill="url(#pattern0_51_2183)"
      />
      <defs>
        <pattern
          id="pattern0_51_2183"
          patternContentUnits="objectBoundingBox"
          width="1"
          height="1"
        >
          <use
            xlinkHref="#image0_51_2183"
            transform="scale(0.005)"
          />
        </pattern>
        <image
          id="image0_51_2183"
          width="200"
          height="200"
          xlinkHref="data:image/png;base64,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"
        />
      </defs>
    </svg>
  );
};

export default IconBehalf;
