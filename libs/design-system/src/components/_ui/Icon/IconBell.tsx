import { cn } from '@cdss-modules/design-system/lib/utils';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
  selected?: boolean;
}

const IconBell: React.FC<IconProps> = ({ size, alt, className, selected }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 40 40"
      aria-label={alt}
    >
      <path
        d="M0 4C0 1.79086 1.79086 0 4 0H30C32.2091 0 34 1.79086 34 4V30C34 32.2091 32.2091 34 30 34H4C1.79086 34 0 32.2091 0 30V4Z"
        fill={className}
      />
      <path
        d="M16.8521 8C16.238 8 15.7418 8.50273 15.7418 9.125V9.8C13.2087 10.3203 11.3002 12.5914 11.3002 15.3125V15.9734C11.3002 17.6258 10.6999 19.2219 9.61726 20.4594L9.36048 20.7512C9.069 21.0816 8.9996 21.5563 9.17657 21.9605C9.35354 22.3648 9.75259 22.625 10.1898 22.625H23.5145C23.9517 22.625 24.3473 22.3648 24.5277 21.9605C24.7081 21.5563 24.6353 21.0816 24.3438 20.7512L24.087 20.4594C23.0044 19.2219 22.4041 17.6293 22.4041 15.9734V15.3125C22.4041 12.5914 20.4956 10.3203 17.9625 9.8V9.125C17.9625 8.50273 17.4663 8 16.8521 8ZM18.424 25.3426C18.8404 24.9207 19.0729 24.3477 19.0729 23.75H16.8521H14.6314C14.6314 24.3477 14.8638 24.9207 15.2802 25.3426C15.6966 25.7645 16.2622 26 16.8521 26C17.442 26 18.0076 25.7645 18.424 25.3426Z"
        // fill="#636363"
        className={cn(
          `fill-[#636363] group-hover:fill-primary-900`,
          selected === true && 'fill-white'
        )}
      />
    </svg>
  );
};

export default IconBell;
