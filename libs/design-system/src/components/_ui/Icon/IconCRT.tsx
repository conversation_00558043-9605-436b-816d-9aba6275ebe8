import React from 'react';

interface IconChatProps {
  size?: string;
  alt?: string;
  className?: string;
  isActive: boolean;
  isDisable?: boolean; // New prop for disabled state
  onClick?: () => void;
}

const IconChat: React.FC<IconChatProps> = ({
  size = '24px',
  alt = 'Chat icon',
  className = '',
  isActive,
  isDisable = false, // Default to false
  onClick,
}) => {
  const handleClick = () => {
    if (onClick && !isDisable) {
      onClick();
    }
  };

  return (
    <svg
      className={`${className} ${isDisable ? 'opacity-50' : 'cursor-pointer'}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onClick={handleClick}
    >
      <path
        d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z"
        stroke={isDisable ? '#A6A6A6' : isActive ? '#FFAC4A' : '#000000'}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8 12C8.53043 12 9.03914 11.7893 9.41421 11.4142C9.78929 11.0391 10 10.5304 10 10V8H8"
        stroke={isDisable ? '#A6A6A6' : isActive ? '#FFAC4A' : '#000000'}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14 12C14.5304 12 15.0391 11.7893 15.4142 11.4142C15.7893 11.0391 16 10.5304 16 10V8H14"
        stroke={isDisable ? '#A6A6A6' : isActive ? '#FFAC4A' : '#000000'}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default IconChat;
