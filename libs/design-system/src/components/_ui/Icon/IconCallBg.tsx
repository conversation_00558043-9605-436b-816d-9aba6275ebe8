import React from 'react';

interface IconProps {
    size?: string;
    alt?: string;
    className?: string;
}

const IconCallBg: React.FC<IconProps> = ({ size, alt, className }) => {
    return (

        <svg style={{ width: size, height: size }} className={`fill-none ${className}`} viewBox="0 0 192 161" xmlns="http://www.w3.org/2000/svg">
            <circle cx="100.5" cy="78" r="65" fill="#F5F5F5" />
            <circle cx="100.5" cy="78" r="77.5" stroke="#F2F2F2" />
            <path d="M13.5 40C13.5 20.67 29.17 5 48.5 5C67.83 5 83.5 20.67 83.5 40C83.5 59.33 67.83 75 48.5 75C29.17 75 13.5 59.33 13.5 40Z" fill="#E7E7E7" />
            <g clipPath="url(#clip0_670_2712)">
                <path d="M139.604 105.402C138.852 103.586 136.869 102.619 134.975 103.136L126.381 105.48C124.682 105.949 123.5 107.492 123.5 109.25C123.5 133.41 143.09 153 167.25 153C169.008 153 170.551 151.818 171.02 150.119L173.363 141.525C173.881 139.631 172.914 137.648 171.098 136.896L161.723 132.99C160.131 132.326 158.285 132.785 157.201 134.123L153.256 138.937C146.381 135.685 140.814 130.119 137.563 123.244L142.377 119.308C143.715 118.215 144.174 116.379 143.51 114.787L139.604 105.412V105.402Z" fill="#FFAC4A" />
                <path d="M139.604 105.403C138.852 103.586 136.869 102.619 134.975 103.137L126.381 105.481C124.682 105.949 123.5 107.492 123.5 109.25C123.5 133.41 143.09 153 167.25 153C169.008 153 170.551 151.819 171.02 150.119L173.363 141.526C173.881 139.631 172.914 137.649 171.098 136.897L161.723 132.99C160.131 132.326 158.285 132.785 157.201 134.123L153.256 138.938C146.381 135.686 140.814 130.119 137.563 123.244L142.377 119.309C143.715 118.215 144.174 116.379 143.51 114.787L139.604 105.412V105.403Z" fill="#D5D5D5" />
            </g>
            <circle cx="132" cy="22.5" r="8.5" fill="#D9D9D9" />
            <circle cx="90.5" cy="153" r="8" fill="#D9D9D9" />
            <circle cx="56.5" cy="97" r="4" fill="#D9D9D9" />
            <circle cx="187.5" cy="107" r="4" fill="#D9D9D9" />
            <circle cx="4.5" cy="74" r="4" fill="#D9D9D9" />
            <path d="M61.4982 49.4801C61.5326 50.8894 61.0679 52.2663 60.1844 53.3734C59.8204 53.873 59.3422 54.2807 58.7887 54.5633C58.2351 54.846 57.6219 54.9956 56.9987 55H39.0009C38.3791 54.9949 37.7674 54.8449 37.2154 54.5623C36.6634 54.2796 36.1868 53.8723 35.8242 53.3734C34.9388 52.2668 34.4711 50.8903 34.5014 49.4801C34.4938 48.3547 34.5539 47.2298 34.6814 46.1114C34.8054 45.0358 35.0283 43.9737 35.3473 42.9381C35.6297 41.9882 36.0446 41.0817 36.5801 40.2448C37.0819 39.4796 37.7604 38.8429 38.5599 38.3871C39.4259 37.9011 40.4077 37.6525 41.4036 37.6671C42.2654 38.5264 43.2936 39.2054 44.4269 39.6636C45.5602 40.1217 46.7754 40.3496 47.9998 40.3337C49.2258 40.3508 50.4426 40.1235 51.5775 39.6653C52.7125 39.2071 53.7422 38.5275 54.605 37.6671C55.6014 37.6486 56.5842 37.8974 57.4487 38.3871C58.2461 38.8455 58.9241 39.4817 59.4285 40.2448C59.9662 41.09 60.3812 42.0056 60.6613 42.9648C60.9796 43.9915 61.2025 45.0447 61.3272 46.1114C61.447 47.2303 61.5041 48.355 61.4982 49.4801ZM53.7231 25.3475C54.489 26.0795 55.095 26.9588 55.5036 27.9311C55.9122 28.9034 56.1148 29.948 56.0989 31.0006C56.1148 32.0532 55.9122 33.0979 55.5036 34.0702C55.095 35.0425 54.489 35.9218 53.7231 36.6538C52.982 37.4103 52.0918 38.0089 51.1074 38.4125C50.1231 38.816 49.0655 39.0161 47.9998 39.0004C46.9341 39.0161 45.8765 38.816 44.8922 38.4125C43.9078 38.0089 43.0176 37.4103 42.2765 36.6538C41.5106 35.9218 40.9046 35.0425 40.496 34.0702C40.0874 33.0979 39.8848 32.0532 39.9008 31.0006C39.8848 29.948 40.0874 28.9034 40.496 27.9311C40.9046 26.9588 41.5106 26.0795 42.2765 25.3475C43.0176 24.5909 43.9078 23.9924 44.8922 23.5888C45.8765 23.1852 46.9341 22.9851 47.9998 23.0009C49.0655 22.9851 50.1231 23.1852 51.1074 23.5888C52.0918 23.9924 52.982 24.5909 53.7231 25.3475Z" fill="white" />
            <defs>
                <clipPath id="clip0_670_2712">
                    <rect width="50" height="50" fill="white" transform="translate(123.5 103)" />
                </clipPath>
            </defs>
        </svg>

    );
};

export default IconCallBg;
