import React from 'react';
interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
  color?: string;
}

const IconChat: React.FC<IconProps> = ({ size, alt, className, color }) => {
  return (
    <svg
      className={`fill-none ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 16 16"
      aria-label={alt}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.26683 13.3337C6.53921 13.9864 8.00289 14.1632 9.39409 13.8322C10.7853 13.5012 12.0125 12.6842 12.8547 11.5285C13.6968 10.3727 14.0984 8.95416 13.9872 7.52846C13.876 6.10277 13.2592 4.76367 12.248 3.75249C11.2368 2.7413 9.89775 2.12452 8.47206 2.0133C7.04636 1.90208 5.62781 2.30372 4.47204 3.14585C3.31627 3.98799 2.49929 5.21523 2.16831 6.60643C1.83733 7.99763 2.01412 9.46131 2.66683 10.7337L1.3335 14.667L5.26683 13.3337Z"
        stroke={color}
        strokeWidth="1.3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default IconChat;
