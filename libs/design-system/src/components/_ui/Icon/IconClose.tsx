import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconClose: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 20 21"
    >
      <g
        id="icon / Close"
        clipPath="url(#clip0_603_666)"
      >
        <path
          id="Vector"
          d="M19.4081 3.91559C20.189 3.1347 20.189 1.86655 19.4081 1.08566C18.6272 0.304779 17.359 0.304779 16.5782 1.08566L10 7.67008L3.41559 1.09191C2.6347 0.311026 1.36655 0.311026 0.585663 1.09191C-0.195221 1.87279 -0.195221 3.14095 0.585663 3.92183L7.17008 10.5L0.59191 17.0844C-0.188974 17.8653 -0.188974 19.1335 0.59191 19.9143C1.37279 20.6952 2.64095 20.6952 3.42183 19.9143L10 13.3299L16.5844 19.9081C17.3653 20.689 18.6335 20.689 19.4143 19.9081C20.1952 19.1272 20.1952 17.859 19.4143 17.0782L12.8299 10.5L19.4081 3.91559Z"
          // fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_603_666">
          <rect
            width="20"
            height="20"
            fill="white"
            transform="translate(0 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconClose;
