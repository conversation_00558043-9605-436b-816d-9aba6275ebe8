import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconConference: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 20 20"
      aria-label={alt}
    >
      <path
        d="M3.49347 15.7554C3.49347 14.191 4.06235 12.7688 4.91567 11.7021C4.56012 11.5599 4.13346 11.4888 3.77791 11.4888C1.71574 11.4888 0.00910598 13.1954 0.00910598 15.2576C-0.0620036 15.6843 0.293544 16.0398 0.720202 16.0398H3.42236C3.49347 15.9687 3.49347 15.8265 3.49347 15.7554ZM3.84902 11.0622C4.91567 11.0622 5.84009 10.4222 6.19564 9.49775C5.62676 8.71554 5.34232 7.79112 5.34232 6.72447V6.44004C4.91567 6.1556 4.4179 5.94227 3.84902 5.94227C2.42683 5.94227 1.36019 7.08002 1.36019 8.50221C1.28908 9.9244 2.42683 11.0622 3.84902 11.0622ZM16.2221 11.4888C15.7954 11.4888 15.4399 11.5599 15.0843 11.7021C16.0088 12.8399 16.5065 14.191 16.5065 15.7554V16.0398H19.2798C19.7065 16.0398 20.062 15.6843 19.9909 15.2576C19.9909 13.1954 18.2843 11.4888 16.2221 11.4888ZM16.2221 11.0622C17.6443 11.0622 18.7109 9.9244 18.7109 8.50221C18.7109 7.08002 17.5732 5.94227 16.2221 5.94227C15.6532 5.94227 15.1554 6.08449 14.7288 6.44004V6.72447C14.7288 7.72001 14.3732 8.71554 13.8755 9.49775C14.3021 10.4222 15.1554 11.0622 16.2221 11.0622ZM10.0356 10.7066C7.26228 10.7066 4.91567 12.9821 4.91567 15.8265C4.84456 16.2532 5.2001 16.6798 5.62676 16.6798H14.3732C14.7999 16.6798 15.1554 16.2532 15.0843 15.8265C15.1554 12.9821 12.8088 10.7066 10.0356 10.7066ZM10.0356 10.0666C11.8844 10.0666 13.3777 8.57332 13.3777 6.72447C13.3777 4.87562 11.8844 3.38232 10.0356 3.38232C8.18671 3.38232 6.69341 4.87562 6.69341 6.72447C6.69341 8.57332 8.18671 10.0666 10.0356 10.0666Z"
        // fill="black"
      />
    </svg>
  );
};

export default IconConference;
