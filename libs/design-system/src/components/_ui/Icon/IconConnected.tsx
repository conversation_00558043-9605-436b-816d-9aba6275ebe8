import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconConnected: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 14 14"
      fill="none"
    >
      <g clipPath="url(#clip0_2175_9787)">
        <path
          d="M4.14814 1.09762C3.42014 0.91282 2.65714 0.99122 2.05934 1.32582C1.45034 1.66602 1.02334 2.26942 1.00234 3.07652C0.970137 4.33792 1.26204 6.03052 2.42124 8.01712C3.56574 9.97922 4.82924 11.1552 5.87784 11.8538C6.54004 12.2955 7.27154 12.3074 7.89664 12.0197C8.51264 11.7362 9.01244 11.1699 9.27214 10.4678C9.33668 10.2932 9.36259 10.1066 9.34809 9.92101C9.33359 9.73539 9.27902 9.55512 9.18814 9.39262L8.52034 8.20122C8.31506 7.83454 7.98468 7.55397 7.58959 7.41079C7.1945 7.26761 6.76107 7.27138 6.36854 7.42142L5.90234 7.59992C5.67624 7.68602 5.46834 7.64612 5.35074 7.52992C4.95524 7.14142 4.67244 6.61432 4.54434 6.04592C4.50374 5.86672 4.58634 5.65532 4.78374 5.50622L5.20444 5.18842C5.54455 4.93177 5.77934 4.55986 5.86479 4.14244C5.95023 3.72502 5.88045 3.29076 5.66854 2.92112L4.98814 1.73462C4.89797 1.57756 4.77714 1.44024 4.63283 1.33081C4.48853 1.22138 4.3237 1.14208 4.14814 1.09762ZM1.70234 3.09542C1.71634 2.55362 1.99074 2.16652 2.40094 1.93692C2.82234 1.70102 3.39774 1.62892 3.97594 1.77592C4.06066 1.79732 4.14021 1.83555 4.20984 1.88835C4.27947 1.94114 4.33776 2.00742 4.38124 2.08322L5.06094 3.26972C5.18807 3.49141 5.22998 3.75186 5.17882 4.00225C5.12766 4.25263 4.98694 4.47576 4.78304 4.62982L4.36234 4.94762C3.99834 5.22202 3.74634 5.69242 3.86114 6.19992C4.01654 6.88732 4.36024 7.53902 4.86004 8.02972C5.22544 8.38812 5.74624 8.40912 6.15224 8.25372L6.61844 8.07522C6.85398 7.98506 7.11411 7.98269 7.35125 8.06855C7.58839 8.15441 7.78671 8.32276 7.90994 8.54282L8.57704 9.73492C8.66104 9.88472 8.67504 10.0639 8.61554 10.2249C8.41254 10.7744 8.03104 11.1867 7.60404 11.3834C7.18614 11.5759 6.71364 11.5703 6.26634 11.2714C5.30664 10.6316 4.11734 9.53472 3.02534 7.66432C1.93264 5.78832 1.67294 4.22452 1.70234 3.09542Z"
          fill="currentColor"
        />
        <path
          d="M10.5 1C9.83696 1 9.20107 1.26339 8.73223 1.73223C8.26339 2.20107 8 2.83696 8 3.5V5.75C8 5.8163 8.02634 5.87989 8.07322 5.92678C8.12011 5.97366 8.1837 6 8.25 6H10.5C11.163 6 11.7989 5.73661 12.2678 5.26777C12.7366 4.79893 13 4.16304 13 3.5C13 2.83696 12.7366 2.20107 12.2678 1.73223C11.7989 1.26339 11.163 1 10.5 1ZM10.5 5.5H8.5V3.5C8.5 3.10444 8.6173 2.71776 8.83706 2.38886C9.05682 2.05996 9.36918 1.80362 9.73463 1.65224C10.1001 1.50087 10.5022 1.46126 10.8902 1.53843C11.2781 1.6156 11.6345 1.80608 11.9142 2.08579C12.1939 2.36549 12.3844 2.72186 12.4616 3.10982C12.5387 3.49778 12.4991 3.89991 12.3478 4.26537C12.1964 4.63082 11.94 4.94318 11.6111 5.16294C11.2822 5.3827 10.8956 5.5 10.5 5.5Z"
          fill="currentColor"
        />
        <circle
          cx="9.35"
          cy="3.35"
          r="0.35"
          fill="currentColor"
        />
        <circle
          cx="10.35"
          cy="3.35"
          r="0.35"
          fill="currentColor"
        />
        <circle
          cx="11.35"
          cy="3.35"
          r="0.35"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_2175_9787">
          <rect
            width="14"
            height="14"
            fill="white"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconConnected;
