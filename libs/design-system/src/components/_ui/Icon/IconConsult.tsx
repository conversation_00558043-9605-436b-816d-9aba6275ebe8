import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconConsult: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 32 32"
      fill="none"
      aria-label={alt}
    >
      <g
        id="icon / Consult"
        clipPath="url(#clip0_3607_4009)"
      >
        <path
          id="Vector"
          d="M16 32C20.2435 32 24.3131 30.3143 27.3137 27.3137C30.3143 24.3131 32 20.2435 32 16C32 11.7565 30.3143 7.68687 27.3137 4.68629C24.3131 1.68571 20.2435 0 16 0C11.7565 0 7.68687 1.68571 4.68629 4.68629C1.68571 7.68687 0 11.7565 0 16C0 20.2435 1.68571 24.3131 4.68629 27.3137C7.68687 30.3143 11.7565 32 16 32Z"
          fill="white"
        />
        <path
          id="Vector_2"
          d="M16 32C20.2435 32 24.3131 30.3143 27.3137 27.3137C30.3143 24.3131 32 20.2435 32 16C32 11.7565 30.3143 7.68687 27.3137 4.68629C24.3131 1.68571 20.2435 0 16 0C11.7565 0 7.68687 1.68571 4.68629 4.68629C1.68571 7.68687 0 11.7565 0 16C0 20.2435 1.68571 24.3131 4.68629 27.3137C7.68687 30.3143 11.7565 32 16 32ZM10.6125 10.3313C11.1062 8.9375 12.4313 8 13.9125 8H17.5562C19.7375 8 21.5 9.76875 21.5 11.9438C21.5 13.3563 20.7437 14.6625 19.5187 15.3687L17.5 16.525C17.4875 17.3375 16.8188 18 16 18C15.1687 18 14.5 17.3312 14.5 16.5V15.6562C14.5 15.1187 14.7875 14.625 15.2563 14.3562L18.025 12.7688C18.3188 12.6 18.5 12.2875 18.5 11.95C18.5 11.425 18.075 11.0063 17.5562 11.0063H13.9125C13.7 11.0063 13.5125 11.1375 13.4438 11.3375L13.4187 11.4125C13.1438 12.1938 12.2812 12.6 11.5063 12.325C10.7313 12.05 10.3188 11.1875 10.5938 10.4125L10.6187 10.3375L10.6125 10.3313ZM14 22C14 21.4696 14.2107 20.9609 14.5858 20.5858C14.9609 20.2107 15.4696 20 16 20C16.5304 20 17.0391 20.2107 17.4142 20.5858C17.7893 20.9609 18 21.4696 18 22C18 22.5304 17.7893 23.0391 17.4142 23.4142C17.0391 23.7893 16.5304 24 16 24C15.4696 24 14.9609 23.7893 14.5858 23.4142C14.2107 23.0391 14 22.5304 14 22Z"
          fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_3607_4009">
          <rect
            width="32"
            height="32"
            fill="white"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconConsult;
