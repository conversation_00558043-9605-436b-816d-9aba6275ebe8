import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconContactBook: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 16 17"
      fill="none"
      aria-label={alt}
    >
      <g clipPath="url(#clip0_354_267)">
        <path d="M3.22908 1.5C3.22908 0.946875 3.66983 0.5 4.21538 0.5L12.1058 0.5C12.6513 0.5 13.0921 0.946875 13.0921 1.5C13.0921 2.05313 12.6513 2.5 12.1058 2.5H11.1965L11.5479 7.13125C12.6791 7.75312 13.5729 8.79375 13.9983 10.0906L14.0291 10.1844C14.1308 10.4906 14.0784 10.825 13.8935 11.0844C13.7085 11.3438 13.4096 11.5 13.0921 11.5L3.22908 11.5C2.91161 11.5 2.61572 11.3469 2.42771 11.0844C2.23969 10.8219 2.19038 10.4875 2.29209 10.1844L2.32291 10.0906C2.74825 8.79375 3.64209 7.75312 4.77326 7.13125L5.12463 2.5L4.21538 2.5C3.66983 2.5 3.22908 2.05313 3.22908 1.5ZM7.17428 12.5H9.14689V15.5C9.14689 16.0531 8.70613 16.5 8.16059 16.5C7.61504 16.5 7.17428 16.0531 7.17428 15.5V12.5Z" fill="currentColor" />
      </g>
      <defs>
        <clipPath id="clip0_354_267">
          <rect width="16" height="16" fill="white" transform="translate(0 0.5)" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconContactBook;
