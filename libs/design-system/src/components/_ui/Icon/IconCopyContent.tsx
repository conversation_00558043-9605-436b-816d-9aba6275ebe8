import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
  disabled?: boolean;
}

const IconCopyContent: React.FC<IconProps> = ({
  size,
  alt,
  className,
  disabled = false,
}) => {
  return (
    <svg
      className={`fill-none ${className} ${disabled ? 'opacity-50' : 'cursor-pointer'}`}
      style={{ width: size, height: size }}
      viewBox="0 0 20 20"
      aria-label={alt}
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.5"
        y="0.5"
        width="19"
        height="19"
        rx="1.5"
        fill="white"
      />
      <rect
        x="0.5"
        y="0.5"
        width="19"
        height="19"
        rx="1.5"
        stroke="#E0E0E0"
      />
      <path
        d="M7.66051 6.89231C7.5571 6.89231 7.45793 6.85139 7.3848 6.77855C7.31168 6.7057 7.2706 6.60691 7.2706 6.50389V6.11547C7.2706 5.80642 7.39384 5.51003 7.61321 5.2915C7.83257 5.07296 8.1301 4.9502 8.44033 4.9502H8.83024C8.93364 4.9502 9.03282 4.99112 9.10594 5.06396C9.17906 5.13681 9.22014 5.2356 9.22014 5.33862C9.22014 5.44164 9.17906 5.54043 9.10594 5.61328C9.03282 5.68612 8.93364 5.72704 8.83024 5.72704H8.44033C8.33692 5.72704 8.23774 5.76797 8.16462 5.84081C8.0915 5.91365 8.05042 6.01245 8.05042 6.11547V6.50389C8.05042 6.60691 8.00934 6.7057 7.93622 6.77855C7.86309 6.85139 7.76392 6.89231 7.66051 6.89231ZM13.899 12.7187H13.5091C13.4057 12.7187 13.3065 12.6777 13.2334 12.6049C13.1603 12.5321 13.1192 12.4333 13.1192 12.3302C13.1192 12.2272 13.1603 12.1284 13.2334 12.0556C13.3065 11.9827 13.4057 11.9418 13.5091 11.9418H13.899C14.0025 11.9418 14.1016 11.9009 14.1747 11.8281C14.2479 11.7552 14.2889 11.6564 14.2889 11.5534V11.165C14.2889 11.062 14.33 10.9632 14.4032 10.8903C14.4763 10.8175 14.5754 10.7765 14.6789 10.7765C14.7823 10.7765 14.8814 10.8175 14.9546 10.8903C15.0277 10.9632 15.0688 11.062 15.0688 11.165V11.5534C15.0688 11.8624 14.9455 12.1588 14.7262 12.3774C14.5068 12.5959 14.2093 12.7187 13.899 12.7187ZM14.6789 6.89231C14.5754 6.89231 14.4763 6.85139 14.4032 6.77855C14.33 6.7057 14.2889 6.60691 14.2889 6.50389V6.11547C14.2889 6.01245 14.2479 5.91365 14.1747 5.84081C14.1016 5.76797 14.0025 5.72704 13.899 5.72704H13.5091C13.4057 5.72704 13.3065 5.68612 13.2334 5.61328C13.1603 5.54043 13.1192 5.44164 13.1192 5.33862C13.1192 5.2356 13.1603 5.13681 13.2334 5.06396C13.3065 4.99112 13.4057 4.9502 13.5091 4.9502H13.899C14.2093 4.9502 14.5068 5.07296 14.7262 5.2915C14.9455 5.51003 15.0688 5.80642 15.0688 6.11547V6.50389C15.0688 6.60691 15.0277 6.7057 14.9546 6.77855C14.8814 6.85139 14.7823 6.89231 14.6789 6.89231ZM14.6789 9.9997C14.5754 9.9997 14.4763 9.95878 14.4032 9.88593C14.33 9.81309 14.2889 9.71429 14.2889 9.61128V8.05758C14.2889 7.95457 14.33 7.85577 14.4032 7.78293C14.4763 7.71008 14.5754 7.66916 14.6789 7.66916C14.7823 7.66916 14.8814 7.71008 14.9546 7.78293C15.0277 7.85577 15.0688 7.95457 15.0688 8.05758V9.61128C15.0688 9.71429 15.0277 9.81309 14.9546 9.88593C14.8814 9.95878 14.7823 9.9997 14.6789 9.9997ZM11.9495 5.72704H10.3899C10.2865 5.72704 10.1873 5.68612 10.1142 5.61328C10.041 5.54043 9.99996 5.44164 9.99996 5.33862C9.99996 5.2356 10.041 5.13681 10.1142 5.06396C10.1873 4.99112 10.2865 4.9502 10.3899 4.9502H11.9495C12.0529 4.9502 12.1521 4.99112 12.2252 5.06396C12.2983 5.13681 12.3394 5.2356 12.3394 5.33862C12.3394 5.44164 12.2983 5.54043 12.2252 5.61328C12.1521 5.68612 12.0529 5.72704 11.9495 5.72704ZM11.5596 7.28074H6.10088C5.79065 7.28074 5.49312 7.40351 5.27376 7.62204C5.05439 7.84057 4.93115 8.13696 4.93115 8.44601V13.8839C4.93115 14.193 5.05439 14.4894 5.27376 14.7079C5.49312 14.9264 5.79065 15.0492 6.10088 15.0492H11.5596C11.8698 15.0492 12.1673 14.9264 12.3867 14.7079C12.6061 14.4894 12.7293 14.193 12.7293 13.8839V8.44601C12.7293 8.13696 12.6061 7.84057 12.3867 7.62204C12.1673 7.40351 11.8698 7.28074 11.5596 7.28074ZM9.99996 11.5534H9.22014V12.3302C9.22014 12.4333 9.17906 12.5321 9.10594 12.6049C9.03282 12.6777 8.93364 12.7187 8.83024 12.7187C8.72682 12.7187 8.62765 12.6777 8.55453 12.6049C8.48141 12.5321 8.44033 12.4333 8.44033 12.3302V11.5534H7.66051C7.5571 11.5534 7.45793 11.5125 7.3848 11.4396C7.31168 11.3668 7.2706 11.268 7.2706 11.165C7.2706 11.062 7.31168 10.9632 7.3848 10.8903C7.45793 10.8175 7.5571 10.7765 7.66051 10.7765H8.44033V9.9997C8.44033 9.89668 8.48141 9.79789 8.55453 9.72504C8.62765 9.6522 8.72682 9.61128 8.83024 9.61128C8.93364 9.61128 9.03282 9.6522 9.10594 9.72504C9.17906 9.79789 9.22014 9.89668 9.22014 9.9997V10.7765H9.99996C10.1034 10.7765 10.2025 10.8175 10.2757 10.8903C10.3488 10.9632 10.3899 11.062 10.3899 11.165C10.3899 11.268 10.3488 11.3668 10.2757 11.4396C10.2025 11.5125 10.1034 11.5534 9.99996 11.5534Z"
        fill="#636363"
      />
    </svg>
  );
};

export default IconCopyContent;
