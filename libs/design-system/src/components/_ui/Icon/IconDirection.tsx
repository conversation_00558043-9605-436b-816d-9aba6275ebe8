interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
  color?: string;
}

const IconDirection: React.FC<IconProps> = ({
  size,
  alt,
  className,
  color,
}) => {
  return (
    <svg
      className={`fill-none ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 16 16"
      aria-label={alt}
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2320_4928)">
        <path
          d="M10.8267 5.17285L9.62401 8.78018C9.55855 8.97657 9.44827 9.15502 9.30189 9.3014C9.15551 9.44778 8.97706 9.55806 8.78067 9.62352L5.17334 10.8262L6.37601 7.21885C6.44146 7.02246 6.55175 6.84401 6.69812 6.69764C6.8445 6.55126 7.02295 6.44097 7.21934 6.37552L10.8267 5.17285Z"
          stroke={color}
          strokeWidth="1.33333"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M8.00016 14.6663C11.6821 14.6663 14.6668 11.6816 14.6668 7.99967C14.6668 4.31778 11.6821 1.33301 8.00016 1.33301C4.31826 1.33301 1.3335 4.31778 1.3335 7.99967C1.3335 11.6816 4.31826 14.6663 8.00016 14.6663Z"
          stroke={color}
          strokeWidth="1.33333"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_2320_4928">
          <rect
            width="16"
            height="16"
            fill="white"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconDirection;
