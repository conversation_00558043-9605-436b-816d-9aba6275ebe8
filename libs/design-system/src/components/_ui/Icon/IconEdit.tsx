import React from 'react';
interface IconProps {
    size?: string;
    alt?: string;
    className?: string;
    color?: string;
}

const IconEdit: React.FC<IconProps> = ({ size, alt, className }) => {
    return (
        <svg className={`fill-none ${className}`}
            style={{ width: size, height: size }}
            aria-label={alt} viewBox="0 0 13 13"  xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_3528_44825)">
                <path d="M8.02819 2.17273L0.875422 9.33355C0.839404 9.3697 0.813734 9.41486 0.80109 9.46433L0.00824775 12.6499C-0.00341295 12.6972 -0.00269834 12.7466 0.0103225 12.7936C0.0233434 12.8405 0.0482306 12.8832 0.08258 12.9177C0.13534 12.9704 0.206776 12.9999 0.281266 13C0.304245 13 0.327136 12.9972 0.349421 12.9915L3.53164 12.1978C3.58112 12.1853 3.62629 12.1596 3.6623 12.1235L10.8157 4.96314L8.02819 2.17273ZM12.5878 1.19636L11.7915 0.399334C11.2594 -0.133376 10.3319 -0.132847 9.80036 0.399334L8.82504 1.3757L11.6124 4.16601L12.5878 3.18966C12.8536 2.92368 13 2.56962 13 2.19308C13 1.81654 12.8536 1.46248 12.5878 1.19636Z" fill="black" />
            </g>
            <defs>
                <clipPath id="clip0_3528_44825">
                    <rect style={{ width: size, height: size }} fill="white" />
                </clipPath>
            </defs>
        </svg>

    );
};

export default IconEdit;
