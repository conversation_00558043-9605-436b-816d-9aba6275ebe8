import React from 'react';
interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
  color?: string;
}

const IconEmail: React.FC<IconProps> = ({ size, alt, className, color }) => {
  return (
    <svg
      className={`fill-none ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 16 16"
      aria-label={alt}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.3335 2.66699H2.66683C1.93045 2.66699 1.3335 3.26395 1.3335 4.00033V12.0003C1.3335 12.7367 1.93045 13.3337 2.66683 13.3337H13.3335C14.0699 13.3337 14.6668 12.7367 14.6668 12.0003V4.00033C14.6668 3.26395 14.0699 2.66699 13.3335 2.66699Z"
        stroke={color}
        strokeWidth="1.3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14.6668 4.66699L8.68683 8.46699C8.48101 8.59594 8.24304 8.66433 8.00016 8.66433C7.75729 8.66433 7.51932 8.59594 7.3135 8.46699L1.3335 4.66699"
        stroke={color}
        strokeWidth="1.3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default IconEmail;
