import React from 'react';
interface IconProps {
    size?: string;
    alt?: string;
    className?: string;
    color?: string;
}

const IconEmail2: React.FC<IconProps> = ({ size, alt, className, color }) => {
    return (
        <svg className={`fill-none ${className}`}
            style={{ width: size, height: size }} aria-label={alt} viewBox="0 0 18 18" xmlns="http://www.w3.org/2000/svg">
            <path d="M1.6875 2.16016C0.755859 2.16016 0 2.90594 0 3.82516C0 4.34894 0.249609 4.8415 0.675 5.15716L8.325 10.8182C8.72578 11.113 9.27422 11.113 9.675 10.8182L17.325 5.15716C17.7504 4.8415 18 4.34894 18 3.82516C18 2.90594 17.2441 2.16016 16.3125 2.16016H1.6875ZM0 6.04516V13.2602C0 14.4846 1.00898 15.4802 2.25 15.4802H15.75C16.991 15.4802 18 14.4846 18 13.2602V6.04516L10.35 11.7062C9.54844 12.2993 8.45156 12.2993 7.65 11.7062L0 6.04516Z" fill="black" />
        </svg>

    );
};

export default IconEmail2;
