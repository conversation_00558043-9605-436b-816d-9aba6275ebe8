import React from 'react';

interface IconProps {
    size?: string;
    alt?: string;
    className?: string;
}

const IconEmailLine: React.FC<IconProps> = ({ size, alt, className }) => {
    return (
        <svg className={`fill-none ${className}`}
            style={{ width: size, height: size }}
            aria-label={alt} viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
            <path d="M13.3334 2.66675H2.66671C1.93033 2.66675 1.33337 3.2637 1.33337 4.00008V12.0001C1.33337 12.7365 1.93033 13.3334 2.66671 13.3334H13.3334C14.0698 13.3334 14.6667 12.7365 14.6667 12.0001V4.00008C14.6667 3.2637 14.0698 2.66675 13.3334 2.66675Z" stroke="black" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M14.6667 4.66675L8.68671 8.46675C8.48089 8.5957 8.24292 8.66409 8.00004 8.66409C7.75716 8.66409 7.51919 8.5957 7.31337 8.46675L1.33337 4.66675" stroke="black" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
        </svg>



    );
};

export default IconEmailLine;
