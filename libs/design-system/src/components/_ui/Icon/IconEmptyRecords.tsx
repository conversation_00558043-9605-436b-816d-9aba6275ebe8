import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconEmptyRecords: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 78 78"
      aria-label={alt}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.64611 9.47248C7.73757 10.5639 9.53067 10.5639 10.6611 9.47248C11.7526 8.38103 11.7526 6.58792 10.6611 5.49647L9.33577 4.17113C8.24431 3.07968 6.45121 3.07968 5.32078 4.17113C4.22932 5.26258 4.22932 7.05569 5.32078 8.14714L6.64611 9.47248ZM75.7196 56.2881L72.952 53.5205C73.9265 50.3241 74.4332 46.8938 74.4332 43.3855C74.4332 23.8953 58.6071 8.10816 39.0389 8.10816C34.3223 8.10816 29.8395 9.04369 25.7076 10.6809L23.6806 8.65389C23.3298 8.30306 22.7061 8.30306 22.3553 8.65389C22.0044 9.00471 22.0044 9.6284 22.3553 9.97923L24.5771 12.1621C25.0839 13.2146 24.928 14.501 24.0314 15.3975C22.94 16.489 21.1469 16.528 20.0164 15.3975L18.925 14.345L15.7676 11.1876C14.6761 10.0962 12.922 10.0962 11.8695 11.1876C10.778 12.2791 10.817 14.0332 11.8695 15.0857L15.1049 18.2821C16.2353 19.5684 16.1574 21.5175 14.949 22.7259C13.6626 24.0122 11.5966 24.0122 10.3103 22.7648V22.8038L7.65961 20.2701C6.13937 18.7888 3.72258 18.7888 2.24132 20.2701C0.760056 21.7513 0.760056 24.1681 2.24132 25.6494L6.17835 29.5474C6.25631 29.6254 6.33427 29.7034 6.41223 29.7423C4.61913 34.0302 3.68359 38.6689 3.68359 43.3465C3.68359 54.6119 8.98494 64.6299 17.2098 71.1007H60.907C62.4662 69.8923 63.9085 68.5669 65.2338 67.1247L66.2473 68.1382C67.5337 69.4245 69.6386 69.4245 70.925 68.1382C72.2113 66.8518 72.2113 64.7468 70.925 63.4605L66.2863 58.8218C66.1304 58.6659 65.9744 58.5489 65.8185 58.432L64.4152 57.0287C63.6746 56.2881 63.6746 55.1187 64.4152 54.378C65.1559 53.6374 66.3643 53.6374 67.1049 54.378L71.1589 58.393L71.1978 58.3151L72.4842 59.6014C73.4197 60.537 74.901 60.537 75.8365 59.6014C76.6551 58.6659 76.6551 57.1846 75.7196 56.2881Z"
        fill="#E0E0E0"
      />
      <path
        d="M22.1216 60.4588C22.1216 61.7841 28.2025 62.8756 35.6868 62.8756C43.1711 62.8756 49.252 61.7841 49.252 60.4588C49.252 59.1334 43.1711 58.042 35.6868 58.042C28.2025 58.042 22.1216 59.1334 22.1216 60.4588Z"
        fill="#DDDBDB"
      />
      <path
        d="M28.2026 57.1453C28.2026 58.4706 34.2836 59.5621 41.7679 59.5621C49.2521 59.5621 55.3331 58.4706 55.3331 57.1453C55.3331 55.82 49.2521 54.7285 41.7679 54.7285C34.2836 54.7285 28.2026 55.82 28.2026 57.1453Z"
        fill="#DDDBDB"
      />
      <path
        d="M51.4349 55.2746C51.6688 54.6899 52.2145 54.3001 52.8772 54.2611C53.1501 54.2611 53.384 54.339 53.5789 54.417C53.8127 53.3255 54.7872 52.468 55.9177 52.468C56.7753 52.468 57.5938 52.9357 58.0226 53.7153C58.2565 53.6374 58.4904 53.5594 58.7633 53.5594C59.7378 53.5594 60.5564 54.2611 60.7123 55.2356L51.4349 55.2746ZM48.1216 39.7603C48.7453 38.1231 50.1486 37.0317 51.7078 37.0317C52.0976 37.0317 52.4874 37.1096 52.8382 37.2266C53.306 35.2386 54.8262 33.7573 56.6583 33.7573C58.3735 33.7573 59.8547 35.0827 60.4004 36.9147C60.8682 36.33 61.4919 35.9402 62.2325 35.9402C63.5968 35.9402 64.6883 37.2656 64.6883 38.9028C64.6883 39.2146 64.6493 39.4875 64.5714 39.7603H48.1216Z"
        fill="white"
      />
      <path
        d="M31.7105 27.52H51.7075C53.5396 27.52 54.4751 28.4556 54.4751 30.2876V54.3386C54.4751 56.1707 53.5396 57.1062 51.7075 57.1062H31.7105C29.8784 57.1062 28.9429 56.1707 28.9429 54.3386V30.2876C28.9429 28.4166 29.8784 27.52 31.7105 27.52Z"
        fill="#F2F2F2"
      />
      <path
        d="M25.3179 30.9507H45.3149C47.147 30.9507 48.0825 31.8862 48.0825 33.7183V57.7693C48.0825 59.6014 47.147 60.5369 45.3149 60.5369H25.3179C23.4858 60.5369 22.5503 59.6014 22.5503 57.7693V33.7183C22.5503 31.8472 23.4858 30.9507 25.3179 30.9507Z"
        fill="white"
      />
      <path
        d="M36.2322 55.8593C37.3237 54.7289 37.7914 53.1307 37.3627 51.6104C36.9729 50.0902 35.7645 48.9208 34.2442 48.492C32.724 48.1022 31.1258 48.531 29.9953 49.6224C28.2802 51.3376 28.2802 54.1442 29.9953 55.8593C31.7105 57.5744 34.4781 57.5744 36.2322 55.8593Z"
        fill="#DADADA"
      />
      <path
        d="M27.6566 35.1216H37.9085C38.1813 35.1216 38.3373 35.2775 38.3373 35.5504C38.3373 35.8232 38.1813 35.9792 37.9085 35.9792H27.6566C27.3837 35.9792 27.2278 35.8232 27.2278 35.5504C27.2278 35.2385 27.3448 35.1216 27.6566 35.1216ZM27.6566 43.7363H34.2053C34.4392 43.7363 34.6341 43.9312 34.6341 44.1651C34.6341 44.3989 34.4392 44.5938 34.2053 44.5938H27.6566C27.4227 44.5938 27.2278 44.3989 27.2278 44.1651C27.2278 43.9312 27.4227 43.7363 27.6566 43.7363ZM27.6566 39.4094H41.6116C41.8455 39.4094 42.0404 39.6043 42.0404 39.8382C42.0404 40.0721 41.8455 40.267 41.6116 40.267H27.6566C27.4227 40.267 27.2278 40.0721 27.2278 39.8382C27.2278 39.6043 27.4227 39.4094 27.6566 39.4094ZM37.9475 56.5219C38.4932 56.21 39.1558 56.288 39.6236 56.7558L44.3403 61.4724C44.886 62.0181 44.886 62.8757 44.3403 63.4214L43.9115 63.8502C43.3657 64.3959 42.5082 64.3959 41.9624 63.8502L37.2458 59.1336C36.817 58.7048 36.7001 58.0031 37.0119 57.4574L35.9205 56.366L36.895 55.4304L37.9475 56.5219ZM8.51716 71.3345H64.3762C64.6491 71.3345 64.805 71.4904 64.805 71.7633C64.805 72.0361 64.6491 72.192 64.3762 72.192H8.51716C8.2443 72.192 8.08838 72.0361 8.08838 71.7633C8.08838 71.4514 8.2443 71.3345 8.51716 71.3345ZM17.6386 74.1021H30.6191C30.892 74.1021 31.0479 74.258 31.0479 74.5309C31.0479 74.8037 30.892 74.9597 30.6191 74.9597H17.6386C17.3657 74.9597 17.2098 74.8037 17.2098 74.5309C17.2098 74.219 17.3657 74.1021 17.6386 74.1021ZM35.6086 74.1021H48.5891C48.862 74.1021 49.0179 74.258 49.0179 74.5309C49.0179 74.8037 48.862 74.9597 48.5891 74.9597H35.6086C35.3358 74.9597 35.1798 74.8037 35.1798 74.5309C35.1798 74.219 35.3358 74.1021 35.6086 74.1021ZM53.9684 77.1426C53.6176 77.3375 53.3837 77.6493 53.3448 77.9612H44.1843C43.9504 77.9612 43.7555 77.7663 43.7555 77.5324C43.7555 77.2985 43.9504 77.1036 44.1843 77.1036H53.9684V77.1426ZM55.7226 77.1426H66.871C67.1049 77.1426 67.2998 77.3375 67.2998 77.5714C67.2998 77.8052 67.1049 78.0001 66.871 78.0001H56.3463C56.3073 77.6103 56.0734 77.3375 55.7226 77.1426ZM28.4362 77.1426C28.0854 77.3375 27.8515 77.6493 27.8125 77.9612H19.8605C19.6266 77.9612 19.4317 77.7663 19.4317 77.5324C19.4317 77.2985 19.6266 77.1036 19.8605 77.1036C19.8605 77.1426 28.4362 77.1426 28.4362 77.1426ZM30.1903 77.1426H32.841C33.0749 77.1426 33.2698 77.3375 33.2698 77.5714C33.2698 77.8052 33.0749 78.0001 32.841 78.0001H30.814C30.814 77.6103 30.5801 77.3375 30.1903 77.1426ZM66.2863 71.3345H68.5082C68.781 71.3345 68.937 71.4904 68.937 71.7633C68.937 72.0361 68.781 72.192 68.5082 72.192H66.2863C66.0134 72.192 65.8575 72.0361 65.8575 71.7633C65.8965 71.4514 66.0134 71.3345 66.2863 71.3345ZM32.5681 74.1021H33.6596C33.9325 74.1021 34.0884 74.258 34.0884 74.5309C34.0884 74.8037 33.9325 74.9597 33.6596 74.9597H32.5681C32.2953 74.9597 32.1394 74.8037 32.1394 74.5309C32.1394 74.219 32.2953 74.1021 32.5681 74.1021ZM41.1439 77.1426H42.2353C42.5082 77.1426 42.6641 77.2985 42.6641 77.5714C42.6641 77.8442 42.5082 78.0001 42.2353 78.0001H41.1439C40.871 78.0001 40.7151 77.8442 40.7151 77.5714C40.7151 77.2595 40.871 77.1426 41.1439 77.1426Z"
        fill="#BBBBBB"
      />
      <path
        d="M36.7002 0C36.973 0 37.1679 0.194903 37.1679 0.467766V1.4033C37.1679 1.67616 36.973 1.87106 36.7002 1.87106C36.4273 1.87106 36.2324 1.67616 36.2324 1.4033V0.467766C36.2324 0.194903 36.4663 0 36.7002 0ZM36.2324 2.33883C36.2324 2.61169 36.0375 2.8066 35.7646 2.8066H34.8291C34.5562 2.8066 34.3613 2.61169 34.3613 2.33883C34.3613 2.06597 34.5562 1.87106 34.8291 1.87106H35.7646C36.0375 1.87106 36.2324 2.10495 36.2324 2.33883ZM36.7002 2.8066C36.973 2.8066 37.1679 3.0015 37.1679 3.27436V4.2099C37.1679 4.48276 36.973 4.67766 36.7002 4.67766C36.4273 4.67766 36.2324 4.48276 36.2324 4.2099V3.27436C36.2324 3.04048 36.4663 2.8066 36.7002 2.8066ZM39.078 2.33883C39.078 2.61169 38.8831 2.8066 38.6102 2.8066H37.6747C37.4018 2.8066 37.2069 2.61169 37.2069 2.33883C37.2069 2.06597 37.4018 1.87106 37.6747 1.87106H38.6102C38.8441 1.87106 39.078 2.10495 39.078 2.33883ZM64.9221 8.02998C65.1949 8.02998 65.3898 8.22489 65.3898 8.49775V9.43328C65.3898 9.70615 65.1949 9.90105 64.9221 9.90105C64.6492 9.90105 64.4543 9.70615 64.4543 9.43328V8.49775C64.4543 8.22489 64.6492 8.02998 64.9221 8.02998ZM64.4543 10.3688C64.4543 10.6417 64.2594 10.8366 63.9865 10.8366H63.051C62.7781 10.8366 62.5832 10.6417 62.5832 10.3688C62.5832 10.096 62.7781 9.90105 63.051 9.90105H63.9865C64.2204 9.90105 64.4543 10.096 64.4543 10.3688ZM64.9221 10.8366C65.1949 10.8366 65.3898 11.0315 65.3898 11.3043V12.2399C65.3898 12.5127 65.1949 12.7076 64.9221 12.7076C64.6492 12.7076 64.4543 12.5127 64.4543 12.2399V11.3043C64.4543 11.0315 64.6492 10.8366 64.9221 10.8366ZM67.2609 10.3688C67.2609 10.6417 67.066 10.8366 66.7931 10.8366H65.8576C65.5847 10.8366 65.3898 10.6417 65.3898 10.3688C65.3898 10.096 65.5847 9.90105 65.8576 9.90105H66.7931C67.066 9.90105 67.2609 10.096 67.2609 10.3688Z"
        fill="#E0E0E0"
      />
      <path
        d="M57.7108 22.375C57.9057 22.375 58.0616 22.5309 58.0616 22.7258V23.5054C58.0616 23.7003 57.9057 23.8563 57.7108 23.8563C57.5159 23.8563 57.36 23.7003 57.36 23.5054V22.7258C57.399 22.5309 57.5549 22.375 57.7108 22.375ZM57.7108 24.5189C57.9057 24.5189 58.0616 24.6749 58.0616 24.8698V25.6494C58.0616 25.8443 57.9057 26.0002 57.7108 26.0002C57.5159 26.0002 57.36 25.8443 57.36 25.6494V24.8698C57.399 24.6749 57.5549 24.5189 57.7108 24.5189ZM59.5429 24.2461C59.5429 24.441 59.387 24.5969 59.1921 24.5969H58.4125C58.2176 24.5969 58.0616 24.441 58.0616 24.2461C58.0616 24.0512 58.2176 23.8952 58.4125 23.8952H59.1921C59.387 23.9342 59.5429 24.0512 59.5429 24.2461ZM57.399 24.2461C57.399 24.441 57.2431 24.5969 57.0482 24.5969H56.2685C56.0736 24.5969 55.9177 24.441 55.9177 24.2461C55.9177 24.0512 56.0736 23.8952 56.2685 23.8952H57.0482C57.2431 23.9342 57.399 24.0512 57.399 24.2461ZM10.4664 44.5159C10.6224 44.5159 10.7783 44.6329 10.7783 44.8278V45.4515C10.7783 45.6074 10.6224 45.7633 10.4664 45.7633C10.3105 45.7633 10.1546 45.6464 10.1546 45.4515V44.8278C10.1546 44.6329 10.2715 44.5159 10.4664 44.5159ZM10.1546 46.0362C10.1546 46.1921 10.0377 46.348 9.84276 46.348H9.21907C9.06315 46.348 8.90723 46.1921 8.90723 46.0362C8.90723 45.8802 9.02417 45.7243 9.21907 45.7243H9.84276C9.99868 45.7243 10.1546 45.8802 10.1546 46.0362ZM10.4664 46.348C10.6224 46.348 10.7783 46.465 10.7783 46.6599V47.2835C10.7783 47.4395 10.6224 47.5954 10.4664 47.5954C10.3105 47.5954 10.1546 47.4784 10.1546 47.2835V46.6599C10.1546 46.465 10.2715 46.348 10.4664 46.348ZM11.9867 46.0362C11.9867 46.1921 11.8697 46.348 11.6748 46.348H11.0512C10.8952 46.348 10.7393 46.1921 10.7393 46.0362C10.7393 45.8802 10.8563 45.7243 11.0512 45.7243H11.6748C11.8308 45.7243 11.9867 45.8802 11.9867 46.0362Z"
        fill="white"
      />
      <path
        d="M28.1246 49.778H10.5444C10.5834 48.6086 11.5189 47.673 12.6494 47.673C12.9612 47.673 13.2731 47.751 13.5459 47.8679C13.8188 46.5036 15.0272 45.4901 16.4305 45.4901C16.7423 45.4901 17.0542 45.5291 17.366 45.646C17.9507 44.1648 19.393 43.1123 21.0692 43.1123C23.2911 43.1123 25.0842 44.9444 25.0842 47.2053C25.396 47.0104 25.7858 46.9324 26.1366 46.9324C27.3061 46.9324 28.2416 47.9069 28.2416 49.0763L28.1246 49.778Z"
        fill="#F2F2F2"
      />
    </svg>
  );
};

export default IconEmptyRecords;
