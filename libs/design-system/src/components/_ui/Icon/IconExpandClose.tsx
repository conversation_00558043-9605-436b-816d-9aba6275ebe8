interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
  color?: string;
  onClick?: () => void;
}

const IconExpandClose: React.FC<IconProps> = ({
  size,
  alt,
  className,
  color,
  onClick,
}) => {
  return (
    <svg
      className={`fill-none ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 24 24"
      fill="none"
      aria-label={alt}
      xmlns="http://www.w3.org/2000/svg"
      onClick={onClick}
    >
      <circle
        cx="12"
        cy="12"
        r="12"
        transform="matrix(-1 0 0 1 24 0)"
        fill={color}
      />
      <path
        d="M10.2197 12.5043C9.92677 12.2254 9.92677 11.7724 10.2197 11.4935L14.7188 7.20919C15.0117 6.93027 15.4874 6.93027 15.7803 7.20919C16.0732 7.48812 16.0732 7.94109 15.7803 8.22002L11.8108 12L15.778 15.78C16.0709 16.0589 16.0709 16.5119 15.778 16.7908C15.4851 17.0697 15.0094 17.0697 14.7165 16.7908L10.2173 12.5065L10.2197 12.5043Z"
        fill="white"
      />
    </svg>
  );
};

export default IconExpandClose;
