import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconEye: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 16 17"
    >
      <g clipPath="url(#clip0_3106_29317)">
        <path
          d="M8 3.73126C4.94303 3.73126 2.17081 5.40376 0.125192 8.12034C-0.0417306 8.3429 -0.0417306 8.65384 0.125192 8.8764C2.17081 11.5963 4.94303 13.2687 8 13.2687C11.057 13.2687 13.8292 11.5963 15.8748 8.87967C16.0417 8.65711 16.0417 8.34618 15.8748 8.12361C13.8292 5.40376 11.057 3.73126 8 3.73126ZM8.21929 11.8581C6.19004 11.9857 4.51427 10.3132 4.64191 8.28071C4.74665 6.60495 6.10494 5.24666 7.78071 5.14192C9.80996 5.01427 11.4857 6.68677 11.3581 8.7193C11.2501 10.3918 9.89179 11.7501 8.21929 11.8581ZM8.11783 10.3067C7.02465 10.3754 6.12131 9.47536 6.19331 8.38218C6.24895 7.47883 6.9821 6.74896 7.88545 6.69004C8.97862 6.62131 9.88197 7.52138 9.80996 8.61456C9.75105 9.52118 9.0179 10.2511 8.11783 10.3067Z"
          fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_3106_29317">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(0 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconEye;
