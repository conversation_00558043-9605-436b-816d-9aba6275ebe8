import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconFile: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 20 21"
    >
      <g clipPath="url(#clip0_2767_2718)">
        <g clipPath="url(#clip1_2767_2718)">
          <path
            d="M12.8906 6.35938C12.1368 6.35938 11.5234 5.74605 11.5234 4.99219V0.5H4.53125C3.3466 0.5 2.38281 1.46379 2.38281 2.64844V18.3516C2.38281 19.5362 3.3466 20.5 4.53125 20.5H15.4688C16.6534 20.5 17.6172 19.5362 17.6172 18.3516V6.35938H12.8906ZM5.58594 14.5625H8.42656C8.75016 14.5625 9.0125 14.8248 9.0125 15.1484C9.0125 15.472 8.75016 15.7344 8.42656 15.7344H5.58594C5.26234 15.7344 5 15.472 5 15.1484C5 14.8248 5.26234 14.5625 5.58594 14.5625ZM5 12.0234C5 11.6998 5.26234 11.4375 5.58594 11.4375H14.1797C14.5033 11.4375 14.7656 11.6998 14.7656 12.0234C14.7656 12.347 14.5033 12.6094 14.1797 12.6094H5.58594C5.26234 12.6094 5 12.347 5 12.0234ZM14.1797 8.3125C14.5033 8.3125 14.7656 8.57484 14.7656 8.89844C14.7656 9.22203 14.5033 9.48438 14.1797 9.48438H5.58594C5.26234 9.48438 5 9.22203 5 8.89844C5 8.57484 5.26234 8.3125 5.58594 8.3125H14.1797Z"
            fill="black"
          />
          <path
            d="M12.6953 4.99222C12.6953 5.09992 12.7829 5.18753 12.8906 5.18753H17.356C17.2483 4.98814 17.1097 4.80709 16.9453 4.65113L13.1788 1.08777C13.0343 0.951095 12.8716 0.835084 12.6954 0.743042V4.99222H12.6953Z"
            fill="black"
          />
        </g>
      </g>
      <defs>
        <clipPath id="clip0_2767_2718">
          <rect
            width="20"
            height="20"
            fill="white"
            transform="translate(0 0.5)"
          />
        </clipPath>
        <clipPath id="clip1_2767_2718">
          <rect
            width="20"
            height="20"
            fill="white"
            transform="translate(0 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconFile;
