import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconMute: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 20 19"
      aria-label={alt}
    >
      <path fillRule="evenodd" clipRule="evenodd" d="M0 10.5328C0 5.02126 4.48433 0.516314 9.99613 0.490723C15.5157 0.516314 20 5.02126 20 10.5328C20 12.2581 19.5549 13.9586 18.7132 15.4536V12.6639C18.7132 12.21 18.5634 11.7864 18.2981 11.4166C18.3291 11.1237 18.3455 10.8288 18.3455 10.5328C18.3455 5.9292 14.6 2.16646 9.99613 2.14516C5.39988 2.16654 1.6544 5.9292 1.6544 10.5328C1.65449 10.7995 1.66727 11.0659 1.69268 11.3314C1.38739 11.7207 1.21323 12.1746 1.21323 12.6639V15.3189C0.419656 13.8567 0 12.2067 0 10.5328ZM1.76469 12.6639C1.76487 12.5186 1.78669 12.3741 1.82943 12.2352C2.12026 11.2834 3.35898 10.5684 4.84343 10.5684H6.13971V18.5095H4.84343C3.14304 18.5095 1.76469 17.5712 1.76469 16.4139V12.6639ZM18.1617 12.664V16.4139C18.1617 17.5712 16.7834 18.5095 15.083 18.5095H13.7867V10.5684H15.083C16.6505 10.5684 17.9435 11.3659 18.136 12.3974C18.1523 12.4847 18.1617 12.5736 18.1617 12.664Z" fill="currentColor" />
    </svg>
  );
};

export default IconMute;
