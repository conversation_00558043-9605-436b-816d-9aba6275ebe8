import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconHistory: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 14 14"
      fill="none"
    >
      <path
        d="M12.0375 2.33708C12.0375 2.14981 11.8876 2 11.7004 2H3.23595C2.5618 2 2 2.5618 2 3.23595V11.3258C2 11.7004 2.29963 12 2.67416 12H11.2884C11.6629 12 11.9625 11.7004 11.9625 11.3258V4.47191C11.9625 4.09738 11.6629 3.79775 11.2884 3.79775H3.23595C2.93633 3.79775 2.71161 3.57303 2.71161 3.27341C2.71161 2.97378 2.93633 2.74906 3.23595 2.74906H11.7004C11.8876 2.71161 12.0375 2.5618 12.0375 2.33708ZM3.23595 4.47191H11.2884V11.3633H2.67416V4.35955C2.86142 4.43446 3.04869 4.47191 3.23595 4.47191Z"
        fill="currentColor"
      />
      <path
        d="M3.57238 3.04883C3.46002 3.04883 3.34766 3.12373 3.34766 3.27355C3.34766 3.42336 3.46002 3.49827 3.57238 3.49827H11.6623C11.7746 3.49827 11.887 3.38591 11.887 3.27355C11.887 3.16119 11.7746 3.04883 11.6623 3.04883H3.57238ZM8.32893 7.91774H7.35515V6.94396C7.35515 6.75669 7.20533 6.60688 7.01807 6.60688C6.8308 6.60688 6.68099 6.75669 6.68099 6.94396V8.25482C6.68099 8.44209 6.8308 8.5919 7.01807 8.5919H8.32893C8.5162 8.5919 8.66601 8.44209 8.66601 8.25482C8.66601 8.06755 8.5162 7.91774 8.32893 7.91774Z"
        fill="currentColor"
      />
      <path
        d="M7.16842 4.99609C5.55793 4.99609 4.24707 6.30696 4.24707 7.91744C4.24707 9.52793 5.55793 10.8388 7.16842 10.8388C8.77891 10.8388 10.0898 9.52793 10.0898 7.91744C10.0898 6.30696 8.77891 4.99609 7.16842 4.99609ZM7.16842 10.1272C5.93246 10.1272 4.92123 9.11594 4.92123 7.87999C4.92123 6.64403 5.93246 5.6328 7.16842 5.6328C8.40437 5.6328 9.41561 6.64403 9.41561 7.87999C9.41561 9.1534 8.40437 10.1272 7.16842 10.1272Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default IconHistory;
