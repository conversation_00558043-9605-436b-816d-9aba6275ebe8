import React from 'react';

interface IconProps {
    size?: string;
    alt?: string;
    className?: string;
}

const IconHome: React.FC<IconProps> = ({ size, alt, className }) => {
    return (
        <svg className={`fill-none ${className}`}
            style={{ width: size, height: size }}
            aria-label={alt} viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
            <path d="M10 14V8.66667C10 8.48986 9.92976 8.32029 9.80474 8.19526C9.67971 8.07024 9.51014 8 9.33333 8H6.66667C6.48986 8 6.32029 8.07024 6.19526 8.19526C6.07024 8.32029 6 8.48986 6 8.66667V14" stroke="black" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M2 6.66673C1.99995 6.47277 2.04222 6.28114 2.12386 6.1052C2.20549 5.92927 2.32453 5.77326 2.47267 5.64806L7.13933 1.64873C7.37999 1.44533 7.6849 1.33374 8 1.33374C8.3151 1.33374 8.62001 1.44533 8.86067 1.64873L13.5273 5.64806C13.6755 5.77326 13.7945 5.92927 13.8761 6.1052C13.9578 6.28114 14 6.47277 14 6.66673V12.6667C14 13.0203 13.8595 13.3595 13.6095 13.6095C13.3594 13.8596 13.0203 14.0001 12.6667 14.0001H3.33333C2.97971 14.0001 2.64057 13.8596 2.39052 13.6095C2.14048 13.3595 2 13.0203 2 12.6667V6.66673Z" stroke="black" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
        </svg>

    );
};

export default IconHome;
