import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconHorizontalDots: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 21 21"
    >
      <path
        d="M2.16562 10.5C2.16562 9.92562 2.39506 9.37478 2.80348 8.96865C3.21189 8.56251 3.76581 8.33435 4.3434 8.33435C4.92098 8.33435 5.47491 8.56251 5.88332 8.96865C6.29173 9.37478 6.52117 9.92562 6.52117 10.5C6.52117 11.0743 6.29173 11.6252 5.88332 12.0313C5.47491 12.4374 4.92098 12.6656 4.3434 12.6656C3.76581 12.6656 3.21189 12.4374 2.80348 12.0313C2.39506 11.6252 2.16562 11.0743 2.16562 10.5ZM8.38784 10.5C8.38784 9.92562 8.61729 9.37478 9.0257 8.96865C9.43411 8.56251 9.98804 8.33435 10.5656 8.33435C11.1432 8.33435 11.6971 8.56251 12.1055 8.96865C12.514 9.37478 12.7434 9.92562 12.7434 10.5C12.7434 11.0743 12.514 11.6252 12.1055 12.0313C11.6971 12.4374 11.1432 12.6656 10.5656 12.6656C9.98804 12.6656 9.43411 12.4374 9.0257 12.0313C8.61729 11.6252 8.38784 11.0743 8.38784 10.5ZM16.7878 8.33435C17.3654 8.33435 17.9194 8.56251 18.3278 8.96865C18.7362 9.37478 18.9656 9.92562 18.9656 10.5C18.9656 11.0743 18.7362 11.6252 18.3278 12.0313C17.9194 12.4374 17.3654 12.6656 16.7878 12.6656C16.2103 12.6656 15.6563 12.4374 15.2479 12.0313C14.8395 11.6252 14.6101 11.0743 14.6101 10.5C14.6101 9.92562 14.8395 9.37478 15.2479 8.96865C15.6563 8.56251 16.2103 8.33435 16.7878 8.33435Z"
        fill="black"
      />
    </svg>
  );
};

export default IconHorizontalDots;
