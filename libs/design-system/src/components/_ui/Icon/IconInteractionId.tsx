interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
  color?: string;
}

const IconInteractionId: React.FC<IconProps> = ({
  size,
  alt,
  className,
  color,
}) => {
  return (
    <svg
      className={`fill-none ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 16 16"
      aria-label={alt}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.6665 2L13.3332 2.66667"
        stroke={color}
        strokeWidth="1.33333"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.3335 1.33301L10.3335 4.33301"
        stroke={color}
        strokeWidth="1.33333"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.3332 5.33301V13.9997C13.3332 14.1765 13.2629 14.3461 13.1379 14.4711C13.0129 14.5961 12.8433 14.6663 12.6665 14.6663H4.33317C3.89114 14.6663 3.46722 14.4907 3.15466 14.1782C2.8421 13.8656 2.6665 13.4417 2.6665 12.9997C2.6665 12.5576 2.8421 12.1337 3.15466 11.8212C3.46722 11.5086 3.89114 11.333 4.33317 11.333H13.3332"
        stroke={color}
        strokeWidth="1.33333"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2.6665 12.9997V2.99967C2.6665 2.55765 2.8421 2.13372 3.15466 1.82116C3.46722 1.5086 3.89114 1.33301 4.33317 1.33301H9.33317"
        stroke={color}
        strokeWidth="1.33333"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.33333 6.66667C10.0697 6.66667 10.6667 6.06971 10.6667 5.33333C10.6667 4.59695 10.0697 4 9.33333 4C8.59695 4 8 4.59695 8 5.33333C8 6.06971 8.59695 6.66667 9.33333 6.66667Z"
        stroke={color}
        strokeWidth="1.33333"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default IconInteractionId;
