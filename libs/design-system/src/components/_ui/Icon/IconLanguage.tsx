import React from 'react';

interface IconProps {
    size?: string;
    alt?: string;
    className?: string;
}

const IconLanguage: React.FC<IconProps> = ({ size, alt, className }) => {
    return (
        <svg className={`fill-none ${className}`}
            style={{ width: size, height: size }}
            aria-label={alt} viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_620_43729)">
                <path d="M8.00004 14.6666C11.6819 14.6666 14.6667 11.6818 14.6667 7.99992C14.6667 4.31802 11.6819 1.33325 8.00004 1.33325C4.31814 1.33325 1.33337 4.31802 1.33337 7.99992C1.33337 11.6818 4.31814 14.6666 8.00004 14.6666Z" stroke="black" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M8.00004 1.33325C6.2882 3.13069 5.33337 5.51775 5.33337 7.99992C5.33337 10.4821 6.2882 12.8691 8.00004 14.6666C9.71188 12.8691 10.6667 10.4821 10.6667 7.99992C10.6667 5.51775 9.71188 3.13069 8.00004 1.33325Z" stroke="black" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M1.33337 8H14.6667" stroke="black" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round" />
            </g>
            <defs>
                <clipPath id="clip0_620_43729">
                    <rect width="16" height="16" fill="white" />
                </clipPath>
            </defs>
        </svg>


    );
};

export default IconLanguage;
