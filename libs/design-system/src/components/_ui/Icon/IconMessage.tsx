import React from 'react';
interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
  color?: string;
}

const IconMessage: React.FC<IconProps> = ({ size, alt, className, color }) => {
  return (
    <svg
      className={`fill-none ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 16 16"
      aria-label={alt}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14 10C14 10.3536 13.8595 10.6928 13.6095 10.9428C13.3594 11.1929 13.0203 11.3333 12.6667 11.3333H4.66667L2 14V3.33333C2 2.97971 2.14048 2.64057 2.39052 2.39052C2.64057 2.14048 2.97971 2 3.33333 2H12.6667C13.0203 2 13.3594 2.14048 13.6095 2.39052C13.8595 2.64057 14 2.97971 14 3.33333V10Z"
        stroke={color}
        strokeWidth="1.3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.3335 6.66699H5.34016"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8 6.66699H8.00667"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.6665 6.66699H10.6732"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default IconMessage;
