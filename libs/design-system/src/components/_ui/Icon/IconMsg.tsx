import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconMsg: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 25 26"
      aria-label={alt}
    >
      <g clipPath="url(#clip0_4249_4621)">
        <path
          d="M3.125 0.5C1.40137 0.5 0 1.90137 0 3.625V17.6875C0 19.4111 1.40137 20.8125 3.125 20.8125H7.8125V24.7188C7.8125 25.0166 7.97852 25.2852 8.24219 25.417C8.50586 25.5488 8.82324 25.5195 9.0625 25.3438L15.1025 20.8125H21.875C23.5986 20.8125 25 19.4111 25 17.6875V3.625C25 1.90137 23.5986 0.5 21.875 0.5H3.125Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_4249_4621">
          <rect
            width="25"
            height="25"
            fill="white"
            transform="translate(0 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconMsg;
