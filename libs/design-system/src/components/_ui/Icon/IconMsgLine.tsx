import React from 'react';

interface IconProps {
    size?: string;
    alt?: string;
    className?: string;
}

const IconMsgLine: React.FC<IconProps> = ({ size, alt, className }) => {
    return (
        <svg aria-label={alt} style={{ width: size, height: size }} className={`fill-none ${className}`} viewBox="0 0 25 26" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 15.5V8.83333C18 8.47971 17.8595 8.14057 17.6095 7.89052C17.3594 7.64048 17.0203 7.5 16.6667 7.5H10" stroke="black" strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M5.33301 6.83398L18.6663 20.1673" stroke="black" strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M6.4 7.90039C6.13333 8.10039 6 8.43372 6 8.83372V19.5004L8.66667 16.8337H15.3333" stroke="black" strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
    );
};

export default IconMsgLine;
