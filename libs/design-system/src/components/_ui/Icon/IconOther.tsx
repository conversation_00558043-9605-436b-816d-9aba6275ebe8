import React from 'react';

interface IconProps {
    size?: string;
    alt?: string;
    className?: string;
}

const IconOther: React.FC<IconProps> = ({ size, alt, className }) => {
    return (
        <svg className={`fill-none ${className}`}
            style={{ width: size, height: size }}
            aria-label={alt} viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
            <path d="M2.66663 12.9999V2.99992C2.66663 2.55789 2.84222 2.13397 3.15478 1.82141C3.46734 1.50885 3.89127 1.33325 4.33329 1.33325H12.6666C12.8434 1.33325 13.013 1.40349 13.138 1.52851C13.2631 1.65354 13.3333 1.82311 13.3333 1.99992V13.9999C13.3333 14.1767 13.2631 14.3463 13.138 14.4713C13.013 14.5963 12.8434 14.6666 12.6666 14.6666H4.33329C3.89127 14.6666 3.46734 14.491 3.15478 14.1784C2.84222 13.8659 2.66663 13.4419 2.66663 12.9999ZM2.66663 12.9999C2.66663 12.5579 2.84222 12.134 3.15478 11.8214C3.46734 11.5088 3.89127 11.3333 4.33329 11.3333H13.3333" stroke="black" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
    );
};

export default IconOther;
