import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconPad: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 16 16"
      fill="none"
      aria-label={alt}
    >
      <g id="icon / Number pad" clipPath="url(#clip0_354_66)">
        <path id="Union" fillRule="evenodd" clipRule="evenodd" d="M5.09076 1.45456C5.09076 2.25789 4.43953 2.90912 3.6362 2.90912C2.83287 2.90912 2.18164 2.25789 2.18164 1.45456C2.18164 0.651228 2.83287 0 3.6362 0C4.43953 0 5.09076 0.651228 5.09076 1.45456ZM9.4544 1.45456C9.4544 2.25789 8.80317 2.90912 7.99984 2.90912C7.19651 2.90912 6.54528 2.25789 6.54528 1.45456C6.54528 0.651228 7.19651 0 7.99984 0C8.80317 0 9.4544 0.651228 9.4544 1.45456ZM12.3635 2.90912C13.1668 2.90912 13.818 2.25789 13.818 1.45456C13.818 0.651228 13.1668 0 12.3635 0C11.5602 0 10.9089 0.651228 10.9089 1.45456C10.9089 2.25789 11.5602 2.90912 12.3635 2.90912ZM9.4544 5.8182C9.4544 6.62153 8.80317 7.27276 7.99984 7.27276C7.19651 7.27276 6.54528 6.62153 6.54528 5.8182C6.54528 5.01487 7.19651 4.36364 7.99984 4.36364C8.80317 4.36364 9.4544 5.01487 9.4544 5.8182ZM3.6362 7.27276C4.43953 7.27276 5.09076 6.62153 5.09076 5.8182C5.09076 5.01487 4.43953 4.36364 3.6362 4.36364C2.83287 4.36364 2.18164 5.01487 2.18164 5.8182C2.18164 6.62153 2.83287 7.27276 3.6362 7.27276ZM13.818 5.8182C13.818 6.62153 13.1668 7.27276 12.3635 7.27276C11.5602 7.27276 10.9089 6.62153 10.9089 5.8182C10.9089 5.01487 11.5602 4.36364 12.3635 4.36364C13.1668 4.36364 13.818 5.01487 13.818 5.8182ZM7.99984 11.6364C8.80317 11.6364 9.4544 10.9852 9.4544 10.1818C9.4544 9.3785 8.80317 8.72728 7.99984 8.72728C7.19651 8.72728 6.54528 9.3785 6.54528 10.1818C6.54528 10.9852 7.19651 11.6364 7.99984 11.6364ZM9.4544 14.5455C9.4544 15.3488 8.80317 16 7.99984 16C7.19651 16 6.54528 15.3488 6.54528 14.5455C6.54528 13.7421 7.19651 13.0909 7.99984 13.0909C8.80317 13.0909 9.4544 13.7421 9.4544 14.5455ZM3.6362 11.6364C4.43953 11.6364 5.09076 10.9852 5.09076 10.1818C5.09076 9.3785 4.43953 8.72728 3.6362 8.72728C2.83287 8.72728 2.18164 9.3785 2.18164 10.1818C2.18164 10.9852 2.83287 11.6364 3.6362 11.6364ZM13.818 10.1818C13.818 10.9852 13.1668 11.6364 12.3635 11.6364C11.5602 11.6364 10.9089 10.9852 10.9089 10.1818C10.9089 9.3785 11.5602 8.72728 12.3635 8.72728C13.1668 8.72728 13.818 9.3785 13.818 10.1818Z" fill="currentColor" />
      </g>
      <defs>
        <clipPath id="clip0_354_66">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconPad;
