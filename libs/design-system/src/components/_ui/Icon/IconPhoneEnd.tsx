import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconPhoneEnd: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 32 32"
      fill="none"
      aria-label={alt}
    >
      <g id="icon / Phone_End">
        <path
          id="Vector"
          d="M27.1081 22.886C28.014 23.2754 29.0542 22.9039 29.5399 22.0216L31.7443 18.0207C32.1783 17.2278 32.0508 16.2311 31.4308 15.5881C22.9094 6.75058 9.09058 6.75058 0.569171 15.5881C-0.0508149 16.2311 -0.178257 17.2278 0.255735 18.0208L2.46013 22.0216C2.9458 22.9039 3.986 23.2754 4.89189 22.886L9.57621 20.8856C10.3719 20.5463 10.861 19.7033 10.7714 18.8173L10.4649 15.6131C14.0367 14.2879 17.9633 14.2879 21.5351 15.6131L21.2251 18.8138C21.139 19.7033 21.6247 20.5427 22.4203 20.8821L27.1047 22.8825L27.1081 22.886Z"
          // fill="white"
        />
      </g>
    </svg>
  );
};

export default IconPhoneEnd;
