import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconPrev10s: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 18 19"
      fill="none"
      aria-label={alt}
    >
      <g id="icon / 10s pre" clipPath="url(#clip0_2021_671)">
        <path
          id="Union"
          fillRule="evenodd"
          clipRule="evenodd"
          d="M4.377 1.79205C7.84379 -0.278855 12.4036 0.169328 15.3677 3.13372C18.8774 6.64422 18.8774 12.3564 15.3677 15.8669C13.9135 17.3191 12.0065 18.2302 9.96319 18.4489C7.9199 18.6675 5.86328 18.1807 4.1348 17.0691C2.40631 15.9576 1.10994 14.2882 0.46092 12.3381C-0.188101 10.388 -0.150728 8.27457 0.566833 6.34866C0.604133 6.24761 0.661009 6.15492 0.734198 6.0759C0.807388 5.99687 0.895453 5.93308 0.993344 5.88816C1.09123 5.84324 1.19703 5.81809 1.30466 5.81415C1.41228 5.81021 1.51963 5.82755 1.62055 5.86519C1.72146 5.90282 1.81395 5.96 1.89273 6.03346C1.9715 6.10692 2.035 6.1952 2.07959 6.29325C2.12418 6.3913 2.14899 6.49719 2.15258 6.60485C2.15617 6.71251 2.13848 6.81982 2.10053 6.92063C1.60875 8.24491 1.50683 9.68244 1.80684 11.0629C2.10685 12.4433 2.79622 13.7088 3.79328 14.7094C6.66567 17.5819 11.3379 17.5819 14.2103 14.7094C17.0819 11.8372 17.0819 7.16342 14.2103 4.29115C11.6728 1.75361 7.70657 1.46741 4.84127 3.42951L5.57476 3.67407C5.67672 3.70808 5.77099 3.76184 5.85218 3.83229C5.93337 3.90274 5.99989 3.98849 6.04794 4.08465C6.09599 4.18081 6.12464 4.28549 6.13224 4.39272C6.13985 4.49996 6.12626 4.60764 6.09225 4.70962C6.05824 4.8116 6.00449 4.90588 5.93405 4.98708C5.86362 5.06828 5.77788 5.13481 5.68173 5.18287C5.58559 5.23093 5.48092 5.25958 5.37371 5.26719C5.26649 5.27479 5.15883 5.2612 5.05686 5.22719L2.93541 4.51979C2.74881 4.4575 2.59087 4.3301 2.49048 4.16089C2.3901 3.99169 2.35398 3.79198 2.38875 3.59833L2.82551 1.17013C2.86478 0.957247 2.98676 0.768582 3.16476 0.645417C3.34276 0.522253 3.56229 0.474614 3.77532 0.512925C3.98835 0.551236 4.17754 0.672377 4.30149 0.849845C4.42544 1.02731 4.47407 1.24666 4.43673 1.45989L4.377 1.79205ZM7.13869 13.3695C6.98521 13.523 6.77705 13.6093 6.56 13.6093C6.34295 13.6093 6.13479 13.523 5.98131 13.3695C5.82783 13.216 5.74161 13.0078 5.74161 12.7907V8.50664L4.93519 8.99066C4.74911 9.10163 4.52663 9.13428 4.31652 9.08145C4.10641 9.02863 3.92581 8.89463 3.81432 8.70885C3.70283 8.52306 3.66953 8.30064 3.72174 8.09034C3.77395 7.88005 3.90739 7.69903 4.09283 7.58698L6.13882 6.35919C6.26301 6.28467 6.40475 6.24445 6.54956 6.24262C6.69438 6.2408 6.83708 6.27743 6.96311 6.34879C7.08915 6.42015 7.19399 6.52368 7.26695 6.6488C7.3399 6.77393 7.37836 6.91618 7.37839 7.06103V12.7907C7.37839 13.0078 7.29217 13.216 7.13869 13.3695ZM13.9256 9.92589C13.9256 11.9914 12.6672 13.6093 11.0612 13.6093C9.45518 13.6093 8.19681 11.9914 8.19681 9.92589C8.19681 7.86036 9.45507 6.24251 11.0612 6.24251C12.6673 6.24251 13.9256 7.8604 13.9256 9.92589ZM12.2888 9.92589C12.2888 8.71982 11.6418 7.87954 11.0612 7.87954C10.4807 7.87954 9.83359 8.71985 9.83359 9.92589C9.83359 11.1319 10.4805 11.9722 11.0612 11.9722C11.6418 11.9722 12.2888 11.132 12.2888 9.92589Z"
          fill="#636363"
        />
      </g>
      <defs>
        <clipPath id="clip0_2021_671">
          <rect
            width="18"
            height="18"
            fill="white"
            transform="translate(0 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconPrev10s;
