interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconSaa: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      width="20"
      height="21"
      viewBox="0 0 20 21"
      aria-label={alt}
    >
      <path
        d="M0.402797 15.1667C0.402797 15.903 0.999762 16.5 1.73612 16.5C1.90313 16.4994 2.06851 16.467 2.22345 16.4046C3.01145 17.008 4.12346 17.3227 5.4901 17.3227C5.69609 17.3227 5.91014 17.313 6.12712 17.299C7.09511 19.277 8.49179 20.5 10.0694 20.5C11.6471 20.5 13.0438 19.277 14.0121 17.3C14.2294 17.314 14.4431 17.3236 14.6491 17.3236C16.0157 17.3236 17.1278 17.009 17.9158 16.4056C18.0706 16.4676 18.2359 16.4996 18.4028 16.5C18.9912 16.4983 19.5091 16.111 19.6769 15.5469C19.8447 14.9829 19.6229 14.3755 19.1311 14.0523C19.1777 12.979 18.7627 11.761 17.9181 10.5C18.7627 9.23934 19.1778 8.02133 19.1311 6.94763C19.7073 6.57576 19.9065 5.82671 19.5909 5.21777C19.2753 4.60882 18.5485 4.33958 17.9124 4.59595C17.0068 3.90297 15.6694 3.59598 14.0124 3.70397C13.0451 1.72434 11.6478 0.5 10.0695 0.5C8.49111 0.5 7.0938 1.72434 6.12546 3.70401C4.46914 3.59667 3.13246 3.90399 2.22545 4.59599C1.58974 4.34168 0.864801 4.61132 0.549767 5.21923C0.234734 5.82715 0.432454 6.57493 1.00678 6.94767C0.960789 8.02069 1.37611 9.23866 2.22079 10.5C1.37611 11.761 0.960789 12.979 1.0078 14.0523C0.822237 14.1731 0.669727 14.3383 0.564086 14.5329C0.458445 14.7274 0.403008 14.9453 0.402797 15.1667ZM1.06948 15.1667C1.06948 14.7985 1.36794 14.5 1.73615 14.5C2.10436 14.5 2.40283 14.7984 2.40283 15.1667C2.40283 15.5349 2.10436 15.8333 1.73615 15.8333C1.55934 15.8333 1.38977 15.7631 1.26474 15.6381C1.13972 15.5131 1.06948 15.3435 1.06948 15.1667ZM6.53914 4.40667C7.42655 4.51291 8.30081 4.70917 9.14847 4.99236C8.55183 5.25863 7.96917 5.55524 7.40284 5.88104C6.83448 6.20906 6.28391 6.56699 5.75347 6.95335C5.92825 6.08 6.1915 5.2267 6.53914 4.40667ZM14.3852 6.95335C13.8548 6.56695 13.3044 6.20898 12.7361 5.88104C12.1706 5.55564 11.5889 5.25903 10.9935 4.99236C11.8402 4.70933 12.7136 4.51307 13.6001 4.40667C13.9476 5.2267 14.2106 6.08 14.3852 6.95335ZM13.5998 16.5934C12.7124 16.4871 11.8381 16.2909 10.9905 16.0077C11.5871 15.7414 12.1698 15.4448 12.7361 15.119C13.3045 14.791 13.855 14.433 14.3855 14.0467C14.2107 14.92 13.9474 15.7733 13.5998 16.5934ZM12.4028 6.45865C13.1545 6.89128 13.8731 7.37894 14.5528 7.91768C14.6754 8.773 14.7366 9.63598 14.7361 10.5C14.7368 11.3641 14.6756 12.227 14.5531 13.0824C13.8733 13.6211 13.1546 14.1087 12.4028 14.5413C11.6534 14.9748 10.8736 15.3533 10.0695 15.674C9.2653 15.3531 8.48547 14.9744 7.73614 14.5407C6.98443 14.108 6.2658 13.6204 5.58612 13.0816C5.34213 11.3687 5.34213 9.62991 5.58612 7.917C6.26584 7.3785 6.98447 6.89108 7.73614 6.45865C8.48615 6.02488 9.26665 5.6461 10.0715 5.32531C10.875 5.64626 11.6541 6.02503 12.4028 6.45865ZM15.3028 8.54767C15.9676 9.13624 16.5715 9.79034 17.1052 10.5C16.5722 11.2096 15.9692 11.8636 15.3052 12.4523C15.3706 11.8036 15.4032 11.152 15.4028 10.5C15.403 9.84801 15.3702 9.19637 15.3045 8.54767H15.3028ZM4.83612 12.4523C4.17112 11.864 3.56727 11.2099 3.03378 10.5C3.56671 9.79046 4.16975 9.13636 4.83378 8.54767C4.70265 9.84594 4.70265 11.1541 4.83378 12.4523H4.83612ZM7.40478 15.119C7.97032 15.4444 8.55199 15.741 9.14747 16.0077C8.30065 16.2907 7.42726 16.4869 6.54077 16.5934C6.19324 15.7733 5.93011 14.92 5.75546 14.0467C6.28518 14.433 6.83507 14.791 7.4028 15.119H7.40478ZM10.0695 19.8334C8.81746 19.8334 7.67979 18.8404 6.84079 17.2297C7.94554 17.073 9.02877 16.7908 10.0695 16.3884C11.1106 16.791 12.1943 17.0732 13.2995 17.2297C12.4588 18.8404 11.3215 19.8334 10.0695 19.8334ZM18.4028 15.8333C18.0347 15.8333 17.7361 15.5349 17.7361 15.1667C17.7361 14.7984 18.0346 14.5 18.4028 14.5C18.771 14.5 19.0695 14.7984 19.0695 15.1667C19.0695 15.5349 18.771 15.8333 18.4028 15.8333ZM18.4728 13.8403C18.4491 13.8403 18.4268 13.8333 18.4028 13.8333C17.6664 13.8333 17.0695 14.4303 17.0695 15.1667C17.0707 15.4647 17.1728 15.7536 17.3592 15.9863C16.6331 16.4653 15.5735 16.693 14.3041 16.6453C14.7174 15.6129 15.0103 14.5362 15.1771 13.4366C16.0393 12.7424 16.8195 11.9522 17.5028 11.0813C18.0656 11.8984 18.4004 12.8508 18.4728 13.8403ZM19.0695 5.83334C19.0695 6.20152 18.771 6.49999 18.4028 6.49999C18.0346 6.49999 17.7361 6.20156 17.7361 5.83334C17.7361 5.46513 18.0346 5.16666 18.4028 5.16666C18.771 5.16666 19.0695 5.46513 19.0695 5.83334ZM17.3592 5.01369C17.1728 5.24636 17.0707 5.53526 17.0695 5.83334C17.0695 6.56973 17.6664 7.16664 18.4028 7.16664C18.4268 7.16664 18.4491 7.16096 18.4728 7.15965C18.4005 8.14924 18.0657 9.10162 17.5028 9.91865C16.8194 9.04777 16.0391 8.25757 15.1768 7.56333C15.0098 6.46374 14.7165 5.38706 14.3028 4.35467C15.5731 4.30668 16.6335 4.53333 17.3592 5.01369ZM10.0695 1.16668C11.3215 1.16668 12.4592 2.15967 13.2982 3.77034C12.194 3.92708 11.1113 4.20935 10.0711 4.61168C9.03004 4.20906 7.94638 3.92678 6.84115 3.77034C7.68015 2.15967 8.81746 1.16668 10.0695 1.16668ZM1.73612 5.1667C2.1043 5.1667 2.4028 5.46513 2.4028 5.83334C2.4028 6.20156 2.10433 6.50003 1.73612 6.50003C1.36791 6.50003 1.06944 6.20156 1.06944 5.83334C1.06944 5.46513 1.36794 5.1667 1.73612 5.1667ZM1.66614 7.15965C1.6898 7.15965 1.71214 7.16664 1.73615 7.16664C2.47254 7.16664 3.06948 6.56969 3.06948 5.83334C3.06823 5.53522 2.96613 5.24636 2.7798 5.01369C3.50547 4.53468 4.56513 4.30734 5.8348 4.35467C5.42156 5.38714 5.1286 6.46381 4.96181 7.56333C4.09974 8.25753 3.3195 9.04765 2.63615 9.91834C2.07314 9.10146 1.73829 8.14916 1.66614 7.15965ZM2.63615 11.0817C3.31981 11.9523 4.10027 12.7424 4.96249 13.4366C5.12944 14.5363 5.42259 15.6131 5.83615 16.6457C4.56747 16.6936 3.50714 16.4653 2.78115 15.9863C2.96698 15.7534 3.0686 15.4646 3.06948 15.1667C3.06948 14.4303 2.47251 13.8333 1.73615 13.8333C1.71214 13.8333 1.68983 13.8394 1.66614 13.8403C1.73841 12.8509 2.07325 11.8986 2.63615 11.0817Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default IconSaa;
