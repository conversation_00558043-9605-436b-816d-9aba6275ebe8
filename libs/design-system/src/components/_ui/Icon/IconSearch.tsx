import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

// in svg file, remove xmlns and fill and add className with fill-current, then can add tailwind style

const IconSearch: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 16 17"
      aria-label={alt}
    >
      <g clipPath="url(#clip0_1727_3907)">
        <path
          d="M13 7C13 8.43437 12.5344 9.75938 11.75 10.8344L15.7063 14.7937C16.0969 15.1844 16.0969 15.8188 15.7063 16.2094C15.3156 16.6 14.6812 16.6 14.2906 16.2094L10.3344 12.25C9.25938 13.0375 7.93437 13.5 6.5 13.5C2.90937 13.5 0 10.5906 0 7C0 3.40937 2.90937 0.5 6.5 0.5C10.0906 0.5 13 3.40937 13 7ZM6.5 11.5C7.09095 11.5 7.67611 11.3836 8.22208 11.1575C8.76804 10.9313 9.26412 10.5998 9.68198 10.182C10.0998 9.76412 10.4313 9.26804 10.6575 8.72208C10.8836 8.17611 11 7.59095 11 7C11 6.40905 10.8836 5.82389 10.6575 5.27792C10.4313 4.73196 10.0998 4.23588 9.68198 3.81802C9.26412 3.40016 8.76804 3.06869 8.22208 2.84254C7.67611 2.6164 7.09095 2.5 6.5 2.5C5.90905 2.5 5.32389 2.6164 4.77792 2.84254C4.23196 3.06869 3.73588 3.40016 3.31802 3.81802C2.90016 4.23588 2.56869 4.73196 2.34254 5.27792C2.1164 5.82389 2 6.40905 2 7C2 7.59095 2.1164 8.17611 2.34254 8.72208C2.56869 9.26804 2.90016 9.76412 3.31802 10.182C3.73588 10.5998 4.23196 10.9313 4.77792 11.1575C5.32389 11.3836 5.90905 11.5 6.5 11.5Z"
          //   fill={className}
        />
      </g>
      <defs>
        <clipPath id="clip0_1727_3907">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(0 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconSearch;
