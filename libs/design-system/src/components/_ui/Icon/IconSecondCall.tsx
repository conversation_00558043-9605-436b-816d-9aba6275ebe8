import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconSecondCall: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 20 20"
      aria-label={alt}
    >
      <g
        id="icon / 2nd call"
        clipPath="url(#clip0_645_745)"
      >
        <path
          id="Union"
          fillRule="evenodd"
          clipRule="evenodd"
          d="M14.4345 5.3125V7.8125C14.4345 8.33203 14.8724 8.75 15.4167 8.75C15.9609 8.75 16.3988 8.33203 16.3988 7.8125V5.3125H19.0179C19.5621 5.3125 20 4.89453 20 4.375C20 3.85547 19.5621 3.4375 19.0179 3.4375H16.3988V0.9375C16.3988 0.417969 15.9609 0 15.4167 0C14.8724 0 14.4345 0.417969 14.4345 0.9375V3.4375H11.8155C11.2712 3.4375 10.8333 3.85547 10.8333 4.375C10.8333 4.89453 11.2712 5.3125 11.8155 5.3125H14.4345ZM3.8247 3.37964C4.45618 3.20712 5.11696 3.52937 5.3676 4.13481V4.13807L6.66963 7.26293C6.89097 7.79351 6.73799 8.40546 6.29204 8.77003L4.68729 10.0818C5.77123 12.3734 7.62662 14.2288 9.91818 15.3127L11.2332 13.708C11.5945 13.262 12.2097 13.109 12.7403 13.3304L15.8652 14.6324C16.4706 14.883 16.7929 15.5438 16.6204 16.1753L15.8391 19.0398C15.6829 19.6061 15.1686 20 14.5827 20C6.52966 20 0 13.4703 0 5.41731C0 4.8314 0.393863 4.3171 0.960244 4.16085L3.8247 3.37964Z"
          // fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_645_745">
          <rect
            width="20"
            height="20"
            fill="white"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconSecondCall;
