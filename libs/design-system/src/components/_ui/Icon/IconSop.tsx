import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
  color?: string;
}

const IconSop: React.FC<IconProps> = ({
  size,
  alt,
  className,
  color = '#636363',
}) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 10 10"
      fill="none"
      aria-label={alt}
    >
      <g clipPath="url(#clip0_1393_1944)">
        <path
          d="M6.44531 2.92969C6.06838 2.92969 5.76172 2.62303 5.76172 2.24609V0H2.26562C1.6733 0 1.19141 0.481895 1.19141 1.07422V8.92578C1.19141 9.51811 1.6733 10 2.26562 10H7.73438C8.3267 10 8.80859 9.51811 8.80859 8.92578V2.92969H6.44531ZM2.79297 7.03125H4.21328C4.37508 7.03125 4.50625 7.16242 4.50625 7.32422C4.50625 7.48602 4.37508 7.61719 4.21328 7.61719H2.79297C2.63117 7.61719 2.5 7.48602 2.5 7.32422C2.5 7.16242 2.63117 7.03125 2.79297 7.03125ZM2.5 5.76172C2.5 5.59992 2.63117 5.46875 2.79297 5.46875H7.08984C7.25164 5.46875 7.38281 5.59992 7.38281 5.76172C7.38281 5.92352 7.25164 6.05469 7.08984 6.05469H2.79297C2.63117 6.05469 2.5 5.92352 2.5 5.76172ZM7.08984 3.90625C7.25164 3.90625 7.38281 4.03742 7.38281 4.19922C7.38281 4.36102 7.25164 4.49219 7.08984 4.49219H2.79297C2.63117 4.49219 2.5 4.36102 2.5 4.19922C2.5 4.03742 2.63117 3.90625 2.79297 3.90625H7.08984Z"
          fill={color}
        />
        <path
          d="M6.34766 2.24568C6.34766 2.29953 6.39146 2.34334 6.44531 2.34334H8.67801C8.62416 2.24364 8.55484 2.15312 8.47264 2.07514L6.58938 0.293457C6.51715 0.22512 6.43581 0.167115 6.34768 0.121094V2.24568H6.34766Z"
          fill={color}
        />
      </g>
      <defs>
        <clipPath id="clip0_1393_1944">
          <rect
            width="10"
            height="10"
            fill="white"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconSop;
