import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconSoundMute: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 32 32"
    >
      <g clipPath="url(#clip0_2033_6505)">
        <path fillRule="evenodd" clipRule="evenodd" d="M16.0096 26.0799C16.0097 26.3615 15.9312 26.6372 15.7832 26.8748C15.6353 27.1124 15.424 27.3021 15.1742 27.4217C14.9246 27.5415 14.6466 27.5863 14.373 27.5508C14.0993 27.5153 13.8412 27.401 13.6288 27.2213L5.68439 20.5038H1.45317C1.26234 20.5039 1.07336 20.4656 0.897038 20.3911C0.720713 20.3167 0.560497 20.2075 0.425544 20.0698C0.290591 19.9321 0.183548 19.7687 0.110531 19.5888C0.0375139 19.4089 -4.50646e-05 19.216 4.05774e-08 19.0213V13.1135C4.05774e-08 12.7202 0.153091 12.343 0.425603 12.0649C0.698115 11.7867 1.06773 11.6304 1.45317 11.6303H5.68473L13.6292 4.91288C13.8414 4.73298 14.0996 4.61863 14.3733 4.58326C14.6471 4.54788 14.925 4.59296 15.1745 4.71318C15.4243 4.83281 15.6354 5.02252 15.7834 5.26013C15.9313 5.49773 16.0099 5.77341 16.01 6.05492L16.0096 26.0799ZM26.6667 14.0898L23.6111 10.8627C23.0944 10.3112 22.25 10.3171 21.7278 10.8627C21.2056 11.4084 21.2056 12.3002 21.7278 12.8518L24.7833 16.0788L21.7278 19.3058C21.2056 19.8515 21.2111 20.7433 21.7278 21.2949C22.2444 21.8464 23.0889 21.8464 23.6111 21.2949L26.6667 18.0678L29.7222 21.2949C30.2389 21.8464 31.0833 21.8405 31.6056 21.2949C32.1278 20.7492 32.1278 19.8574 31.6056 19.3058L28.55 16.0788L31.6056 12.8518C32.1278 12.3061 32.1222 11.4143 31.6056 10.8627C31.0889 10.3112 30.2444 10.3112 29.7222 10.8627L26.6667 14.0898Z" fill="black" />
      </g>
      <defs>
        <clipPath id="clip0_2033_6505">
          <rect width="32" height="32" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconSoundMute;
