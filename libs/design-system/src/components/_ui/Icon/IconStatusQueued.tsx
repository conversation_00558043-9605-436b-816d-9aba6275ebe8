import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconStatusQueued: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_1610_25328)">
        <path
          d="M9.21875 2.71875C9.21875 2.39552 9.09035 2.08552 8.86179 1.85696C8.63323 1.6284 8.32323 1.5 8 1.5C7.67677 1.5 7.36677 1.6284 7.13821 1.85696C6.90965 2.08552 6.78125 2.39552 6.78125 2.71875C6.78125 3.04198 6.90965 3.35198 7.13821 3.58054C7.36677 3.8091 7.67677 3.9375 8 3.9375C8.32323 3.9375 8.63323 3.8091 8.86179 3.58054C9.09035 3.35198 9.21875 3.04198 9.21875 2.71875ZM9.21875 13.2812C9.21875 12.958 9.09035 12.648 8.86179 12.4195C8.63323 12.1909 8.32323 12.0625 8 12.0625C7.67677 12.0625 7.36677 12.1909 7.13821 12.4195C6.90965 12.648 6.78125 12.958 6.78125 13.2812C6.78125 13.6045 6.90965 13.9145 7.13821 14.143C7.36677 14.3716 7.67677 14.5 8 14.5C8.32323 14.5 8.63323 14.3716 8.86179 14.143C9.09035 13.9145 9.21875 13.6045 9.21875 13.2812ZM2.71875 9.21875C3.04198 9.21875 3.35198 9.09035 3.58054 8.86179C3.8091 8.63323 3.9375 8.32323 3.9375 8C3.9375 7.67677 3.8091 7.36677 3.58054 7.13821C3.35198 6.90965 3.04198 6.78125 2.71875 6.78125C2.39552 6.78125 2.08552 6.90965 1.85696 7.13821C1.6284 7.36677 1.5 7.67677 1.5 8C1.5 8.32323 1.6284 8.63323 1.85696 8.86179C2.08552 9.09035 2.39552 9.21875 2.71875 9.21875ZM14.5 8C14.5 7.67677 14.3716 7.36677 14.143 7.13821C13.9145 6.90965 13.6045 6.78125 13.2812 6.78125C12.958 6.78125 12.648 6.90965 12.4195 7.13821C12.1909 7.36677 12.0625 7.67677 12.0625 8C12.0625 8.32323 12.1909 8.63323 12.4195 8.86179C12.648 9.09035 12.958 9.21875 13.2812 9.21875C13.6045 9.21875 13.9145 9.09035 14.143 8.86179C14.3716 8.63323 14.5 8.32323 14.5 8ZM5.12832 12.5957C5.24152 12.4825 5.33132 12.3481 5.39258 12.2002C5.45385 12.0523 5.48538 11.8938 5.48538 11.7337C5.48538 11.5736 5.45385 11.4151 5.39258 11.2672C5.33132 11.1193 5.24152 10.9849 5.12832 10.8717C5.01512 10.7585 4.88073 10.6687 4.73283 10.6074C4.58492 10.5462 4.4264 10.5146 4.26631 10.5146C3.94299 10.5146 3.63292 10.6431 3.4043 10.8717C3.17568 11.1003 3.04724 11.4104 3.04724 11.7337C3.04724 12.057 3.17568 12.3671 3.4043 12.5957C3.63292 12.8243 3.94299 12.9528 4.26631 12.9528C4.58963 12.9528 4.8997 12.8243 5.12832 12.5957ZM5.12832 5.12578C5.24715 5.0139 5.34229 4.87929 5.4081 4.72994C5.47392 4.58059 5.50907 4.41954 5.51146 4.25635C5.51386 4.09315 5.48345 3.93114 5.42204 3.77993C5.36064 3.62871 5.26949 3.49137 5.15399 3.37604C5.0385 3.26072 4.90103 3.16977 4.74972 3.10859C4.59841 3.04741 4.43635 3.01724 4.27317 3.01987C4.10998 3.02251 3.94898 3.05789 3.79973 3.12393C3.65047 3.18997 3.516 3.28531 3.4043 3.4043C3.17568 3.63292 3.04724 3.94299 3.04724 4.26631C3.04724 4.58963 3.17568 4.8997 3.4043 5.12832C3.63292 5.35694 3.94299 5.48538 4.26631 5.48538C4.58963 5.48538 4.8997 5.35694 5.12832 5.12832V5.12578ZM10.8717 12.5957C10.9849 12.7089 11.1193 12.7987 11.2672 12.86C11.4151 12.9212 11.5736 12.9528 11.7337 12.9528C11.8938 12.9528 12.0523 12.9212 12.2002 12.86C12.3481 12.7987 12.4825 12.7089 12.5957 12.5957C12.7089 12.4825 12.7987 12.3481 12.86 12.2002C12.9212 12.0523 12.9528 11.8938 12.9528 11.7337C12.9528 11.5736 12.9212 11.4151 12.86 11.2672C12.7987 11.1193 12.7089 10.9849 12.5957 10.8717C12.4825 10.7585 12.3481 10.6687 12.2002 10.6074C12.0523 10.5462 11.8938 10.5146 11.7337 10.5146C11.5736 10.5146 11.4151 10.5462 11.2672 10.6074C11.1193 10.6687 10.9849 10.7585 10.8717 10.8717C10.7585 10.9849 10.6687 11.1193 10.6074 11.2672C10.5462 11.4151 10.5146 11.5736 10.5146 11.7337C10.5146 11.8938 10.5462 12.0523 10.6074 12.2002C10.6687 12.3481 10.7585 12.4825 10.8717 12.5957Z"
          fill="#E7CC83"
        />
      </g>
      <defs>
        <clipPath id="clip0_1610_25328">
          <rect
            width="13"
            height="13"
            fill="white"
            transform="translate(1.5 1.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconStatusQueued;
