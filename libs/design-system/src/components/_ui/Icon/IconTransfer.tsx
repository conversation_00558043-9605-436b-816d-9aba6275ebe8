import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconTransfer: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 33 32"
      aria-label={alt}
    >
      <g
        id="icon / Transfer"
        clipPath="url(#clip0_3607_4317)"
      >
        <path
          id="Vector"
          d="M21.5567 2.99777C22.0793 2.43532 22.9524 2.41189 23.5038 2.94504L31.6428 10.831C32.7857 11.9383 32.7857 13.8015 31.6428 14.9088L23.5038 22.7948C22.9524 23.3279 22.0793 23.3044 21.5567 22.742C21.034 22.1795 21.057 21.289 21.6083 20.7559L29.7473 12.8699L21.6083 4.98391C21.057 4.45076 21.034 3.56022 21.5567 2.99777ZM13.366 4.43318C13.366 3.69497 13.7911 3.02121 14.4573 2.7224C15.1237 2.4236 15.8933 2.54664 16.4333 3.03878L25.6232 11.4755C26.0081 11.8329 26.2321 12.3367 26.2321 12.8699C26.2321 13.403 26.0081 13.9069 25.6232 14.2643L16.4333 22.701C15.8933 23.199 15.1179 23.322 14.4573 23.0174C13.7968 22.7127 13.366 22.0448 13.366 21.3066V17.5569H11.5281C8.48387 17.5569 6.01402 20.0762 6.01402 23.1814C6.01402 24.9625 6.74922 25.9878 7.28913 26.5033C7.60502 26.8022 7.85203 27.2064 7.85203 27.6458C7.85203 28.2844 7.34657 28.8001 6.72051 28.8001C6.55968 28.8001 6.39886 28.7649 6.25526 28.6887C5.18118 28.097 0.5 25.1324 0.5 18.4944C0.5 12.7996 5.02609 8.18283 10.6091 8.18283H13.366V4.43318Z"
          // fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_3607_4317">
          <rect
            width="32"
            height="32"
            fill="white"
            transform="translate(0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconTransfer;
