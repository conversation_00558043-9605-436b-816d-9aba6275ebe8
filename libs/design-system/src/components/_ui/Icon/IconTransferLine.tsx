import React, { forwardRef } from 'react';

interface IconProps {
    size?: string;
    alt?: string;
    className?: string;
    color?: string;
}

const IconTransferLine= forwardRef<HTMLDivElement, IconProps>(({ size, alt, className, color }, ref) => {
    return (
        <svg aria-label={alt} style={{ width: size, height: size }} className={`fill-none ${className}`} viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M7 7H17V17" stroke={color} strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M7 17L17 7" stroke={color} strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
    );
});

export default IconTransferLine;
