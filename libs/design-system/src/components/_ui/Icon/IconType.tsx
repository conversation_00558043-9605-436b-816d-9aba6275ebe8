interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
  color?: string;
}

const IconType: React.FC<IconProps> = ({ size, alt, className, color }) => {
  return (
    <svg
      className={`fill-none ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 16 16"
      aria-label={alt}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.66648 1.08932C7.76782 1.0308 7.88279 1 7.99981 1C8.11684 1 8.2318 1.0308 8.33314 1.08932L13.8178 4.25598C13.9192 4.31449 14.0033 4.39865 14.0618 4.49999C14.1203 4.60134 14.1511 4.7163 14.1511 4.83332V11.1666C14.1511 11.2837 14.1203 11.3986 14.0618 11.5C14.0033 11.6013 13.9192 11.6855 13.8178 11.744L8.33314 14.9106C8.2318 14.9692 8.11684 15 7.99981 15C7.88279 15 7.76782 14.9692 7.66648 14.9106L2.18148 11.744C2.08013 11.6855 1.99598 11.6013 1.93747 11.5C1.87895 11.3986 1.84815 11.2837 1.84814 11.1666V4.83332C1.84815 4.7163 1.87895 4.60134 1.93747 4.49999C1.99598 4.39865 2.08013 4.31449 2.18148 4.25598L7.66648 1.08932ZM3.18148 5.21832V10.7816L7.99981 13.5636L12.8178 10.7816V5.21832L7.99981 2.43632L3.18148 5.21832Z"
        fill={color}
      />
      <path
        d="M5.07929 5.5918L7.99896 7.23513L10.921 5.5918L11.5743 6.75413L8.32529 8.5808C8.22555 8.63686 8.11305 8.66632 7.99863 8.66632C7.8842 8.66632 7.7717 8.63686 7.67196 8.5808L4.42529 6.75413L5.07929 5.5918Z"
        fill={color}
      />
      <path
        d="M8.66683 7.33301V11.333H7.3335V7.33301H8.66683Z"
        fill={color}
      />
    </svg>
  );
};

export default IconType;
