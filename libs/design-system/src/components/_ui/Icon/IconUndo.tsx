import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconUndo: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 13 13"
      aria-label={alt}
    >
      <g clipPath="url(#clip0_1266_2420)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M4.63683 1.04746C4.78915 1.19983 4.87472 1.40646 4.87472 1.62191C4.87472 1.83736 4.78915 2.04398 4.63683 2.19635L2.77374 4.05944H7.31242C8.82086 4.05944 10.2675 4.65867 11.3341 5.72529C12.4008 6.79192 13 8.23858 13 9.74702V11.372C13 11.5875 12.9144 11.7942 12.762 11.9466C12.6096 12.0989 12.403 12.1845 12.1875 12.1845C11.972 12.1845 11.7653 12.0989 11.613 11.9466C11.4606 11.7942 11.375 11.5875 11.375 11.372V9.74702C11.375 8.66956 10.947 7.63623 10.1851 6.87436C9.42321 6.11248 8.38988 5.68446 7.31242 5.68446H2.77374L4.63683 7.54755C4.71443 7.6225 4.77633 7.71216 4.81891 7.81129C4.86149 7.91042 4.88391 8.01703 4.88485 8.12492C4.88578 8.2328 4.86522 8.33979 4.82437 8.43965C4.78352 8.5395 4.72319 8.63022 4.6469 8.70651C4.57061 8.7828 4.47989 8.84313 4.38003 8.88398C4.28018 8.92484 4.17319 8.9454 4.0653 8.94446C3.95742 8.94352 3.8508 8.92111 3.75167 8.87852C3.65254 8.83594 3.56289 8.77404 3.48794 8.69644L0.237892 5.4464C0.0855698 5.29403 0 5.0874 0 4.87195C0 4.6565 0.0855698 4.44987 0.237892 4.29751L3.48794 1.04746C3.6403 0.89514 3.84693 0.80957 4.06238 0.80957C4.27783 0.80957 4.48446 0.89514 4.63683 1.04746Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_1266_2420">
          <rect
            width="13"
            height="13"
            fill="white"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconUndo;
