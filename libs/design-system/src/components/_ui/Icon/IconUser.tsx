import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconUser: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 20 21"
      aria-label={alt}
    >
      <g clipPath="url(#clip0_2871_724)">
        <path
          d="M4.97162 5.5C4.97162 4.17392 5.50142 2.90215 6.44446 1.96447C7.3875 1.02678 8.66653 0.5 10.0002 0.5C11.3339 0.5 12.6129 1.02678 13.5559 1.96447C14.499 2.90215 15.0288 4.17392 15.0288 5.5C15.0288 6.82608 14.499 8.09785 13.5559 9.03553C12.6129 9.97322 11.3339 10.5 10.0002 10.5C8.66653 10.5 7.3875 9.97322 6.44446 9.03553C5.50142 8.09785 4.97162 6.82608 4.97162 5.5ZM1.2002 19.3398C1.2002 15.4922 4.33519 12.375 8.20484 12.375H11.7956C15.6652 12.375 18.8002 15.4922 18.8002 19.3398C18.8002 19.9805 18.2777 20.5 17.6334 20.5H2.36698C1.7227 20.5 1.2002 19.9805 1.2002 19.3398Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_2871_724">
          <rect
            width="20"
            height="20"
            fill="white"
            transform="translate(0 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconUser;
