import React from 'react';
interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
  color?: string;
}

const IconWhatsapp: React.FC<IconProps> = ({
  size,
  alt,
  className,
  color = '#1CC500',
}) => {
  return (
    <svg
      className={`fill-none ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 16 16"
      aria-label={alt}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.5983 9.57058C11.4036 9.47052 10.4341 8.99564 10.2536 8.93123C10.0733 8.86346 9.94113 8.83133 9.81071 9.03129C9.67868 9.22948 9.30354 9.67063 9.18575 9.80442C9.07146 9.9366 8.95542 9.95259 8.76074 9.85445C7.60368 9.27584 6.84478 8.82222 6.0822 7.51331C5.88049 7.16519 6.28391 7.19012 6.66081 6.43825C6.72506 6.30623 6.69293 6.1937 6.64291 6.09364C6.59288 5.99358 6.2 5.02577 6.03585 4.63113C5.87697 4.24721 5.71266 4.30075 5.59294 4.29356C5.47866 4.28637 5.34823 4.28637 5.21604 4.28637C5.08402 4.28637 4.87144 4.33639 4.69098 4.53108C4.51069 4.72927 4.00177 5.20607 4.00177 6.17388C4.00177 7.14185 4.70712 8.0793 4.80351 8.21132C4.90356 8.34351 6.19105 10.3292 8.16775 11.1844C9.41767 11.7237 9.90693 11.7703 10.5319 11.6774C10.9121 11.6202 11.6961 11.2024 11.8587 10.7399C12.0212 10.2791 12.0212 9.88465 11.973 9.8025C11.925 9.71507 11.7928 9.6652 11.5983 9.57058ZM15.3786 4.90094C14.975 3.94208 14.3964 3.08135 13.6591 2.34195C12.9216 1.60446 12.0609 1.02409 11.1001 0.622423C10.1179 0.209883 9.07514 0.000976562 8.00008 0.000976562H7.96427C6.88218 0.00625119 5.83397 0.220592 4.84826 0.642083C3.89659 1.04919 3.04291 1.6278 2.31261 2.36528C1.58216 3.10277 1.00899 3.95982 0.612592 4.91516C0.201812 5.90439 -0.00517629 6.95628 9.83195e-05 8.03822C0.00553277 9.27759 0.30187 10.5079 0.857302 11.6079V14.3224C0.857302 14.776 1.22509 15.1438 1.6787 15.1438H4.3948C5.4948 15.6992 6.72522 15.9955 7.96443 16.001H8.00184C9.07146 16.001 10.109 15.7938 11.0857 15.3885C12.0411 14.9903 12.9 14.4189 13.6356 13.6885C14.3731 12.9582 14.9534 12.1045 15.3588 11.1528C15.7801 10.1671 15.9945 9.11888 15.9999 8.03678C16.0053 6.94909 15.7947 5.89385 15.3786 4.90094ZM12.6804 12.7224C11.4286 13.9616 9.76787 14.6436 8.00008 14.6436H7.96971C6.89305 14.6384 5.82326 14.3705 4.87879 13.8668L4.7287 13.7866H2.21447V11.2724L2.13424 11.1223C1.63059 10.1778 1.3627 9.10801 1.35743 8.03134C1.35024 6.25092 2.0305 4.57951 3.27867 3.32063C4.52507 2.06175 6.19105 1.36534 7.97147 1.35815H8.00184C8.89468 1.35815 9.76084 1.53126 10.5768 1.87427C11.3733 2.20817 12.0874 2.68848 12.7018 3.30289C13.3143 3.91538 13.7966 4.63145 14.1304 5.42792C14.477 6.25284 14.6501 7.12795 14.6466 8.03151C14.6357 9.80985 13.9375 11.4758 12.6804 12.7224Z"
        fill={color}
      />
    </svg>
  );
};

export default IconWhatsapp;
