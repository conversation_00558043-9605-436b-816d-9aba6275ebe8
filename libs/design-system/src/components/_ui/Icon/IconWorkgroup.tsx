import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconWorkgroup: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 20 21"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.6667 4.66675V3.83341H8.33333V4.66675H11.6667ZM6.66667 4.66675V3.83341C6.66667 2.91425 7.41417 2.16675 8.33333 2.16675H11.6667C12.5858 2.16675 13.3333 2.91425 13.3333 3.83341V4.66675H17.7083C18.975 4.66675 20 5.69175 20 6.95841V8.86675L10.2 12.1334C10.1333 12.1584 10.0667 12.1667 10 12.1667C9.93333 12.1667 9.86667 12.1584 9.8 12.1334L0 8.86675V6.95841C0 5.69175 1.025 4.66675 2.29167 4.66675H6.66667ZM9.35833 13.3001C9.75506 13.4485 10.1911 13.4544 10.5917 13.3167L20 10.1834V16.5417C20 17.8084 18.975 18.8334 17.7083 18.8334H2.29167C1.025 18.8334 0 17.8084 0 16.5417V10.1834L9.35833 13.3001Z"
        fill="black"
      />
    </svg>
  );
};

export default IconWorkgroup;
