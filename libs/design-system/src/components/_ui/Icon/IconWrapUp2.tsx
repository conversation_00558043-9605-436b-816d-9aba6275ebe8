import React from 'react';

interface IconProps {
    size?: string;
    alt?: string;
    className?: string;
}

const IconWrapup2: React.FC<IconProps> = ({ size, alt, className }) => {
    return (
        <svg className={`fill-none ${className}`}
            style={{ width: size, height: size }}
            aria-label={alt} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M13.333 5.3335H9.33299C8.9648 5.3335 8.66632 5.63197 8.66632 6.00016V7.3335C8.66632 7.70169 8.9648 8.00016 9.33299 8.00016H13.333C13.7012 8.00016 13.9997 7.70169 13.9997 7.3335V6.00016C13.9997 5.63197 13.7012 5.3335 13.333 5.3335Z" stroke="black" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M8.66697 6.6665H7.33364C6.98002 6.6665 6.64088 6.80698 6.39083 7.05703C6.14078 7.30708 6.00031 7.64622 6.00031 7.99984V17.3332C6.00031 17.6868 6.14078 18.0259 6.39083 18.276C6.64088 18.526 6.98002 18.6665 7.33364 18.6665H15.3336C15.6873 18.6665 16.0264 18.526 16.2764 18.276C16.5265 18.0259 16.667 17.6868 16.667 17.3332V16.9998" stroke="black" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M14.0003 6.6665H15.3336C15.5675 6.66674 15.7971 6.72847 15.9995 6.84548C16.202 6.96249 16.3701 7.13068 16.487 7.33317" stroke="black" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M8.66632 16H9.33299" stroke="black" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M17.585 12.4174C17.8505 12.1519 17.9997 11.7917 17.9997 11.4161C17.9997 11.0405 17.8505 10.6803 17.585 10.4148C17.3194 10.1492 16.9592 10 16.5836 10C16.2081 10 15.8479 10.1492 15.5823 10.4148L12.909 13.0894C12.7505 13.2478 12.6345 13.4437 12.5716 13.6588L12.0136 15.5721C11.9969 15.6295 11.9959 15.6903 12.0107 15.7482C12.0256 15.806 12.0557 15.8589 12.0979 15.9011C12.1402 15.9434 12.193 15.9735 12.2509 15.9883C12.3088 16.0032 12.3696 16.0022 12.427 15.9854L14.3403 15.4274C14.5554 15.3646 14.7512 15.2486 14.9096 15.0901L17.585 12.4174Z" stroke="black" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
    );
};

export default IconWrapup2;
