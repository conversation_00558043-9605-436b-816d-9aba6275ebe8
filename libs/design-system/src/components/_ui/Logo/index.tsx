import CDSSImage from '../CDSSImage';
interface LogoProps {
  size?: number | string;
}

const Logo = ({ size = '100%' }: LogoProps) => {
  return (
    <div
      className="relative aspect-[225/60]"
      style={{
        width: size,
      }}
    >
      <CDSSImage
        className="object-contain"
        src={'/images/logo-logo.svg'}
        alt=""
        fill
      />
    </div>
  );
};

export default Logo;
