import Icon from '../Icon';
import { cn } from '../../../lib/utils';
export type TMessageStatus = 'danger' | 'info' | 'success' | 'warning';

type TCommonProps = {
  icon?: React.ReactNode;
  message: string;
  status?: TMessageStatus;
};

export type TMessageProps = TCommonProps & React.HTMLAttributes<HTMLDivElement>;

const Message: React.FC<TMessageProps> = ({
  icon = <Icon name="error" />,
  message,
  status = 'danger',
  ...props
}) => {
  const classes = cn(`flex flex-row items-start gap-2 text-remark italic`);

  const messageClasses = {
    danger: 'text-status-danger',
    info: 'text-status-info',
    success: 'text-status-success',
    warning: 'text-status-warning',
  };

  return (
    <div className={cn(classes, messageClasses[status], props?.className)}>
      <span className="inline-flex mt-1">{icon}</span>
      <p>{message}</p>
    </div>
  );
};

export default Message;
