import { useCallback, useEffect, useMemo, useState } from "react"
import { cn, secondsToFormat } from "@cdss-modules/design-system/lib/utils"
import { Panel, Tabs, TabsContent, useTabsContext } from "@cdss-modules/design-system"
import dayjs from 'dayjs';
import Icon from "@cdss-modules/design-system/components/_ui/Icon"
import { useTbarContext } from "@cdss-modules/design-system/context/TBarContext";

interface MiniWallboardProps {
    hidden?: boolean;
}

type MiniWallboard = {
    setIsFullMiniWallboard: (v: boolean) => void
}
type Tpinned = {
    [agent: string]: any[]
    // [workgroup:string]: any[]
}
const initPinned = { "agent": [], "workgroup": [] }
export function MiniWallboard({hidden}:MiniWallboardProps) {

    if (hidden) return null;

    const triggers = [
        {
            value: 'agent',
            label: 'Agent',
        },
        {
            value: 'workgroup',
            label: 'Workgroup',
        },

    ]
    const [currentIndex, setCurrentIndex] = useState(0);
    const [autoPlay, setAutoPlay] = useState(true);
    const [pinned, setPinned] = useState<Tpinned>(initPinned);
    const { defaultMiniWallboardView, onChangeDefaultMiniWallboardView } = useTabsContext();
    const [isFullMiniWallboard, setIsFullMiniWallboard] = useState(false)
    const {
        userMiniWallboardList,
        queueMiniWallboardList,
        statusContext: { isOnQueue },
    } = useTbarContext();
    const handlePin = useCallback((type: any, id: string) => {
        const config:any = {...pinned };
        config.defaultMiniWallboardView=defaultMiniWallboardView        
        if (type == "agent") {
            pinned["workgroup"] = []
            if (pinned["agent"]?.includes(id)) {
                pinned["agent"] = pinned["agent"]?.filter((item: string) => item !== id)

            } else {
                pinned["agent"]?.unshift(id);
            }
            setPinned(pinned);
            // onChange("agent")
            localStorage.setItem('miniWallboardConfig', JSON.stringify(config))
        }
        if (type == "workgroup") {
            pinned["agent"] = []
            if (pinned["workgroup"]?.includes(id)) {
                pinned["workgroup"] = pinned["workgroup"]?.filter((item: string) => item !== id)

            } else {
                pinned["workgroup"]?.unshift(id);
            }
            setPinned(pinned);
            // onChange("workgroup")
            localStorage.setItem('miniWallboardConfig', JSON.stringify(config))
        }
        sortData()
    }, [pinned, queueMiniWallboardList,defaultMiniWallboardView]);
    const pinnedList = pinned[defaultMiniWallboardView || "agent"]
    const [sortedMiniWallboardList, setSortedMiniWallboardList] = useState<any[]>([]);
    const sortData = () => {
        const newSortedList = [...queueMiniWallboardList].sort((a, b): number => {
            const isAPinned = pinned["workgroup"]?.some((item) => item === a?.queueId);
            const isBPinned = pinned["workgroup"]?.some((item) => item === b?.queueId);

            if (isAPinned && !isBPinned) {
                return -1; // a 是 pinned，b 不是，a 排在前面
            } else if (!isAPinned && isBPinned) {
                return 1; // b 是 pinned，a 不是，b 排在前面
            } else {
                // 如果 a 和 b 都是 pinned 或者都不是 pinned，则按照 pinned 列表中的顺序排序
                const indexA = pinned["workgroup"]?.indexOf(a?.queueId);
                const indexB = pinned["workgroup"]?.indexOf(b?.queueId);

                if (indexA !== -1 && indexB !== -1) {
                    // 如果 a 和 b 都在 pinned 列表中，按照 pinned 列表中的顺序排序
                    return pinned["workgroup"].indexOf(a?.queueId) - pinned["workgroup"].indexOf(b?.queueId);
                } else if (indexA !== -1) {
                    // 如果只有 a 在 pinned 列表中，a 排在前面
                    return -1;
                } else if (indexB !== -1) {
                    // 如果只有 b 在 pinned 列表中，b 排在前面
                    return 1;
                } else {
                    // 如果 a 和 b 都不在 pinned 列表中，保持原有顺序
                    return 0;
                }
            }
        });
        setCurrentIndex(0)
        setSortedMiniWallboardList(newSortedList);
    }
    useEffect(() => {
        sortData()
    }, [pinned, queueMiniWallboardList, pinnedList]);


    const onChange = useCallback((value: string) => {        
        const config:any = {...pinned };
        config.defaultMiniWallboardView=value
        onChangeDefaultMiniWallboardView?.(value);
        localStorage.setItem('miniWallboardConfig', JSON.stringify(config));
    }, [pinned, onChangeDefaultMiniWallboardView]);
    

    const onOpenFullMiniWallboardView = useCallback((value: boolean) => {
        setIsFullMiniWallboard(value)
    }, [])
    const newDefaultMiniWallboardView = useMemo(() => {
        return triggers.filter((item) => {
            return item.value == defaultMiniWallboardView

        })
    }, [defaultMiniWallboardView])
    const [onQueueSum, setOnQueueSum] = useState(
        userMiniWallboardList?.[0]?.presence?.onQueue || 0
    );
    const [offQueueSum, setOffQueueSum] = useState(
        userMiniWallboardList?.[0]?.presence?.offQueue || 0
    );

    useEffect(() => {
        const miniWallboardConfig = localStorage.getItem("miniWallboardConfig")
        if ((miniWallboardConfig && JSON.parse(miniWallboardConfig)?.defaultMiniWallboardView) || (miniWallboardConfig && (JSON.parse(miniWallboardConfig)?.defaultMiniWallboardView !== ""))) {
            onChange(JSON.parse(miniWallboardConfig)?.defaultMiniWallboardView)
        } else {
            onChange("agent")
        }
        if (miniWallboardConfig && JSON.parse(miniWallboardConfig)?.["agent"]?.length > 0) {
            setPinned(JSON.parse(miniWallboardConfig));
        } else if (miniWallboardConfig && JSON.parse(miniWallboardConfig)?.["workgroup"]?.length > 0) {
            setPinned(JSON.parse(miniWallboardConfig));
        }
        setAutoPlay(true);
    }, [])
    useEffect(() => {
        setOnQueueSum(userMiniWallboardList?.[0]?.presence?.onQueue);
        setOffQueueSum(userMiniWallboardList?.[0]?.presence?.offQueue);
        const interval = setInterval(() => {
            if (isOnQueue) {
                setOnQueueSum((prevTime) => prevTime + 1000);
            } else {
                setOffQueueSum((prevTime) => prevTime + 1000);
            }
        }, 1000);
        return () => clearInterval(interval);
    }, [isOnQueue, userMiniWallboardList]);

    const agentItem = (data: any[]=[]) => {
        return <div>
            {data?.map((item, i: number) => (<div className="flex items-center gap-6 pt-2 px-4 border-b-2 border-grey-200" key={`mini-wallboard-${i}`}>
                <p><strong>{item?.name == "" ? '-' : item?.name}</strong></p>
                <p><strong>OnQ</strong>: <span>{onQueueSum?dayjs(onQueueSum).utc().format('HH:mm:ss'):"00:00:00"}</span></p>
                <p className="text-[#1CC500]">OffQ: <span>{offQueueSum?dayjs(offQueueSum).utc().format('HH:mm:ss'):"00:00:00"}</span></p>
                <p className="text-[#FF271C]">TTT: <span> {secondsToFormat(
                    (item?.activity?.totalTalkTime as number) / 1000 || 0,
                    'HH:mm:ss'
                )}</span></p>
                <p>Ans: <span> {item?.activity?.tAlert || 0}</span></p>
                <p>Alert: <span> {item?.activity?.inboundCalls +
                    item?.activity?.outboundCalls || 0}</span></p>
                <p>
                    <button
                        className={cn(
                            'flex items-center',
                            pinnedList?.includes(item?.userId)
                                ? 'text-tertiary-400'
                                : 'text-grey-400 hover:text-tertiary-400'
                        )}
                        onClick={() => handlePin("agent", item?.userId)}
                    >
                        <Icon
                            name="pin"
                            className={cn('transition', !pinnedList?.includes(item?.userId) && "rotate-[35deg]")}
                        />
                    </button>
                </p>
            </div>))}
        </div>
    }
    const workgroupItem = (data: any[]) => {

        return <div>
            {data?.map((item, i: number) => (<div className="flex items-center gap-6 pt-2 px-4 border-b-2 border-grey-200" key={`mini-wallboard-${i}`}>

                <p><strong>Queue</strong>: <span>{item?.name ?? '-'}</span></p>
                <p><strong>OnQ</strong>: <span>{item?.queue?.oOnQueueUsers ?? '-'}</span></p>
                <p className="text-[#1CC500]">OffQ: <span>{item?.queue?.oOffQueueUsers ?? '-'}</span></p>
                <p className="text-[#FF271C]">WT: <span>{item?.call?.nWait ?? 0}</span></p>
                <p>Aban(%): <span>{item?.call?.abandonRate ?? '-'}</span></p>
                <p>SVL: <span>{(item?.call?.oServiceLevel && Math.round(item?.call?.oServiceLevel) + "%") ?? '-'}</span></p>
                <p>
                    <button
                        className={cn(
                            'flex items-center',
                            pinnedList?.includes(item?.queueId)
                                ? 'text-tertiary-400'
                                : 'text-grey-400 hover:text-tertiary-400'
                        )}
                        onClick={() => handlePin("workgroup", item?.queueId)}
                    >
                        <Icon
                            name="pin"
                            className={cn('transition ', !pinnedList?.includes(item?.queueId) && "rotate-[35deg]")}

                        />
                    </button>
                </p>
            </div>))}
        </div>
    }

    useEffect(() => {
        let interval: NodeJS.Timeout;

        if (autoPlay) {
            interval = setInterval(() => {
                setCurrentIndex((prevIndex) => (prevIndex + 1) % pinnedList.length);
            }, 3000); // 每隔3秒切换一次
        }

        return () => clearInterval(interval);
    }, [autoPlay, pinnedList.length]);
    const defaultMiniWallItem = () => {


        const handleMouseEnter = () => {
            setAutoPlay(false);
        };

        const handleMouseLeave = () => {
            setAutoPlay(true);
        };

        if (newDefaultMiniWallboardView[0]?.value == "agent" && pinnedList.length > 0) {
            const newList = pinnedList?.map((item) => {
                return userMiniWallboardList?.find((user) => {
                    return user?.userId == item
                })
            });
            const currentItem = newList[currentIndex];
            return (
                <div onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
                    {agentItem([currentItem])}
                </div>
            );
        }

        if (newDefaultMiniWallboardView[0]?.value == "workgroup" && pinnedList.length > 0) {
            const newList = pinnedList?.map((item) => {
                return queueMiniWallboardList?.find((queue) => {
                    return queue?.queueId == item
                })
            });
            const currentItem = newList[currentIndex];
            return (
                <div onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
                    {workgroupItem([currentItem])}
                </div>
            );
        }

        if (pinnedList.length == 0 && newDefaultMiniWallboardView[0]?.value == "agent") {
            return agentItem([userMiniWallboardList[0]]);
        }

        if (pinnedList.length == 0 && newDefaultMiniWallboardView[0]?.value == "workgroup") {
            return workgroupItem([queueMiniWallboardList[0]]);
        }

        return (
            <div className="fixed right-8 bottom-[14px]">
                <div className="flex justify-center gap-2">
                    {newDefaultMiniWallboardView[0]?.value && <p className="bg-[#F2A751] rounded-md pr-2 pl-2">{newDefaultMiniWallboardView[0]?.label}</p>}

                    <button onClick={() => { onOpenFullMiniWallboardView(true) }}>
                        <Icon
                            name="arrow-left"
                            className="-rotate-90 group-hover:block"
                        />
                    </button>
                </div>
            </div>
        );
    };

    return (<>
        {isFullMiniWallboard && <>
            <Panel containerClassName="absolute  w-full bottom-0 rounded-t-lg z-40 overflow-hidden">
                <Tabs
                    defaultTab={newDefaultMiniWallboardView[0]?.value || 'agent'}
                    defaultMiniWallboardView={'agent'}
                    triggers={triggers}
                    triggerClassName="py-2 px-2 text-body "
                    tabsClassName="bg-[#FFAC4A]"
                    defaultNode={(value) => {
                        return <>
                            <button
                                className={cn(
                                    'flex items-center',
                                    defaultMiniWallboardView == value
                                        ? 'text-tertiary-400'
                                        : 'text-grey-400 hover:text-tertiary-400'
                                )}
                                onClick={() => onChange(value)}
                                key={value}
                            >
                                <Icon
                                    name="pin"
                                    className={cn('transition ', !(defaultMiniWallboardView == value) && "rotate-[35deg]")}

                                />
                            </button>
                        </>
                    }

                    }
                >
                    <TabsContent
                        value={'agent'}
                        className="h-0 flex-1 flex flex-col"
                    >
                        {agentItem(userMiniWallboardList)}

                    </TabsContent>
                    <TabsContent
                        value={'workgroup'}
                        className="h-0 flex-1 flex flex-col"
                    >
                        {workgroupItem(sortedMiniWallboardList)}

                    </TabsContent>
                </Tabs>
                <div className="absolute right-2 bottom-2">
                    <div className="flex justify-center gap-2">
                        <p className="bg-[#F2A751] rounded-md pr-2 pl-2">{newDefaultMiniWallboardView[0]?.label}</p>

                        <button onClick={() => { onOpenFullMiniWallboardView(false) }}>
                            <Icon
                                name="arrow-right"
                                className="-rotate-90 group-hover:block"
                            />
                        </button>
                    </div>
                </div>
            </Panel >
        </>}
        <div className=' h-[35px] relative w-full bg-[#ffffff] rounded-t-lg pr-2 pl-2 overflow-auto'>
            {defaultMiniWallItem()}
            {!isFullMiniWallboard&&<div className="fixed right-8 bottom-[22px]">
                <div className="flex justify-center gap-2">
                    {newDefaultMiniWallboardView[0]?.value && <p className="bg-[#F2A751] rounded-md pr-2 pl-2">{newDefaultMiniWallboardView[0]?.label}</p>}

                    <button onClick={() => { onOpenFullMiniWallboardView(true) }}>
                        <Icon
                            name="arrow-left"
                            className="-rotate-90 group-hover:block"
                        />
                    </button>
                </div>
            </div>}
        </div>


    </>
    )
}