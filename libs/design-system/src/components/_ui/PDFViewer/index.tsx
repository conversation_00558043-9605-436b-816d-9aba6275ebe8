'use client';

import { useState, memo, useMemo } from 'react';

import { Document, Page, pdfjs } from 'react-pdf';

import clsx from 'clsx';
import { debounce } from 'lodash';
import {
  ArrowLeft,
  ArrowRight,
  ExternalLinkIcon,
  Minus,
  Plus,
} from 'lucide-react';
import { useObserveElementWidth } from '../../../lib/hooks/useObserveElementWidth';
// remove some third-party library for pdfjs
// pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;
pdfjs.GlobalWorkerOptions.workerSrc =
  process.env['NODE_ENV'] === 'production'
    ? new URL('pdfjs-dist/build/pdf.worker.js', import.meta.url).toString()
    : `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

const PDFViewer = ({ pdf }: any) => {
  const [numPages, setNumPages] = useState<number>();
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [isDocumentReady, setIsDocumentReady] = useState(false);

  const [scale, setScale] = useState(1);

  const { width, ref } = useObserveElementWidth<HTMLDivElement>();
  //   const [elementWidth, setElementWidth] = useState<number>(0);
  //   const [elementHeight, setElementHeight] = useState<number>(0);

  //   const [ref, { width }, _resizing, layoutReady, updateDocumentReady] =
  //     usePdfViewerDimensions(elementWidth, elementHeight);

  const onDocumentLoadSuccess = async ({ numPages }: any) => {
    // setElementWidth(page?.view?.[2] ?? 0);
    // setElementHeight(page?.view?.[3] ?? 0);

    setNumPages(numPages ?? 0);
    setIsDocumentReady(true);
    // updateDocumentReady();
  };

  const actionDebounceDuration = 300;

  const goToNextPage = debounce(() => {
    setPageNumber((prevPageNumber) => prevPageNumber + 1);
  }, actionDebounceDuration);

  const goToPreviousPage = debounce(() => {
    setPageNumber((prevPageNumber) => prevPageNumber - 1);
  }, actionDebounceDuration);

  const magnify = debounce(() => {
    setScale((prevScale) => prevScale + 0.5);
  }, actionDebounceDuration);

  const shrink = debounce(() => {
    if (scale > 1) {
      setScale((prevScale) => prevScale - 0.5);
    } else {
      setScale(1);
    }
  }, actionDebounceDuration);

  const renderDocument = useMemo(() => {
    return (
      <>
        {isDocumentReady && (
          <Page
            key={pageNumber}
            pageNumber={pageNumber}
            className="flex justify-center items-center m-auto border"
            renderAnnotationLayer={false}
            renderTextLayer={false}
            // width={width - 2 ?? 0}
            scale={scale}
          />
        )}
      </>
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pdf, pageNumber, width, scale, isDocumentReady]);

  return (
    <div className="size-full flex flex-col">
      <div
        className={`flex items-center justify-between w-full mb-4 flex-none bg-white z-30`}
      >
        <div className="flex">
          <div className="font-bold text-body">Standard Script:</div>
        </div>
        <div className="flex gap-6">
          <button
            onClick={goToPreviousPage}
            disabled={pageNumber <= 1}
            className="relative hover:text-primary-contrast curson-pointer focus:z-20 disabled:opacity-30 disabled:pointer-events-none"
          >
            <ArrowLeft
              className="h-6 w-6 "
              aria-hidden="true"
            />
          </button>
          <button
            onClick={goToNextPage}
            disabled={pageNumber >= numPages!}
            className="relative hover:text-primary-contrast curson-pointer focus:z-20 disabled:opacity-30 disabled:pointer-events-none"
          >
            <ArrowRight
              className="h-6 w-6"
              aria-hidden="true"
            />
          </button>
          <button
            onClick={magnify}
            disabled={scale >= 5}
            className="relative hover:text-primary-contrast curson-pointer focus:z-20 disabled:opacity-30 disabled:pointer-events-none"
          >
            <Plus
              className="h-6 w-6"
              aria-hidden="true"
            />
          </button>
          <button
            onClick={shrink}
            disabled={scale === 1}
            className="relative hover:text-primary-contrast curson-pointer focus:z-20 disabled:opacity-30 disabled:pointer-events-none"
          >
            <Minus
              className="h-6 w-6"
              aria-hidden="true"
            />
          </button>
          <a
            target="_blank"
            href={pdf}
            className="hover:text-primary-contrast"
          >
            <ExternalLinkIcon
              className="h-6 w-6"
              aria-hidden="true"
            />
          </a>
        </div>
      </div>
      <div className={clsx('relative w-full h-0 flex-1')}>
        <div
          className="relative w-full h-full overflow-auto border-gray-200"
          ref={ref}
        >
          <Document
            file={pdf}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={console.error}
            externalLinkRel={'noopener'}
            externalLinkTarget={'_blank'}
            options={{
              cMapUrl: 'cmaps/',
              cMapPacked: true,
              standardFontDataUrl: 'standard_fonts/',
            }}
            renderMode="canvas"
          >
            {renderDocument}
          </Document>
        </div>
      </div>
    </div>
  );
};

export default memo(PDFViewer);
