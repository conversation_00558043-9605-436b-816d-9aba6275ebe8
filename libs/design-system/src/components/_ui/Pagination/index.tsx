'use client';

import { cn } from '../../../lib/utils';
import Icon from '../Icon';
import { useEffect, useMemo, useState } from 'react';
import {
  DropdownArrow,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '../DropdownMenu';
import Input from '../Input';
import _ from 'lodash';
import { Button } from '@cdss-modules/design-system';
import { Select } from '../Select';
import { ChevronLeft, ChevronRight, Ellipsis } from 'lucide-react';
import { useTranslation } from 'react-i18next';

type TSetPageProps = {
  key: string;
  isPageOpen: boolean;
};

type TPaginationProps = {
  total: number;
  totalCount: number;
  current?: number;
  perPage: number;
  initialPage?: number;
  siblings?: number;
  onChange?: (e: number) => void;
  paginationItemClass?: string;
  handleOnNext?: () => void;
  handleOnPrevious?: () => void;
  handlePerPageSetter: (page: number) => void;
};

export const Pagination: React.FC<TPaginationProps> = ({
  total = 10,
  current,
  initialPage = 1,
  siblings = 1,
  onChange,
  paginationItemClass,
  handleOnNext,
  handleOnPrevious,
  totalCount,
  perPage,
  handlePerPageSetter,
}) => {
  //console.log("pagnation render");
  const { t } = useTranslation();
  const [currentPage, setCurrentPage] = useState<number>(
    current ?? initialPage
  );
  const [openSetPage, setOpenSetPage] = useState<TSetPageProps>({
    key: '',
    isPageOpen: false,
  });
  const [inputValue, setInputValue] = useState<number | null>(null);

  const page = current ?? currentPage;
  const updatePage = onChange ?? setCurrentPage;
  const onNext = () => {
    if (page === total) return;
    updatePage(page + 1);
  };

  const jumpTo = (num: number) => {
    if (num > total) return;

    if (inputValue) {
      console.log('have input value');
      updatePage(inputValue);
    }
  };

  const onPrevious = () => {
    if (page === 1) return;
    updatePage(page - 1);
  };

  const createPaginationItem = (i: number) => {
    return (
      <div
        key={i}
        onClick={() => updatePage(i)}
        className={cn(
          `w-6 h-6 flex items-center justify-center cursor-pointer ${paginationItemClass}`,
          page === i && 'text-primary-500 border border-primary-500 rounded'
        )}
      >
        {i}
      </div>
    );
  };

  const ellipsis = (key: string) => {
    return (
      <Ellipsis 
        color="#dedede" 
        size={24}
      />
    );
  };

  // const ellipsis = (key: string) => {
  //   return (
  //     <DropdownMenu key={key}>
  //       <DropdownMenuTrigger>
  //         <li
  //           onClick={() => {
  //             setOpenSetPage({ key: key, isPageOpen: !openSetPage.isPageOpen });
  //           }}
  //           className={cn(
  //             `w-8 h-8 rounded-full flex items-center justify-center text-remark cursor-pointer pt-[1px] hover:border hover:border-black`,
  //             key === openSetPage.key && openSetPage.isPageOpen
  //               ? 'bg-black text-white'
  //               : 'bg-grey-100 text-black'
  //           )}
  //         >
  //           ...
  //         </li>
  //       </DropdownMenuTrigger>
  //       <DropdownMenuContent
  //         side="top"
  //         arrowPadding={2}
  //         className="overflow-visible"
  //       >
  //         <div className="relative px-4 py-2 bg-white flex flex-row gap-2.5 items-center">
  //           <p className=" whitespace-nowrap">Jump to page</p>
  //           <Input
  //             className="w-16"
  //             onChange={(e: any) => setInputValue(Number(e))}
  //           />
  //           <Button
  //             onClick={() => inputValue && jumpTo(inputValue)}
  //             variant="primary"
  //             size={'mini'}
  //             bodyClassName="w-[32px] h-[32px]"
  //           >
  //             Go
  //           </Button>

  //           <DropdownArrow width={12} height={10} className="fill-white" />
  //         </div>
  //       </DropdownMenuContent>
  //     </DropdownMenu>
  //   );
  // };

  const renderPaginationRange: any = useMemo(() => {
    let totalPageNoInArray = 5 + siblings;
    if (totalPageNoInArray >= total) {
      return [_.range(1, total + 1).map((n) => createPaginationItem(n))];
    }

    let leftSiblingsIndex = Math.max(page - siblings, 1);
    let rightSiblingsIndex = Math.min(page + siblings, total);
    let showLeftDots = leftSiblingsIndex > 2;
    let showRightDots = rightSiblingsIndex < total - 2;

    if (!showLeftDots && showRightDots) {
      let leftItemsCount = 3 + 2 * siblings;
      let leftRange = _.range(1, leftItemsCount + 1);

      const leftRangeItems = leftRange.map((n) => {
        return createPaginationItem(n);
      });
      return [
        ...leftRangeItems,
        ellipsis(`${current}-showRightDots`),
        createPaginationItem(total),
      ];
    } else if (showLeftDots && !showRightDots) {
      let rightItemsCount = 3 + 2 * siblings;
      let rightRange = _.range(total - rightItemsCount + 1, total + 1);
      const rightRangeItems = rightRange.map((n) => {
        return createPaginationItem(n);
      });

      return [
        createPaginationItem(1),
        ellipsis(`${current}-showLeftDots`),
        ...rightRangeItems,
      ];
    } else {
      let middleRange = _.range(leftSiblingsIndex, rightSiblingsIndex + 1);
      const middleRangeItems = middleRange.map((n) => {
        return createPaginationItem(n);
      });
      return [
        createPaginationItem(1),
        ellipsis(`${current}-showLeftDots`),
        ,
        middleRangeItems,
        ellipsis(`${current}-showRightDots`),
        ,
        createPaginationItem(total),
      ];
    }
  }, [page, currentPage, total, openSetPage, inputValue]);

  useEffect(() => {
    return () => {
      setOpenSetPage({ key: '', isPageOpen: false });
    };
  }, [page]);

  const options = [5, 10, 20, 50]

  return (
    <div className="w-full min-w-[384px] flex justify-end items-center gap-2">
      <>{t('pagination.total', { count: totalCount })}</>
      <button onClick={handleOnPrevious ?? onPrevious} disabled={page === 1}>
        <ChevronLeft
          size={18}
          color={page === 1 ? "#dedede" : "black"}
        />
      </button>
      {renderPaginationRange}
      <button onClick={handleOnNext ?? onNext} disabled={page === total}>
        <ChevronRight
          size={18}
          color={page === total ? "#dedede" : "black"}
        />
      </button>
      <Select
        mode="single"
        options={options.map((option) => ({
          id: `${option}-perpage`,
          label: option.toString(),
          value: option.toString(),
        }))}
        value={perPage.toString()}
        onChange={handlePerPageSetter}
      />
      <section className='flex flex-row items-center gap-2'>
        <span className='truncate'>{t('pagination.jump')}</span>
        <input
          type='number'
          className="w-[50px] px-2 py-1 rounded border border-grey text-center appearance-none focus:outline-none custom-input"
          onChange={(e: any) => setInputValue(Number(e.target.value))}
          onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
            if (e.key === 'Enter') {
              console.log('Enter key pressed:', inputValue);
              inputValue && jumpTo(inputValue)
            }
          }}
          value={inputValue ?? ''}
        />
        {/* remove the default style of increase button and reduce button */}
        <style>{`
          .custom-input::-webkit-outer-spin-button,
          .custom-input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
          }

          .custom-input[type='number'] {
            -moz-appearance: textfield; /* Firefox */
          }
        `}</style>
      </section>
    </div>
  );
};

type TPerPageSetterProps = {
  id?: string;
  perPage: number;
  totalCount: number;
  currentPage: number;
  handleChange: (page: number) => void;
  options?: number[];
};
export const PerPageSetter = ({
  id = 'perPageSetter',
  perPage,
  currentPage,
  totalCount,
  handleChange,
  options = [5, 10, 20, 50],
}: TPerPageSetterProps) => {
  // show start to end index in pagination
  const startPage = (currentPage - 1) * perPage + 1;
  const endPage = currentPage * perPage;
  const totalItems = totalCount;


  return (
    <div className="flex items-center gap-x-4">
      <span className="italic">
        {startPage} ~ {endPage} / {totalItems}
      </span>

      <Select
        mode="single"
        options={options.map((option) => ({
          id: `${id}-${option}-perpage`,
          label: option.toString(),
          value: option.toString(),
        }))}
        value={perPage.toString()}
        onChange={handleChange}
      />
    </div>
  )
};

export default Pagination;
