import { ReactNode, Suspense, useState } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '../DropdownMenu';
import { cn } from '../../../lib/utils';
import Loader, { LoadingBlock } from '../Loader';
import <PERSON>th<PERSON><PERSON><PERSON>, { TAuthCheckerProps } from '../AuthChecker';
import Icon from '../Icon';
import { Popup } from '@cdss-modules/design-system';

export type TPanelHeaderProps = {
  title?: string;
  customTitle?: ReactNode;
  menu?: ReactNode;
  collapsible?: boolean;
  headerClassName?: string;
  headerTitleSize?: string;
  popupContent?: ReactNode;
  collapsibleOpen?: boolean;
  handleCollapsible?: () => void;
  dropdownMenuOpen?: boolean;
  handleDropdownMenuOpen?: () => void;
  customItems?: ReactNode;
};

export const PanelHeader = ({
  title,
  customTitle,
  menu,
  collapsible,
  headerClassName,
  headerTitleSize,
  popupContent,
  collapsibleOpen = true,
  handleCollapsible,
  dropdownMenuOpen,
  handleDropdownMenuOpen,
  customItems,
}: TPanelHeaderProps) => {
  return (
    <>
      <div
        className={cn(
          'flex w-full bg-primary-500 justify-between items-center px-4 py-2 rounded-t-2xl',
          headerClassName
        )}
      >
        <div className="flex items-center gap-4">
          {title && (
            <button
              onClick={collapsible ? handleCollapsible : undefined}
              className={cn(
                'inline-flex gap-2 flex-1 items-center group',
                collapsible ? 'cursor-pointer' : 'cursor-default'
              )}
            >
              <h2 className={cn('text-lg font-semibold', headerTitleSize)}>
                {title}
              </h2>
              {collapsible && (
                <Icon
                  name="arrow-left"
                  size={16}
                  className={`group-hover:fill-tertiary-500 transition-all ease-in-out ${collapsibleOpen ? 'rotate-90' : '-rotate-90'
                    } `}
                />
              )}
            </button>
          )}
          {customTitle}
        </div>
        <div className="flex gap-4 items-center justify-between">
          {customItems}
          {menu && (
            <Popup>
              <DropdownMenu
                open={dropdownMenuOpen}
                onOpenChange={handleDropdownMenuOpen}
              >
                <DropdownMenuTrigger asChild>
                  <button
                    onClick={handleDropdownMenuOpen}
                    className="p-1"
                  >
                    <Icon
                      name="verticalDots"
                      size={23}
                    />
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">{menu}</DropdownMenuContent>
              </DropdownMenu>
              {popupContent && popupContent}
            </Popup>
          )}
        </div>
      </div>
      {collapsible && !collapsibleOpen && (
        <button
          onClick={handleCollapsible}
          className={cn(
            'transition bg-common-bg group w-full h-4 flex items-center justify-center rounded-b-xl border-b border-x border-dashed border-tertiary-500 hover:bg-white hover:border-white hover:border-solid hover:shadow-md'
          )}
        >
          <Icon
            name="arrow-left"
            size={12}
            className="opacity-0 transition group-hover:opacity-100 fill-tertiary-500 -rotate-90"
          />
        </button>
      )}
    </>
  );
};

export type TPanelProps = {
  header?: TPanelHeaderProps;
  headerClassName?: string;
  headerTitleSize?: string;
  customHeader?: ReactNode;
  collapsible?: boolean;
  collapsibleOpen?: boolean;
  collapsibleHandle?: (open: boolean) => void;
  children?: ReactNode;
  className?: string;
  containerClassName?: string;
  loading?: boolean;
  authCheck?: Omit<TAuthCheckerProps, 'children'>;
  footer?: ReactNode;
  id?: string;
  style?: any;
  popupContent?: ReactNode;
  dropdownMenuOpen?: boolean;
  handleDropdownMenuOpen?: () => void;
  customItems?: ReactNode;
};

export const Panel = ({
  children,
  customHeader,
  collapsible = false,
  header,
  headerClassName,
  headerTitleSize,
  className,
  containerClassName,
  loading,
  authCheck,
  footer,
  id,
  style,
  popupContent,
  collapsibleOpen,
  collapsibleHandle,
  dropdownMenuOpen,
  handleDropdownMenuOpen,
  customItems,
}: TPanelProps) => {
  const [localOpen, setLocalOpen] = useState(true);

  const panelOpen = collapsibleHandle ? collapsibleOpen : localOpen;

  const handleCollapsible = () => {
    collapsibleHandle
      ? collapsibleHandle(!panelOpen)
      : setLocalOpen(!panelOpen);
  };

  const body = (
    <>
      {loading ? (
        <LoadingBlock />
      ) : (
        <>
          <div className={cn('h-full flex flex-col max-h-full', className)}>
            {customHeader ??
              (header && (
                <PanelHeader
                  collapsible={collapsible}
                  headerClassName={headerClassName}
                  headerTitleSize={headerTitleSize}
                  popupContent={popupContent}
                  collapsibleOpen={panelOpen}
                  handleCollapsible={handleCollapsible}
                  dropdownMenuOpen={dropdownMenuOpen}
                  handleDropdownMenuOpen={handleDropdownMenuOpen}
                  customItems={customItems}
                  {...header}
                />
              ))}

            {panelOpen && children}
            {panelOpen && footer && (
              <div
                className={`flex items-center w-full px-4 pt-2 pb-4 border-t border-grey-200 transition-all ease-in-out duration-300 `}
              >
                {footer}
              </div>
            )}
          </div>
        </>
      )}
    </>
  );
  return (
    <Suspense
      fallback={
        <>
          <Loader />
        </>
      }
    >
      <div
        id={id}
        style={style}
        className={cn('rounded-2xl w-full bg-white', containerClassName)}
      >
        {authCheck ? <AuthChecker {...authCheck}>{body}</AuthChecker> : body}
      </div>
    </Suspense>
  );
};

export default Panel;
