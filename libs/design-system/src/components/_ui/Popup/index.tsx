'use client';

import * as React from 'react';
import * as PopupPrimitive from '@radix-ui/react-dialog';
import { cn } from '../../../lib/utils';
import Icon from '../Icon';

const Popup = PopupPrimitive.Root;

const PopupTrigger = PopupPrimitive.Trigger;

const PopupPortal = PopupPrimitive.Portal;

const PopupClose = PopupPrimitive.Close;

const PopupOverlay = React.forwardRef<
  React.ElementRef<typeof PopupPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof PopupPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <PopupPrimitive.Overlay
    ref={ref}
    className={cn(
      'fixed inset-0 z-50 bg-white/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 dark:bg-slate-950/80',
      className
    )}
    {...props}
  />
));
PopupOverlay.displayName = PopupPrimitive.Overlay.displayName;

const PopupContent = React.forwardRef<
  React.ElementRef<typeof PopupPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof PopupPrimitive.Content> & {
    headerClassName?: string;
    hideCloseButton?: boolean;
  }
>(
  (
    { className, children, title, headerClassName, hideCloseButton, ...props },
    ref
  ) => (
    <PopupPortal>
      <PopupOverlay />
      <PopupPrimitive.Content
        ref={ref}
        className={cn(
          'fixed left-[50%] top-[50%] z-50 flex flex-col max-w-[814px] w-full overflow-hidden translate-x-[-50%] translate-y-[-50%] bg-white rounded-2xl duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] md:w-full',
          className
        )}
        {...props}
      >
        <div
          className={cn(
            'px-4 py-2 bg-primary-500 text-black font-bold flex justify-between items-center',
            headerClassName
          )}
        >
          <p>{title}</p>
          {!hideCloseButton && (
            <PopupPrimitive.Close className="outline-none">
              <Icon
                name="cross"
                size={20}
              />
            </PopupPrimitive.Close>
          )}
        </div>
        {children}
      </PopupPrimitive.Content>
    </PopupPortal>
  )
);
PopupContent.displayName = PopupPrimitive.Content.displayName;
const PopupFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      'flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2',
      className
    )}
    {...props}
  />
);
PopupFooter.displayName = 'PopupFooter';

const PopupTitle = React.forwardRef<
  React.ElementRef<typeof PopupPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof PopupPrimitive.Title>
>(({ className, ...props }, ref) => (
  <PopupPrimitive.Title
    ref={ref}
    className={cn(
      'text-lg font-semibold leading-none tracking-tight',
      className
    )}
    {...props}
  />
));
PopupTitle.displayName = PopupPrimitive.Title.displayName;
export {
  Popup,
  PopupPortal,
  PopupOverlay,
  PopupClose,
  PopupTrigger,
  PopupContent,
  PopupFooter,
  PopupTitle,
};
