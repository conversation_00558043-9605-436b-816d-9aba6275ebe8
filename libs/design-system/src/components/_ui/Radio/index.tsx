'use client';

import React from 'react';
import { cn } from '../../../lib/utils';

export type TRadioProps = {
  testId?: string;
  className?: string;
  label: string;
} & React.InputHTMLAttributes<HTMLInputElement>;

const Radio = ({ className, label, ...props }: TRadioProps) => {
  return (
    <label
      htmlFor={props.id}
      className={cn(
        `flex items-start gap-2`,
        className,
        props.disabled && 'opacity-50 pointer-events-none'
      )}
    >
      <div className="flex items-center gap-2 pt-1">
        <input
          id={props.id}
          type="radio"
          className="hidden pl-4 pointer-events-none"
          aria-hidden
          {...props}
        />
        <span
          className={cn(
            `w-4 h-4 rounded-full bg-white border-primary-500 cursor-pointer ${
              props.checked ? `border-[5px]` : `border`
            }`,
            className
          )}
        />
      </div>
      <span
        className="w-full break-words"
        dangerouslySetInnerHTML={{ __html: label }}
      />
    </label>
  );
};

export default Radio;
