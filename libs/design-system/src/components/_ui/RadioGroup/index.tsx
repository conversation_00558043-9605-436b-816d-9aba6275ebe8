import { cn } from '../../../lib/utils';
import Radio from '../Radio';

interface TItemProps {
  label: string;
  value: string;
  id: string; // make the option id unique
}

export type TRadioGroupProps = {
  testId?: string;
  items: TItemProps[];
  name: string;
  checked?: boolean;
  onChange?: (value: string) => void;
  direction?: 'vertical' | 'horizontal';
  className?: string;
} & React.InputHTMLAttributes<HTMLInputElement>;

const RadioGroup: React.FC<TRadioGroupProps> = ({
  name,
  items,
  value,
  onChange,
  direction = 'horizontal',
  className,
  ...props
}) => {
  const isRadioChecked = (checkedValue: string): boolean =>
    value === checkedValue;

  const directionClass = {
    horizontal: 'flex flex-wrap gap-2',
    vertical: 'flex flex-col gap-2',
  };

  return (
    <div className={cn(directionClass[direction], className)}>
      {items?.map((item: TItemProps) => (
        <Radio
          id={item.id}
          key={item.id}
          name={name}
          value={item.value}
          label={item.label}
          onChange={onChange}
          checked={isRadioChecked(item.value)}
          {...props}
        />
      ))}
    </div>
  );
};

export default RadioGroup;
