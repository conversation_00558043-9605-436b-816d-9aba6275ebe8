import { InputComponentProps } from '../types';

const TextInputField = (props: InputComponentProps) => {
  return (
    <input
      className="w-full h-full outline-none ring-0 p-2 border border-grey-300 rounded-md disabled:bg-grey-200 disabled:text-neutral-500"
      type="text"
      value={props.value}
      onChange={(e) => props.onChange(e.target.value)}
    />
  );
};

export default TextInputField;
