import { memo, MutableRefObject } from 'react';
import SAAv2 from '../CallInfo/Info/SAAv2';
import SAA from './SAA';
import { SAAContentSection, SAASearchTerm } from '../../../@types/SAA';

type TProps = {
  loading?: boolean;
  data: any;
  onSaaContentCopy?: (contentToSet: string) => void;
  onSaaContentSend?: (contentToSet: string) => void;
  useSAAv2?: boolean;
  hasAutoMod?: boolean;
  isShowCopy?: boolean;
  setIsFullResizablePanel?: (v: boolean) => void;
  autoSAAResult?: Record<string, SAAContentSection[]>;
  SAAManualSearchTerm?: SAASearchTerm;
};
const Saa: React.FC<TProps> = memo((props) => {
  const { data, setIsFullResizablePanel } = props;
  const useSAAv2 = props?.useSAAv2 || false;
  return useSAAv2 ? (
    <SAAv2
      data={{ convId: data?.convId }}
      onSaaContentCopy={props.onSaaContentCopy}
      onSaaContentSend={props.onSaaContentSend}
      hasAutoMod={props?.hasAutoMod}
      isShowCopy={props?.isShowCopy}
      setIsFullResizablePanel={setIsFullResizablePanel}
      autoSAAResult={props.autoSAAResult}
      SAAManualSearchTerm={props.SAAManualSearchTerm}
    />
  ) : (
    <SAA
      data={{ SAADetail: data?.SAADetail, getSAADetail: data?.getSAADetail }}
      setIsFullResizablePanel={setIsFullResizablePanel}
    />
  );
});
export default Saa;
