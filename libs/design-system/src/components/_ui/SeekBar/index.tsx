'use client';
import { forwardRef } from 'react';

export type TSeekBarProps = {
  progress: number;
  handleSeekMouseDown: (e: any) => void;
  handleSeekChange: (e: any) => void;
  handleSeekMouseUp: (e: any) => void;
  goTo?: (e: any) => void;
};

export const SeekBar = forwardRef<HTMLDivElement, TSeekBarProps>(
  (
    {
      handleSeekMouseDown,
      handleSeekChange,
      handleSeekMouseUp,
      progress,
      goTo,
    }: TSeekBarProps,
    ref
  ) => {
    return (
      <div
        className="w-full h-[6px] bg-grey-200 relative cursor-pointer rounded-full"
        ref={ref}
        onClick={goTo}
      >
        <input
          type="range"
          min={0}
          max={0.999999}
          step="any"
          value={progress}
          onMouseDown={handleSeekMouseDown}
          onChange={handleSeekChange}
          onMouseUp={handleSeekMouseUp}
          className="absolute w-full h-full border-none accent-primary appearance-none bg-transparent z-20 cursor-pointer"
        />
        <div
          className="absolute w-full h-full rounded-tl-full rounded-bl-full bg-primary z-10 left-0 top-0"
          style={{ width: `${progress * 100}%` }}
        />
      </div>
    );
  }
);

SeekBar.displayName = 'SeekBar';

export default SeekBar;
