import { cn } from '../../../lib/utils';

export type StatusType = 'active' | 'inactive';

type StatusBadgeProps = {
  status: StatusType;
  className?: string;
};

const statusStyles: Record<StatusType, string> = {
  active: 'bg-[#F6FFED] text-status-success border-[#B7EB8F]',
  inactive: 'bg-[#F5EDFF] text-[#9747FF] border-[#C79DFF]',
};

const StatusBadge: React.FC<StatusBadgeProps> = ({ status, className }) => {
  return (
    <div
      className={cn(
        'px-2 rounded-sm text-sm inline-block border',
        statusStyles[status],
        className
      )}
    >
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </div>
  );
};

export default StatusBadge;
