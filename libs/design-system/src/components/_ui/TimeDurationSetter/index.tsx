import React, { useState, useMemo, useEffect } from 'react';
import { useGlobalAudioPlayer } from 'react-use-audio-player';
import { secondsToFormat } from '../../../lib/utils';
import {
  DropdownArrow,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '../DropdownMenu';
import Input from '../Input';
import Button from '../Button';
import { useQM } from '../../../context/QMContext';

const isValidTimeFormat = (inputValue: string) => {
  const timeString = inputValue;
  // Regular expression to match the mm:ss format
  const timeFormatRegex = /^\d{2}:\d{2}$/;

  // Check if the string matches the mm:ss format
  if (!timeFormatRegex.test(timeString)) {
    return false;
  }

  // Split the string into minutes and seconds
  const [minutes, seconds] = timeString.split(':').map(Number);

  // Check if minutes and seconds are within the 0-59 range
  if (minutes < 0 || minutes > 59 || seconds < 0 || seconds > 59) {
    return false;
  }

  return true;
};

const convertTimeToSeconds = (inputValue: string, duration: number) => {
  const [minutes, seconds] = inputValue.split(':').map(Number);
  let totalSecs = minutes * 60 + seconds;
  if (totalSecs > duration) totalSecs = duration;
  if (totalSecs < 0) totalSecs = 0;
  return totalSecs;
};

export const TimeDurationSetter = ({
  label,
  updateStartTime,
  updateEndTime,
  startTime,
  endTime,
}: {
  label?: any;
  updateStartTime: (time: number) => void;
  updateEndTime: (time: number) => void;
  startTime: number;
  endTime: number;
}) => {
  const {
    closeTempTime,
    showTempTime,
    updateTempStartTime,
    updateTempEndTime,
  } = useQM();
  const { duration } = useGlobalAudioPlayer();
  const [startInputValue, setStartInputValue] = useState<string>('00:00');
  const [endInputValue, setEndInputValue] = useState<string>('00:00');
  const [open, setOpen] = useState(false);

  useEffect(() => {
    setEndInputValue(secondsToFormat(duration || 0));
  }, [duration]);

  useEffect(() => {
    setStartInputValue(secondsToFormat(startTime));
    setEndInputValue(secondsToFormat(endTime));
  }, [startTime, endTime]);

  const isAllValidTimeFormat = useMemo(() => {
    return (
      updateTempStartTime(convertTimeToSeconds(startInputValue, duration)),
      updateTempEndTime(convertTimeToSeconds(endInputValue, duration)),
      isValidTimeFormat(startInputValue) && isValidTimeFormat(endInputValue)
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [startInputValue, endInputValue]);

  const seekToTarget = () => {
    if (!isAllValidTimeFormat) return;
    updateStartTime(convertTimeToSeconds(startInputValue, duration));
    updateEndTime(convertTimeToSeconds(endInputValue, duration));
  };

  return (
    <div className="relative">
      <DropdownMenu
        open={open}
        onOpenChange={(open) => {
          if (open) {
            showTempTime();
          } else {
            closeTempTime();
          }
          setOpen(open);
        }}
      >
        <DropdownMenuTrigger>
          <button className="block text-left text-footnote text-status-info underline group">
            <span className="group-hover:text-primary-900 group-hover:underline">
              Add Time
            </span>
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          side="top"
          arrowPadding={2}
          className="overflow-visible"
        >
          <div className="relative px-4 py-2 bg-white flex flex-row gap-2.5 items-center">
            <Input
              value={startInputValue}
              size="s"
              placeholder="mm:ss"
              className="w-16 text-center"
              maxLength={5}
              onChange={(v) => setStartInputValue(v as string)}
            />
            {` -`}
            <Input
              value={endInputValue}
              size="s"
              placeholder="mm:ss"
              className="w-16 text-center"
              maxLength={5}
              onChange={(v) => setEndInputValue(v as string)}
            />
            <Button
              bodyClassName="min-w-[50px]"
              size="s"
              onClick={() => seekToTarget()}
              disabled={!isAllValidTimeFormat}
            >
              <span className="whitespace-nowrap">
                {isAllValidTimeFormat
                  ? label?.['go'] || 'Save'
                  : label?.['invalidTime'] || 'Invalid Time'}
              </span>
            </Button>
            <DropdownArrow
              width={12}
              height={10}
              className="fill-white"
            />
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default TimeDurationSetter;
