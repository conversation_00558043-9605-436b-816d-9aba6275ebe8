'use client';

import Icon from '../../_ui/Icon';
import { useState } from 'react';

export type TTipProp = {
    id: number;
    title: string;
    content: string;
};

const DUMMY_TIPS: TTipProp[] = new Array(4).fill(null).map((_, i) => ({
    id: i,
    title: `Use tips ${i}`,
    content:
        `${i} Lorem ipsum dolor sit amet consectetur. Quisque imperdiet dui nunc tortor pellentesque turpis. Volutpat ullamcorper egestas nunc arcuiaculis a egestas. Egestas mauris dui augue nam. Sit.`,
}));


type TTipsSliderProps = {
    tips?: TTipProp[];
};

const TipsSlider = ({ tips = DUMMY_TIPS }: TTipsSliderProps) => {
    const [slideIndex, setSlideIndex] = useState<number>(1);

    const nextSlide = () => {
        if (slideIndex !== tips.length) {
            setSlideIndex(slideIndex + 1);
        } else if (slideIndex === tips.length) {
            setSlideIndex(1);
        }
    };

    const prevSlide = () => {
        if (slideIndex !== 1) {
            setSlideIndex(slideIndex - 1);
        } else if (slideIndex === 1) {
            setSlideIndex(tips.length);
        }
    };

    return (
        <div className="absolute p-4 w-[88%] md:w-1/2 max-w-[814px] h-[170px] md:h-[160px] flex flex-col items-center bottom-3 md:bottom-6 gap-4 origin-center mx-auto left-0 right-0 text-remark md:text-body">
            <div className="overflow-hidden w-full h-full flex flex-col items-center">
                <div className="relative flex flex-row gap-4 w-full touch-pan-x">
                    {tips.map((tip: TTipProp, i: number) => (
                        <div
                            key={tip.id}
                            className={`absolute flex-none flex-col gap-2 w-full ${slideIndex === tip.id ? 'opacity-100' : 'opacity-0'
                                }  `}
                        >
                            <div>{tip.title}</div>
                            <p>{tip.content}</p>
                        </div>
                    ))}
                    <div className="flex flex-row gap-4 absolute top-0 right-0">
                        <button onClick={prevSlide} className="">
                            <Icon name="arrow-left" className="hover:fill-tertiary-500" />
                        </button>
                        <button onClick={nextSlide}>
                            <Icon name="arrow-right" className="hover:fill-tertiary-500" />
                        </button>
                    </div>
                </div>
            </div>
            <div className="flex flex-row gap-2">
                {Array.from({ length: tips.length }).map((item: any, i: number) => (
                    <button
                        onClick={() => setSlideIndex(i + 1)}
                        key={i + 1}
                        className={`w-2 h-2 rounded-full ${slideIndex === i + 1 ? 'bg-black' : 'bg-white'
                            } `}
                    />
                ))}
            </div>
        </div>
    );
};

export default TipsSlider;
