import React, {useEffect, useMemo, useState} from 'react';
import Checkbox from '@cdss-modules/design-system/components/_ui/Checkbox';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import {
  Button,
  useRole,
} from '@cdss-modules/design-system';
import { cn } from '@cdss-modules/design-system/lib/utils';
import {TWrapUpCodeQueueData, TWrapUpFormState, TWrapUpOption} from "@cdss-modules/design-system/@types/Interaction";







interface WrapUpProps {
  onRevert: () => void;
  onSubmit: () => void;
  formState: TWrapUpFormState;
  onFormStateChange: (newState: TWrapUpFormState) => void;
  wrapUpData: TWrapUpCodeQueueData[];//传入来
  loading: boolean;
  error: string | null;
  className?: string;
  buttonClassName?: string;
  isSubmit?: boolean;
  isSubmitting?: boolean;
}




const WrapUp: React.FC<WrapUpProps> = ({
  onRevert,
  onSubmit,
  formState,
  onFormStateChange,
  wrapUpData,
  loading,
  error,
  className="",
  buttonClassName="",
  isSubmit,
  isSubmitting,
}) => {
  const [hoverItemId, setHoverItemId] = useState<string | null>(null);

  const updateFormState = (updates: Partial<TWrapUpFormState>) => {
    const newState = { ...formState, ...updates };
    onFormStateChange(newState);
  };



  const findItemById = (
    items: TWrapUpOption[],
    targetId?: string
  ): TWrapUpOption | undefined => {
    if (!targetId) return undefined;
    for (const item of items) {
      if (item.id === targetId) return item;
      if (item.items) {
        const found = findItemById(item.items, targetId);
        if (found) return found;
      }
    }
    return undefined;
  };

  const handleItemSelect = (
    itemId: string,
    item: TWrapUpOption,
    shouldHandleChildren = false
  ) => {
    const newSelected = new Set(formState.selectedItems);
    const shouldSelect = !formState.selectedItems.includes(itemId);

    if (shouldSelect) {
      newSelected.add(itemId);
    } else {
      newSelected.delete(itemId);
    }

    const handleChildren = (items?: TWrapUpOption[]) => {
      if (!items || !shouldHandleChildren) return;
      items.forEach((child) => {
        if (shouldSelect) {
          newSelected.add(child.id);
        } else {
          newSelected.delete(child.id);
        }
        handleChildren(child.items);
      });
    };

    const handleParent = (parentId?: string) => {
      if (!parentId) return;
      const parentItem = findItemById(wrapUpData[0]?.items || [], parentId);
      if (!parentItem) return;

      const allSiblings =
        findItemById(wrapUpData[0]?.items || [], parentItem.parentId)?.items ||
        [];
      const allSiblingsSelected = allSiblings.every(
        (sibling) =>
          shouldSelect || (sibling.id !== itemId && newSelected.has(sibling.id))
      );

      if (allSiblingsSelected) {
        newSelected.add(parentItem.id);
      } else {
        newSelected.delete(parentItem.id);
      }

      handleParent(parentItem.parentId);
    };

    if (shouldHandleChildren) {
      handleChildren(item.items);
    }
    handleParent(item.parentId);

    updateFormState({ selectedItems: Array.from(newSelected) });
  };

  // 检查是否有选中的 CODE 类型项目
  const hasSelectedCodeItem = useMemo(() => {
    const findCodeItem = (items: TWrapUpOption[]): boolean => {
      for (const item of items) {
        // 如果当前项是 CODE 类型且被选中，返回 true
        if (item.type === 'CODE' && formState.selectedItems.includes(item.id)) {
          return true;
        }
        // 如果有子项，递归检查子项
        if (item.items && findCodeItem(item.items)) {
          return true;
        }
      }
      return false;
    };

    return findCodeItem(wrapUpData[0]?.items || []);
  }, [formState.selectedItems, wrapUpData]);



  const handleExpandToggle = (itemId: string) => {
    const newExpandedItems = formState.expandedItems.includes(itemId)
      ? formState.expandedItems.filter((id) => id !== itemId)
      : [...formState.expandedItems, itemId];
    updateFormState({ expandedItems: newExpandedItems });
  };

  const handleItemRemarkChange = (itemId: string, remark: string,item: TWrapUpOption) => {
    const newRemarks = [...formState.itemRemarks];
    const existingIndex = newRemarks.findIndex((r) => r.itemId === itemId);

    if (existingIndex !== -1) {
      newRemarks[existingIndex] = { ...newRemarks[existingIndex], remark, wrapUpCode:item.code,wrapUpName:item.name};
    } else {
      newRemarks.push({ itemId, remark ,wrapUpCode:item.code,wrapUpName:item.name});
    }

    updateFormState({ itemRemarks: newRemarks });
  };

  const handleReset = () => {
    const initialState: TWrapUpFormState = {
      selectedItems: [],
      notes: '',
      expandedItems: [],
      itemRemarks: [],
      activeRemarkItems: [],
    };
    onFormStateChange(initialState);
  };

  const handleRemarkIconClick = (e: React.MouseEvent, itemId: string) => {
    e.stopPropagation();
    const newActiveRemarkItems = formState.activeRemarkItems.includes(itemId)
      ? formState.activeRemarkItems.filter((id) => id !== itemId)
      : [...formState.activeRemarkItems, itemId];
    updateFormState({ activeRemarkItems: newActiveRemarkItems });
  };

  const renderRemarkIcon = (itemId: string) => {
    const isActive = formState.activeRemarkItems.includes(itemId);
    return (
      <svg
        width="20"
        height="20"
        viewBox="0 0 10 10"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={cn(
          'ml-2 cursor-pointer transition-colors',
          isActive ? 'hover:opacity-80' : 'hover:opacity-70'
        )}
        onClick={(e) => handleRemarkIconClick(e, itemId)}
      >
        <g clipPath="url(#clip0_1635_157806)">
          <path
            d="M7.88477 2.86621L7.60254 3.14844L7.71484 3.03613C7.80469 2.93555 7.80078 2.78125 7.7041 2.68457L7.88477 2.86621ZM6.35254 1.33105L6.63477 1.61328L6.52246 1.50098C6.42188 1.41113 6.26758 1.41504 6.1709 1.51172L6.35254 1.33105ZM9.37793 9.03906C9.37793 8.81836 9.19922 8.63867 8.97754 8.63867H0.977539C0.756836 8.63867 0.577148 8.81738 0.577148 9.03906C0.577148 9.25977 0.755859 9.43945 0.977539 9.43945H8.97852C9.19922 9.43848 9.37793 9.25977 9.37793 9.03906ZM1.60938 8.01367C1.83008 8.01367 2.00977 7.83496 2.00977 7.61328V5.51465C2.00977 5.29395 1.83105 5.11426 1.60938 5.11426C1.38867 5.11426 1.20898 5.29297 1.20898 5.51465V7.61328C1.20898 7.83496 1.38867 8.01367 1.60938 8.01367Z"
            fill={isActive ? '#FFAC4A' : '#000000'}
          />
          <path
            d="M1.20898 7.61426C1.20898 7.83496 1.3877 8.01465 1.60938 8.01465H3.70215C3.92285 8.01465 4.10254 7.83594 4.10254 7.61426C4.10254 7.39355 3.92383 7.21387 3.70215 7.21387H1.60938C1.38867 7.21387 1.20898 7.39355 1.20898 7.61426Z"
            fill={isActive ? '#FFAC4A' : '#000000'}
          />
          <path
            d="M8.54395 2.39744C8.54297 2.39647 8.54102 2.39549 8.54004 2.39354L6.78809 0.641583C6.52734 0.41502 6.13086 0.424786 5.88281 0.67381C5.88184 0.674786 5.88086 0.676739 5.87891 0.677716L1.32227 5.23338C1.16602 5.38963 1.16602 5.64256 1.32227 5.79881C1.47852 5.95506 1.73145 5.95506 1.8877 5.79881L6.35059 1.33592L7.88086 2.86522L3.41895 7.32713C3.2627 7.48338 3.2627 7.73631 3.41895 7.89256C3.5752 8.04881 3.82812 8.04881 3.98438 7.89256L8.58789 3.28904C8.80176 3.0283 8.78711 2.64158 8.54395 2.39744Z"
            fill={isActive ? '#FFAC4A' : '#000000'}
          />
        </g>
        <defs>
          <clipPath id="clip0_1635_157806">
            <rect
              width="10"
              height="10"
              fill="white"
            />
          </clipPath>
        </defs>
      </svg>
    );
  };
// function getParentIDs(tree: TWrapUpOption[], code: string, parentIDs: string[] = []): { p: string[], c?: any } {
//         // 遍历当前节点的子节点
//         for (const node of tree) {
//             // 如果找到了子节点，将其ID添加到parentIDs数组中
//             if (node?.code === code) {
//                 parentIDs.push(node?.id);
//                 // 如果找到了目标节点，则返回已收集的父节点ID数组
//                 return { p: parentIDs, c: node?.id };
//             }
//             // 如果当前节点有子节点，递归查找
//             if (node?.items) {
//                 // 将当前节点的ID添加到parentIDs数组中
//                 parentIDs.push(node?.id);
//                 // 在子节点中递归查找
//                 const foundInChildren: any = getParentIDs(node.items, code, parentIDs);
//                 if (foundInChildren) {
//                     return foundInChildren;
//                 } else {
//                     // 如果在子节点中未找到，移除当前节点的ID
//                     parentIDs.pop();
//                 }
//             }
//         }
//         // 如果遍历完整棵树未找到，返回undefined
//         return { p: parentIDs };
//     }
//
//     function removeDuplicates(array: any[]) {
//         const map = new Map();
//         const result = [];
//
//         for (const item of array) {
//             if (!map.has(item)) {
//                 map.set(item, true);
//                 result.push(item);
//             }
//         }
//
//         return result;
//     }
//     useEffect(() => {
//         formState.itemRemarks?.forEach((item, i) => {
//             const r = getParentIDs(wrapUpData[0]?.items, item?.wrapUpCode, [])
//             formState.itemRemarks[i].itemId = r?.c
//             formState.expandedItems = removeDuplicates([...r?.p, ...formState.expandedItems])
//             formState.selectedItems = removeDuplicates([...r.p, ...formState.selectedItems])
//             formState.activeRemarkItems = removeDuplicates([...r.p, ...formState.activeRemarkItems])
//         })
//         updateFormState(formState)
//     }, [])

  const hasSelectedLeafItem = useMemo(() => {
    const findLeafItem = (items: TWrapUpOption[]): boolean => {
      for (const item of items) {
        // 如果当前项是叶子节点(没有子项)且被选中，返回 true
        if ((!item.items || item.items.length === 0) && formState.selectedItems.includes(item.id)) {
          return true;
        }
        // 如果有子项，递归检查子项
        if (item.items && findLeafItem(item.items)) {
          return true;
        }
      }
      return false;
    };

    return findLeafItem(wrapUpData[0]?.items || []);
  }, [formState.selectedItems, wrapUpData]);

  const renderTreeItem = (item: TWrapUpOption, level = 0) => {
    const hasChildren = item.items && item.items.length > 0;
    const isExpanded = formState.expandedItems.includes(item.id);
    const isSelected = formState.selectedItems.includes(item.id);
    // const showRemarkIcon = item.type === 'CODE';
    // const isRemarkActive = formState.activeRemarkItems.includes(item.id);
    // const itemRemark = formState.itemRemarks.find((r) => r.itemId === item.id);

    const showRemarkIcon = !hasChildren;
    const isRemarkActive = formState.activeRemarkItems.includes(item.id);
    const itemRemark = formState.itemRemarks.find((r) => r.itemId === item.id);

    return (
      <div
        key={item.id}
        className="w-full mb-2"
      >
        <div
          className={cn(
            'flex items-center px-2 py-1 rounded cursor-pointer',
            'hover:bg-primary-100 group',
            isSelected && 'bg-primary-300',
            item.type === 'CATEGORY' && 'bg-[#FFF9E6]'
          )}
          style={{ paddingLeft: `${level * 24}px` }}
          onMouseEnter={() => setHoverItemId(item.id)}
          onMouseLeave={() => setHoverItemId(null)}
          onClick={() => {
            if (hasChildren) {
              handleExpandToggle(item.id);
            }
          }}
        >
          <div className="flex items-center">
            <div className="flex items-center w-4">
              {hasChildren && (
                <Icon
                  name="dropdown-arrow"
                  size={6}
                  className={cn(
                    'transform transition-transform',
                    !isExpanded && '-rotate-90'
                  )}
                />
              )}
            </div>
          </div>

          <div className="flex-1 flex items-center">
            <Checkbox
              id={item.id.toString()}
              label={item.name}
              value={item.id}
              className={cn(isSelected && 'font-bold')}
              checked={isSelected}
              onChange={(e) => {
                e.stopPropagation();
                handleItemSelect(item.id, item, true);
              }}
            />
            {showRemarkIcon && isSelected && renderRemarkIcon(item.id)}
          </div>
        </div>

        {/* 备注输入框 */}
        {showRemarkIcon && isSelected && isRemarkActive && (
          <div
            className="w-full pl-10"
            style={{ paddingLeft: `${level * 24 + 32}px` }}
          >
            <textarea
              value={itemRemark?.remark || ''}
              onChange={(e) =>
                handleItemRemarkChange(item.id, e.target.value, item)
              }
              onClick={(e) => e.stopPropagation()}
              placeholder="Add remark..."
              className="w-full p-2 mt-1 mb-2 text-sm border rounded resize-none"
              rows={2}
            />
          </div>
        )}

        {/* 子项目渲染 */}
        {hasChildren && isExpanded && (
          <div className="w-full">
            {item.items?.map((childItem) =>
              renderTreeItem(childItem, level + 1)
            )}
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return <div className="p-4">Loading wrap-up options...</div>;
  }

  if (error) {
    return <div className="p-4 text-red-500">{error}</div>;
  }
  return (
    <div className={cn("flex flex-col p-4",className)}>
      {/* 头部 */}
      <div className="flex items-center gap-2 mb-4">
        <button
          onClick={onRevert}
          className="hover:opacity-80"
        >
          <svg
            width="6"
            height="9"
            viewBox="0 0 6 9"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M0.187289 4.04613C-0.0624296 4.29717 -0.0624296 4.70484 0.187289 4.95587L4.02297 8.81173C4.27268 9.06276 4.67823 9.06276 4.92795 8.81173C5.17766 8.56069 5.17766 8.15302 4.92795 7.90199L1.54376 4.5L4.92595 1.09801C5.17567 0.846982 5.17567 0.439306 4.92595 0.188274C4.67623 -0.062758 4.27069 -0.062758 4.02097 0.188274L0.185291 4.04413L0.187289 4.04613Z"
              fill="black"
            />
          </svg>
        </button>
        <h2 className="text-lg font-medium">Wrap up</h2>
      </div>

      {/* 树形列表 */}
      <div className="mb-6">
        {wrapUpData[0]?.items?.map((category) => renderTreeItem(category))}
      </div>

      {/* 备注区域 */}
      <div className="mb-4">
        <h3 className="font-medium mb-2">Overall remarks</h3>
        <textarea
          value={formState.notes}
          onChange={(e) => updateFormState({ notes: e.target.value })}
          placeholder="Please enter here..."
          className="w-full p-2 border rounded-lg h-24 resize-none"
        />
      </div>

      {/* 底部按钮 */}
      <div className={cn("flex justify-end gap-2 mt-4",buttonClassName)}>
        <Button
          onClick={onRevert}
          variant="secondary"
          className="w-24"
        >
          Return
        </Button>
        <Button
          onClick={handleReset}
          variant="secondary"
          className="w-24"
        >
          Reset
        </Button>
        <Button onClick={onSubmit} disabled={!hasSelectedLeafItem || !isSubmit || isSubmitting}  className="w-24" >Submit</Button>
      </div>
    </div>
  );
};

export default WrapUp;
