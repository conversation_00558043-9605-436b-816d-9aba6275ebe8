import { MutableRefObject, ReactNode, useContext, useRef, useState } from "react";
import { createRegisteredContext } from "react-singleton-context";
type TBlockingContextType = {
    open: boolean,
    setBlockingOpen: (b: boolean) => void
    isBlocked:MutableRefObject<boolean>
    blockingList:any[]
    setBlockingList: (b:any[])=>void
}
const BlockingContext = createRegisteredContext<TBlockingContextType>('BlockingContext', {
    open: false,
    setBlockingOpen: (v:boolean) => null,
    isBlocked:{ current: false }
    ,blockingList:[],
    setBlockingList: (b:any[])=>null
});
export const OpenBlockingProvider = ({ children }: { children: ReactNode }) => {
    const [open, setBlockingOpen] = useState<boolean>(false);
    const [blockingList, setBlockingList] = useState<any[]>([]);
    const isBlocked= useRef(false);
    return (
        <BlockingContext.Provider
            value={{
                open,
                setBlockingOpen,
                isBlocked,
                blockingList,
                setBlockingList
            }}
        >
            {children}
        </BlockingContext.Provider>
    );
};
export const useOpenBlockingContext = () => useContext(BlockingContext);