import dayjs from 'dayjs';
import { useState, useContext, ReactNode, useMemo } from 'react';
import { createRegisteredContext } from 'react-singleton-context';
import { get2LettersFromName } from '../lib/utils';

export type TEmail = {
  id: string;
  from: string;
  to: string[];
  cc?: string[];
  bcc?: string[];
  subject: string;
  body: string;
  date: string;
};

export const DUMMY_EMAIL_BODY =
  '<div class="email-container"><p>Dear [Company/Organization Name],</p><p>I am writing to express my profound disappointment and frustration with the recent incident involving your [product/service] on [random date] this year. The experience I had was extremely unsatisfactory, and I feel compelled to bring this matter to your attention.</p><p>During my interaction with your [product/service], I encountered significant issues that severely impacted my overall experience. Specifically, [describe the specific problem or incident in detail, providing relevant facts and supporting evidence if available]. Despite reaching out to your customer support team on [date of contact], I have not received a satisfactory response or any indication of a resolution being pursued, which has further aggravated my dissatisfaction.</p><p>As a loyal customer, I believe it is imperative for you to address this matter with utmost urgency and take immediate steps to rectify the situation. I request a thorough investigation into the incident and a detailed explanation of what went wrong. Additionally, I expect a comprehensive plan for resolution, including appropriate compensation or remedies for the inconvenience caused.</p><p>I sincerely hope that you will prioritize this complaint and treat it with the seriousness it deserves. Your prompt attention and satisfactory resolution will not only restore my faith in your organization but also demonstrate your commitment to customer satisfaction.</p><p>I anticipate your swift action and request a response within [reasonable time frame, e.g., 7 business days]. Thank you for your prompt attention to this matter.</p><p>Yours sincerely,</p><p>[Your Name]<br>[Your Contact Information]</p><div class="footer"><p>This is an automatically generated email. Please do not reply to this address.</p></div></div>';

export const DUMMY_EMAIL: TEmail = {
  id: 'dummy-email-1',
  from: '<EMAIL>',
  to: ['<EMAIL>'],
  cc: ['<EMAIL>'],
  subject: 'Hello from Peter',
  date: dayjs().toISOString(),
  body: DUMMY_EMAIL_BODY,
};
const DUMMY_MSG_1 = [
  {
    time: dayjs().add(-12, 'minute').toISOString(),
    speaker: 'Agent',
    text: '你好，今日是 2024年八月十七日，就您打算認購的股票掛鈎產品開始錄音。我是銀行職員。',
  },
  {
    time: dayjs().add(-12, 'minute').toISOString(),
    speaker: 'Agent',
    text: '陳大欣小姐，您的全名是陳大欣。請您提供全名、身份證號碼後面的 4 位數字及以下其中 2 項資料核對身份：出生日期（日子及月份）、住址或公司地址。',
  },
  {
    time: dayjs().add(-11, 'minute').toISOString(),
    speaker: 'Customer',
    text: '我叫 陳大欣，身份證號碼後四位是5678，住址是香港鰂魚涌456號7樓8室。',
  },
  {
    time: dayjs().add(-10, 'minute').toISOString(),
    speaker: 'Agent',
    text: '有關您的弱勢客戶評估，您表示在本行以外有複雜產品及其他產品的投資經驗。加上本行紀錄，您的評估結果為非弱勢客戶。',
  },
  {
    time: dayjs().add(-10, 'minute').toISOString(),
    speaker: 'Agent',
    text: '本行建議，您應由「客戶見證人」陪同協助您明白產品內容及風險，並進行有關投資。客戶見證人 XXX 先生/小姐是您的朋友，並確認是 65 歲以下、教育水平是中學程度或以上，並確認對此產品有認識及有足夠理解能力協助您了解此交易。',
  },
  {
    time: dayjs().add(-9, 'minute').toISOString(),
    speaker: 'Customer',
    text: '我確認這些資訊是正確的。',
  },
  {
    time: dayjs().add(-8, 'minute').toISOString(),
    speaker: 'Agent',
    text: '您向本行作出聲明確認您並非首次購買此產品類別，及已有此類投資產品經驗，是不受「落單冷靜期」安排所覆蓋。請確認。',
  },
  {
    time: dayjs().add(-7, 'minute').toISOString(),
    speaker: 'Customer',
    text: '是的，我確認這些資料。',
  },
  {
    time: dayjs().add(-6, 'minute').toISOString(),
    speaker: 'Agent',
    text: '我們現在進行產品適合性評估。您是否有足夠時間考慮投資適合性，明白及接受此產品之產品的特質、運作模式、相關風險、潛在損失及確定進行交易? 請確認。',
  },
  {
    time: dayjs().add(-5, 'minute').toISOString(),
    speaker: 'Customer',
    text: '是的，我已經考慮充分並接受這些風險。',
  },
  {
    time: dayjs().add(-4, 'minute').toISOString(),
    speaker: 'Agent',
    text: '根據產品適合性評估，您會預留多於 6 個月但少於或等如 12 個月的家庭開支備用現金或高流動性資產。在本行已擁有此類產品的投資經驗。是次投資金額 CCY。股票掛鈎投資戶口。現金結算戶口。當此申請獲接納便不可撤回，如果您接受交易，銀行會即時凍結資金。您是否確認此申請?',
  },
  {
    time: dayjs().add(-3, 'minute').toISOString(),
    speaker: 'Customer',
    text: '我確認所有資訊並願意繼續進行。',
  },
  {
    time: dayjs().add(-1, 'minute').toISOString(),
    speaker: 'Agent',
    text: '非常感謝您的申請，我們會盡快通知您此產品的發行結果。錄音完成，多謝您的申請。',
  },
];
const DUMMY_MSG_2 = [
  {
    time: dayjs().add(-12, 'minute').toISOString(),
    speaker: 'Customer',
    text: '你好',
  },
];
const DUMMY_MSG_3 = [
  {
    time: dayjs().add(-3, 'minute').toISOString(),
    speaker: 'Customer',
    text: '你好',
    attachedImages: [
      'https://ctint-company-website-static.azurewebsites.net/images/Geneva/0.jpg',
    ],
  },
  {
    time: dayjs().add(-2, 'minute').toISOString(),
    speaker: 'Agent',
    text: '你好，我是Agent。',
  },
  {
    time: dayjs().add(-3, 'minute').toISOString(),
    speaker: 'Customer',
    text: '想請問你關於<span class="saa-keyword" data-saa-id="dummy-saa-4">SAA Keyword</span>的事情。',
  },
];

const DUMMY_INTERACTIONS = [
  {
    id: 'dummy-int-1',
    customerName: 'Tigger Wong',
    phone: '+852 6123 4567',
    type: 'msg',
    gender: 'Male',
    preferredTitle: 'Mr.',
    preferredLanguage: 'Cantonese',
    time: dayjs().toISOString(),
    content: DUMMY_MSG_1,
  },
  {
    id: 'dummy-int-4',
    customerName: 'Mobile Number, Hong Kong',
    phone: '+852 5331 3557',
    gender: 'Unknown',
    preferredTitle: 'Unknown',
    preferredLanguage: 'English',
    time: dayjs().add(-0.5, 'minutes').toISOString(),
    type: 'call',
  },
  {
    id: 'dummy-int-2',
    customerName: 'Peter Parker',
    phone: '+852 5123 4567',
    gender: 'Male',
    preferredTitle: 'Mr.',
    preferredLanguage: 'English',
    type: 'msg',
    time: dayjs().add(-3, 'minutes').toISOString(),
    content: DUMMY_MSG_2,
  },
  {
    id: 'dummy-int-3',
    customerName: 'Mary Ng',
    phone: '+852 2532 1234',
    gender: 'Female',
    preferredTitle: 'Ms.',
    preferredLanguage: 'Cantonese',
    type: 'msg',
    time: dayjs().add(-4, 'minutes').toISOString(),
    content: DUMMY_MSG_3,
  },
];
const DUMMY_INTERACTIONS_HISTORY = [
  {
    id: 'dummy-int-history-1',
    customerName: 'John Wong',
    phone: '+852 6123 4567',
    type: 'msg',
    gender: 'Male',
    preferredTitle: 'Mr.',
    preferredLanguage: 'Cantonese',
    time: dayjs().add(-2.1, 'days').toISOString(),
    content: DUMMY_MSG_1,
  },
  {
    id: 'dummy-int-history-4',
    customerName: 'Mobile Number, Hong Kong',
    phone: '+852 5331 3557',
    gender: 'Male',
    preferredTitle: 'Mr.',
    preferredLanguage: 'Cantonese',
    time: dayjs().add(-3.3, 'days').toISOString(),
    type: 'call',
  },
  {
    id: 'dummy-int-history-2',
    customerName: 'Jay Fung',
    phone: '+852 5123 4567',
    type: 'msg',
    gender: 'Male',
    preferredTitle: 'Mr.',
    preferredLanguage: 'Cantonese',
    time: dayjs().add(-4.2, 'days').toISOString(),
    content: DUMMY_MSG_2,
  },
];
const DUMMY_QUEUES = [
  {
    id: 'dummy-queue-1',
    name: 'Customer Service Queue',
    items: [
      {
        id: 'dummy-int-1',
        customerName: 'Tigger Wong',
        from: '+852 6123 4567',
        type: 'voicemail',
        status: 'WAITING',
        createdAt: dayjs().toISOString(),
        detail: {
          transcript:
            'Hello, I am Mr. Wong. I have a question about my bill. Can you please call me back?',
        },
      },
      {
        id: 'dummy-int-2',
        customerName: 'Peter Parker',
        from: '<EMAIL>',
        type: 'email',
        status: 'WAITING',
        createdAt: dayjs().toISOString(),
        detail: {
          subject: 'Hello from Peter',
          to: ['<EMAIL>', '<EMAIL>'],
          cc: ['<EMAIL>', '<EMAIL>'],
          content: DUMMY_EMAIL_BODY,
        },
      },
      {
        id: 'dummy-int-3',
        customerName: 'Mary Ng',
        from: '<EMAIL>',
        type: 'email',
        status: 'WAITING',
        createdAt: dayjs().toISOString(),
        detail: {
          subject: 'Hello from Mary Ng',
          to: ['<EMAIL>', '<EMAIL>'],
          cc: ['<EMAIL>', '<EMAIL>'],
          content: DUMMY_EMAIL_BODY,
        },
      },
    ],
  },
  {
    id: 'dummy-queue-2',
    name: 'Technical Support Queue',
    items: [
      {
        id: 'dummy-int-4',
        customerName: 'Mobile Number, Hong Kong',
        from: '+852 5331 3557',
        type: 'voicemail',
        status: 'WAITING',
        createdAt: dayjs().toISOString(),
        detail: {
          transcript:
            '你好，我姓黃，我有個問題想問。係關於我電話 53313557 既賬單問題，方便既話可唔可以打番比我。',
        },
      },
    ],
  },
  {
    id: 'dummy-queue-3',
    name: 'Sales Queue',
    items: [
      {
        id: 'dummy-int-5',
        customerName: 'Paul Chan',
        from: '+852 3333 3333',
        type: 'voicemail',
        status: 'ASSIGNED',
        createdAt: dayjs().toISOString(),
        detail: {
          transcript: '你好，我想了解你們的產品。請回電給我。',
        },
      },
      {
        id: 'dummy-int-5',
        customerName: 'Paul Chan',
        from: '<EMAIL>',
        type: 'email',
        status: 'CLOSED',
        createdAt: dayjs().toISOString(),
        detail: {
          subject: 'Hello from Mary Ng',
          to: ['<EMAIL>', '<EMAIL>'],
          cc: ['<EMAIL>', '<EMAIL>'],
          content: DUMMY_EMAIL_BODY,
        },
      },
    ],
  },
];

type TCDSSContextType = {
  interactions: any[];
  interactionHistory: any[];
  queueData: any[];
  openQueueData: any;
  openQueueItem: any;
  activeSAA: string;
  activeInteraction: any;

  sideBarExpanded: boolean;
  queuePanelExpanded: any;
  toggleSideBar: (expanded: boolean) => void;
  toggleQueuePanel: (expanded: any) => void;
  updateActiveInteraction: (id: string) => void;
  updateActiveSAA: (id: string) => void;
  updateOpenQueueData: (data: any) => void;
  updateOpenQueueItemForDetail: (data: any) => void;
  addInteractionFromQueue: (queueId: any) => void;
};

const CDSSContext = createRegisteredContext<TCDSSContextType>('CDSSContext', {
  interactions: [],
  interactionHistory: [],
  openQueueData: null,
  openQueueItem: null,
  queueData: [],
  activeSAA: '',
  activeInteraction: null,
  sideBarExpanded: false,
  queuePanelExpanded: false,
  toggleQueuePanel: () => null,
  toggleSideBar: () => null,
  updateActiveInteraction: () => null,
  updateActiveSAA: () => null,
  updateOpenQueueData: () => null,
  addInteractionFromQueue: () => null,
  updateOpenQueueItemForDetail: () => null,
});

export const CDSSProvider = ({ children }: { children: ReactNode }) => {
  const [sideBarExpanded, setSideBarExpanded] = useState(false);
  const [queuePanelExpanded, setQueuePanelExpanded] = useState<any>(null);
  const [activeInteractionId, setActiveInteractionId] = useState('dummy-int-1');
  const [queueData, setQueueData] = useState(DUMMY_QUEUES);
  const [addedInteractions, setAddedInteractions] = useState<any[]>([]);
  const [openQueueData, setOpenQueueData] = useState<any>();
  const [openQueueItem, setOpenQueueItem] = useState<any>();
  const interactions = [...DUMMY_INTERACTIONS, ...addedInteractions]?.map(
    (int, i) => {
      const customerName = int?.customerName;
      const shortName =
        customerName?.indexOf('+') > -1 ||
        customerName?.indexOf(',') > -1 ||
        !customerName
          ? `C${i + 1}`
          : get2LettersFromName(customerName);
      return {
        ...int,
        customerShortName: shortName,
      };
    }
  );
  const interactionHistory = DUMMY_INTERACTIONS_HISTORY?.map((int, i) => {
    const customerName = int?.customerName;
    const shortName =
      customerName?.indexOf('+') > -1 ||
      customerName?.indexOf(',') > -1 ||
      !customerName
        ? `H${i + 1}`
        : get2LettersFromName(customerName);
    return {
      ...int,
      isHistory: true,
      customerShortName: shortName,
    };
  });
  const activeInteraction = useMemo(
    () => interactions.find((int) => int.id === activeInteractionId),
    [activeInteractionId, interactions]
  );

  //SAA
  const [activeSAAId, setActiveSAAId] = useState('');
  const updateActiveSAA = (id: string) => {
    setActiveSAAId(id);
  };

  const updateActiveInteraction = (id: string) => {
    setActiveInteractionId(id);
  };

  const toggleSideBar = (expanded: boolean) => {
    setSideBarExpanded(expanded);
  };

  const toggleQueuePanel = (queue?: any | false) => {
    setQueuePanelExpanded(queue);
  };
  const addInteractionFromQueue = (queueId: any) => {
    const flatQueueData = queueData.reduce((acc: any, q: any) => {
      return [...acc, ...q.items];
    }, []);
    const targetQueue = flatQueueData.find((q) => q.id === queueId);
    const interactionObject =
      targetQueue?.type === 'voicemail'
        ? {
            id: 'dummy-int-' + targetQueue?.id,
            customerName: targetQueue?.customerName,
            phone: targetQueue?.from,
            type: 'call',
          }
        : {
            id: 'dummy-int-' + targetQueue?.id,
            customerName: targetQueue?.customerName,
            email: targetQueue?.from,
            type: 'email',
            gender: 'Male',
            preferredTitle: 'Mr.',
            preferredLanguage: 'Cantonese',
            time: dayjs().toISOString(),
            emailDetail: {
              subject: 'Hello from Peter',
              from: targetQueue?.from,
              to: DUMMY_EMAIL.to,
              content: DUMMY_EMAIL_BODY,
              date: dayjs().toISOString(),
            },
          };
    setAddedInteractions([...addedInteractions, interactionObject]);
    updateActiveInteraction('dummy-int-' + targetQueue?.id);
    setQueueData((prev) => {
      return prev.map((q) => {
        const hasItem = q.items.find((item) => item.id === targetQueue?.id);
        if (hasItem) {
          return {
            ...q,
            items: q.items.map((item) => {
              if (item.id === targetQueue?.id) {
                return {
                  ...item,
                  status: 'ASSIGNED',
                };
              }
              return item;
            }),
          };
        }
        return q;
      });
    });
    toggleSideBar(true);
  };

  return (
    <CDSSContext.Provider
      value={{
        interactions,
        interactionHistory,
        activeInteraction,
        activeSAA: activeSAAId,
        sideBarExpanded,
        queuePanelExpanded,
        queueData,
        openQueueData,
        openQueueItem,
        toggleSideBar,
        toggleQueuePanel,
        updateActiveInteraction,
        updateActiveSAA,
        updateOpenQueueData: (data) => setOpenQueueData(data),
        updateOpenQueueItemForDetail: (data) => setOpenQueueItem(data),
        addInteractionFromQueue,
      }}
    >
      {children}
    </CDSSContext.Provider>
  );
};

export const useCDSS = () => useContext(CDSSContext);
