import { ReactNode, useContext, useEffect, useRef, useState } from 'react';
import { createRegisteredContext } from 'react-singleton-context';
import { useInteractionContext } from '@cdss-modules/design-system/context/InteractionContext';
import {
  extractIdandStatus,
  extractErrorMessage,
} from '@cdss-modules/design-system/lib/utils';
import { toast, useRole } from '@cdss-modules/design-system';
import { useRouteHandler } from '@cdss-modules/design-system/context/RouteContext';
import {
  fireGetAllActiveConverstaions,
  fireGetAllConversations,
} from '@cdss/lib/api';

import { useSearchParams, useLocation } from 'react-router-dom';
import { useInfiniteQuery } from '@tanstack/react-query';
import { useStation } from '../lib/hooks/useStation';
import { useMiniWallBoard } from '../lib/hooks/useMiniWallBoard';
import { useStatus } from '../lib/hooks/useStatus';
import { useWrapUp } from '../lib/hooks/useWrapUp';
import {
  IInteractionItemResp,
  TInteractionFilter,
  TTbarContextType,
} from '../@types/Interaction';
import { useGetCallDetail } from '../lib/hooks/useGetCallDetail';
const stationContext = {
  station: undefined,
  stationSearchKeyword: '',
  setStationSearchKeyword: () => null,
  getCurrentStation: () => null,
  stationSearchHandler: undefined,
  stationHandler: undefined,
  updateCurrentStation: () => null,
  deleteCurrentStation: () => null,
  handleEvenStation: () => null,
};

const statusContext = {
  onChangeAgentStatus: () => null,
  onChangeIsOnQueue: () => null,
  onChangeAgentColor: () => null,
  agentStatusList: [],
  isOnQueue: false,
  agentStatus: '',
  agentColor: '',
  currentStateId: '',
};

const wrapupContext = {
  selectLastActiveAgentId: '',
  selectLastActiveConvId: '',
  showWrapup: false,
  setShowWrapup: (v: boolean) => null,
  selectedWrapupList: [],
  updateSelectedWrapupList: () => null,
  selectedWrapupId: null,
  updateSelectedWrapupId: (id: string | null) => null,
  wrapupCategoryListHandle: undefined,
  wrapupCategoryFullListHandle: undefined,
};
const TbarContext = createRegisteredContext<TTbarContextType>('TbarContext', {
  eventData: null,
  stationContext,
  statusContext,
  wrapupContext,
  conversationHistoryListHandle: null,
  primaryContactInfo: [],
  activeParticipantData: null,
  updateActiveParticipantData: () => null,
  interactions: [],
  selectedInteraction: undefined,
  activeModal: undefined,
  interactionFilter: null,
  openToolbarModal: () => null,
  closeToolbarModal: () => null,
  handleAfterCallFunc: () => null,
  selectInteraction: () => null,
  removeInteraction: () => null,
  refreshing: false,
  refreshInteractions: () => null,
  updateInteractionFilter: () => null,
  userMiniWallboardList: [],
  queueMiniWallboardList: [],
  loading: false,
  getSingleHistoryConversation: (v: string) => {},
  getSingleActiveConversation: (v: string) => {},
  customerInfo: {},
  infoLoading: false,
  interactionsHistory: null,
  historyLoading: false,
  searchConversations: (value: any) => {
    return new Promise(()=>{});
  },
});

export const TbarProvider = ({ children }: { children: ReactNode }) => {
  const [searchParams] = useSearchParams();
  const location = useLocation();
  const [convId, setConversationId] = useState<string | null>('');
  const { userConfig,globalConfig } = useRole();
  const { basePath } = useRouteHandler();

  const microfrontendsConfig = globalConfig?.microfrontends
//   const outboundCall = microfrontendsConfig?.["ctint-mf-cdss"]?.["functionConfig"]?.["enableOutboundCall"];
  const selectStation = microfrontendsConfig?.["ctint-mf-cdss"]?.["functionConfig"]?.["enableSelectStation"];
  const selectStatus = microfrontendsConfig?.["ctint-mf-cdss"]?.["functionConfig"]?.["enableSelectStatus"];
  const conversationHistoryList = microfrontendsConfig?.["ctint-mf-cdss"]?.["functionConfig"]?.["conversationHistoryList"];
  const conversationActiveList = microfrontendsConfig?.["ctint-mf-cdss"]?.["functionConfig"]?.["conversationActiveList"];
//   const onQueueSwitch = microfrontendsConfig?.["ctint-mf-cdss"]?.["functionConfig"]?.["enableOnQueueSwitch"];

  const {
    station,
    stationSearchKeyword,
    setStationSearchKeyword,
    getCurrentStation,
    stationSearchHandler,
    stationHandler,
    updateCurrentStation,
    deleteCurrentStation,
    handleEvenStation,
  } = useStation();
  const {
    queueMiniWallboardList,
    userMiniWallboardList,
    handleEventSetQueueMiniwallBoard,
    handleEventSetUserMiniwallBoard,
  } = useMiniWallBoard();

  const {
    agentColor,
    getCurrentStatus,
    isOnQueue,
    agentStatus,
    agentStatusList,
    onChangeIsOnQueue,
    onChangeAgentColor,
    onChangeAgentStatus,
    handleEvenStatus,
    primaryContactInfo,
    getAllAgentStatus,
    currentStateId,
  } = useStatus();

  const {
    showWrapup,
    setShowWrapup,
    wrapupCategoryListHandle,
    wrapupCategoryFullListHandle,
    selectLastActiveAgentId,
    setSelectLastActiveAgentId,
    selectLastActiveConvId,
    setSelectLastActiveConvId,
    selectedWrapupList,
    setSelectedWrapupList,
    selectedWrapupId,
    setSelectedWrapupId,
  } = useWrapUp();

  const { eventData } = useInteractionContext();
  useEffect(() => {
    if (!eventData) {
      return;
    }
    const { eventType, status, eventSubType, monitor } =
      extractIdandStatus(eventData?.event) || {};
    if (eventType === 'conversation') {
      handleConversationEvent(eventData);
    }
    if (eventType === 'agent') {
      if (eventSubType === 'state' && status == 'changed') {
        handleEvenStatus(eventData?.eventData?.data);
      }
      if (eventSubType === 'station' && status === 'changed') {
        handleEvenStation(eventData?.eventData?.data);
      }
    }
    if (
      eventType === 'queues' &&
      monitor === 'aggregate' &&
      status === 'changed'
    ) {
      handleEventSetQueueMiniwallBoard(eventData?.eventData?.data);
    }
    if (
      eventType === 'user' &&
      monitor === 'aggregate' &&
      status === 'changed'
    ) {
      handleEventSetUserMiniwallBoard(eventData?.eventData?.data);
    }
  }, [eventData]);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(false);
  const refreshInteractions = async () => {
    setRefreshing(true);
    conversationHistoryListHandle.refetch();
    getAllActiveConverstaions();
    selectStatus&&getCurrentStatus();
    setRefreshing(false);
  };
  const [interactionFilter, setInteractionFilter] =
    useState<TInteractionFilter>('current');
  const [activeModal, setActiveModal] = useState<string | undefined>(undefined);
  // active interaction list
  const [interactions, setInteractions] = useState<IInteractionItemResp[]>([]);
  const [selectedInteraction, updateSelectedInteraction] =
    useState<IInteractionItemResp | null>(null);

  const [activeParticipantData, setActiveParticipantData] = useState<
    null | any
  >(null);

  const openToolbarModal = (modal: string) => {
    setActiveModal(modal);
  };
  const closeToolbarModal = () => {
    setActiveModal(undefined);
  };
  const queryParams=useRef(undefined)
  async function fetchGetAllConversations({ pageParam = '' }) {    
    try {
      const res = await fireGetAllConversations(
        basePath,
        userConfig?.id,
        pageParam,
        "voice",
        queryParams.current
      );
      const historyConversations = res?.data?.conversations || [];
      const newHistoryConversations = historyConversations.filter((v: any) => {
        let isActive = false;
        for (const vs of interactions) {
          const { conversationId,status } = extractIdandStatus(vs?.event || '') || {};
          if (v?.conversationId === conversationId&&status!=="disconnected") {
            v.isHistory = false;
            isActive = true;
            break;
          }
        }
        v.isHistory = true;
        return !isActive;
      });
      return {
        data: newHistoryConversations,
        nextLink: res?.data?.nextLink,
      };
    } catch (error) {
      toast({
        variant: 'error',
        title: 'Error getting conversation history',
        description: `These is an error: ${extractErrorMessage(error)}. If problem persists, please re-login.`,
      });
      return {
        data: [],
        nextLink: undefined,
      };
    }
  }

  const conversationHistoryListHandle = useInfiniteQuery({
    queryKey: ['get-conversation-history'],
    initialPageParam: '',
    queryFn: fetchGetAllConversations,
    getNextPageParam: (lastPage) => {
      return lastPage?.nextLink || undefined;
    },
    staleTime: 1000 * 60 * 60,
    enabled: conversationHistoryList,
  });
  const searchConversations = (value:any) => {
    return new Promise((resolve) => {        
        queryParams.current=value
        conversationHistoryListHandle.refetch();
        resolve(value);
    });
  };

  function removeInteraction(conversationId: string) {
    setInteractions(
      (prev: any) =>
        prev?.filter(
          (interaction: any) =>
            extractIdandStatus(interaction?.event || '')?.conversationId !==
            conversationId
        ) || []
    );
    selectInteraction(null);
  }
  const updateInteraction = (interaction: IInteractionItemResp) => {
    const selectedConversationId = extractIdandStatus(
      selectedInteraction?.event || ''
    )?.conversationId;
    const conversationIdOfInteraction = extractIdandStatus(
      interaction?.event || ''
    )?.conversationId;
    if (
      selectedConversationId == conversationIdOfInteraction ||
      interactions.length == 0
    ) {
      selectInteraction(interaction);
    }
    setInteractions((prevInteractions: IInteractionItemResp[]) => {
      const index = prevInteractions.findIndex(
        (i) =>
          extractIdandStatus(i?.event || '')?.conversationId ===
          conversationIdOfInteraction
      );
      if (index !== -1) {
        const updatedInteractions = [...prevInteractions];
        updatedInteractions[index] = interaction;
        return updatedInteractions;
      } else {
        return [...prevInteractions, interaction];
      }
    });
  };
  const selectInteraction = (
    IInteractionItemResp: IInteractionItemResp | null
  ) => {
    updateSelectedInteraction(IInteractionItemResp);
  };
  const handleAfterCallFunc = (conversationId: string) => {
    interactions.forEach((v) => {
      const { conversationId: extraConvId } =
        extractIdandStatus(v?.event || '') || {};
      if (conversationId == extraConvId) {
        selectInteraction(v);
      }
    });
  };
  const { getSingleActiveConversation, getSingleHistoryConversation } =
    useGetCallDetail({
      setShowWrapup: (v: any) => setShowWrapup(v),
      selectInteraction,
    });
  const getAllActiveConverstaions = async () => {
    try {
      const res = await fireGetAllActiveConverstaions(
        basePath,
        userConfig?.id,
        localStorage.getItem('deviceId') || ''
      );
      conversationHistoryListHandle.refetch();
      const newList = res?.data?.data?.filter((v: any) => {
        return v.type === 'call';
      });
      setInteractions(newList);
      selectInteraction(newList?.[0]);
    } catch (error) {
      toast({
        variant: 'error',
        title: 'Error refreshing the toolbar',
        description: `These is an error: ${extractErrorMessage(error)}. If problem persists, please re-login.`,
      });
    }
  };
  useEffect(() => {
    if (agentStatusList.length > 0) {
      selectStatus&&getCurrentStatus();
    }
  }, [agentStatusList]);
  useEffect(() => {
    selectStatus&&getAllAgentStatus();
    selectStation&&getCurrentStation();
    if (location?.pathname?.includes('toolbar')) {
      conversationActiveList&&getAllActiveConverstaions();
    }
    if (showWrapup) {
      setInteractionFilter('wrapup');
    } else {
      setInteractionFilter('current');
    }
  }, []);
  useEffect(() => {
    setConversationId(searchParams?.get('conversationId'));
  }, [searchParams]);
  //   useEffect(() => {
  //     if (convId && convId !== '' && !loading) {
  //       getSingleActiveConversation(convId);
  //     }
  //   }, [convId]);
  function handleConversationEvent(formattedEvent: any) {
    const { conversationId, status } =
      extractIdandStatus(formattedEvent.event) || {};
    switch (status) {
      case 'alerting':
      case 'connecting':
      case 'connected': {
        updateInteraction(formattedEvent);
        break;
      }
      case 'disconnected': {
        const agents = formattedEvent?.eventData?.data?.agent;
        const agent = agents?.[agents?.length - 1];
        if (
          (!convId || convId == '') &&
          location?.pathname?.includes('toolbar')
        ) {
          removeInteraction(conversationId || '');
          conversationHistoryListHandle.refetch();
        } else {
            updateInteraction(formattedEvent);
        }
        setShowWrapup(agent?.wrapupRequired);
        if (agent?.wrapupRequired) {
          setInteractionFilter('wrapup');
        }
        setSelectLastActiveAgentId(agent?.id);
        setSelectLastActiveConvId(conversationId || '');
        break;
      }
    }
  }
  return (
    <TbarContext.Provider
      value={{
        stationContext: {
          station,
          stationSearchKeyword,
          setStationSearchKeyword,
          getCurrentStation,
          stationSearchHandler,
          stationHandler,
          updateCurrentStation,
          deleteCurrentStation,
          handleEvenStation,
        },
        statusContext: {
          agentStatusList,
          isOnQueue,
          agentStatus,
          agentColor,
          onChangeAgentStatus,
          onChangeIsOnQueue,
          onChangeAgentColor,
          currentStateId,
        },
        wrapupContext: {
          selectLastActiveAgentId,
          selectLastActiveConvId,
          showWrapup,
          setShowWrapup,
          selectedWrapupList,
          updateSelectedWrapupList: (data: any) => {
            setSelectedWrapupList(data);
          },
          selectedWrapupId,
          updateSelectedWrapupId: (id: string | null) => {
            setSelectedWrapupId(id);
          },
          wrapupCategoryListHandle,
          wrapupCategoryFullListHandle,
        },
        eventData,
        conversationHistoryListHandle,
        handleAfterCallFunc,
        primaryContactInfo,
        activeParticipantData,
        updateActiveParticipantData: (data: any) => {
          setActiveParticipantData(data);
        },
        userMiniWallboardList,
        queueMiniWallboardList,
        interactions,
        selectedInteraction,
        activeModal,
        interactionFilter,
        openToolbarModal,
        closeToolbarModal,
        selectInteraction,
        refreshing,
        refreshInteractions,
        updateInteractionFilter: (filter: TInteractionFilter) => {
          setInteractionFilter(filter);
        },
        removeInteraction,
        getSingleHistoryConversation,
        getSingleActiveConversation,
        loading,
        searchConversations
      }}
    >
      {children}
    </TbarContext.Provider>
  );
};

export const useTbarContext = () => useContext(TbarContext);
