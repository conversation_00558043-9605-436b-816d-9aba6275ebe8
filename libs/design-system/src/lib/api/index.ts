import { apiConfig } from './config';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { addLoggingToAxios } from '../logging';

export type TCallActionProps =
  | 'pickup'
  | 'disconnect'
  | 'mute'
  | 'unmute'
  | 'hold'
  | 'unhold';

const composedUri = () => {
  const port = window.location.port ? `:${window.location.port}` : '';
  const basePath = (window as any)?.GLOBAL_BASE_PATH;
  const redirectUri = window
    ? `${window?.location?.protocol}//${window?.location?.hostname}${port}${basePath}/login`
    : '';
  return redirectUri;
};

export const axiosInstance = axios.create({
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    traceId: uuidv4(),
    tenant: 'ccba',
    sourceId: 'ctint-mf-cdss',
    previousId: 'ctint-bff-cdss',
  },
});
export const axiosDownloadInstance = axios.create({
  timeout: 20000,
  headers: {
    traceId: uuidv4(),
    tenant: 'ctint',
    sourceId: 'ctint-mf-interaction',
    previousId: 'ctint-bff-cdss',
  },
});
axiosDownloadInstance.interceptors.request.use(
  (config) => {
    // TODO: should call the auth api and set the token when program initialize
    if (
      process.env['NODE_ENV'] === 'development' &&
      localStorage.getItem('cdss-auth-token') === null
    ) {
      localStorage.setItem('deviceId', uuidv4());
      localStorage.setItem(
        'gc-access-token',
        'mE75EO3TZDLSZOvB-G28PeszsRavXQeMoFzxUe2w87M8KHsvUGZaLZuGiACChwsrTY5vvDN8eQX47OMajb2TcA'
      );
      localStorage.setItem(
        'cdss-auth-token',
        'EdOImMlG47o8McBVtqY2QKHtIoT7OPznIInDv7cOXG+0DnbPZAcG6Ra+604VzhdT+fX3I2pbhPKWfQSrGpd+hZ5OubOcpskiUuT7pHNE/Ygpgv45MOm'
      );
    }
    const cdssAuthToken = localStorage.getItem('cdss-auth-token') || '';
    const gcAccessToken = localStorage.getItem('gc-access-token') || '';
    const deviceId = localStorage.getItem('deviceId') || '';
    config.headers['tenant'] = localStorage.getItem('tenant') || 'ctint';
    if (cdssAuthToken) {
      config.headers['cdss_authorization'] = 'Bearer ' + cdssAuthToken;
    }
    if (gcAccessToken) {
      config.headers['gc_authorization'] = 'Bearer ' + gcAccessToken;
    }
    if (deviceId) {
      config.headers['deviceId'] = deviceId;
    }
    config.headers['mediaSource'] = 'aws'; // TODO: should get from config
    return config;
  },
  (error) => {
    Promise.reject(error);
  }
);
axiosInstance.interceptors.request.use(
  (config) => {
    const cdssAuthToken = localStorage.getItem('cdss-auth-token') || '';
    const gcAccessToken = localStorage.getItem('gc-access-token') || '';
    const deviceId = localStorage.getItem('deviceId') || '';
    config.headers['tenant'] = localStorage.getItem('tenant') || 'ctint';
    if (cdssAuthToken) {
      config.headers['cdss-authorization'] = 'Bearer ' + cdssAuthToken;
    }
    if (deviceId) {
      config.headers['deviceId'] = deviceId;
    }
    return config;
  },
  (error) => {
    Promise.reject(error);
  }
);

axiosInstance.interceptors.response.use(
  (response) => {
    // Return response as is if the request is successful
    return response;
  },
  (error) => {
    const errorCode = (localStorage.getItem('errorCode') || '').split(',');
    if (errorCode && errorCode.includes(error?.response?.status.toString())) {
      const currUrl = window.location.href;
      if (currUrl?.indexOf('login') === -1) {
        window.location.href = composedUri();
      }
      localStorage.removeItem('cdss-auth-token');
      localStorage.removeItem('gc-access-token');
      localStorage.removeItem('permissions');
    }
    return Promise.reject(error);
  }
);

addLoggingToAxios(axiosInstance);

export const fireSaveSession = (key: string, data: any, basePath = '') => {
  return axiosInstance.post(`${basePath}${apiConfig.paths.session}`, {
    session: key,
    data: {
      userSession: data,
    },
  });
};

export const fireUpdateSession = (key: string, data: any, basePath = '') => {
  return axiosInstance.put(`${basePath}${apiConfig.paths.session}`, {
    session: key,
    data,
  });
};

export const fireGetSession = (key: string, basePath = '') => {
  return axiosInstance.get(`${basePath}${apiConfig.paths.session}?key=${key}`);
};

export const fireDeleteSession = (key: string, basePath = '') => {
  return axiosInstance.delete(`${basePath}${apiConfig.paths.session}`, {
    data: {
      session: key,
    },
  });
};

export const fireLogout = (basePath = '') => {
  return axiosInstance.post(`${basePath}${apiConfig.paths.logout}`);
};

// Greeting Message API
export interface GreetingMessageItem {
  language: 'en' | 'zh-HK';
  message: string;
}

export const fireUpdateGreetingMessage = (
  messages: GreetingMessageItem[],
  basePath = ''
) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.greetingMessage}`,
    messages
  );
};

export const fireCallControlAction = (
  conversationsId: string,
  participantId: string,
  action: TCallActionProps,
  basePath = ''
) => {
  return axiosInstance.patch(
    `${basePath}${apiConfig.paths.callControl.action}/${conversationsId}/participants/${participantId}`,
    {
      state: action,
    }
  );
};

export const fireGetUserRoutingStatus = (userId: string, basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.callControl.userRoutingStatus}/${userId}`
  );
};

export const fireUpdateUserRoutingStatus = (
  userId: string,
  basePath = '',
  statusId: string
) => {
  return axiosInstance.patch(
    `${basePath}${apiConfig.paths.callControl.updateRoutingStatus}/${userId}`,
    { presenceId: statusId }
  );
};

export const fireGetAllAgentStatus = (basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.callControl.getAllAgentStatus}`
  );
};
export const fireGetAllStations = (basePath = '', page = 1, name?: string) => {
  const params = new URLSearchParams();
  params.append('pageNum', page.toString());
  if (name) {
    params.append('name', name);
  }
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.callControl.getAllStations}?${params}`
  );
};

export const fireGetCurrentStation = (userId: string, basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.callControl.getAllStations}/users/${userId}`
  );
};

export const fireGetUpdateStation = (
  userId: string,
  newStationId: string,
  oldStationId: string | undefined,
  basePath = ''
) => {
  return axiosInstance.put(
    `${basePath}${apiConfig.paths.callControl.getAllStations}/users/${userId}/associate`,
    {
      newStationId,
      oldStationId,
    }
  );
};

export const fireDeleteCurrentStation = (
  userId: string,
  stationId: string,
  basePath = ''
) => {
  return axiosInstance.delete(
    `${basePath}${apiConfig.paths.callControl.getAllStations}/users/${userId}/available/${stationId}`
  );
};

export const fireMakeCall = (
  payload: { phoneNumber?: string; callUserId?: string; callQueueId?: string },
  basePath = ''
) => {
  return axiosInstance.post(`${basePath}${apiConfig.paths.callControl.call}`, {
    ...payload,
  });
};

export const fireTransferCall = (
  conversationId: string,
  agentParticipantId: string,
  payload: {
    destinationUserId?: string;
    destinationAddress?: string;
    destinationQueueId?: string;
  },
  basePath = ''
) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.callControl.blindTransfer}`,
    {
      conversationId,
      agentParticipantId,
      ...payload,
    }
  );
};

export const fireConsultCall = (
  conversationId: string,
  customerParticipantId: string,
  payload: {
    destinationUserId?: string;
    destinationAddress?: string;
    destinationQueueId?: string;
  },
  basePath = ''
) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.callControl.consult}`,
    {
      conversationId,
      customerParticipantId,
      ...payload,
    }
  );
};

export const fireConsultDisconnect = (
  state: string,
  conversationId: string,
  agentParticipantId: string,
  basePath = ''
) => {
  return axiosInstance.patch(
    `${basePath}${apiConfig.paths.callControl.consultDisconnect}`,
    {
      state: state,
      conversationId: conversationId,
      agentParticipantId: agentParticipantId,
    }
  );
};

// ref: https://github.com/axios/axios/issues/5682
// axios delete requests with a body need it to be set under a data key
export const fireConsultCancel = (
  conversationId: string,
  customerParticipantId: string,
  basePath = ''
) => {
  return axiosInstance.delete(
    `${basePath}${apiConfig.paths.callControl.consultCancel}`,
    {
      data: {
        conversationId: conversationId,
        customerParticipantId: customerParticipantId,
      },
    }
  );
};

export const fireConferenceCall = (
  speakTo: string,
  conversationId: string,
  customerParticipantId: string,
  basePath = ''
) => {
  return axiosInstance.patch(
    `${basePath}${apiConfig.paths.callControl.conference}`,
    {
      speakTo: speakTo,
      conversationId: conversationId,
      customerParticipantId: customerParticipantId,
    }
  );
};

export const fireIVRConferenceCall = (
  conversationId: string,
  destinationAddress: string,
  basePath = ''
) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.callControl.conference}`,
    {
      conversationId: conversationId,
      destinationAddress: destinationAddress,
    }
  );
};

export const fireGetDirectory = (basePath = '', page = 1, keyword?: string) => {
  const params = new URLSearchParams();
  params.append('pageNum', page.toString());
  params.append('filterType', 'all');

  if (keyword) {
    return axiosInstance.post(
      `${basePath}${apiConfig.paths.users.searchUsers}`,
      {
        keyword,
      }
    );
  } else {
    return axiosInstance.get(
      `${basePath}${apiConfig.paths.users.getAllUsers}?${params}`
    );
  }
};

export const fireGetCurrentUser = (basePath = '') => {
  return axiosInstance
    .get(`${basePath}${apiConfig.paths.users.getCurrentUser}`)
    .then((res) => res.data);
};
export const fireGetAllActiveConverstaions = (
  basePath = '',
  userId = '',
  deviceId = ''
) => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.callControl.getAllActiveConversations}?userId=${userId}&deviceId=${deviceId}`
  );
};

export const fireGetAllConversations = (basePath = '', userId = '') => {
  return axiosInstance
    .get(
      `${basePath}${apiConfig.paths.callControl.getAllConversations}?userId=${userId}`
    )
    .then((res) => res?.data);
};

export const fireGetAllWorkgroups = (
  basePath = '',
  page = 1,
  keyword?: string
) => {
  const params = new URLSearchParams();
  params.append('pageNumber', page.toString());
  params.append('filterType', 'all');

  if (keyword) {
    params.append('keyword', keyword);

    return axiosInstance.get(
      `${basePath}${apiConfig.paths.users.searchWorkgroups}?${params}`
    );
  } else {
    return axiosInstance.get(
      `${basePath}${apiConfig.paths.users.getAllWorkgroups}?${params}`
    );
  }
};

export const fireGetWorkgroupsByUser = (
  basePath = '',
  page = 1,
  userId: string
) => {
  const params = new URLSearchParams();
  params.append('filterType', 'user');
  params.append('userId', userId);
  params.append('pageNumber', page.toString());

  return axiosInstance.get(
    `${basePath}${apiConfig.paths.users.getWorkGroupsByUser}?${params}`
  );
};

export const fireGetMiniWallboardUserStat = (
  basePath = '',
  startDate?: string,
  endDate?: string,
  userId?: string
) => {
  axiosInstance.interceptors.request.use((config) => {
    config.headers['startDate'] = startDate;
    config.headers['endDate'] = endDate;
    config.headers['userId'] = userId;

    return config;
  });

  return axiosInstance.get(
    `${basePath}${apiConfig.paths.miniWallboard.getUserStat}`
  );
};

export const fireGetMiniWallboardUserConversationStat = (
  basePath = '',
  startDate?: string,
  endDate?: string,
  userId?: string
) => {
  axiosInstance.interceptors.request.use((config) => {
    config.headers['startDate'] = startDate;
    config.headers['endDate'] = endDate;
    config.headers['userId'] = userId;

    return config;
  });

  return axiosInstance.get(
    `${basePath}${apiConfig.paths.miniWallboard.getUserConversationStat}`
  );
};

export const fireGetMiniWallboardQueueStat = (
  basePath = '',
  startDate?: string,
  endDate?: string,
  userId?: string
) => {
  axiosInstance.interceptors.request.use((config) => {
    config.headers['userId'] = userId;

    return config;
  });

  return axiosInstance.get(
    `${basePath}${apiConfig.paths.miniWallboard.getQueueStat}`
  );
};

export const fireGetMiniWallboardQueueConversationStat = (
  basePath = '',
  startDate?: string,
  endDate?: string,
  userId?: string
) => {
  axiosInstance.interceptors.request.use((config) => {
    config.headers['startDate'] = startDate;
    config.headers['endDate'] = endDate;
    config.headers['userId'] = userId;

    return config;
  });

  return axiosInstance.get(
    `${basePath}${apiConfig.paths.miniWallboard.getQueueConversationStat}`
  );
};

export const fireGetWrapupCode = (
  basePath = '',
  participantId: string,
  conversationId: string
) => {
  const params = new URLSearchParams();
  params.append('participantId', participantId);
  params.append('conversationId', conversationId);

  return axiosInstance.get(
    `${basePath}${apiConfig.paths.callControl.getWrapup}?${params}`
  );
};

export const fireGetWrapupCategory = (userId: string, basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.callControl.getWrapupCategory}?userId=${userId}`
  );
};

export const fireSubmitAttributes = (
  conversationId: string,
  participantId: string,
  attributes: object,
  basePath = ''
) => {
  return axiosInstance.patch(
    `${basePath}${apiConfig.paths.callControl.attributes}`,
    {
      conversationId,
      participantId,
      attributes,
    }
  );
};

export const fireGetTenantConfig = (basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.config.getTenatConfig}`
  );
};

export const fireGetCustomerInfo = (body: any, basePath = '') => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.callControl.getCustomerInfo}`,
    {
      ...body,
    }
  );
};
export const fireGetCustomerHistory = (body: any, basePath = '') => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.callControl.getCustomerHistory}`,
    {
      ...body,
    }
  );
};
export const fireGetInteractionHistoryDetail = (convId: any, basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.callControl.getInteractionHistoryDetail}/${convId}`
  );
};
export const fireGetContactDetail = (convId: any, basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.callControl.getContactDetail}/${convId}`
  );
};
export const fireGetSAADetail = (body: any, basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.users.getSAA}?search=${body?.search || ''}&nextPageLink=${body?.nextPageLink || ''}`
  );
};
export const fireGetInteractionHistoryRecordingId = (
  convId: any,
  basePath = ''
) => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.callControl.getInteractionHistoryRecordingId}/${convId}`
  );
};
export const fireGetInteractionHistoryRecording = (rId: any, basePath = '') => {
  return axiosDownloadInstance({
    method: 'GET',
    url:
      basePath +
      `${apiConfig.paths.callControl.getInteractionHistoryRecording}/${rId}`,
    responseType: 'blob',
  });
  // return axiosInstance.get(
  //   `${basePath}${apiConfig.paths.callControl.getInteractionHistoryRecording}/${rId}`,{
  //     responseType: 'blob'
  //   }
  // );
};

export const fireGetSingleHistoryConversation = (
  conversationId: string,
  basePath = ''
) => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.callControl.getSingleHistoryConversation}/${conversationId}`
  );
};

export const fireGetSingleActiveConversation = (
  conversationId: string,
  basePath = ''
) => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.callControl.getSingleActiveConversation}/${conversationId}`
  );
};
export const getTemplate = (
  basePath = '',
  spaceId = '',
  keyword = '',
  spaceType = ''
) => {
  // 创建URL参数
  const params = new URLSearchParams();

  // 始终添加spaceId参数（即使是空值）
  params.append('spaceId', spaceId);

  // 始终添加keyword参数（即使是空值）
  params.append('title', keyword);

  params.append('spaceType', spaceType);

  // 构建请求URL，添加参数字符串
  const url = `${basePath}${apiConfig.paths.conv.getTemplate}?${params.toString()}`;

  return axiosInstance.get(url);
};
export const getTemplateDetail = (basePath = '', templateId = '') => {
  // 创建URL参数
  const params = new URLSearchParams();

  // 添加contentId参数
  if (templateId) {
    params.append('contentId', templateId);
  }

  // 构建请求URL，添加参数字符串
  const url = `${basePath}${apiConfig.paths.conv.getTemplateDetail}?${params.toString()}`;

  return axiosInstance.get(url);
};

export const fireGetMiniWallboardUserAggregates = (basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.miniWallboard.getUserAggregates}`
  );
};
export const fireGetMiniWallboardQueueAggregates = (basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.miniWallboard.getQueueAggregates}`
  );
};
export const CreateWhatsAppMessageConversation = (
  payload: { queueId: string; toAddress: string },
  basePath = ''
) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.message.createWhatsAppMessageConversation}`,
    { ...payload }
  );
};
export const GetSAAMatch = (payload: { content: string }, basePath = '') => {
  return axiosInstance.post(`${basePath}${apiConfig.paths.saa.matchContent}`, {
    ...payload,
  });
};

export const getEmailDetails = (conversationId: string, basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.conv.get_email_details.replace(
      ':conversationId',
      conversationId
    )}`
  );
};

export const getWhatsAppDetails = (conversationId: string, basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.conv.get_whatsapp_messages.replace(
      ':conversationId',
      conversationId
    )}`
  );
};
export default axiosInstance;
