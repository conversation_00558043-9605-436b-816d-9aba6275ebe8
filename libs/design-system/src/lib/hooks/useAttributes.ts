import { toast } from '@cdss-modules/design-system';
import { fireSubmitAttributes, fireGetTenantConfig } from '@cdss-modules/design-system/lib/api';
import { useRouteHandler } from '@cdss-modules/design-system/context/RouteContext';
import { useQuery } from '@tanstack/react-query';
import { TAttributesRequest } from '@cdss-modules/design-system/@types/attributes';
export const useAttributes = () => {
  const { basePath } = useRouteHandler();
  const getTenantAttributesHandle = useQuery({
    queryKey: ['get-tenant-attribute'],
    queryFn: async () => {
      const res = await fireGetTenantConfig(basePath);
      const tenantConfig =
        res?.data?.data != null && JSON.parse(res.data.data.tenantConfig);
      return tenantConfig?.participantData;
    },
  });

  const submitAttributes = (
    conversationId: string,
    customerOrConsultParticipantId: string,
    attributes: TAttributesRequest
  ) => {
    return new Promise((resolve, reject) => {
      fireSubmitAttributes(
        conversationId,
        customerOrConsultParticipantId,
        attributes,
        basePath
      )
        .then((res) => {
          if (res?.data?.isSuccess) {
            resolve(res?.data?.isSuccess);
          } else {
            toast({
              variant: 'error',
              title: 'Error Submitting Participant Data',
              description: `These is an error: `,
            });
            reject();
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  };
  return {
    submitAttributes,
    getTenantAttributesHandle,
  };
};
