import { useState, useEffect } from 'react';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
dayjs.extend(utc);

export const useKeepCountingTime = (baseTime: number, onQueue = false) => {
  const [time, setTime] = useState(baseTime); //baseTime is millisecond

  useEffect(() => {
    setTime(baseTime);
  }, [baseTime]);

  useEffect(() => {
    if (onQueue) {
      const interval = setInterval(() => {
        setTime((prevTime) => prevTime + 1000);
      }, 1000);
      return () => clearInterval(interval);
    }

    return;
  }, [onQueue]);

  const formattedResult = dayjs(time).utc().format('HH:mm:ss');

  return formattedResult;
};
