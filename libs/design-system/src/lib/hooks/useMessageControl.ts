import { CreateMessagePayLoad } from '@cdss-modules/design-system/@types/Message';
import { toast, useBlocking } from "@cdss-modules/design-system";
import { useRouteHandler } from '@cdss-modules/design-system/context/RouteContext';
import { CreateWhatsAppMessageConversation } from "@cdss-modules/design-system/lib/api";

export const useMessageControl = () => {
  const { basePath } = useRouteHandler();
  const {handleTriggerBlocking}=useBlocking()

  const newWhatsAppMessageConversation = (data: CreateMessagePayLoad): Promise<string> => {
    console.log("create payload", data);
    return new Promise((resolve, reject) => {
      handleTriggerBlocking(data?.toAddress,"whatsAppMessage").then((res)=>{
        if(res){
          return
        }
        CreateWhatsAppMessageConversation(data, basePath)
          .then((res) => {
            if (res?.data?.isSuccess) {
              resolve(res.data.conversationId); // 假设 conversationId 是成功响应中的一个字段
            } else {
              const errorMessage = parseErrorMessage(res?.data?.error);
              toast({
                variant: 'error',
                title: 'Error',
                description: errorMessage,
              });
              reject(new Error(errorMessage));
            }
          })
          .catch((error) => {
            let errorMessage = 'An error occurred';
            if (error.response) {
              // 请求已发出，但服务器响应的状态码不在 2xx 范围内
              const responseError = error.response.data.error;
              if (responseError) {
                errorMessage = parseErrorMessage(responseError);
              } else {
                errorMessage = `Error ${error.response.status}: ${error.response.statusText}`;
              }
            } else if (error.request) {
              // 请求已发出，但没有收到响应
              errorMessage = 'No response received';
            } else {
              // 一些设置请求时发生错误
              errorMessage = error.message;
            }
            toast({
              variant: 'error',
              title: 'Error',
              description: errorMessage,
            });
            reject(new Error(errorMessage));
          });

      })
    });
  };

  const parseErrorMessage = (error: any): string => {
    if (typeof error.message === 'string') {
      try {
        const parsedMessage = JSON.parse(error.message);
        return parsedMessage.message || parsedMessage.messageWithParams || 'An error occurred';
      } catch (parseError) {
        return error.message;
      }
    }
    return error.message || 'An error occurred';
  };

  return {
    newWhatsAppMessageConversation
  };
};
