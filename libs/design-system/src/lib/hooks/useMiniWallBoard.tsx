import { useState, useEffect } from 'react';
import { toast, useRole, useRoute<PERSON><PERSON><PERSON> } from '@cdss-modules/design-system';
import {
  fireGetMiniWallboardUserAggregates,
  fireGetMiniWallboardQueueAggregates,
} from '@cdss-modules/design-system/lib/api';
import { TMiniWallboardQueue, TMiniWallboardUser } from '@cdss-modules/design-system/@types/miniwallboard';

export const useMiniWallBoard = () => {
  const { basePath } = useRouteHandler();
  const { globalConfig } = useRole();
  const [queueMiniWallboardList, setQueueMiniWallboardList] = useState<
    TMiniWallboardQueue[]
  >([]);
  const [userMiniWallboardList, setUserMiniWallboardList] = useState<
    TMiniWallboardUser[]
  >([]);

  const microfrontendsConfig = globalConfig?.microfrontends
 const miniWallBoard = microfrontendsConfig?.["ctint-mf-cdss"]?.["functionConfig"]?.["miniWallBoard"];

  const getMiniWallboardUserAggregates = async () => {
    await fireGetMiniWallboardUserAggregates(basePath)
      .then((res: any) => {
        if (res?.data?.isSuccess) {
          setUserMiniWallboardList(res?.data?.data || {});
        }
      })
      .catch((error) => {
        return toast({
          variant: 'error',
          title: 'User State Error',
          description: `${error?.response?.data?.error}`,
        });
      });
  };

  const getMiniWallboardQueueAggregates = async () => {
    await fireGetMiniWallboardQueueAggregates(basePath)
      .then((res: any) => {
        if (res?.data?.isSuccess) {
          setQueueMiniWallboardList(res?.data?.data || []);
        }
      })
      .catch((error) => {
        return toast({
          variant: 'error',
          title: 'User Conversation Error',
          description: `${error?.response?.data?.error}`,
        });
      });
  };

  const handleEventSetQueueMiniwallBoard = (eventData: any) => {
    const newQueueMiniWallboardList = queueMiniWallboardList?.map((v) => {
      if (v?.queueId == eventData?.queueId) {
        v = { ...eventData, name: v.name };
      }
      return v;
    });
    setQueueMiniWallboardList(newQueueMiniWallboardList);
  };

  const handleEventSetUserMiniwallBoard = (eventData: any) => {
    setUserMiniWallboardList([eventData]);
  };

  const getMiniWallBoardInitiation = () => {
    miniWallBoard&&getMiniWallboardQueueAggregates();
    miniWallBoard&&getMiniWallboardUserAggregates();
  };
  useEffect(() => {
    getMiniWallBoardInitiation();
  }, []);
  return {
    handleEventSetUserMiniwallBoard,
    handleEventSetQueueMiniwallBoard,
    getMiniWallBoardInitiation,
    queueMiniWallboardList,
    userMiniWallboardList,
  };
};
