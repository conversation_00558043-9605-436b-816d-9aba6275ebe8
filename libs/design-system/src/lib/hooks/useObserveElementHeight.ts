import { useState, useRef, useEffect } from 'react';

export const useObserveElementHeight = <
  T extends HTMLElement | HTMLDivElement,
>() => {
  const [height, setHeight] = useState(0);
  const ref = useRef<T>(null);

  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      setHeight(entries[0].contentRect.height);
    });
    const { current } = ref;

    if (current) {
      observer.observe(current);
    }

    return () => {
      current && observer.unobserve(current);
    };
  }, []);

  return {
    height,
    ref,
  };
};
