trigger:
  tags:
    include:
      - 'release-image-fwd-mf-super-dashboard*'

variables:
  tenant: 'fwd'
  appName: 'ctint-mf-super-dashboard'
  imageRepository: '$(tenant)-mf-super-dashboard'
  containerRegistry: 'cdss3projectdevacr'
  containerRegistryUrl: 'cdss3projectdevacr.azurecr.io'
  tag: '1.0.$(Build.BuildId)'

stages:
- stage: Build
  displayName: Build Docker Image
  jobs:
  - job: Build
    displayName: Build and Push Docker Image
    pool:
      vmImage: 'ubuntu-latest'
    steps:
    - task: Docker@2
      displayName: 'Login to ACR'
      inputs:
        command: 'login'
        containerRegistry: '$(containerRegistry)'
    - task: Docker@2
      displayName: 'Build Docker Image'
      inputs:
        command: 'build'
        repository: '$(imageRepository)'
        containerRegistry: '$(containerRegistry)'
        Dockerfile: '**/Dockerfile'
        buildContext: '.'
        arguments: '--build-arg APP_NAME=$(appName)'
        tags: '$(tag)'
    - task: Docker@2
      displayName: 'Push Docker Image'
      inputs:
        command: 'push'
        repository: '$(imageRepository)'
        containerRegistry: '$(containerRegistry)'
        tags: '$(tag)'
