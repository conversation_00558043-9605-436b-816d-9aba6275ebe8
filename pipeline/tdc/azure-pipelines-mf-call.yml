trigger:
  tags:
    include:
    - release-tdc-mf-call*

variables:
  tenant: 'tdc'
  appName: 'ctint-mf-call'
  imageRepository: '$(tenant)-mf-call'
  containerRegistry: 'cdss3projectdevacr' 
  containerRegistryUrl: 'cdss3projectdevacr.azurecr.io' 
  kubernetesService: 'uat_project_aks_ctint_tdc' 
  tag: '1.0.$(Build.BuildId)'

stages:
- stage: Build
  displayName: Build Docker Image
  jobs:
  - job: Build
    displayName: Build and Push Docker Image
    pool:
      vmImage: 'ubuntu-latest'
    steps:
    - task: Docker@2
      displayName: 'Login to ACR'
      inputs:
        command: 'login'
        containerRegistry: '$(containerRegistry)'
    - task: Docker@2
      displayName: 'Build Docker Image'
      inputs:
        command: 'build'
        repository: '$(imageRepository)'
        containerRegistry: '$(containerRegistry)'
        Dockerfile: '**/Dockerfile'
        buildContext: '.'
        arguments: '--build-arg APP_NAME=$(appName)'
        tags: '$(tag)'
    - task: Docker@2
      displayName: 'Push Docker Image'
      inputs:
        command: 'push'
        repository: '$(imageRepository)'
        containerRegistry: '$(containerRegistry)'
        tags: '$(tag)'
  - job: DeployToK8s
    displayName: Deploy to AKS
    dependsOn: Build
    steps:
    - checkout: self
    - script: |
        echo "Updating deployment.yml with tag $(tag)"
        sed -i 's|image: .*|image: $(containerRegistryUrl)/$(imageRepository):$(tag)|' ./k8s/$(tenant)/deployment/$(appName)-deployment-uat.yaml
      displayName: Update deployment.yml with the new version
    - task: KubernetesManifest@0
      displayName: Deploy to Kubernetes cluster
      inputs:
        action: deploy
        manifests: |
          ./k8s/$(tenant)/deployment/$(appName)-deployment-uat.yaml
        kubernetesServiceConnection: '$(kubernetesService)'